var express = require('express');
var path = require('path');
var favicon = require('serve-favicon');
var logger = require('morgan');
var cookieParser = require('cookie-parser');
var bodyParser = require('body-parser');
var redisSession = require('node-redis-session');
var index = require('./routes/index');
var customer = require('./routes/customer/customer');
var news = require('./routes/news/news');
var loginRouter = require('./routes/login/login');
var sysuserRouter = require('./routes/sysuser/sysuser');
//角色管理
var role = require('./routes/systemmgr/role');
//部门
var department = require('./routes/systemmgr/department');
//权限管理
var permission = require('./routes/systemmgr/permission');
var investor = require('./routes/investor/investor');
var honor = require('./routes/honor/honor');
var approved = require('./routes/approved/approved');
//支付管理
var pay = require('./routes/systemmgr/pay');
//日志管理
var log = require('./routes/systemmgr/log');

var excelRouter = require('./routes/common/excelRouter');
var imageUploadRouter = require('./routes/common/imageUploadRouter');
var portalConfig = require('./libs/config');

// add <EMAIL>
var setTemplate = require('./routes/websiteManage/set-template');
var customModuleEdit = require('./routes/websiteManage/custom-module-edit');
var customBottomEdit = require('./routes/websiteManage/custom-bottom-edit');
var customNaviEdit = require('./routes/websiteManage/custom-navi-edit');
var customTemplate = require('./routes/websiteManage/custom-template-layout')
var templateChooseRouter = require('./routes/websiteManage/template-choose');
var setLink = require('./routes/websiteManage/set-link-list');
var tempModule = require('./routes/websiteManage/temp-module');
var tempBottom = require('./routes/websiteManage/temp-bottom');
var tempFooter = require('./routes/websiteManage/temp-footer');
var tempGoods = require('./routes/websiteManage/temp-goods');
var tempHeaderRouter = require('./routes/websiteManage/temp-header');
var tempSolutionLst = require('./routes/websiteManage/temp-solution-lst');
var tempmodulecommon = require('./routes/websiteManage/temp-module-common');
var tempIndustryLst = require('./routes/websiteManage/temp-industry-lst');
//商品分类
var goodsCategory = require('./routes/goodsCategory/goodsCategory');
//商品
var goods= require('./routes/goods/goods');
var vendor= require('./routes/vendor/vendor');
var enquiry= require('./routes/enquiry/enquiry');
var employment= require('./routes/employment/employment');
var fun= require('./routes/funs/funs');
//办事处管理
var agency = require('./routes/agency/agency');
var merchant= require('./routes/merchant/merchant');
//获取联动的路由
var district = require('./routes/common/district');

//联系我们
var contactUs = require('./routes/contactUs/contactUs');

//url选择路由
var urlSelectRouter = require('./routes/common/urlSelect');

//file路由
var fileRouter = require('./routes/common/fileRouter');

var app = express();

// view engine setup
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'jade');
app.locals.pretty = true;

// uncomment after placing your favicon in /public
//app.use(favicon(path.join(__dirname, 'public', 'favicon.ico')));
app.use(logger('dev'));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: false ,limit:"10000kb"}));
app.use(cookieParser());

// 修复静态文件服务 - 使用更简单的方式
app.use('/plugins', express.static(path.join(__dirname, 'public/plugins'), {
    maxAge: '1d',
    etag: false,
    lastModified: false
}));
app.use('/stylesheets', express.static(path.join(__dirname, 'public/stylesheets'), {
    maxAge: '1d',
    etag: false,
    lastModified: false
}));
app.use('/images', express.static(path.join(__dirname, 'public/images'), {
    maxAge: '1d',
    etag: false,
    lastModified: false
}));
app.use('/module', express.static(path.join(__dirname, 'public/module'), {
    maxAge: '1d',
    etag: false,
    lastModified: false
}));
app.use('/html', express.static(path.join(__dirname, 'public/html'), {
    maxAge: '1d',
    etag: false,
    lastModified: false
}));
app.use('/fontc', express.static(path.join(__dirname, 'public/fontc'), {
    maxAge: '1d',
    etag: false,
    lastModified: false
}));
app.use('/fonts', express.static(path.join(__dirname, 'public/fonts'), {
    maxAge: '1d',
    etag: false,
    lastModified: false
}));
app.use('/download', express.static(path.join(__dirname, 'public/download'), {
    maxAge: '1d',
    etag: false,
    lastModified: false
}));

// 处理favicon
app.get('/favicon.ico', function(req, res) {
    res.sendFile(path.join(__dirname, 'public/favicon.ico'));
});

app.use(redisSession({ cookieName: 'portalUid',expireTime:36000000,redisOptions:[portalConfig.redis.port, portalConfig.redis.ip,{auth_pass: portalConfig.redis.password}] }));

app.use('/login',loginRouter);
////负责登录，如果没有登录，直接跳转到登录页面
app.use(function(req, res, next){
  var reqUrl = req.originalUrl;
  console.log('系统拦截器，seesion中的用户信息:'+req.session.sysuser);
  if(req.session.sysuser){
    next();
  }else{
    //执行页面跳转的逻辑，直接跳转到登录页，逻辑暂时不去实现
    console.log("跳转到登录页面");
    res.render("login/forwardLogin",{reqUrl:reqUrl});
  }
});
app.use('/', index);
app.use('/sysuser', sysuserRouter);
app.use('/excel',excelRouter)
app.use('/imageUpload',imageUploadRouter)

app.use('/customer', customer);
app.use('/news', news);
app.use('/role', role);
app.use('/department', department);
app.use('/honor', honor);
app.use('/permission', permission);
app.use('/investor', investor);
app.use('/approved', approved);
// add <EMAIL>
app.use('/set-template', setTemplate);
app.use('/customModuleEdit', customModuleEdit);
app.use('/customBottomEdit', customBottomEdit);
app.use('/customNaviEdit', customNaviEdit);
app.use('/customTemplate', customTemplate);
app.use('/templateChoose', templateChooseRouter);
app.use('/setLink', setLink);
app.use('/tempModule',tempModule);
app.use('/tempbottom', tempBottom);
app.use('/tempfooter', tempFooter);
app.use('/tempgoods', tempGoods);
app.use('/tempsolutionlst',tempSolutionLst);
app.use('/tempindustrylst',tempIndustryLst);

app.use('/tempmodulecommon', tempmodulecommon);


//商品
app.use('/goodsCategory', goodsCategory);
app.use('/goods', goods);
app.use('/vendor', vendor);
app.use('/enquiry', enquiry);
app.use('/employment', employment);
app.use('/funt', fun);
//商家
app.use('/merchant', merchant);
//支付管理
app.use('/pay', pay);
//日志管理
app.use('/log', log);
app.use('/tempHeader',tempHeaderRouter);


//办事处管理
app.use('/agency', agency);
//联系我们
app.use('/contactUs', contactUs);
//地区
app.use('/district', district);
//url选择器
app.use('/us',urlSelectRouter);
app.use('/file',fileRouter);
// catch 404 and forward to error handler
app.use(function(req, res, next) {
  console.log('catch 404 and forward to error handler');
  var err = new Error('Not Found');
  err.status = 404;
  next(err);
});

// error handlers

// development error handler
// will print stacktrace
if (app.get('env') === 'development') {
  app.use(function(err, req, res, next) {
    res.status(err.status || 500);
    res.render('error', {
      message: err.message,
      error: err
    });
  });
}

// production error handler
// no stacktraces leaked to user
app.use(function(err, req, res, next) {
  res.status(err.status || 500);
  res.render('error', {
    message: err.message,
    error: {}
  });
});


module.exports = app;
