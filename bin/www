#!/usr/bin/env node

/**
 * Module dependencies.
 */

var app = require('../app');
var debug = require('debug')('portal:server');
var http = require('http');
//var RestApiProxy = require("../libs/RestApiProxy");
//var redisClient = require('../libs/redisClient');
var config = require('../libs/config');
var async = require('async');
/**
 * Get port from environment and store in Express.
 */
//var hasSend = {
//  lims_action:false,
//  lims_role:false,
//  lims_employee_role:false,
//  lims_role_action:false,
//  lims_action_group:false,
//  lims_group_child_list:false
//};

//var authHaddler = function(dataMap,key,path){

  //hasSend['key'] = true;
  //var sys = new RestApiProxy();
  //sys.on("error",function(httpcode,status,message){
  //  console.log("执行出现错误了");
  //});
  //sys.on("failure",function(data){
  //  console.log("执行失败了");
  //
  //});
  //sys.on('success',function(data){
  //  console.log("执行成功了");
  //  //缓存相关数据到redis中
  //  //action_list<Map<String,Object>>
  //  var data=JSON.stringify(data);
  //  redisClient.set(key,data);
  //});

//};




var port = normalizePort(process.env.PORT || '8088');
app.set('port', port);

/**
 * Create HTTP server.
 */

var server = http.createServer(app);

/**
 * Listen on provided port, on all network interfaces.
 */

server.listen(port);
server.on('error', onError);
server.on('listening', onListening);

/**
 * Normalize a port into a number, string, or false.
 */

function normalizePort(val) {
  var port = parseInt(val, 10);

  if (isNaN(port)) {
    // named pipe
    return val;
  }

  if (port >= 0) {
    // port number
    return port;
  }

  return false;
}

/**
 * Event listener for HTTP server "error" event.
 */

function onError(error) {
  if (error.syscall !== 'listen') {
    throw error;
  }

  var bind = typeof port === 'string'
    ? 'Pipe ' + port
    : 'Port ' + port;

  // handle specific listen errors with friendly messages
  switch (error.code) {
    case 'EACCES':
      console.error(bind + ' requires elevated privileges');
      process.exit(1);
      break;
    case 'EADDRINUSE':
      console.error(bind + ' is already in use');
      process.exit(1);
      break;
    default:
      throw error;
  }
}

/**
 * Event listener for HTTP server "listening" event.
 */

function onListening() {
  var addr = server.address();
  var bind = typeof addr === 'string'
    ? 'pipe ' + addr
    : 'port ' + addr.port;
  debug('Listening on ' + bind);
}
