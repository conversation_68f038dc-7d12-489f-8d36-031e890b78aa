#!/usr/bin/env node

const http = require('http');
const querystring = require('querystring');

function makeRequest(options, postData, cookies = '') {
    return new Promise((resolve, reject) => {
        if (cookies) {
            options.headers = options.headers || {};
            options.headers['Cookie'] = cookies;
        }
        
        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    body: data
                });
            });
        });
        
        req.on('error', (err) => {
            reject(err);
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

function extractCookies(headers) {
    const setCookieHeader = headers['set-cookie'];
    if (!setCookieHeader) return '';
    
    return setCookieHeader.map(cookie => cookie.split(';')[0]).join('; ');
}

async function detailedLoginTest() {
    console.log('🔍 详细登录测试');
    console.log('=' .repeat(60));
    
    try {
        // 测试1: 管理员登录
        console.log('\n1️⃣ 管理员登录测试');
        const adminLoginData = querystring.stringify({
            username: 'admin',
            password: 'rongzhilian1819'
        });
        
        const loginOptions = {
            hostname: 'localhost',
            port: 8088,
            path: '/login/li',
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Content-Length': Buffer.byteLength(adminLoginData)
            }
        };
        
        const adminResponse = await makeRequest(loginOptions, adminLoginData);
        const adminCookies = extractCookies(adminResponse.headers);
        const adminResult = JSON.parse(adminResponse.body);
        
        console.log('📊 管理员登录结果:');
        console.log(`   状态码: ${adminResponse.statusCode}`);
        console.log(`   响应: ${JSON.stringify(adminResult, null, 2)}`);
        console.log(`   Cookie: ${adminCookies}`);
        
        // 测试2: 使用管理员会话访问首页
        console.log('\n2️⃣ 管理员访问首页');
        const indexOptions = {
            hostname: 'localhost',
            port: 8088,
            path: '/index',
            method: 'GET'
        };
        
        const indexResponse = await makeRequest(indexOptions, null, adminCookies);
        console.log('📊 首页访问结果:');
        console.log(`   状态码: ${indexResponse.statusCode}`);
        console.log(`   内容类型: ${indexResponse.headers['content-type']}`);
        console.log(`   内容长度: ${indexResponse.body.length}`);
        console.log(`   内容预览: ${indexResponse.body.substring(0, 200)}...`);
        
        // 检查是否包含错误信息
        if (indexResponse.body.includes('error') || indexResponse.body.includes('Error')) {
            console.log('   ⚠️ 响应中包含错误信息');
        }
        
        // 测试3: 普通用户登录
        console.log('\n3️⃣ 普通用户登录测试');
        const userLoginData = querystring.stringify({
            username: 'testuser',
            password: 'testpass'
        });
        
        const userLoginOptions = {
            hostname: 'localhost',
            port: 8088,
            path: '/login/li',
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Content-Length': Buffer.byteLength(userLoginData)
            }
        };
        
        const userResponse = await makeRequest(userLoginOptions, userLoginData);
        const userCookies = extractCookies(userResponse.headers);
        const userResult = JSON.parse(userResponse.body);
        
        console.log('📊 普通用户登录结果:');
        console.log(`   状态码: ${userResponse.statusCode}`);
        console.log(`   响应: ${JSON.stringify(userResult, null, 2)}`);
        console.log(`   Cookie: ${userCookies}`);
        
        // 测试4: 测试API端点
        console.log('\n4️⃣ 测试API端点');
        const apiTests = [
            { path: '/sysuser', name: '用户管理' },
            { path: '/customer', name: '客户管理' },
            { path: '/news', name: '新闻管理' },
            { path: '/goods', name: '商品管理' }
        ];
        
        for (const api of apiTests) {
            try {
                const apiOptions = {
                    hostname: 'localhost',
                    port: 8088,
                    path: api.path,
                    method: 'GET'
                };
                
                const apiResponse = await makeRequest(apiOptions, null, adminCookies);
                console.log(`   ${api.name} (${api.path}): ${apiResponse.statusCode} - ${apiResponse.body.length}字符`);
                
                if (apiResponse.statusCode !== 200) {
                    console.log(`     响应内容: ${apiResponse.body.substring(0, 100)}...`);
                }
            } catch (error) {
                console.log(`   ${api.name} (${api.path}): ❌ 错误 - ${error.message}`);
            }
        }
        
        // 测试5: 检查Redis中的会话
        console.log('\n5️⃣ 检查Redis会话状态');
        const { exec } = require('child_process');
        
        exec('redis-cli keys "portal:session:*"', (error, stdout, stderr) => {
            if (error) {
                console.log(`   Redis查询错误: ${error.message}`);
            } else {
                const sessionKeys = stdout.trim().split('\n').filter(key => key);
                console.log(`   活跃会话数: ${sessionKeys.length}`);
                sessionKeys.forEach((key, index) => {
                    console.log(`   会话${index + 1}: ${key}`);
                });
            }
        });
        
        // 等待Redis查询完成
        await new Promise(resolve => setTimeout(resolve, 1000));
        
    } catch (error) {
        console.log(`❌ 测试过程中出错: ${error.message}`);
        console.log(`错误堆栈: ${error.stack}`);
    }
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎯 详细测试完成');
}

// 运行测试
detailedLoginTest().catch(console.error);
