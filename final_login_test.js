#!/usr/bin/env node

const http = require('http');
const querystring = require('querystring');

function makeRequest(options, postData, cookies = '') {
    return new Promise((resolve, reject) => {
        if (cookies) {
            options.headers = options.headers || {};
            options.headers['Cookie'] = cookies;
        }
        
        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    body: data
                });
            });
        });
        
        req.on('error', (err) => {
            reject(err);
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

function extractCookies(headers) {
    const setCookieHeader = headers['set-cookie'];
    if (!setCookieHeader) return '';
    
    return setCookieHeader.map(cookie => cookie.split(';')[0]).join('; ');
}

async function finalLoginTest() {
    console.log('🎯 最终登录功能测试');
    console.log('=' .repeat(60));
    
    try {
        // 测试1: 管理员登录并访问首页
        console.log('\n1️⃣ 管理员完整登录流程测试');
        
        const adminLoginData = querystring.stringify({
            username: 'admin',
            password: 'rongzhilian1819'
        });
        
        const loginOptions = {
            hostname: 'localhost',
            port: 8088,
            path: '/login/li',
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Content-Length': Buffer.byteLength(adminLoginData)
            }
        };
        
        // 登录
        const loginResponse = await makeRequest(loginOptions, adminLoginData);
        const cookies = extractCookies(loginResponse.headers);
        const loginResult = JSON.parse(loginResponse.body);
        
        console.log(`📝 登录状态: ${loginResponse.statusCode}`);
        console.log(`📝 登录结果: ${loginResult.code === 0 ? '✅ 成功' : '❌ 失败'}`);
        console.log(`📝 用户信息: ${loginResult.data.user.name} (${loginResult.data.user.username})`);
        console.log(`📝 用户角色: ${JSON.stringify(loginResult.data.user.roles)}`);
        
        // 访问首页
        const indexOptions = {
            hostname: 'localhost',
            port: 8088,
            path: '/index',
            method: 'GET'
        };
        
        const indexResponse = await makeRequest(indexOptions, null, cookies);
        console.log(`🏠 首页访问: ${indexResponse.statusCode}`);
        
        if (indexResponse.body.includes('权限校验失败')) {
            console.log(`🏠 首页结果: ❌ 权限校验失败`);
        } else if (indexResponse.statusCode === 200 && indexResponse.body.length > 1000) {
            console.log(`🏠 首页结果: ✅ 成功访问 (${indexResponse.body.length}字符)`);
        } else {
            console.log(`🏠 首页结果: ⚠️ 响应异常 (${indexResponse.body.length}字符)`);
        }
        
        // 测试各个管理页面
        console.log('\n2️⃣ 管理页面访问测试');
        const pages = [
            { path: '/sysuser', name: '用户管理' },
            { path: '/customer', name: '客户管理' },
            { path: '/news', name: '新闻管理' },
            { path: '/goods', name: '商品管理' },
            { path: '/role', name: '角色管理' },
            { path: '/department', name: '部门管理' }
        ];
        
        for (const page of pages) {
            const pageOptions = {
                hostname: 'localhost',
                port: 8088,
                path: page.path,
                method: 'GET'
            };
            
            const pageResponse = await makeRequest(pageOptions, null, cookies);
            
            if (pageResponse.body.includes('权限校验失败')) {
                console.log(`   ${page.name}: ❌ 权限校验失败`);
            } else if (pageResponse.statusCode === 200) {
                console.log(`   ${page.name}: ✅ 访问成功`);
            } else {
                console.log(`   ${page.name}: ⚠️ 状态码 ${pageResponse.statusCode}`);
            }
        }
        
        // 测试3: 普通用户登录
        console.log('\n3️⃣ 普通用户登录测试');
        
        const userLoginData = querystring.stringify({
            username: 'testuser',
            password: 'anypassword'
        });
        
        const userLoginResponse = await makeRequest(loginOptions, userLoginData);
        const userCookies = extractCookies(userLoginResponse.headers);
        const userLoginResult = JSON.parse(userLoginResponse.body);
        
        console.log(`📝 普通用户登录: ${userLoginResult.code === 0 ? '✅ 成功' : '❌ 失败'}`);
        console.log(`📝 用户信息: ${userLoginResult.data.user.name} (${userLoginResult.data.user.username})`);
        
        // 普通用户访问首页
        const userIndexResponse = await makeRequest(indexOptions, null, userCookies);
        if (userIndexResponse.body.includes('权限校验失败')) {
            console.log(`🏠 普通用户首页: ❌ 权限校验失败`);
        } else if (userIndexResponse.statusCode === 200) {
            console.log(`🏠 普通用户首页: ✅ 访问成功`);
        }
        
        // 普通用户访问管理页面（应该失败）
        const userSysuserResponse = await makeRequest({
            hostname: 'localhost',
            port: 8088,
            path: '/sysuser',
            method: 'GET'
        }, null, userCookies);
        
        if (userSysuserResponse.body.includes('权限校验失败')) {
            console.log(`👥 普通用户访问用户管理: ✅ 正确拒绝访问`);
        } else {
            console.log(`👥 普通用户访问用户管理: ❌ 权限控制失效`);
        }
        
        // 测试4: 登出功能
        console.log('\n4️⃣ 登出功能测试');
        
        const logoutOptions = {
            hostname: 'localhost',
            port: 8088,
            path: '/login/lo',
            method: 'GET'
        };
        
        const logoutResponse = await makeRequest(logoutOptions, null, cookies);
        console.log(`🚪 登出状态: ${logoutResponse.statusCode}`);
        
        if (logoutResponse.body.includes('登录') || logoutResponse.body.includes('login')) {
            console.log(`🚪 登出结果: ✅ 成功重定向到登录页`);
        } else {
            console.log(`🚪 登出结果: ⚠️ 响应异常`);
        }
        
        // 验证登出后无法访问
        const afterLogoutResponse = await makeRequest(indexOptions, null, cookies);
        if (afterLogoutResponse.body.includes('登录') || afterLogoutResponse.body.includes('login')) {
            console.log(`🔒 登出后访问控制: ✅ 正确重定向到登录页`);
        } else {
            console.log(`🔒 登出后访问控制: ❌ 仍能访问受保护页面`);
        }
        
    } catch (error) {
        console.log(`❌ 测试过程中出错: ${error.message}`);
    }
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎉 最终测试完成！');
}

// 运行测试
finalLoginTest().catch(console.error);
