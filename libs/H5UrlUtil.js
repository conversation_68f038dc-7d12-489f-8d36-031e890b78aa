/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2016-09-08 .
 */
var config = require('../libs/config.js');
var constants = require('../libs/constants.js');
var log4js = require('../libs/log4jsUtil.js');
var logger = log4js.getLogger('h5UrlUtil');
var h5UrlUtil = {};

/**
 *将菜单对象转化为url
 * @param menuObj 菜单对象
 * {
          "type":"GOODS_LIST",
          "name":"商品列表",
          "data":""
       }
 */
h5UrlUtil.menuObjToUrl = function(wxData,menuObj){
    switch (menuObj.type) {
        case constants.MENU_URL_TYPE.GOODS://商品
        {
            var url = genWxUrl(wxData.appId,menuObj,'/goods/qd/'+menuObj.data);
            return url;
        }
        case constants.MENU_URL_TYPE.GOODS_LIST://商品列表
        {
            var url = genWxUrl(wxData.appId,menuObj,'/goods/index');
            return url;
        }
        case constants.MENU_URL_TYPE.NEWS://新闻
        {
            var url = genWxUrl(wxData.appId,menuObj,'/news/qd/'+menuObj.data);
            return url;
        }
        case constants.MENU_URL_TYPE.NEWS_LIST://新闻列表
        {
            var url = genWxUrl(wxData.appId,menuObj,'/news/index');
            return url;
        }
        case constants.MENU_URL_TYPE.SAMPLE_BIND://样品绑定
        {
            var url = genWxUrl(wxData.appId,menuObj,'/sampling/s');
            return url;
        }
        case constants.MENU_URL_TYPE.REPORT_PROCESS://报告进度
        {
            var url = genWxUrl(wxData.appId,menuObj,'/sampling/ss');
            return url;
        }
        case constants.MENU_URL_TYPE.CUST_PAGE://自定义页面
        {
            var url = genWxUrl(wxData.appId,menuObj,'/custpage/by/page/'+menuObj.data);
            return url;
        }
        case constants.MENU_URL_TYPE.PAGE_MGR://页面管理
        {
            var url = genWxUrl(wxData.appId,menuObj,'/websitecontent/index/'+menuObj.data);
            return url;
        }
        case constants.MENU_URL_TYPE.REPORT_CENTER://报告中心
        {
            var url = genWxUrl(wxData.appId,menuObj,'/report/index');
            return url;
        }
        case constants.MENU_URL_TYPE.MY_ORDER://我的订单
        {
            var url = genWxUrl(wxData.appId,menuObj,'/order');
            return url;
        }
        case constants.MENU_URL_TYPE.OTHERS://其他，自定义的url
        {
            return menuObj.data;
        }
        default:
        {
            return genWxUrl(wxData.appId,menuObj,'/');
        }
    }
}
/**
 *生成微信授权url
 * @param appId 微信appId
 * @param menuObj 菜单对象
 *
    {
          "type":"GOODS_LIST",
          "name":"商品列表",
          "data":""
    }
 * @param orgnUrl 原始的相对url
 */
function genWxUrl(appId,menuObj,orgnUrl){
    var reditUrl =config.weixinApp.domainUrl+orgnUrl;
    //logger.info('加密前：'+JSON.stringify(menuObj));
    var base64Str = new Buffer(JSON.stringify(menuObj),'utf-8').toString("base64");//state参数，通过base64编码和解码
    //logger.info('加密后：'+base64Str);
    //var decodeStr = new Buffer(base64Str,'base64').toString("utf-8");
    //logger.info('解密后：'+decodeStr);
    var url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +appId+
        '&redirect_uri=' +reditUrl+
        '&response_type=code&scope=snsapi_base&state=' +
        base64Str.replace('=','') +  '#wechat_redirect';
    logger.info('url：'+url);
    return url;
}

/**
 * 解析url，将url转化为菜单对象
 * @param url h5的网址
 * @param callback 回调
 * return
 * {
          "type":"GOODS_LIST",
          "name":"商品列表",
          "data":""
    }
 */
h5UrlUtil.urlToMenuObj = function(url){

    var endFlag ='#wechat_redirect';
    var endIndex = url.indexOf(endFlag);
    if(endIndex==-1){
        var menuData = {};
        menuData.type=constants.MENU_URL_TYPE.OTHERS;
        menuData.data=url;
        return menuData;
    }else{
        var startFlag ='&state=';
        var startIndex = url.indexOf(startFlag)+startFlag.length;
        var state = url.substring(startIndex,endIndex);
        var decodeStr = new Buffer(state,'base64').toString("utf-8");
        //logger.info(state);
        logger.info('解密后：'+decodeStr);
        return JSON.parse(decodeStr);
    }

}

module.exports = h5UrlUtil;