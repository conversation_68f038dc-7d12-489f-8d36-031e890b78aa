/**
 * 权限控制功能点常量
 */
var actionsConstants = {};

//<EMAIL> begin
actionsConstants.QUESTIONNAIRE_ADD = 'questionnaireAdd'; //调查问卷添加
actionsConstants.QUESTIONNAIRE_UPDATE = 'questionnaireUpdate'; //调查问卷修改
actionsConstants.QUESTIONNAIRE_DELETE  = 'questionnaireDelete'; //调查问卷删除
actionsConstants.QUESTIONNAIRE_MGR  = 'questionnaireMgr'; //调查问卷列表查询
actionsConstants.QUESTIONNAIRE_SINGLE  = 'questionnaireSingle'; //单个调查问卷查询
actionsConstants.QUESTIONNAIRE_DELETE  = 'questionnaireDelete'; //调查问卷删除
actionsConstants.QUESTIONNAIRE_RELEASE='questionnaireRelease';//调查问卷发布
actionsConstants.QUESTIONNAIRE_RESULT='questionnaireResult';//调查问卷结果查询

//<EMAIL> end

/**
 * 采样盒类型管理
 * @type {String}
 */
actionsConstants.SAMPLING_BOX_TYPE = 'samplingBoxType';
actionsConstants.SAMPLEBOX_TYPE_ADD = 'sampleBoxTypeAdd';//添加采样盒类型
actionsConstants.SAMPLEBOX_TYPE_DELETE = 'sampleBoxTypeDelete';//删除采样盒类型
actionsConstants.SAMPLEBOX_TYPE_EDIT='sampleBoxTypeEdit';//采样盒类型 编辑
/**
 * 采样盒库存管理
 * @type {String}
 */
actionsConstants.SAMPLING_CODE_MGR = 'samplingCodeMgr';
actionsConstants.SAMPLING_CODE_LIST = 'barcodeList';
actionsConstants.SAMPLING_CODE_STORAGE = 'barcodeStorage';
actionsConstants.SAMPLING_CODE_IMPORT = 'barcodeImport';
actionsConstants.SAMPLING_CODE_TEMP_DOWNLOAD = 'barcodeTempDownload';
actionsConstants.SAMPLING_CODE_DETAIL = 'barcodeDetail';
actionsConstants.SAMPLING_CODE_DELETE = 'barcodeDelete';
/**
 * 样品检测管理
 * @type {string}
 */
actionsConstants.SAMPLING_TEST_MGR = 'samplingTestMgr';
actionsConstants.SAMPLING_TEST_LIST = 'sampleList';
actionsConstants.SAMPLING_TEST_EDIT = 'sampleStatusEdit';
actionsConstants.SAMPLING_TEST_REPORT_UPLOAD = 'sampleReportUpload';
actionsConstants.SAMPLING_TEST_IMPORT = 'sampleStatusImport';
/**
 * 对账管理
 * @type {string}
 */
actionsConstants.ACCOUNT_CHECK_MGR = 'accountCheckMgr';
actionsConstants.ACCOUNT_CHECK_LIST = 'acList';
actionsConstants.ACCOUNT_CHECK_DETAIL = 'acDetail';
actionsConstants.ACCOUNT_CHECK_REMARK = 'acRemark';
actionsConstants.ACCOUNT_CHECK_EXPORT = 'acExport';
actionsConstants.ACCOUNT_CHECK_CONFIRM ='acConfirm';
/**
 * 条码管理
 * @type {String}
 */
actionsConstants.BARCODE_MGR = 'barcodeMgr';
//生成编码
actionsConstants.BARCODE_GEN = 'barcodeGen';
//编码查询
actionsConstants.BARCODE_LIST = 'barcodeList';
//删除编码
actionsConstants.BARCODE_DELETE = 'barcodeDelete';
//导出编码
actionsConstants.BARCODE_EXPORT = 'barcodeExport';
//打印编码
actionsConstants.BARCODE_PRINT = 'barcodePrint';
//编码详情
actionsConstants.BARCODE_DETAIL = 'barcodeDetail';

//chendongjie begin-------------------------------------------------------------
//首页
actionsConstants.INDEX='index';

//商品
actionsConstants.GOODS_LIST='goodsList';//商品 查询
actionsConstants.GOODS_EDIT='goodsEdit';//商品 编辑
actionsConstants.GOODS_ADD='goodsAdd';//商品 新增
actionsConstants.GOODS_DELETE='goodsDelete';//商品 删除
actionsConstants.GOODS_UP_DOWN='goodsUpDown';//商品 上架、下架
actionsConstants.GOODS_REVIEW='goodsReview';//商品 预览
actionsConstants.GOODS_UPLOAD_FILE = 'goodsUploadFile';//上传商品附件

//厂商
actionsConstants.VENDOR_LIST='vendorList';//厂商 查询
actionsConstants.VENDOR_EDIT='vendorEdit';//厂商 编辑
actionsConstants.VENDOR_ADD='vendorAdd';//厂商 新增
actionsConstants.VENDOR_DELETE='vendorDelete';//厂商 删除

//询价
actionsConstants.ENQUIRY_LIST='enquiryList';//询价 查询
actionsConstants.ENQUIRY_ADD='enquiryAdd';//询价 查询

//招聘信息
actionsConstants.EMPLOYMENT_LIST='employmentList';// 查询
actionsConstants.EMPLOYMENT_EDIT='employmentEdit';// 编辑
actionsConstants.EMPLOYMENT_ADD='employmentAdd';// 新增
actionsConstants.EMPLOYMENT_DELETE='employmentDelete';// 删除
actionsConstants.EMPLOYMENT_FUNS_MAINTAIN='funsMaintain';// 职能维护

//商品分类
actionsConstants.GOODCATEGORY_LIST='goodsCategoryList';//商品分类 查询
actionsConstants.GOODCATEGORY_ADD='goodsCategoryAdd';//商品分类 新增
actionsConstants.GOODCATEGORY_DELETE='goodsCategoryDelete';//商品分类 删除
actionsConstants.GOODCATEGORY_EDIT='goodsCategoryEdit';//商品分类 编辑
//投资者关系管理
actionsConstants.INVESTOR_LIST='investorList';//商品分类 查询
actionsConstants.INVESTOR_ADD='investorAdd';//商品分类 新增
actionsConstants.INVESTOR_DELETE='investorDelete';//商品分类 删除
//投资者关系管理
actionsConstants.APPROVED_LIST='approvedList';//审核管理 查询
actionsConstants.APPROVED_AUDIT='approvedAudit';//审核 
actionsConstants.APPROVED_RELEASE='approvedPublic';//发布
actionsConstants.APPROVED_VIEW='approvedView';//预览变更

//账号管理
actionsConstants.SYSUSER_LIST='sysuserList';//用户列表
actionsConstants.SYSUSER_ADD='sysuserAdd';//用户新增
actionsConstants.SYSUSER_EDIT='sysuserEdit';//用户编辑
actionsConstants.SYSUSER_EFFECT='sysuserEffect';//用户 启用、停用
actionsConstants.SYSUSER_RESET_PWD='sysuserResetPwd';//用户 修改密码
actionsConstants.SYSUSER_DELETE='sysuserDelete';//用户删除
actionsConstants.SYSUSER_EXPORT='sysuserExport';//用户导出
actionsConstants.SYSUSER_IMPORT='sysuserImport';//用户导入
//角色权限管理
actionsConstants.ROLE_LIST='roleList';//角色列表
actionsConstants.ROLE_ADD='roleAdd';//角色新增
actionsConstants.ROLE_EDIT='roleEdit';//角色信息修改
actionsConstants.ROLE_DELETE='roleDelete';//角色删除
//部门权限管理
actionsConstants.DEPARTMENT_LIST='departmentList';//部门列表
actionsConstants.DEPARTMENT_ADD='departmentAdd';//部门新增
actionsConstants.DEPARTMENT_EDIT='departmentEdit';//部门信息修改
actionsConstants.DEPARTMENT_DELETE='departmentDelete';//部门删除
//企业荣誉管理
actionsConstants.HONOR_LIST='honortList';//企业荣誉列表
actionsConstants.HONOR_ADD='honorAdd';//企业荣誉新增
actionsConstants.HONOR_EDIT='honorEdit';//企业荣誉信息修改
actionsConstants.HONOR_DELETE='honorDelete';//企业荣誉删除
actionsConstants.HONOR_ADD_IMAGE='honorAddImage';//企业荣誉图片上传
//权限设置
actionsConstants.ACTION_SET='actionSet';//权限设置

//订单管理
actionsConstants.ORDER_MGR='orderMgr';//订单管理
actionsConstants.ORDER_DETAIL='orderDetail';//订单详情
actionsConstants.ORDER_EXPORT='orderExport';//订单导出
actionsConstants.ORDER_REMARK='orderRemark';//订单备注
actionsConstants.ORDER_SHIPPING='orderShipping';//发货
actionsConstants.ORDER_INSTEAD='orderInstead';//带下单

//支付方式管理
actionsConstants.PAYTYPE_MGR='payTypeMgr';//支付方式管理
//日志管理
actionsConstants.LOG_MGR='logMgr';//日志管理

//页面管理
actionsConstants.WEBSITECONTENT_MGR='websiteContentMgr';//页面管理
actionsConstants.WEBSITECONTENT_ADD='websiteContentAdd';//页面管理添加
actionsConstants.WEBSITECONTENT_UPDATE='websiteContentUpdate';//页面管理更新
actionsConstants.WEBSITECONTENT_DELETE='websiteContentDelete';//页面管理删除
actionsConstants.WEBSITECONTENT_PREVIEW='websiteContentPreview';//页面管理预览

//商家物流管理
actionsConstants.SHIPPING_MGR = 'shippingMgr';//物流管理
actionsConstants.SHIPPING_ADD = 'shippingAdd';//添加物流
actionsConstants.SHIPPING_DELETE = 'shippingDelete';//删除物流

//采样盒类型管理
actionsConstants.SAMPLEBOXTYPE_LIST='sampleBoxTypeList';//采样盒类型 查询
actionsConstants.SAMPLEBOXTYPE_ADD = 'sampleBoxTypeAdd';//添加采样盒类型
actionsConstants.SAMPLEBOXTYPE_DELETE = 'sampleBoxTypeDelete';//删除采样盒类型
actionsConstants.SAMPLEBOXTYPE_EDIT='sampleBoxTypeEdit';//编辑采样盒类型

//chendongjie end-----------------------------------------------------------------

/**
 * 新闻分类管理
 * @type {string}
 */
actionsConstants.NEWSCATEGORY_MGR = 'newsCategoryMgr';
actionsConstants.NEWSCATEGORY_ADD = 'newsCategoryAdd';
actionsConstants.NEWSCATEGORY_UPDATE = 'newsCategoryUpdate';
actionsConstants.NEWSCATEGORY_DELETE = 'newsCategoryDelete';

/**
 * 新闻管理
 * @type {string}
 */
actionsConstants.NEWS_PREVIEW = 'newsPreview';//预览
actionsConstants.NEWS_PUBLISH = 'newsPublish';//发布
actionsConstants.NEWS_UPDATE = 'newsUpdate';
actionsConstants.NEWS_ADD = 'newsAdd';
actionsConstants.NEWS_MGR = 'newsMgr';
actionsConstants.NEWS_DELETE = 'newsDelete';

/**
 * 用户管理
 * @type {string}
 */
actionsConstants.CUSTOMER_MGR = 'customerMgr';
actionsConstants.CUSTOMER_DETAIL = 'customerDetail';//单个用户信息
actionsConstants.CUSTOMER_EXPORT = 'customerExport';//导出
actionsConstants.EFFECT_CUSTOMER = 'effectCustomer';

/**
 * 模板管理
 * @type {string}
 */
actionsConstants.MODEL_MGR = 'modelMgr';
actionsConstants.MODEL_CHANGE = 'modelChange';
actionsConstants.MODEL_EDIT = 'modelEdit';

/**
 * 优惠券管理
 * @type {string}
 */
actionsConstants.COUPON_MGR = 'couponMgr';//优惠券 管理
actionsConstants.COUPON_ADD = 'couponAdd';//优惠券 新增
actionsConstants.COUPON_EDIT = 'couponEdit';//优惠券 编辑
actionsConstants.COUPON_DELETE = 'couponDelete';//优惠券 删除
actionsConstants.COUPON_PUBLISH = 'couponPublish';//优惠券 发布
actionsConstants.COUPON_PAUSE = 'couponPause';//优惠券 暂停
actionsConstants.COUPON_DETAIL = 'couponDetail';//优惠券 详情
actionsConstants.COUPON_CODEMGR = 'couponCodeMgr';//优惠券  券码管理
actionsConstants.COUPON_CODE_EXPORT = 'couponCodeExport';//券码导出

/**
 * 办事处管理
 * @type {String}
 */
actionsConstants.AGENCY_LIST = 'agencyList';
actionsConstants.AGENCY_MGR = 'agencyMgr';
actionsConstants.AGENCY_ADD = 'agencyAdd';
actionsConstants.AGENCY_EDIT = 'agencyEdit';
actionsConstants.AGENCY_DELETE = 'agencyDelete';
actionsConstants.AGENCY_DETAIL = 'agencyDetail';

/**
 * 联系我们
 * @type {String}
 */
actionsConstants.CONTACTUS_LIST = 'contactUsList';
actionsConstants.CONTACTUS_MGR = 'contactUsMgr';
actionsConstants.CONTACTUS_ADD = 'contactUsAdd';
actionsConstants.CONTACTUS_EDIT = 'contactUsEdit';
actionsConstants.CONTACTUS_DELETE = 'contactUsDelete';
actionsConstants.CONTACTUS_DETAIL = 'contactUsDetail';

//微信管理
/**
 * 公帐号管理
 * @type {string}
 */
actionsConstants.WX_CONFIG_MGR = 'wxConfigMgr';//公众号管理
actionsConstants.WX_CUST_MENU = 'wxCustMenu';//自定义菜单

//微信自定义页面管理
actionsConstants.WX_CUST_PAGE = 'wxCustPage';
module.exports = actionsConstants;