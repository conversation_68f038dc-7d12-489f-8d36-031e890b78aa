/**
 * Created by RONGLIAN on 2016/1/20.
 */
var restApiProxy = require('./RestApiProxy');
var querystring = require('querystring');
var url = require('url');
var cryptoUtils = require('./cryptoUtils');
var config = require('./config');

var authRestApiProxy = {};
/**
 *
 * @param systemName
 * @param option
 * @param callback 第一个参数为错误对象
 * id 1 请求后台服务超时
 * id 2 后台请求数据错误
 * id 3 请求后台服务错误
 * id 4  数字签名错误    --
 * id 5  未知错误    --
 */
authRestApiProxy.sendRequest = function (systemName, option, callback) {
    var systemsConfig = config[systemName];
    var postData = '';
    if (option['data']) {
        postData = option['data'];
    }
    postData = new Buffer(JSON.stringify(postData));
    //计算数字签名
    var mySignature = cryptoUtils.createSignature(systemsConfig['subsystemKey'], postData);
    var params = {
        access_signature: mySignature,
        access_subsystem_name: systemsConfig['subsystemName'],
        retailersId: systemsConfig['retailersId']
    };
    var paramString = querystring.stringify(params);
    var symbol = '?';
    if (!option.path) {
        option.path = '/';
    }
    if (option.path.indexOf('?') != -1) {
        symbol = '&';
    }
    option.path = option.path + symbol + paramString;
    if (systemsConfig['encrypt']) {
        postData = cryptoUtils.encryptUseAes(new Buffer(systemsConfig['subsystemKey']), postData);
    }

    option['data'] = postData;

    if (option['headers']) {
        option['headers']['Content-Length'] = postData.length;
    } else {
        option['headers'] = {};
        option['headers']['Content-Length'] = postData.length;
    }
    var realDataType = option['dataType'];
    option['dataType'] = 'json';
    restApiProxy.sendRequest(option, function (err, data) {
        if (err) {
            callback(err);
            return;
        }
        var error={};
        if (data.status === 'SUCCESS') {
            var signature = data.signature;
            var textPlain = '';
            if (data.data) {
                if (systemsConfig['encrypt']) {
                    textPlain = cryptoUtils.decryptUseAes(new Buffer(systemsConfig['subsystemKey']), new Buffer(data.data, 'hex'));
                }
                else {
                    textPlain = data.data;
                }
                //计算前面，检查数据的有效性
                var signature = cryptoUtils.createSignature(systemsConfig['subsystemKey'], textPlain);
                //console.info("signature:"+signature);
                if (signature !== data.signature) {
                    error.message=data.errorMsg;
                    error.code=4;
                    callback(error);
                    return;
                }
                var returnData = '';
                if (realDataType === 'json') {
                    if (textPlain.length > 0) {
                        returnData = JSON.parse(textPlain.toString());
                    }
                    callback(null, returnData);
                } else {
                    callback(null, returnData);
                }
            } else {
                callback(null, '');
            }

        } else if (data === '') {
            return;
        } else if (data.status === 'FAILURE') {
            error.message=data.errorMsg;
            error.code=2;
            callback(error);
        }
        else {
            var messageList=['未知错误'];
            error.message=messageList;
            error.code=5;
            callback(error);
        }
    });

}


authRestApiProxy.post = function (systemName, urlString, option, callback) {
    var systemsConfig = config[systemName];
    option.retailersId=systemsConfig['retailersId'];
    urlString = 'http://' + systemsConfig['host'] + ':' + systemsConfig['port'] + urlString;
    var allOptions = url.parse(urlString);
    allOptions['host'] = systemsConfig.host;
    allOptions['hostname'] = systemsConfig.host;
    var dataResult = {
        data:option,
        dataType:'json',
        method:'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    }
    for (var item in dataResult) {
        allOptions[item] = dataResult[item];
    }
    authRestApiProxy.sendRequest(systemName, allOptions, callback);
}

authRestApiProxy.get = function (systemName, urlString, callback) {
    var systemsConfig = config[systemName];
    urlString = 'http://' + systemsConfig['host'] + ':' + systemsConfig['port'] + urlString;
    var allOptions = url.parse(urlString);
    allOptions['host'] = systemsConfig.host;
    allOptions['hostname'] = systemsConfig.host;
    allOptions['dataType'] = 'json';
    authRestApiProxy.sendRequest(systemName, allOptions, callback);
}

module.exports = authRestApiProxy;