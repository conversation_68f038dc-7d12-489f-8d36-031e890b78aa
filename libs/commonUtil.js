/**
 * Created by r<PERSON><PERSON> on 2016/3/8.
 */
var authRestApiProxy = require('./authRestApiProxy');
var redisClient = require("./redisClient");
var log4js = require('./log4jsUtil.js');
var logger = log4js.getLogger('commonUtil');
var constants = require("./constants");
var actionsConstants = require("./actionsConstants");
var cache = require('memory-cache');
var config = require('./config');
var cookieUtil = require('./cookieUtil');

var commonUtil = {};
/**
 * 把商户的商品分类信息放入redis
 * @param callback
 */
commonUtil.setGoodsCategories=function (callback){
    var systemsConfig = config['PermissionService'];
    var retailersId = systemsConfig['retailersId'];
    var key = constants.GOODSCATEGORY_TREE_KEY+retailersId;
    authRestApiProxy.post('RetailerService','/good/category/qa',{},function(err,data){
        if(err){
            logger.error(JSON.stringify(err));
            if(callback!=undefined){
                callback(err,null);
            }
        }else  if(data){
            redisClient.set(key,JSON.stringify(data),null,function(redisErr,redisData){
                if(redisErr){
                    logger.error('redis exception happened when cacheActions: '+JSON.stringify(redisErr));
                    if(callback!=undefined) {
                        callback(redisErr,null);
                    }
                }else if(redisData!=null){
                    //logger.debug(redisData);
                    if(callback!=undefined){
                        callback(null,data);
                    }
                }
            });
        }
    });

}


/**
 *加载商户下的全部商品分类
 * @param callback 回调
 */
commonUtil.loadGoodsCategories=function (callback){
    var systemsConfig = config['PermissionService'];
    var retailersId = systemsConfig['retailersId'];
    var key = constants.GOODSCATEGORY_TREE_KEY+retailersId;
    redisClient.get(key,function(err1,data1){
        if(err1){
            callback(err1,null);
        }else if(data1){
            callback(null,JSON.parse(data1));
        }else{//redis没有取到值，调用restful接口从库里取最新数据
            commonUtil.setGoodsCategories(function(err,data){
                if(err){
                    logger.error(JSON.stringify(err));
                    callback(err,null);
                }else if(data){
                    callback(null,data);
                }
            });
        }
    });
}


/**
 * 把商户的问卷调查信息放入redis
 * @param callback
 */
commonUtil.setQuestionnaire=function (callback){
    var systemsConfig = config['PermissionService'];
    var retailersId = systemsConfig['retailersId'];
    var key = constants.QUESTION_TREE_KEY+retailersId;
    var questionnaireRequestVo={};
    questionnaireRequestVo.retailersid=retailersId
    authRestApiProxy.post('RetailerService','/questionnaire/qv',questionnaireRequestVo,function(err,data){
        if(err){
            logger.error(JSON.stringify(err));
            if(callback!=undefined){
                callback(err,null);
            }
        }else{
            redisClient.set(key,JSON.stringify(data),null,function(redisErr,redisData){
                if(redisErr){
                    logger.error('redis exception happened when cacheActions: '+JSON.stringify(redisErr));
                    if(callback!=undefined) {
                        callback(redisErr,null);
                    }
                }else if(redisData!=null){
                    //logger.debug(redisData);
                    if(callback!=undefined){
                        callback(null,data);
                    }
                }
            });
        }
    });

}

/**
 *加载商户下的全部调查问卷
 * @param callback 回调
 */
commonUtil.loadQuestionnaire=function (callback){
    var systemsConfig = config['PermissionService'];
    var retailersId = systemsConfig['retailersId'];
    var key = constants.QUESTION_TREE_KEY+retailersId;
    redisClient.get(key,function(err1,data1){
        if(err1){
            callback(err1,null);
        }else if(data1){
            callback(null,JSON.parse(data1));
        }else{//redis没有取到值，调用restful接口从库里取最新数据
            commonUtil.setQuestionnaire(function(err,data){
                if(err){
                    logger.error(JSON.stringify(err));
                    callback(err,null);
                }else if(data){
                    callback(null,data);
                }
            });
        }
    });
}


/**
 * 加载全部省份
 * @param callback
 */
commonUtil.loadProvs = function(callback){
    var url = '/district/q?parentId='+config.DistrictService.superId;
    authRestApiProxy.get('DistrictService',url,function(err,data){
        callback(err,data);
    });
}

/**
 * 加载全部商品
 * @param callback
 */
commonUtil.loadGoods = function(callback){
    var url = '/goods/qa';
    var goodsConditionVo={};
    authRestApiProxy.post('RetailerService',url,goodsConditionVo,function(err,data){
        callback(err,data);
    });
}
/**
 * 把商户的配送方式信息放入redis
 * @param callback
 */
commonUtil.setShippings=function (callback){
    var systemsConfig = config['PermissionService'];
    var retailersId = systemsConfig['retailersId'];
    var key = constants.SHIPPING_TREE_KEY+retailersId;
    authRestApiProxy.get('RetailerService','/ship/q',function(err,data){
        if(err){
            logger.error(JSON.stringify(err));
            if(callback!=undefined){
                callback(err,null);
            }
        }else if(data){
            redisClient.set(key,JSON.stringify(data),null,function(redisErr,redisData){
                if(redisErr){
                    logger.error('redis exception happened when cacheActions: '+JSON.stringify(redisErr));
                    if(callback!=undefined) {
                        callback(redisErr,null);
                    }
                }else if(redisData!=null){
                    //logger.debug(redisData);
                    if(callback!=undefined){
                        callback(null,data);
                    }
                }
            });
        }
    });
}

/**
 *加载商户下的全部配送方式
 * @param callback 回调
 */
commonUtil.loadShippings=function (callback){
    var systemsConfig = config['PermissionService'];
    var retailersId = systemsConfig['retailersId'];
    var key = constants.SHIPPING_TREE_KEY+retailersId;
    redisClient.get(key,function(err1,data1){
        if(err1){
            callback(err1,null);
        }else if(data1){
            callback(null,JSON.parse(data1));
        }else{//redis没有取到值，调用restful接口从库里取最新数据
            commonUtil.setShippings(function(err,data){
                if(err){
                    logger.error(JSON.stringify(err));
                    callback(err,null);
                }else if(data){
                    callback(null,data);
                }
            });
        }
    });
}

/**
 * 页面初始化数据
 * @param title 主页面title
 * @param req 请求对象,从session里可以获取权限、角色、账号等信息
 * @param menuCurrent 当前选中的菜单编码 高亮显示
 * @param filterList  字符串数组，用于过滤权限列表；减小权限的集合
 * @returns {{}}
 */
commonUtil.pageInitRenderData =  function(title,req,menuCurrent,filterList){
    var currentUser = req.session.sysuser;
    var data = {};
    //左侧菜单使用，左侧树形的全部权限集合
    data.menuActionLst=req.session.actions.actionInfos;
    data.menuCurrent = menuCurrent;
    data.title = title;
    data.firstLogin = currentUser.loginCount;
    data.currentUser=currentUser;
    data.pageSize = cookieUtil.getPageSizeCookie(req);
    data.actionsConstants=actionsConstants;
    //权限校验使用，模块权限 和账号拥有的系统权限 交集
    data.filterActionList = commonUtil.filterActionList(req.session.actions.actionInfos,filterList);
    return data;
}

/**
 * 优化权限的集合，权限与模块的过滤集合 取交集
 */

commonUtil.filterActionList= function(menuActionLst,moduleFilterList){
    if(moduleFilterList==undefined){
        return menuActionLst;
    }
    var filterStr = moduleFilterList.join(',');
    var actionList = new Array();
    for(var i=0;i<menuActionLst.length;i++){
        if(filterStr.indexOf(menuActionLst[i].accode)!=-1){
            actionList.push(commonUtil.clone(menuActionLst[i]));
        }
    }
    return actionList;
}
/**
 * 克隆对象
 * @param myObj
 */
 commonUtil.clone=function(myObj){

    var jsonStr = JSON.stringify(myObj);
    return JSON.parse(jsonStr);
}
/**
 * 对象合并(属性重复时保留obj2中的值)
 * @param obj1
 * @param obj2
 * <AUTHOR>
 */
commonUtil.mergeObject = function(obj1,obj2,callback){
    for(var element in obj2){
            obj1[element] = obj2[element];
    }
    callback(obj1);
}
/**
 * 权限校验失败，跳转到错误页
 * @param res
 */
commonUtil.permissionErrPage = function(res){
    var data={};
    data.message='权限校验失败';
    res.render('error',data);
    //commonUtil.errPage(res,data);
}

/**
 * 跳转到错误页
 * @param res
 *  @param errdata
 */
commonUtil.errPage = function(res,errdata){
    res.render('error',errdata);
}

module.exports = commonUtil;