//所有的配置，都需要写在这个文件中，如果需要用zookeeper进行集中化配置，将此文件的逻辑重新实现
/**
 * [config description]
 * @type {Object}
 */
var config = {
    RetailerService: {
        host: '**********',//retailersdev.ronglian.com //************
        port: 8101,
        context: '',
        encrypt: true,//数据是否加密
        subsystemName: 'portal',//系统名字
        subsystemKey: 'e79c77d9bb8f4cc185dc29f237e62289',//系统密钥
        retailersId: "255fa8683f6d4693a2b8d616b240a93e"//商户编号
    },
    PermissionService: {
        host: '**********',
        port: 8102,
        context: '',
        encrypt: true,//数据是否加密
        subsystemName: 'reatil',//系统名字
        subsystemKey: '38b8c2c1093dd0fec383a9d9ac940515',//系统密钥
        retailersId: "255fa8683f6d4693a2b8d616b240a93e"//商户编号
    },
    ImageService: {
        host: '**********',
        port: 8103,
        context: '',
        encrypt: true,//数据是否加密
        subsystemName: 'portal',//系统名字
        subsystemKey: 'e79c77d9bb8f4cc185dc29f237e62289',//系统密钥
        retailersId: "255fa8683f6d4693a2b8d616b240a93e",//商户编号
        domain:"http://im1.ronglian.com:8103"
    },
    LogService: {
        host: '**********',
        port: 8092,
        context: '',
        encrypt: true,//数据是否加密
        subsystemName:'retailersnode',
        subsystemKey:'erewfewfwe',
        retailersId: "255fa8683f6d4693a2b8d616b240a93e",//商户编号
        system: 'retailer'
    },
    EmailService:{
        host:'**********',
        port:8093,
        context:'',
        encrypt: true,//数据是否加密
        subsystemName:'retailersnode',
        subsystemKey:'erewfewfwe',
        retailersId: "255fa8683f6d4693a2b8d616b240a93e"//商户编号
    },
    DistrictService: {
        host: '**********',
        port: 8091,
        context: '',
        encrypt: true,//数据是否加密
        subsystemName: 'shopping',//系统名字
        subsystemKey: '2cfca5a66e8e409ab85d2f400b2d4e2a',//系统密钥
        retailersId: "255fa8683f6d4693a2b8d616b240a93e",//商户编号
        superId: '4cd36dbcc8b011e584680050569b1c58'//国家id
    },
    weixinApp:{
        domainUrl:'http://weixin.bio.ronglian.com',//域名url
        template_notify_shipping:'zwdR5dIg6xdcpjiYhhLE15m3r-P7VgSedYTMNTzLn9I',//发货通知
        template_notify_sampleCheck:'imWKhpx_zty9XeXrhyL9ZbWGgMGt4i_JeV-UvS89nto'//样品检测模板
    },
    smsService:{//阿里短信平台
        appkey:'23440508',
        appsecret:'e16b1ffe621c5cc9ef585f3d4f766547',
        sms_free_sign_name: '荣之联生物云',//头部签名
        verify_template_code:               'SMS_13285533',//验证码，模板ID
        notify_template_shipping:          'SMS_13810392',//发货通知
        notify_template_checkFail:         'SMS_13835315',//检测失败通知
        notify_template_checkFinish:       'SMS_13805411',//检测完成通知
        notify_template_checkStart:        'SMS_13870331',//检测开始通知
        notify_template_checkUnqualified :'SMS_13880382',//质检不合格通知
        notify_template_receiveSample :   'SMS_13810410',//收到样品通知
        retailersId:'255fa8683f6d4693a2b8d616b240a93e'//商户ID
    },
    redis: {
        port: '6379',
        ip: '127.0.0.1',
        password: '',
        timeOut: '3000' //单位秒
    },
    mongodb:{
        db: 'retailers',
        host: '**********',
        port:27017,
        dbRoot:'file',
        url:'mongodb://**********:27017/retailers',
        encoding:'utf-8'
    }

};

config.SHOPPINGURL=(process.env.SHOPPING_URL || "http://**********:8080");
//读取环境变量
//商户
config.RetailerService.host = (process.env.RetailerService_SERVICE_HOST || config.RetailerService.host);
config.RetailerService.port = (process.env.RetailerService_SERVICE_PORT || config.RetailerService.port);
config.RetailerService.encrypt = (process.env.RetailerService_SERVICE_ENCRYPT || config.RetailerService.port);
config.RetailerService.subsystem_name = (process.env.RetailerService_SERVICE_SUBSYSTEM_NAME || config.RetailerService.subsystem_name);
config.RetailerService.subsystem_key = (process.env.RetailerService_SERVICE_SUBSYSTEM_KEY || config.RetailerService.subsystem_key);
config.RetailerService.retailersId = (process.env.RetailerService_SERVICE_RETAILERSID || config.RetailerService.retailersId);

//角色
config.PermissionService.host = (process.env.PermissionService_SERVICE_HOST || config.PermissionService.host);
config.PermissionService.port = (process.env.PermissionService_SERVICE_PORT || config.PermissionService.port);
config.PermissionService.encrypt = (process.env.PermissionService_SERVICE_ENCRYPT || config.PermissionService.port);
config.PermissionService.subsystem_name = (process.env.PermissionService_SERVICE_SUBSYSTEM_NAME || config.PermissionService.subsystem_name);
config.PermissionService.subsystem_key = (process.env.PermissionService_SERVICE_SUBSYSTEM_KEY || config.PermissionService.subsystem_key);
config.PermissionService.retailersId = (process.env.PermissionService_SERVICE_RETAILERSID || config.PermissionService.retailersId);

//图片
config.ImageService.host = (process.env.ImageService_SERVICE_HOST || config.ImageService.host);
config.ImageService.port = (process.env.ImageService_SERVICE_PORT || config.ImageService.port);
config.ImageService.encrypt = (process.env.ImageService_SERVICE_ENCRYPT || config.ImageService.port);
config.ImageService.subsystem_name = (process.env.ImageService_SERVICE_SUBSYSTEM_NAME || config.ImageService.subsystem_name);
config.ImageService.subsystem_key = (process.env.ImageService_SERVICE_SUBSYSTEM_KEY || config.ImageService.subsystem_key);
config.ImageService.retailersId = (process.env.ImageService_SERVICE_RETAILERSID || config.ImageService.retailersId);

//log
config.LogService.host = (process.env.LogService_SERVICE_HOST || config.LogService.host);
config.LogService.port = (process.env.LogService_SERVICE_PORT || config.LogService.port);
config.LogService.encrypt = (process.env.LogService_SERVICE_ENCRYPT || config.LogService.port);
config.LogService.subsystem_name = (process.env.LogService_SERVICE_SUBSYSTEM_NAME || config.LogService.subsystem_name);
config.LogService.subsystem_key = (process.env.LogService_SERVICE_SUBSYSTEM_KEY || config.LogService.subsystem_key);
config.LogService.retailersId = (process.env.LogService_SERVICE_RETAILERSID || config.LogService.retailersId);

//email
config.EmailService.host = (process.env.EmailService_SERVICE_HOST || config.EmailService.host);
config.EmailService.port = (process.env.EmailService_SERVICE_PORT || config.EmailService.port);
config.EmailService.encrypt = (process.env.EmailService_SERVICE_ENCRYPT || config.EmailService.port);
config.EmailService.subsystem_name = (process.env.EmailService_SERVICE_SUBSYSTEM_NAME || config.EmailService.subsystem_name);
config.EmailService.subsystem_key = (process.env.EmailService_SERVICE_SUBSYSTEM_KEY || config.EmailService.subsystem_key);
config.EmailService.retailersId = (process.env.EmailService_SERVICE_RETAILERSID || config.EmailService.retailersId);

//redis
config.redis.port = (process.env.REDIS_PORT || config.redis.port);
config.redis.ip = (process.env.REDIS_IP || config.redis.ip);
config.redis.timeOut = (process.env.REDIS_TIMEOUT || config.redis.timeOut);
config.redis.password = (process.env.REDIS_PASSWORD || config.redis.password);
//mongodb
config.mongodb.db = (process.env.MONGODB_DB || config.mongodb.db);
config.mongodb.host = (process.env.MONGODB_HOST || config.mongodb.host);
config.mongodb.port = (process.env.MONGODB_PORT || config.mongodb.port);
config.mongodb.dbRoot = (process.env.MONGODB_DBROOT || config.mongodb.dbRoot);
config.mongodb.url = (process.env.MONGODB_URL || config.mongodb.url);
config.mongodb.encoding = (process.env.MONGODB_ENCODING || config.mongodb.encoding);

//地区
config.DistrictService.host = (process.env.DistrictService_SERVICE_HOST || config.DistrictService.host);
config.DistrictService.port = (process.env.DistrictService_SERVICE_PORT || config.DistrictService.port);
config.DistrictService.encrypt = (process.env.DistrictService_SERVICE_ENCRYPT || config.DistrictService.port);
config.DistrictService.subsystem_name = (process.env.DistrictService_SERVICE_SUBSYSTEM_NAME || config.DistrictService.subsystem_name);
config.DistrictService.subsystem_key = (process.env.DistrictService_SERVICE_SUBSYSTEM_KEY || config.DistrictService.subsystem_key);
config.DistrictService.retailersId = (process.env.DistrictService_SERVICE_RETAILERSID || config.DistrictService.retailersId);
config.DistrictService.superId = (process.env.DISTRICTSERVICE_SERVICE_SUPERID || config.DistrictService.superId);

//解析命令行参数，使其支持命令行参数将默认配置覆盖，这样可以很好的支持PASS平台建设
var argv = process.argv;
for (var i = 2; i < argv.length; i++) {
    var kvs = argv[i].split('=');//key-values
    if (kvs.length == 2) {
        var keyItem = kvs[0].split('\.');
        var str = 'config';
        for (var j = 0; j < keyItem.length; j++) {
            str = str + "['" + keyItem[j] + "']";
            var ifStatement = "if(!" + str + "){" + str + "={};}";
            eval(ifStatement);
        }
        str = str + "='" + kvs[1] + "'";
        eval(str);
    }
}

module.exports = config;
