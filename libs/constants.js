/**
 * 常量
 */
var retailersConstants = {};
//密码
retailersConstants.USER_PASSWORD_TOKER = 'retailers_password_token_';
//权限
retailersConstants.ACTION_TREE_KEY = 'retailers_action_tree_';
//角色
retailersConstants.ROLES_TREE_KEY = 'retailers_roles_';
//商品分类
retailersConstants.GOODSCATEGORY_TREE_KEY = 'retailers_goodsCategory_';
//调查问卷
retailersConstants.QUESTION_TREE_KEY = 'retailers_questionnaire_';
//配送方式
retailersConstants.SHIPPING_TREE_KEY = 'retailers_shipping_';
//微信token缓存 key
retailersConstants.wechatToken ='wechatToken_';
//微信配置信息缓存 key
retailersConstants.wxConfigToken = 'wxConfigToken_';

//7天 ,本地缓存失效时间
retailersConstants.CACHE_TIMEOUT = 1000*60*60*24*7;
//30天 ,redis数据缓存失效时间
retailersConstants.REDIS_CACHE_TIMEOUT = 1000*60*60*24*30;
//默认100条
retailersConstants.DEFAULT_PAGE_SIZE = 100;
//弹窗默认6条
retailersConstants.POPUO_DEFAULT_PAGE_SIZE = 6;
//弹窗有图片 4条
retailersConstants.POPUO_PIC_PAGE_SIZE = 4;
retailersConstants.merchantInfo="retailers_merchantInfo_";

//文件下载路径
retailersConstants.FILE_DOWNLOAD_PATH="/file/download/";

/**
 * 用户状态禁用
 * @type {number}
 */
retailersConstants.CUSTOMER_STATUS_DISABLE=0;
/**
 * 用户状态启用
 * @type {number}
 */
retailersConstants.CUSTOMER_STATUS_ENABLE=1;

//栏目相关内容类型（组件URL）
retailersConstants.PAGE_TABS_TYPE_NEWS = 'news/index';
retailersConstants.PAGE_TABS_TYPE_GOODS = 'goods/index';
retailersConstants.PAGE_TABS_TYPE_CONTENT = 'content/index';
retailersConstants.PAGE_TABS_TYPE_PRODUCT = 'product/index';

//商家所有信息缓存（栏目，底部，商家信息）
retailersConstants.MERCHANTALLINFO="retailers_merchantAllInfo_";


//页面显示类型
retailersConstants.GOODS_SHOWTYPE = "goodsShowType";
retailersConstants.NEWS_SHOWTYPE = "newsShowType";


//日志常量区
//操作成功
retailersConstants.RESULT_SUCCESS="1";
//操作失败
retailersConstants.RESULT_FAILURE="0";

//调查问卷日志
//调查问卷模块名字
retailersConstants.LOG_QUESTIONNAIRE_OPERATEPATH="questionnaire";
retailersConstants.LOG_QUESTIONNAIRE_QUERY="查询:调查问卷查询";
retailersConstants.LOG_QUESTIONNAIRE_INSERT="添加:调查问卷添加";
retailersConstants.LOG_QUESTIONNAIRE_UPDATE="修改:调查问卷修改";
retailersConstants.LOG_QUESTIONNAIRE_DELETE="删除:调查问卷删除";
retailersConstants.LOG_QUESTIONNAIRE_PUBLISH="修改:调查问卷发布状态修改";


//新闻日志
//新闻模块名字
retailersConstants.LOG_NEWS_OPERATEPATH="news";
retailersConstants.LOG_NEWS_QUERY="查询:新闻查询";
retailersConstants.LOG_NEWS_INSERT="添加:新闻添加";
retailersConstants.LOG_NEWS_UPDATE="修改:新闻修改";
retailersConstants.LOG_NEWS_DELETE="删除:新闻删除";
retailersConstants.LOG_NEWS_PUBLISH="修改:新闻发布状态修改";

//新闻分类日志
//新闻分类模块名字
retailersConstants.LOG_NEWSCATEGORY_OPERATEPATH="newscategory";
retailersConstants.LOG_NEWSCATEGORY_INSERT="添加:新闻分类添加";
retailersConstants.LOG_NEWSCATEGORY_UPDATE="修改:新闻分类修改";
retailersConstants.LOG_NEWSCATEGORY_DELETE="删除:新闻分类删除";

//会员分类日志
//会员模块名字
retailersConstants.LOG_CUSTOMER_OPERATEPATH="customer";
retailersConstants.LOG_CUSTOMER_QUERY="查询:会员查询";

//页面管理日志
//页面管理模块名字
retailersConstants.LOG_WEBSITECONTENT_OPERATEPATH="websitecontent";
retailersConstants.LOG_WEBSITECONTENT_QUERY="查询:页面管理查询";
retailersConstants.LOG_WEBSITECONTENT_INSERT="添加:页面管理添加";
retailersConstants.LOG_WEBSITECONTENT_UPDATE="修改:页面管理修改";
retailersConstants.LOG_WEBSITECONTENT_DELETE="删除:页面管理删除";

//登录日志
//登录模块名字
retailersConstants.LOG_LOGINROUTER_OPERATEPATH="loginRouter";
retailersConstants.LOG_LOGINROUTER_QUERY="查询:登录查询";
retailersConstants.LOG_LOGINROUTER_UPDATE="修改:登录修改";

// 采样盒类型管理日志
// 采样盒类型管模块名字
retailersConstants.LOG_SAMPLEBOXTYPE_OPERATEPATH="sampleBoxTypeRouter";
retailersConstants.LOG_SAMPLEBOXTYPE_QUERY="查询:采样盒类型查询";
retailersConstants.LOG_SAMPLEBOXTYPE_INSERT="添加:采样盒类型添加";
retailersConstants.LOG_SAMPLEBOXTYPE_UPDATE="修改:采样盒类型修改";
retailersConstants.LOG_SAMPLEBOXTYPE_DELETE="删除:采样盒类型删除";

// 样品编码日志
// 样品编码模块名字
retailersConstants.LOG_SAMPLINGBOXROUTER_OPERATEPATH="samplingBoxRouter";
retailersConstants.LOG_SAMPLINGBOXROUTER_QUERY="查询:样品编码查询";
retailersConstants.LOG_SAMPLINGBOXROUTER_INSERT="添加:样品编码添加";
retailersConstants.LOG_SAMPLINGBOXROUTER_UPDATE="修改:样品编码修改";
retailersConstants.LOG_SAMPLINGBOXROUTER_DELETE="删除:样品编码删除";

// 样品检测日志
// 样品检测模块名字
retailersConstants.LOG_SAMPLINGTESTINGROUTER_OPERATEPATH="samplingTestingRouter";
retailersConstants.LOG_SAMPLINGTESTINGROUTER_QUERY="查询:样品检测查询";
retailersConstants.LOG_SAMPLINGTESTINGROUTER_INSERT="添加:样品检测添加";
retailersConstants.LOG_SAMPLINGTESTINGROUTER_UPDATE="修改:样品检测修改";
retailersConstants.LOG_SAMPLINGTESTINGROUTER_DELETE="删除:样品检测删除";

// 系统用户日志
retailersConstants.LOG_SYSUSERROUTER_OPERATEPATH="sysuserRouter";
retailersConstants.LOG_SYSUSERROUTER_QUERY="系统用户查询";
retailersConstants.LOG_SYSUSERROUTER_INSERT="账号添加";
retailersConstants.LOG_SYSUSERROUTER_UPDATE="账号修改";
retailersConstants.LOG_SYSUSERROUTER_DELETE="账号删除";
retailersConstants.LOG_SYSUSERROUTER_UP_PASSWORD="系统用户密码修改";
retailersConstants.LOG_SYSUSERROUTER_RESET_PASSWORD= '管理员重置密码';
retailersConstants.LOG_SYSUSERROUTER_ON_OFF= '账号启用/停用';
retailersConstants.LOG_SYSUSERROUTER_PUSH_TO_PERMISSION='推送用户到权限系统';
retailersConstants.LOG_SYSUSERROUTER_PUSH_TO_DELETE='同步删除权限系统用户';
retailersConstants.LOG_SYSUSERROUTER_SET_ROLE='设置用户角色';

//角色管理日志
retailersConstants.LOG_ROLEROUTER_OPERATEPATH="roleRouter";
retailersConstants.LOG_ROLEROUTER_INSERT='角色添加';
retailersConstants.LOG_ROLEROUTER_UPDATE='角色修改';
retailersConstants.LOG_ROLEROUTER_DELETE='角色删除';

//部门管理日志
retailersConstants.LOG_DEPARTMENTROUTER_OPERATEPATH="departmentRouter";
retailersConstants.LOG_DEPARTMENTROUTER_INSERT='部门添加';
retailersConstants.LOG_DEPARTMENTROUTER_UPDATE='部门修改';
retailersConstants.LOG_DEPARTMENTROUTER_DELETE='部门删除';

//企业荣誉管理日志
retailersConstants.LOG_HONORROUTER_OPERATEPATH="honorRouter";
retailersConstants.LOG_HONORROUTER_INSERT='企业荣誉添加';
retailersConstants.LOG_HONORROUTER_UPDATE='企业荣誉修改';
retailersConstants.LOG_HONORROUTER_DELETE='企业荣誉删除';

//投资者关系管理日志
retailersConstants.LOG_INVESTORROUTER_OPERATEPATH="investorRouter";
retailersConstants.LOG_INVESTORROUTER_INSERT='投资者关系添加';
retailersConstants.LOG_INVESTORROUTER_DELETE='投资者关系删除';
//审核管理日志
retailersConstants.LOG_APPROVEDROUTER_OPERATEPATH="approvedRouter";
retailersConstants.LOG_APPROVEDROUTER_AUDIT='审核管理审核';
retailersConstants.LOG_APPROVEDROUTER_PUBLIC='审核管理发布';

//设置权限日志
retailersConstants.LOG_PERMISSIONROUTER_OPERATEPATH="permissionRouter";
retailersConstants.LOG_PERMISSIONROUTER_SET='设置权限';

//支付管理日志
retailersConstants.LOG_PAYROUTER_OPERATEPATH="payRouter";
retailersConstants.LOG_PAYROUTER_CONFIG='配置支付方式';

//订单管理日志
retailersConstants.LOG_ORDERROUTER_OPERATEPATH="orderRouter";
retailersConstants.LOG_ORDERROUTER_QUERY='订单查询';
retailersConstants.LOG_ORDERROUTER_ADD_REMARK='订单添加备注';
retailersConstants.LOG_ORDERROUTER_ADD_SHIPPING='订单发货';
retailersConstants.LOG_ORDERROUTER_INSTEAD='代下单';


//商品管理日志
retailersConstants.LOG_GOODSROUTER_OPERATEPATH="goodsRouter";
retailersConstants.LOG_GOODSROUTER_QUERY='商品查询';
retailersConstants.LOG_GOODSROUTER_INSERT='商品添加';
retailersConstants.LOG_GOODSROUTER_DELETE='商品删除';
retailersConstants.LOG_GOODSROUTER_UPDATE='商品修改';
retailersConstants.LOG_GOODSROUTER_BATCH_OFF='商品批量下架';
retailersConstants.LOG_GOODSROUTER_BATCH_ON='商品批量上架';

//厂商管理日志
retailersConstants.LOG_VENDORROUTER_OPERATEPATH="vendorRouter";
retailersConstants.LOG_VENDORROUTER_QUERY='商品查询';
retailersConstants.LOG_VENDORROUTER_INSERT='商品添加';
retailersConstants.LOG_VENDORROUTER_DELETE='商品删除';
retailersConstants.LOG_VENDORROUTER_UPDATE='商品修改';

//常用物流管理
retailersConstants.LOG_SHIPPINGROUTER_OPERATEPATH="shippingRouter";
retailersConstants.LOG_SHIPPINGROUTER_ADD="常用物流公司添加";
retailersConstants.LOG_SHIPPINGROUTER_DELETE="常用物流公司删除";




// 编辑底部链接日志
// 编辑底部链接模块名字
retailersConstants.LOG_CUSTOMBOTTOMEDIT_OPERATEPATH="customBottomEdit";
retailersConstants.LOG_CUSTOMBOTTOMEDIT_INSERT="添加:编辑底部链接添加";

// 添加底部栏目日志
// 添加底部栏目模板名字
retailersConstants.LOG_CUSTOMMODULEEDIT_OPERATEPATH="customModuleEdit";
retailersConstants.LOG_CUSTOMMODULEEDIT_INSERT="添加:底部栏目添加";
retailersConstants.LOG_CUSTOMMODULEEDIT_UPDATE="修改:底部栏目修改";

// 编辑页面栏目日志
// 编辑页面栏目模块名字
retailersConstants.LOG_CUSTOMNAVIEDIT_OPERATEPATH="customNaviEdit";
retailersConstants.LOG_CUSTOMNAVIEDIT_UPDATE="修改:编辑页面栏目修改";

// 自定义模板日志
// 自定义模板名字
retailersConstants.LOG_CUSTOMTEMPLATE_OPERATEPATH="customTemplate";
retailersConstants.LOG_CUSTOMTEMPLATE_UPDATE="修改:自定义模板修改";

// 模板底部栏目日志
// 模板底部栏目模块名字
retailersConstants.LOG_TEMPBOTTOM_OPERATEPATH="tempBottom";
retailersConstants.LOG_TEMPBOTTOME_QUERY="查询:模板底部栏目查询";

// 中间模板日志
// 中间模板模块名字
retailersConstants.LOG_TEMPMODULE_OPERATEPATH="tempModule";
retailersConstants.LOG_TEMPMODULE_QUERY="查询:中间模板查询";

// 网站管理日志
// 网站管理模块名字
retailersConstants.LOG_TEMPLATECHOOSEROUTER_OPERATEPATH="templateChooseRouter";
retailersConstants.LOG_TEMPLATECHOOSEROUTER_UPDATE="修改:网站管理修改";

// 模板管理日志
// 模板管理模块名字
retailersConstants.LOG_TEMPHEADERROUTER_OPERATEPATH="tempHeaderRouter";
retailersConstants.LOG_TEMPHEADERROUTER_QUERY="查询:模板管理查询";
retailersConstants.LOG_TEMPHEADERROUTER_UPDATE="修改:商品默认展示";

// 商品模板管理日志
// 商品模板管理模块名字
retailersConstants.LOG_TEMPGOODS_OPERATEPATH="websiteManage/temp-goods";
retailersConstants.LOG_TEMPGOODS_UPDATE="修改:商品默认展示";

//商户未登录提示语
retailersConstants.RETAILERS_DISABLE_MSG = "商户未启用，登录失败";

//编码管理模块名字
retailersConstants.LOG_BARCODE_OPERATEPATH="barcodeRouter";
retailersConstants.LOG_BARCODE_QUERY="查询:编码管理查询";
retailersConstants.LOG_BARCODE_INSERT="添加:编码管理添加";
retailersConstants.LOG_BARCODE_UPDATE="修改:编码管理修改";
retailersConstants.LOG_BARCODE_DELETE="删除:编码管理删除";
retailersConstants.LOG_BARCOD_PRINTCONFIG="打印:编码打印设置";

//优惠券管理日志
retailersConstants.LOG_COUPONROUTER_OPERATEPATH="couponRouter";
retailersConstants.LOG_COUPONROUTER_QUERY='优惠券查询';
retailersConstants.LOG_COUPONROUTER_INSERT='优惠券添加';
retailersConstants.LOG_COUPONROUTER_DELETE='优惠券删除';
retailersConstants.LOG_COUPONROUTER_UPDATE='优惠券修改';
retailersConstants.LOG_COUPONROUTER_PUBLISH='优惠券发布';
retailersConstants.LOG_COUPONROUTER_PAUSE='优惠券暂停';

//券码管理
retailersConstants.LOG_COUPONCODEROUTER_OPERATEPATH="couponCodeRouter";
retailersConstants.LOG_COUPONCODEROUTER_QUERY='券码查询';
retailersConstants.LOG_COUPONCODEROUTER_EXPORT='券码导出';

//公众号管理日志
retailersConstants.LOG_WXCONFIG_ROUTER_OPERATEPATH="wxConfigRouter";
retailersConstants.LOG_WXCONFIG_ROUTER_CONFIG='配置公众号';

// 微信自定义页面日志
retailersConstants.LOG_WXCUSTPAGEROUTER_OPERATEPATH="wxCustPageRouter";
retailersConstants.LOG_WXCUSTPAGEROUTER_QUERY="查询:微信自定义页面查询";
retailersConstants.LOG_WXCUSTPAGEROUTER_INSERT="添加:微信自定义页面添加";
retailersConstants.LOG_WXCUSTPAGEROUTER_UPDATE="修改:微信自定义页面修改";
retailersConstants.LOG_WXCUSTPAGEROUTER_DELETE="删除:微信自定义页面删除";
//办事处日志
retailersConstants.LOG_AGENCYROUTER_OPERATEPATH="agencyRouter";
retailersConstants.LOG_AGENCYROUTER_INSERT='办事处添加';
retailersConstants.LOG_AGENCYROUTER_UPDATE='办事处修改';
retailersConstants.LOG_AGENCYROUTER_DELETE='办事处删除';

//联系我们日志
retailersConstants.LOG_CONTACTUSROUTER_OPERATEPATH="contactUsRouter";
retailersConstants.LOG_CONTACTUSROUTER_INSERT='联系我们添加';
retailersConstants.LOG_CONTACTUSROUTER_UPDATE='联系我们修改';
retailersConstants.LOG_CONTACTUSROUTER_DELETE='联系我们删除';

//提交审核
retailersConstants.LOG_APPROVED_INSERT='提交审核';

/**
 * 自定义菜单url类型
 * @type
 * {{NONE: string, GOODS: string, GOODS_LIST: string,
 * NEWS: string, NEWS_LIST: string, SAMPLE_BIND: string, REPORT_PROCESS: string, CUST_PAGE:
 * string, PAGE_MGR: string, REPORT_CENTER: string, MY_ORDER: string, OTHERS: string
 * }}
 */
retailersConstants.MENU_URL_TYPE ={
    GOODS:'GOODS',//商品
    GOODS_LIST: 'GOODS_LIST',//商品列表
    NEWS: 'NEWS',//新闻
    NEWS_LIST: 'NEWS_LIST',//新闻列表
    SAMPLE_BIND: 'SAMPLE_BIND',//样品绑定
    REPORT_PROCESS:'REPORT_PROCESS',//报告进度
    CUST_PAGE:'CUST_PAGE',//自定义页面
    PAGE_MGR :'PAGE_MGR',//页面管理
    REPORT_CENTER : 'REPORT_CENTER',//报告中心
    MY_ORDER:'MY_ORDER',//我的订单
    OTHERS:'OTHERS'//其他
};

//市场部（部门ID）固定ID
retailersConstants.MARKETING_DEPT_ID = "bb590fd43d554e1390750686bbcd5aae";
//证券部（部门ID）固定ID
retailersConstants.SECURITIES_DEPT_ID = "63f3d204b5384cbe9063126fdc65736d";
module.exports = retailersConstants;