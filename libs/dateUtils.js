/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2016/1/15.
 */
var dateUtils={};
/**
 * 初始化时间的format函数
 */
function  initFormat(){
    Date.prototype.format = function (fmt) { //author: meizz
        var o = {
            "M+": this.getMonth() + 1, //月份
            "d+": this.getDate(), //日
            "h+": this.getHours(), //小时
            "m+": this.getMinutes(), //分
            "s+": this.getSeconds(), //秒
            "q+": Math.floor((this.getMonth() + 3) / 3), //季度
            "S": this.getMilliseconds() //毫秒
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (var k in o)
            if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    }
}
//调用初始化方法
initFormat();
/**
 *
 * @param date 日期
 * @param format 格式： 如 'yyyy-MM-dd hh:mm:ss'
 * @returns {*}
 */
dateUtils.date2str = function(date,format) {
    return date.format(format);
}

/**
 * 当前时间
 */
dateUtils.nowTime=function(){
  return  new Date().format('yyyy-MM-dd hh:mm:ss');
}

/**
 * 指定时间增加或者减少days天（正值为增加，负值为减少）
 */
dateUtils.daysChange=function(date,days){
    date.setDate(date.getDate() + days);
};
module.exports=dateUtils;