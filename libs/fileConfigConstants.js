/**
 * 权限图片尺寸和大小常量
 */
var fileConfigConstants = {
    /**
     * 通用图片上传文件大小(单位M)
     * rule(LEQ:小于等于,EQ:等于, GTE:大于等于)
     * @type {number}
     */
    CommonConfig: {
        size: 5 * 1024 * 1024,
        width: 3000,
        height: 3000,
        rule: 'LEQ',
        type: 'gif,png,jpg,jpeg',
        prompt: '最大图片尺寸：宽度3000px*高度3000px'
    },
    //新闻
    NewsConfig: {
        size: 5 * 1024 * 1024,
        width: 480,
        height: 266,
        rule: 'GTE',
        type: 'gif,png,jpg,jpeg',
        prompt: '最小图片尺寸：宽度480px*高度266px'
    },
    //大图，轮播图片 ：宽度1920px 高度700px    必须大于等于这个尺寸。    提示信息：最小图片尺寸：1920像素*700像素。
    BigImgConfig: {
        size: 5 * 1024 * 1024,
        width: 1920,
        height: 700,
        rule: 'GTE',
        type: 'gif,png,jpg,jpeg',
        prompt: '最小图片尺寸：宽度1920px*高度700px'
    },
    //左图右文 ，右图左文：宽度500px 高度500px      不限制，建议用户上传这个尺寸。提示信息：建议图片尺寸：450像素*320像素
    TextImgConfig:{
        size: 5 * 1024 * 1024,
        width: 9999,
        height: 9999,
        rule: 'LEQ',
        type: 'gif,png,jpg,jpeg',
        prompt: '建议图片尺寸：450像素*320像素'
    },
    SolProListBannerConfig:{
        size: 5 * 1024 * 1024,
        width: 9999,
        height: 9999,
        rule: 'LEQ',
        type: 'gif,png,jpg,jpeg',
        prompt: '建议图片尺寸：1920像素*402像素'
    },
    //logo：宽度200px 高度78px                       不限制，建议用户上传这个尺寸。提示信息：建议图片尺寸：200像素*78像素
    LogoImgConfig:{
        size: 5 * 1024 * 1024,
        width: 9999,
        height: 9999,
        rule: 'LEQ',
        type: 'gif,png,jpg,jpeg',
        prompt: '建议图片尺寸：200像素*78像素'
    },
    //微信：宽度 84px 高度84px            必须大于等于这个尺寸。   提示信息：最小图片尺寸：84像素*84像素
    WeixinImgConfig:{
        size: 5 * 1024 * 1024,
        width: 84,
        height: 84,
        rule: 'GTE',
        type: 'gif,png,jpg,jpeg',
        prompt: '最小图片尺寸：84像素*84像素'
    },
    //商品
    GoodsConfig: {
        size: 5 * 1024 * 1024,
        width: 320,
        height: 430,
        rule: 'GTE',
        type: 'gif,png,jpg,jpeg',
        prompt: '最小图片尺寸：宽度320px*高度430px'
    },
    HonorInfoConfig: {
        size: 5 * 1024 * 1024,
        width: 490,
        height: 320,
        rule: 'GTE',
        type: 'png,jpg,jpeg',
        prompt: '最小图片尺寸：宽度490px*高度320px'
    },
    HomepageBannerConfig: {
        size: 5 * 1024 * 1024,
        width: 1920,
        height: 700,
        rule: 'GTE',
        type: 'gif,png,jpg,jpeg',
        prompt: '最小图片尺寸：宽度1920px*高度700px'
    },
    HomepageProPicConfig: {
        size: 5 * 1024 * 1024,
        width: 1920,
        height: 585,
        rule: 'GTE',
        type: 'gif,png,jpg,jpeg',
        prompt: '最小图片尺寸：宽度1920px*高度585px'
    },
    HomepageIndustryConfig: {
        size: 5 * 1024 * 1024,
        width: 526,
        height: 356,
        rule: 'GTE',
        type: 'gif,png,jpg,jpeg',
        prompt: '最小图片尺寸：宽度526px*高度356px'
    },
    HomepageSolConfig: {
        size: 5 * 1024 * 1024,
        width: 256,
        height: 326,
        rule: 'GTE',
        type: 'gif,png,jpg,jpeg',
        prompt: '最小图片尺寸：宽度256px*高度326px'
    },
    ProductBannerConfig: {
        size: 5 * 1024 * 1024,
        width: 1920,
        height: 402,
        rule: 'GTE',
        type: 'gif,png,jpg,jpeg',
        prompt: '最小图片尺寸：宽度1920px*高度402px'
    },
    ProductImgColumnConfig: {
        size: 5 * 1024 * 1024,
        width: 372,
        height: 235,
        rule: 'GTE',
        type: 'gif,png,jpg,jpeg',
        prompt: '最小图片尺寸：宽度3720px*高度235px'
    },
    SolutionListConfig: {
        size: 5 * 1024 * 1024,
        width: 256,
        height: 326,
        rule: 'GTE',
        type: 'gif,png,jpg,jpeg',
        prompt: '最小图片尺寸：宽度256px*高度326px'
    },
    //产品特点
    ProductMixedConfig: {
        size: 5 * 1024 * 1024,
        width: 375,
        height: 316,
        rule: 'GTE',
        type: 'gif,png,jpg,jpeg',
        prompt: '最小图片尺寸：宽度375px*高度316px'
    },
    //产品功能
    ProductTextConfig: {
        size: 5 * 1024 * 1024,
        width: 89,
        height: 89,
        rule: 'GTE',
        type: 'gif,png,jpg,jpeg',
        prompt: '最小图片尺寸：宽度89px*高度89px'
    },
    IndustryLogoConfig: {
        size: 5 * 1024 * 1024,
        width: 260,
        height: 105,
        rule: 'GTE',
        type: 'gif,png,jpg,jpeg',
        prompt: '最小图片尺寸：宽度260px*高度105px'
    },
    //产品描述
    ProductDescConfig: {
        size: 5 * 1024 * 1024,
        width: 450,
        height: 320,
        rule: 'GTE',
        type: 'gif,png,jpg,jpeg',
        prompt: '最小图片尺寸：宽度450px*高度320px'
    },
    IndustryImgTextConfig: {
        size: 5 * 1024 * 1024,
        width: 470,
        height: 300,
        rule: 'GTE',
        type: 'gif,png,jpg,jpeg',
        prompt: '最小图片尺寸：宽度470px*高度300px'
    },
	caseImgColumnConfig:{
		size: 5 * 1024 * 1024,
		width: 240,
		height: 170,
		rule: 'GTE',
		type: 'gif,png,jpg,jpeg',
		prompt: '最小图片尺寸：宽度240px*高度170px'
    }
};
fileConfigConstants.IMG_FORMAT_ERROR='上传文件类型错误';
fileConfigConstants.IMG_SIZE_ERROR='上传文件大小应该小于5M';
fileConfigConstants.IMG_CONFIG_ERROR='上传文件配置错误';
module.exports = fileConfigConstants;


