/**
 * Created by RONGLIAN on 2016/1/26.
 */
var log4js = require('log4js')

log4js.configure({
    levels: {
        "[all]": "INFO"
    },
    appenders: [
        { type: 'console' },
        //{
        //    type: 'file',
        //    filename: 'portal/logs/portal.log',
        //    pattern: "_yyyy-MM-dd.log",
        //    maxLogSize: 1024*30,
        //    alwaysIncludePattern: true
        //},
    ],
    replaceConsole: true
});

module.exports = log4js;
