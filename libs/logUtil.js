/**
 * Created by ch<PERSON><PERSON><PERSON><PERSON> on 2016/3/28.
 * 日志管理工具类
 */

var authRestApiProxy = require('./authRestApiProxy');
var log4js = require('./log4jsUtil.js');
var logger = log4js.getLogger('logUtil');
var config = require('./config');
var dateUtils = require("./dateUtils");
var cookieUtil = require('./cookieUtil');
var logUtil = {};

/**
 * 新增日志
 * @param req
 * @param log
 * @param callback
 */
logUtil.saveLog= function(req,log,callback){
    if(req.session.sysuser){
        logger.debug('====id=====',req.session.sysuser.userId);
        log.creatorId=req.session.sysuser.userId;
        log.username=req.session.sysuser.accountName;
        log.mobile=req.session.sysuser.mobile;
        log.email=req.session.sysuser.email;
    }
    log.shopId=config.LogService.retailersId;
    log.system=config.LogService.system;
    log.accessIp =req.headers['x-forwarded-for'] ||
        req.connection.remoteAddress ||
        req.socket.remoteAddress ||
        req.connection.socket.remoteAddress;
    log.operateTime=dateUtils.nowTime();
    var strJson = JSON.stringify(log);
    logger.debug(strJson);
    authRestApiProxy.post('LogService','/operationLog/a',log,function(err,data){
        if(err){
            logger.error(JSON.stringify(err));
        }
        if(callback!=undefined){
            callback(err,data);
        }
    });
};

/**
 * 查询日志
 * @param req
 * @param log
 * @param callback
 */
logUtil.query= function(req,log,callback){
    log.shopId=config.LogService.retailersId;
    log.system=config.LogService.system;
    if(log.pi == undefined || log.pi <= 0){
        log.pi = 0;
    }else{//日志系统规范，页码从0开始
        log.pi = log.pi-1;
    }
    if(log.ps == undefined ||log.ps <= 0){
        log.ps=cookieUtil.getPageSizeCookie(req);
    }
    var param=objToParam(log);
    var url = '/operationLog/q?'+param;
    authRestApiProxy.get('LogService',url,function(err,data){
        callback(err,data);
    });
};

/**
 * 转化get参数
 * @param log
 * @returns {string}
 */
function objToParam(log){
    var param='';
    for(var i in log){
        param+="&"+i+"="+log[i];
    }
  return  param.substr(1);
}

/**
 * 初始化Log的基本信息
 * @param operatePath 路径，模块名
 * @param operateItem 操作条目名
 * @param remark 日志明细 ，最大1024
 * @param operateResult 操作结果：1成功，0失败
 * @param objectId 主题ID 可为空
 * @returns {{objectId: null, operateItem: *, operatePath: string, operateResult: number, remark: *}}
 */
logUtil.initLogMsg=function (operatePath,operateItem,remark,operateResult,objectId){
    var log = {
        objectId:objectId!=undefined?objectId:null,
        operateItem:operateItem,
        operatePath:operatePath,
        operateResult:operateResult!=undefined?operateResult:1,
        remark:remark
    };
    return log;
}
module.exports = logUtil;