var redisClient = require("../libs/redisClient");
var constants = require("../libs/constants.js");
var authRestApiProxy = require("../libs/authRestApiProxy")
var log4js = require('../libs/log4jsUtil.js')
var config = require('../libs/config.js')
var logger = log4js.getLogger('merchantUtil');
var merchantUtil = {};


/**
 * 获取商家商家信息
 * @param callback
 */
merchantUtil.getMerchantInfo = function (callback) {
    var key = constants.merchantInfo + config["RetailerService"].retailersId;
    redisClient.get(key, function (err, merchantData) {
        if (merchantData != null && err == null) {
            callback(null, JSON.parse(merchantData));
        } else {
            authRestApiProxy.post('RetailerService', "/merchant/q", {}, function resultData(err, resultData) {
                if (resultData != null && err == null) {
                    var redisData = JSON.stringify(resultData)
                    redisClient.set(key, redisData, null, function (err, data) {
                        if (data != null && err == null) {
                            logger.info("商家信息缓存设置成功")
                            callback(null, resultData);
                        }
                        else {
                            logger.info("商家信息缓存设置失败")
                            callback(err, null);
                        }
                    });

                } else {
                    logger.info("商家信息查询失败")
                    callback(err, null);
                }
            });
        }
    })

};
/**
 * 更新商户缓存
 * @param data
 * @param callback
 */
merchantUtil.updateCacheMerchantInfo = function (callback) {
    var key = constants.merchantInfo + config["RetailerService"].retailersId;
    authRestApiProxy.post('RetailerService', "/merchant/q", {}, function resultData(err, resultData) {
        if (resultData != null && err == null) {
            var redisData = JSON.stringify(resultData)
            redisClient.set(key, redisData, null, function (err, data) {
                if (data != null && err == null) {
                    callback(null, resultData);
                }
                else {
                    logger.info("商家信息缓存设置失败")
                    callback(err, null);
                }
            });

        } else {
            logger.info("商家信息查询失败")
            callback(err, null);
        }
    });
};

module.exports = merchantUtil;