/**
 * Created by zhangxingbing on 2016/4/26.
 */

var authRestApiProxy = require('./authRestApiProxy');
var log4js = require('./log4jsUtil.js');
var logger = log4js.getLogger('logUtil');
var config = require('./config');
var pageTabConfigUtil={};

/**
 * 获取配置信息
 * @param configType  配置类型
 * @param tabsId  栏目ID
 * @param callback
 */
pageTabConfigUtil.pageConfigValue= function(configType,tabsId,callback){
    var retailersId = config.RetailerService.retailersId;
    var pccVo = {};
    pccVo.configType = configType;
    pccVo.tabsId = tabsId;
    pccVo.retailersId = retailersId;
    authRestApiProxy.post('RetailerService','/page/tab/config/qt',pccVo,function(err,data){
        if(err){
            logger.error(JSON.stringify(err));
        }
        if(callback!=undefined){
            callback(err,data);
        }
    });
};

/**
 * 修改配置信息
 * @param configType  配置类型
 * @param tabsId  栏目ID
 * * @param configValue  配置类型
 * @param callback
 */
pageTabConfigUtil.updateConfigValue= function(configType,tabsId,configValue,callback){
    var pccVo = {};
    pccVo.configType = configType;
    pccVo.tabsId = tabsId;
    pccVo.retailersId = config.RetailerService.retailersId;
    pccVo.configValue = configValue;
    authRestApiProxy.post('RetailerService','/page/tab/config/ui',pccVo,function(err,data){
        if(err){
            logger.error(JSON.stringify(err));
            if(callback!=undefined){
                callback(err,null);
            }
        }else{
            if(callback!=undefined){
                callback(null,data);
            }
        }
    });
};


module.exports = pageTabConfigUtil;