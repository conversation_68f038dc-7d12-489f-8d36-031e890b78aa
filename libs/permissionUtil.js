/**
 * Created by ch<PERSON><PERSON><PERSON><PERSON> on 2016/2/19.
 */

var authRestApiProxy = require('./authRestApiProxy');
var redisClient = require("./redisClient");
var log4js = require('./log4jsUtil.js');
var logger = log4js.getLogger('permissionUtil');
var constants = require("./constants");
var cache = require('memory-cache');
var config = require('./config');

var permissionUtil = {};

/**
 * 将当前用户的权限信息放入session
 * @param req 请求
 * @param userId 用户ID
 * @param callback 回调
 */
permissionUtil.pushUserActionsToSession=function(req,userId,callback){

        var url = '/permission/action/q/u/'+userId;
        authRestApiProxy.get('PermissionService', url, function resultData(err, data) {
             if(err){
                var jsonStr = JSON.stringify(err);
                logger.debug(jsonStr);
                callback(err,null);
            }else{
                 req.session.actions=data;
                 logger.debug('权限信息session:'+data );
                 callback(null,data);
             }
        });
}

/**
 *将当前用户的角色信息放入session
 * @param req
 * @param userId 用户ID
 * @param callback 回调
 */
permissionUtil.pushUserRolesToSession=function(req,userId,callback){

    var url = '/permission/role/q/u/'+userId;
    authRestApiProxy.get('PermissionService', url, function resultData(err, data) {
        if(err){
            var jsonStr = JSON.stringify(err);
            logger.error('角色信息获取失败：'+ jsonStr);
            if(callback!==undefined){
                callback(err,null);
            }
        }else{
            req.session.roles=data;
            logger.info('角色信息session:'+data );
            if(callback!==undefined){
                callback(null,data);
            }
        }
    });
}
/**
 *判断当前action是否有权限
 * @param req 请求对象
 * @param action 当前功能action 字符串
 * @returns {{}}
 */
permissionUtil.hasPermission=function hasPermission(req,action){
    var actionInfos =  req.session.actions.actionInfos;
    if(actionInfos){//如果不为空对象，遍历判断
        var actionsLen = actionInfos.length,
            k = 0;
        for(; k < actionsLen; ++k){
            if(actionInfos[k].accode===action){
                return true;
            }
        }
    }
    return false;
}

/**
 * 电商专用
 * 权限校验，如果校验不通过跳转到错误页面
 * @param req
 * @param res
 * @param action 权限的code ，字符串
 * @returns {boolean}
 */
permissionUtil.checkPermision=function(req,res,action){
    var actionInfos =  req.session.actions.actionInfos;
    if(actionInfos){//如果不为空对象，遍历判断
        var actionsLen = actionInfos.length;
        for(var    k = 0; k < actionsLen; ++k){
            if(actionInfos[k].accode===action){
                return true;
            }
        }
    }
    var data={};
    data.message='权限校验失败';
    res.render('error',data);
    return false;
}

/**
 * 校验权限,比如按钮权限，不能跳转到错误页，需要自行处置的情况
 * @param req
 * @param action 权限的code ，字符串
 * @param callback
 */
permissionUtil.validPermision=function(req,action,callback){
    var actionInfos =  req.session.actions.actionInfos;
    var err={};
    err.nopermission=true;
    err.message='权限校验失败';
    if(actionInfos){//如果不为空对象，遍历判断
        var data = null;
        var actionsLen = actionInfos.length;
        for(var    k = 0; k < actionsLen; ++k){
            if(actionInfos[k].accode===action){
                data=true;
                break;
            }
        }
        if(data){
            callback(null,data);
        }else{
            callback(err,null);
        }
    }else{
        callback(err,null);
    }
}
/**
 * set的通用回调
 * @param err
 * @param data
 * @param key
 * @param timeOut
 * @param callback
 */
function  commonSetCallBack(err, data,key,timeOut,callback){
    if (err) {
        logger.debug(err);
        if(callback!=undefined){
            callback(err,null);
        }
    }
    if (data) {
        var redisData = JSON.stringify(data);
        //把返回数据放入redis
        redisClient.set(key, redisData, timeOut, function redisCallBack(redisErr,redisData1){
            //只有redis异常时才会进入这个回调
            if(redisErr){
                logger.error('redis exception happened when cacheActions: '+redisErr);
                if(callback!=undefined) {
                    callback(redisErr,null);
                }
            }else if(redisData1!=null){
                logger.debug(redisData);
                //成功给出回调
                if(callback!=undefined) {
                    callback(null,data);
                }
            }
        });
    }
}

/**
 * 加载权限到redis缓存
 * @param key
 * @param timeOut
 * @param callback
 */
permissionUtil.cacheActions = function (key,timeOut,callback) {
    authRestApiProxy.get('PermissionService', "/permission/group/b/t", function (err, data) {
        commonSetCallBack(err, data,key,timeOut,callback);
    });
}

/**
 * 从缓存获取所有actions集合,如果缓存里已经没有数据 刷新缓存 返回取值
 * @param key  不同系统key不同
 * @param callback
 */
permissionUtil.getActions = function(key,callback){

    redisClient.get(key,function redisGetCallBack(err,data){
        if(err){
            if(callback!=undefined){
                callback(err,null);
            }
        }else if(data){
            if(callback!=undefined) {
                callback(null,JSON.parse(data));
            }
        }else{//如果取不到数据，应该是缓存被清除了；重新加载
            permissionUtil.cacheActions(key,null,function setCallBack(setErr,setData){
                if(setErr){
                    if(callback!=undefined){
                        callback(setErr,null);
                    }
                }
                if(setData){
                    if(callback!=undefined){
                        callback(null,setData);
                    }
                }
            });
        }
    });
}


/**
 * 缓存商户的全部角色，对角色进行改动时需要调用此方法
 * @param key  系统角色的token
 * @param timeOut
 * @param callback
 */
permissionUtil.cacheRetailRoles =function  (key,timeOut,callback){
    var systemsConfig = config['PermissionService'];
    var retailersId = systemsConfig['retailersId'];//商户ID
    var url = '/permission/role/q/rt/'+retailersId;
    authRestApiProxy.get('PermissionService', url, function resultData(err, data) {
        //token + retailersId 才是真正传入redis的key
        commonSetCallBack(err, data,key+retailersId,timeOut,callback);
    });
}
/**
 *
 * @param key
 * @param callback
 */
permissionUtil.getRetailRoles = function(key,callback){
    var systemsConfig = config['PermissionService'];
    var retailersId = systemsConfig['retailersId'];
    redisClient.get(key+retailersId,function redisGetCallBack(err,data){
        if(err){
            if(callback!=undefined){
                callback(err,null);
            }
        }else if(data){
            if(callback!=undefined) {
                callback(null,JSON.parse(data));
            }
        }else{//如果取不到数据，应该是缓存被清除了；重新加载
            permissionUtil.cacheRetailRoles(key,null,function setCallBack(setErr,setData){
                if(setErr){
                    if(callback!=undefined){
                        callback(setErr,null);
                    }
                }
                if(setData){
                    if(callback!=undefined){
                        callback(null,setData);
                    }
                }
            });
        }
    });
}
/**
 * 根据角色ID查询全部绑定的用户ID
 * @param roleId
 * @param callback
 */
permissionUtil.getBindUserIdsByRoleId = function(roleId,callback){
    var url='/permission/ur/q/r/'+roleId;
    //获取角色绑定的全部用户
    authRestApiProxy.get('PermissionService', url, function resultData(err, data) {
        if(err){
          callback(err,null);
        }else{
            callback(null,data);
        }
    });
}
/**
 * 根据用户主键ID的数组，获取每个用户的角色集合；返回key，value的数据
 * @param userIds 用户ID的数组
 * @param callback
 */
permissionUtil.getUserRolesMap = function(userIds,callback){
    authRestApiProxy.post('PermissionService', '/permission/role/q/us',userIds, function resultData(err, data) {
        if(err){
            callback(err,null);
        }else{
            callback(null,data);
        }
    });
}

module.exports = permissionUtil;