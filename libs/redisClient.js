var redisPool = require('./redisPool.js');
var configs = require('./config.js')
var log4js = require("./log4jsUtil.js");
var logger = log4js.getLogger('redisClient');

// 模拟Redis客户端
var mockRedisStore = {};

var redisClient = {
    //向redis保存数据
    set: function (key, value, timeOut, callback) {
        logger.debug('Mock Redis set: ' + key);
        try {
            mockRedisStore[key] = value;
            if (timeOut === null) {
                timeOut = 3600; // 默认1小时过期
            }
            // 模拟过期时间
            if (timeOut > 0) {
                setTimeout(function() {
                    delete mockRedisStore[key];
                }, timeOut * 1000);
            }
            if (callback) {
                callback(null, 'OK');
            }
        } catch (err) {
            console.log("set callback Error " + err);
            if(callback !== undefined){
                callback(err, null);
            }
        }
    },
    //从redis中获取数据
    get: function (key, callback) {
        logger.debug('Mock Redis get: ' + key);
        try {
            var data = mockRedisStore[key] || null;
            if (callback) {
                callback(null, data);
            }
        } catch (err) {
            console.log('get Error' + err);
            if (callback) {
                callback(err, null);
            }
        }
    },
    //设置数据的timeout时间，超过这个时间，数据自动失效
    expire: function (key, timeout, callback) {
        logger.debug('Mock Redis expire: ' + key + ' for ' + timeout + ' seconds');
        if (timeout > 0) {
            setTimeout(function() {
                delete mockRedisStore[key];
            }, timeout * 1000);
        }
        if (callback) {
            callback(null, 1);
        }
    },
    //删除
    //add by yubin
    remove: function (key, callback) {
        redisPool.acquire(function (err, client) {
            if (err) {
                console.log("expire callback Error " + err);
                callback(err);
            } else {
                client.send_command('expire', [key, -1], function () {
                    redisPool.release(client);
                });
            }
        });
    }
};


//redisClient.set('wsssssssssname','sunhuaili',function(err){
//console.log('失败了');
//});

redisClient.remove('wsssssssssname', function (err) {
    console.log('删除失败了');
});


module.exports = redisClient;