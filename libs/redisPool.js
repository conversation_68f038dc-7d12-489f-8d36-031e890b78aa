/**
 * Created by RONGLIAN on 2016/1/21.
 */

var config=require('./config.js');

var Pool = require('generic-pool').Pool;
var redisPool = new Pool({
    name     : 'portalRedis',
    create   : function(callback) {
        var redisOptions = {
            host: config.redis.ip,
            port: config.redis.port
        };
        
        // Only add password if it's configured
        if (config.redis.password && config.redis.password !== '') {
            redisOptions.password = config.redis.password;
        }
        
        var client = require('redis').createClient(redisOptions);
        client.on("connect", function() {
            callback(null, client);
        });
        client.on("error", function(err) {
            console.error('Redis connection error:', err);
            callback(err);
        });
    },
    destroy  : function(client) { client.end(); },
    max      : 300,
    // optional. if you set this, make sure to drain() (see step 3)
    min      : 5,
    // specifies how long a resource can stay idle in pool before being removed
    idleTimeoutMillis : 60000,
    // if true, logs via console.log - can also be a function
    log : false
});

module.exports = redisPool;