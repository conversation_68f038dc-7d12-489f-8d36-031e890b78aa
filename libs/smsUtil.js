/**
 * Created by <PERSON><PERSON><PERSON> on 2016-08-25 .
 */
var config = require('../libs/config.js');
var log4js = require('../libs/log4jsUtil.js');
var logger = log4js.getLogger('smsUtil');
var smsUtil = {};
TopClient = require('./topClient').TopClient;
var client = new TopClient({
    'appkey':config.smsService.appkey,
    'appsecret':config.smsService.appsecret,
    'REST_URL':'http://gw.api.taobao.com/router/rest'});

/**
 * 发送短信验证码
 * @param recNum 目标号码，字符串
 * @param num 验证码
 */
smsUtil.sendVerifyCode = function(recNum,num,callback){

    client.execute('alibaba.aliqin.fc.sms.num.send', {
        'sms_type':'normal',
        'sms_free_sign_name':config.smsService.sms_free_sign_name,//头部签名
        'sms_param':'{\"num\":\"' +num+ '\"}',
        'rec_num':recNum,//'18210554795'
        'sms_template_code':config.smsService.verify_template_code//'SMS_13285533'
    }, function(error, response) {
        if (!error){
            console.log(response);
            callback(null, response);
        }else {
            console.log(error);
            callback(error, null);
        }
    });
}

/**
 * 检测状态变更通知，发送通知消息
 * @param templateCode 模板号
 * @param recNum 目标号码
 * @param goodsName 发送的变量数据，商品名称
 * @param callback 回调
 */
smsUtil.sendSampleCheckNotify = function(testingInfo,callback){
    var templateCode = '';
    var needSend = false;
    switch (testingInfo.testingStatus) {
        case 1://收到样品
        {
            templateCode = config.smsService.notify_template_receiveSample;
            needSend = true;
            break;
        }
        case 3://质检不合格
        {
            templateCode = config.smsService.notify_template_checkUnqualified;
            needSend = true;
            break;
        }
        case 4://检测开始
        {
            templateCode = config.smsService.notify_template_checkStart;
            needSend = true;
            break;
        }
        case 5://检测完成
        {
            templateCode = config.smsService.notify_template_checkFinish;
            needSend = true;
            break;
        }
        case 6://检测失败
        {
            templateCode = config.smsService.notify_template_checkFail;
            needSend = true;
            break;
        }
        default:
            break;
    }
    var recNum = testingInfo.custMobile;
    var goodsName =testingInfo.goodsName;
    client.execute('alibaba.aliqin.fc.sms.num.send', {
        'sms_type':'normal',
        'sms_free_sign_name':config.smsService.sms_free_sign_name,//头部签名
        'sms_param':'{\"goodsName\":\"' +goodsName+ '\"}',
        'rec_num':recNum,//'18210554795'
        'sms_template_code':templateCode
    }, function(error, response) {
        if (!error) {
            logger.info(response);
            if(callback!=undefined){
                callback(null, response);
            }
        }else{
            logger.error(error);
            if(callback!=undefined){
                callback(error, null);
            }
        }
    });
}

/**
 * 检测状态变更通知，发送通知消息
 * @param recNum 目标号码
 * @param orderObj 订单信息（包含发货），对象
 * @param callback 回调
 */
smsUtil.sendOrderShippingNotify = function(recNum,orderObj,callback){

    client.execute('alibaba.aliqin.fc.sms.num.send', {
        'sms_type':'normal',
        'sms_free_sign_name':config.smsService.sms_free_sign_name,//头部签名
        'sms_param':'{\"shippingName\":\"' +orderObj.shippingName+ '\",\"shippingNum\":\"' +orderObj.sendId+ '\"}',
        'rec_num':recNum,//'18210554795'
        'sms_template_code':config.smsService.notify_template_shipping
    }, function(error, response) {
        if (!error) {
            logger.info(response);
            //console.log(response);
            if(callback!=undefined){
                callback(null, response);
            }
        }else{
            logger.error(error);
            //console.log(error);
            if(callback!=undefined){
                callback(error, null);
            }
        }
    });
}

module.exports = smsUtil;