/**
 * Created by r<PERSON><PERSON> on 2016-08-10 .
 */
var config = require('../libs/config.js');
var WechatAPI = require('wechat-api');
var log4js = require('../libs/log4jsUtil.js');
var constants = require('../libs/constants.js');
var logger = log4js.getLogger('wechatUtil');
var redisClient = require("./redisClient");
var dateUtils = require("./dateUtils");
var authRestApiProxy = require('./authRestApiProxy');
var wechatUtil = {};

/**
 * 把商户的微信配置信息放入redis
 * @param callback
 */
wechatUtil.setWxConfig=function (callback){
    var key = constants.wxConfigToken+config.RetailerService.retailersId;
    var url = '/wx/config/q/'+config.RetailerService.retailersId;
    authRestApiProxy.get('RetailerService',url,function(err,data){
        if(err){
            logger.error(JSON.stringify(err));
            if(callback!=undefined){
                callback(err,null);
            }
        }else if(data){
            redisClient.set(key,JSON.stringify(data),null,function(redisErr,redisData){
                if(redisErr){
                    logger.error('redis exception happened when cacheActions: '+JSON.stringify(redisErr));
                    if(callback!=undefined) {
                        callback(redisErr,null);
                    }
                }else if(redisData!=null){
                    //logger.debug(redisData);
                    if(callback!=undefined){
                        callback(null,data);
                    }
                }
            });
        }
    });
}
/**
 *加载商户 的微信配置信息
 * @param callback 回调
 */
wechatUtil.loadWxConfig=function (callback){
    var key = constants.wxConfigToken+config.RetailerService.retailersId;
    redisClient.get(key,function(err1,data1){
        if(err1){
            callback(err1,null);
        }else if(data1){
            callback(null,JSON.parse(data1));
        }else{//redis没有取到值，调用restful接口从库里取最新数据
            wechatUtil.setWxConfig(function(err,data){
                if(err){
                    logger.error(JSON.stringify(err));
                    callback(err,null);
                }else if(data){
                    callback(null,data);
                }
            });
        }
    });
}
/**
 * 获取API
 * @param wxData
 * @param callback
 * @returns {API|exports|module.exports}
 */
wechatUtil.getApi = function getApi(wxData) {
    var api = new WechatAPI(wxData.appId,  wxData.appSecret, function (callback) {
        wechatUtil.getToken(function (err,data) {
            logger.info('gettoken:'+JSON.stringify(data));
            callback(err,data);
        });
    }, function (token, callback){
        logger.info('settoken:'+JSON.stringify(token));
        wechatUtil.setToken(token,function (err,data) {
            callback(err,data);
        });
    });
    return api;
}


/**
 * 获取临时调用token
 * @param callback
 */
wechatUtil.getToken = function (callback) {
    //从redis里获取token
    var key = constants.wechatToken+config.RetailerService.retailersId;
    var token =redisClient.get(key,function(err,data){
        if(err){//
            logger.info('从redis获取到token出错：'+err);
            callback(JSON.parse(err),null);
        }else{//如果获取到了有效数据，直接回调结束
            logger.info('从redis获取到token：'+data);
            callback(null,JSON.parse(data));
        }
    });
}

/**
 * 设置临时调用Token
 * @param token
 * @param callback
 */
wechatUtil.setToken = function(token,callback) {
    //从redis里获取token
    var key = constants.wechatToken+config.RetailerService.retailersId;
    logger.info('设置token：'+JSON.stringify(token));
    redisClient.set(key, JSON.stringify(token), 3600);
    if(callback!=undefined){
        callback(null,token);
    }
}

/**
 *微信发送发货通知
 * @param openID o1qyUw9EtewVMs6csO3ke3aEetew,从会员中获取
 * @param callback
 */
wechatUtil.notifyShipping = function(order,callback) {
    if(order.wxId==undefined||order.wxId==null||order.wxId==''){
        logger.error('没有微信的ID无法发送消息');
        if(callback!=undefined){
            callback('没有微信的ID无法发送消息',null);
        }
    }else{
        wechatUtil.loadWxConfig(function(wxErr,wxData){
                if(wxErr){
                    logger.error(JSON.stringify(wxErr));
                    if(callback!=undefined){
                        callback(wxErr,null);
                    }
                }else{
                    var reditUrl = config.weixinApp.domainUrl+ '/order/q/'+order.orderId;//跳转到订单详情页面，H5
                    //拼接页面授权链接
                    var url ='https://open.weixin.qq.com/connect/oauth2/authorize?appid='
                        +wxData.appId + '&redirect_uri=' + reditUrl
                        +'&response_type=code&scope=snsapi_base&state=1#wechat_redirect';
                    var templateData = {
                        "first": {
                            "value":"您好，您购买的商品已发货！",
                            "color":"#173177"
                        },
                        "keyword1":{
                            "value":order.orderSn,
                            "color":"#173177"
                        },
                        "keyword2": {
                            "value":order.shippingName,
                            "color":"#173177"
                        },
                        "keyword3": {
                            "value":order.sendId,
                            "color":"#173177"
                        },
                        "keyword4": {
                            "value":dateUtils.nowTime(),
                            "color":"#173177"
                        },
                        "remark":{
                            "value":"",
                            "color":""
                        }
                    };
                    var api = wechatUtil.getApi(wxData);
                    //发送消息
                    api.sendTemplate(order.wxId,config.weixinApp.template_notify_shipping, url, templateData, function (err, data){
                        if(err){
                            logger.error(JSON.stringify(err));
                            if(err.code==40001) {//说明token已经失效,重试
                                wechatUtil.setToken(null,function(redisErr,redisData) {//清空缓存中的微信token
                                    api.sendTemplate(order.wxId,config.weixinApp.template_notify_shipping, url, templateData, function (err1, data1){
                                        if(callback!=undefined){
                                            callback(err1,data1);
                                        }
                                    });
                                });
                            }else if(callback!=undefined){
                                callback(err,null);
                            }
                        }else{
                            logger.info(JSON.stringify(data));
                            if(callback!=undefined){
                                callback(null,data);
                            }
                        }
                    });
                }
        });

    }
}

/**
 *
 * @param samplingStatus 样品检测状态
 * @param callback
 */
wechatUtil.notifySampleCheck = function(samplingStatus,callback) {
    if(samplingStatus.wxId==undefined||samplingStatus.wxId==null||samplingStatus.wxId==''){
        logger.error('没有微信的ID无法发送消息');
        if(callback!=undefined){
            callback('没有微信的ID无法发送消息',null);
        }
    }else{
        var firstData = '样本已收到，将尽快安排检测。';
        var currentStatus = '';
        var needSend = false;
        switch (samplingStatus.testingStatus) {
            case 1://收到样品
                {
                    currentStatus = '收到样品';
                    firstData = '样本已收到，将尽快安排检测。';
                    needSend = true;
                    break;
                }
            case 3://质检不合格
                {
                    currentStatus = '质检不合格';
                    firstData = '样本质检不合格，工作人员会尽快联系您。';
                    needSend = true;
                    break;
                }
            case 4://检测开始
                {
                    currentStatus = '检测开始';
                    firstData = '样本已通过质检并开始检测。';
                    needSend = true;
                    break;
                }
            case 5://检测完成
                {
                    currentStatus = '检测完成';
                    firstData = '样本已完成检测并生成报告，请登录网站查看。';
                    needSend = true;
                    break;
                }
            case 6://检测失败
                {
                    currentStatus = '检测失败';
                    firstData = '样本检测失败，工作人员会尽快联系您。';
                    needSend = true;
                    break;
                }
            default:
                break;
        }
        if(needSend){
            var reditUrl = config.weixinApp.domainUrl+ '/sampling/ss';//跳转到进度详情页面，H5
            wechatUtil.loadWxConfig(function(wxErr,wxData) {
                if (wxErr) {
                    logger.error(JSON.stringify(wxErr));
                    if (callback != undefined) {
                        callback(wxErr, null);
                    }
                } else {
                    //拼接页面授权链接
                    var url ='https://open.weixin.qq.com/connect/oauth2/authorize?appid='
                        +wxData.appId + '&redirect_uri=' + reditUrl
                        +'&response_type=code&scope=snsapi_base&state=1#wechat_redirect';
                    var templateData = {
                        "first": {
                            "value":firstData,
                            "color":"#173177"
                        },
                        "keyword1":{
                            "value":samplingStatus.boxSn,//样品编码
                            "color":"#173177"
                        },
                        "keyword2": {
                            "value":samplingStatus.goodsName,//商品名称
                            "color":"#173177"
                        },
                        "keyword3": {
                            "value":currentStatus,//当前状态
                            "color":"#173177"
                        },
                        "remark":{
                            "value":"",
                            "color":""
                        }
                    };
                    var api = wechatUtil.getApi(wxData);
                    //发送消息
                    api.sendTemplate(samplingStatus.wxId,config.weixinApp.template_notify_sampleCheck, url, templateData, function (err, data){
                        if(err){
                            logger.error(JSON.stringify(err));
                            if(err.code==40001) {//说明token已经失效,重试
                                wechatUtil.setToken(null,function(redisErr,redisData) {//清空缓存中的微信token
                                    api.sendTemplate(samplingStatus.wxId,config.weixinApp.template_notify_sampleCheck, url, templateData, function (err1, data1){
                                        if(callback!=undefined){
                                            callback(err1,data1);
                                        }
                                    });
                                });
                            }else if(callback!=undefined){
                                callback(err,null);
                            }
                        }else{
                            logger.info(JSON.stringify(data));
                            if(callback!=undefined){
                                callback(null,data);
                            }
                        }
                    });
                }
            });
        }
    }

}

module.exports = wechatUtil;