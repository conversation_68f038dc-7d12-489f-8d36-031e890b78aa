#!/usr/bin/env node

const http = require('http');
const querystring = require('querystring');

// 测试配置
const TEST_CONFIG = {
    host: 'localhost',
    port: 8088,
    loginPath: '/login/li',
    indexPath: '/index'
};

// 测试用例
const TEST_CASES = [
    {
        name: '管理员登录测试',
        username: 'admin',
        password: 'rongzhilian1819',
        expectedSuccess: true
    },
    {
        name: '普通用户登录测试',
        username: 'testuser',
        password: 'anypassword',
        expectedSuccess: true
    },
    {
        name: '错误密码测试',
        username: 'admin',
        password: 'wrongpassword',
        expectedSuccess: true // 根据代码逻辑，任何非admin的凭据都会创建测试用户
    },
    {
        name: '空用户名测试',
        username: '',
        password: 'password',
        expectedSuccess: true
    }
];

function makeRequest(options, postData) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    body: data
                });
            });
        });
        
        req.on('error', (err) => {
            reject(err);
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

async function testLogin(testCase) {
    console.log(`\n🧪 执行测试: ${testCase.name}`);
    console.log(`   用户名: ${testCase.username || '(空)'}`);
    console.log(`   密码: ${testCase.password || '(空)'}`);
    
    try {
        // 准备登录数据
        const loginData = querystring.stringify({
            username: testCase.username,
            password: testCase.password
        });
        
        // 登录请求选项
        const loginOptions = {
            hostname: TEST_CONFIG.host,
            port: TEST_CONFIG.port,
            path: TEST_CONFIG.loginPath,
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Content-Length': Buffer.byteLength(loginData)
            }
        };
        
        // 发送登录请求
        const loginResponse = await makeRequest(loginOptions, loginData);
        
        console.log(`   响应状态: ${loginResponse.statusCode}`);
        
        // 解析响应
        let responseData;
        try {
            responseData = JSON.parse(loginResponse.body);
            console.log(`   响应内容:`);
            console.log(`     代码: ${responseData.code}`);
            console.log(`     消息: ${responseData.message}`);
            
            if (responseData.data && responseData.data.user) {
                const user = responseData.data.user;
                console.log(`     用户信息:`);
                console.log(`       用户ID: ${user.userId}`);
                console.log(`       用户名: ${user.username}`);
                console.log(`       显示名: ${user.name}`);
                console.log(`       角色: ${JSON.stringify(user.roles)}`);
                console.log(`       权限: ${JSON.stringify(user.permissions)}`);
            }
            
            // 判断登录是否成功
            const isSuccess = responseData.code === 0;
            console.log(`   登录结果: ${isSuccess ? '✅ 成功' : '❌ 失败'}`);
            
            return {
                success: isSuccess,
                response: responseData,
                statusCode: loginResponse.statusCode
            };
            
        } catch (parseError) {
            console.log(`   响应内容 (原始): ${loginResponse.body}`);
            console.log(`   ❌ JSON解析失败: ${parseError.message}`);
            return {
                success: false,
                error: parseError.message,
                statusCode: loginResponse.statusCode
            };
        }
        
    } catch (error) {
        console.log(`   ❌ 请求失败: ${error.message}`);
        return {
            success: false,
            error: error.message
        };
    }
}

async function testIndexAccess() {
    console.log(`\n🔐 测试首页访问 (无登录状态)`);
    
    try {
        const indexOptions = {
            hostname: TEST_CONFIG.host,
            port: TEST_CONFIG.port,
            path: TEST_CONFIG.indexPath,
            method: 'GET'
        };
        
        const indexResponse = await makeRequest(indexOptions);
        console.log(`   响应状态: ${indexResponse.statusCode}`);
        console.log(`   内容长度: ${indexResponse.body.length} 字符`);
        
        // 检查是否被重定向到登录页
        if (indexResponse.body.includes('登录') || indexResponse.body.includes('login')) {
            console.log(`   ✅ 正确重定向到登录页面`);
        } else {
            console.log(`   ⚠️  未重定向到登录页面`);
        }
        
    } catch (error) {
        console.log(`   ❌ 请求失败: ${error.message}`);
    }
}

async function runAllTests() {
    console.log('🚀 开始登录功能测试');
    console.log(`📍 测试目标: http://${TEST_CONFIG.host}:${TEST_CONFIG.port}`);
    
    // 测试首页访问
    await testIndexAccess();
    
    // 执行所有登录测试
    const results = [];
    for (const testCase of TEST_CASES) {
        const result = await testLogin(testCase);
        results.push({
            testCase: testCase.name,
            ...result
        });
    }
    
    // 汇总结果
    console.log('\n📊 测试结果汇总:');
    console.log('=' .repeat(50));
    
    let successCount = 0;
    results.forEach((result, index) => {
        const status = result.success ? '✅ 通过' : '❌ 失败';
        console.log(`${index + 1}. ${result.testCase}: ${status}`);
        if (result.success) successCount++;
    });
    
    console.log('=' .repeat(50));
    console.log(`总计: ${results.length} 个测试, ${successCount} 个通过, ${results.length - successCount} 个失败`);
    
    if (successCount === results.length) {
        console.log('🎉 所有测试通过！');
    } else {
        console.log('⚠️  部分测试失败，请检查详细信息');
    }
}

// 运行测试
runAllTests().catch(console.error);
