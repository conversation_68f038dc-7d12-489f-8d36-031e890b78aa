{"name": "bson", "description": "A bson parser for node.js and the browser", "keywords": ["mongodb", "bson", "parser"], "version": "0.4.23", "author": "<PERSON> <<EMAIL>>", "contributors": [], "repository": {"type": "git", "url": "git://github.com/mongodb/js-bson.git"}, "bugs": {"mail": "<EMAIL>", "url": "https://github.com/mongodb/js-bson/issues"}, "devDependencies": {"nodeunit": "0.9.0", "gleak": "0.2.3", "benchmark": "1.0.0", "colors": "1.1.0"}, "config": {"native": false}, "main": "./lib/bson/index", "directories": {"lib": "./lib/bson"}, "engines": {"node": ">=0.6.19"}, "scripts": {"test": "nodeunit ./test/node"}, "browser": "lib/bson/bson.js", "license": "Apache-2.0"}