{"name": "send", "description": "Better streaming static file server with Range and conditional-GET support", "version": "0.13.2", "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "repository": "pillarjs/send", "keywords": ["static", "file", "server"], "dependencies": {"debug": "~2.2.0", "depd": "~1.1.0", "destroy": "~1.0.4", "escape-html": "~1.0.3", "etag": "~1.7.0", "fresh": "0.3.0", "http-errors": "~1.3.1", "mime": "1.3.4", "ms": "0.7.1", "on-finished": "~2.3.0", "range-parser": "~1.0.3", "statuses": "~1.2.1"}, "devDependencies": {"after": "0.8.1", "istanbul": "0.4.2", "mocha": "2.4.5", "supertest": "1.1.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot"}}