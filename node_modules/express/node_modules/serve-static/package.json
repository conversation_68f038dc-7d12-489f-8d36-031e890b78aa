{"name": "serve-static", "description": "Serve static files", "version": "1.10.3", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": "expressjs/serve-static", "dependencies": {"escape-html": "~1.0.3", "parseurl": "~1.3.1", "send": "0.13.2"}, "devDependencies": {"eslint": "2.11.1", "eslint-config-standard": "5.3.1", "eslint-plugin-promise": "1.3.1", "eslint-plugin-standard": "1.3.2", "istanbul": "0.4.3", "mocha": "2.5.3", "supertest": "1.1.0"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "engines": {"node": ">= 0.8.0"}, "scripts": {"lint": "eslint **/*.js", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}}