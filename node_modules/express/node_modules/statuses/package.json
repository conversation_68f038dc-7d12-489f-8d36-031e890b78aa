{"name": "statuses", "description": "HTTP status utility", "version": "1.2.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com", "twitter": "https://twitter.com/jongleberry"}, "repository": "jshttp/statuses", "license": "MIT", "keywords": ["http", "status", "code"], "files": ["index.js", "codes.json", "LICENSE"], "devDependencies": {"csv-parse": "0.0.6", "istanbul": "0", "mocha": "1", "stream-to-array": "2"}, "scripts": {"build": "node scripts/build.js", "update": "node scripts/update.js", "test": "mocha --reporter spec --bail --check-leaks", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks"}}