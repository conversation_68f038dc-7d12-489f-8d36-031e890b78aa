{"name": "formidable", "description": "A node.js module for parsing form data, especially file uploads.", "homepage": "https://github.com/felixge/node-formidable", "version": "1.0.17", "devDependencies": {"gently": "0.8.0", "findit": "0.1.1", "hashish": "0.0.4", "urun": "~0.0.6", "utest": "0.0.3", "request": "~2.11.4"}, "directories": {"lib": "./lib"}, "main": "./lib/index", "scripts": {"test": "node test/run.js", "clean": "rm test/tmp/*"}, "engines": {"node": ">=0.8.0"}, "repository": {"type": "git", "url": "git://github.com/felixge/node-formidable.git"}, "bugs": {"url": "http://github.com/felixge/node-formidable/issues"}, "optionalDependencies": {}}