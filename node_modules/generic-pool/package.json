{"name": "generic-pool", "description": "Generic resource pooling for Node.JS", "version": "2.4.6", "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://poetro.hu/"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "url": "http://www.developmentseed.org/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://somethingdoug.com/"}, {"name": "calibr"}, {"name": "<PERSON>", "email": "j<PERSON><PERSON><PERSON>@redventures.com>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "sandfox", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "keywords": ["pool", "pooling", "throttle"], "main": "lib/generic-pool.js", "repository": {"type": "git", "url": "http://github.com/coopernurse/node-pool.git"}, "devDependencies": {"expresso": ">0.0.0"}, "engines": {"node": ">= 0.2.0"}, "scripts": {"lint": "eslint lib test", "lint-install": "npm install eslint@^1.10.2 eslint-config-standard@^4.4.0 eslint-plugin-standard@^1.3.1", "test": "expresso test/*.js"}, "publishConfig": {"tag": "2.4.x"}, "license": "MIT"}