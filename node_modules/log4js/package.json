{"name": "log4js", "version": "0.6.38", "description": "Port of Log4js to work with node.", "keywords": ["logging", "log", "log4j", "node"], "license": "Apache-2.0", "main": "./lib/log4js", "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/nomiddlename/log4js-node.git"}, "bugs": {"url": "http://github.com/nomiddlename/log4js-node/issues"}, "engines": {"node": ">=0.8"}, "scripts": {"pretest": "jshint lib/ test/", "test": "vows"}, "directories": {"test": "test", "lib": "lib"}, "dependencies": {"readable-stream": "~1.0.2", "semver": "~4.3.3"}, "devDependencies": {"jshint": "^2.9.2", "sandboxed-module": "0.1.3", "vows": "0.7.0"}, "browser": {"os": false}}