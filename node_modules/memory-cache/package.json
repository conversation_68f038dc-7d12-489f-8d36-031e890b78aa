{"name": "memory-cache", "description": "A simple in-memory cache. put(), get() and del()", "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "keywords": ["cache", "ram", "simple", "storage"], "main": "./index.js", "version": "0.2.0", "repository": {"type": "git", "url": "git://github.com/ptarjan/node-cache.git"}, "scripts": {"test": "./node_modules/.bin/gulp test"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"chai": "^2.2.0", "gulp": "^3.8.11", "gulp-exit": "0.0.2", "gulp-istanbul": "^0.7.0", "gulp-jshint": "^1.10.0", "gulp-mocha": "^2.0.1", "jshint-stylish": "^1.0.1", "sinon": "^1.14.1", "sinon-chai": "^2.7.0"}}