{"plugins": ["plugins/markdown", "docs/lib/jsdoc/examples_plugin.js"], "source": {"include": ["test/tests/functional/operation_example_tests.js", "lib/topologies/mongos.js", "lib/topologies/command_result.js", "lib/topologies/read_preference.js", "lib/topologies/replset.js", "lib/topologies/server.js", "lib/topologies/session.js", "lib/topologies/replset_state.js", "lib/connection/logger.js", "lib/connection/connection.js", "lib/cursor.js", "lib/error.js", "node_modules/bson/lib/bson/binary.js", "node_modules/bson/lib/bson/code.js", "node_modules/bson/lib/bson/db_ref.js", "node_modules/bson/lib/bson/double.js", "node_modules/bson/lib/bson/long.js", "node_modules/bson/lib/bson/objectid.js", "node_modules/bson/lib/bson/symbol.js", "node_modules/bson/lib/bson/timestamp.js", "node_modules/bson/lib/bson/max_key.js", "node_modules/bson/lib/bson/min_key.js"]}, "templates": {"cleverLinks": true, "monospaceLinks": true, "default": {"outputSourceFiles": true}, "applicationName": "Node.js MongoDB Driver API", "disqus": true, "googleAnalytics": "***********-1", "openGraph": {"title": "", "type": "website", "image": "", "site_name": "", "url": ""}, "meta": {"title": "", "description": "", "keyword": ""}, "linenums": true}, "markdown": {"parser": "gfm", "hardwrap": true, "tags": ["examples"]}, "examples": {"indent": 4}}