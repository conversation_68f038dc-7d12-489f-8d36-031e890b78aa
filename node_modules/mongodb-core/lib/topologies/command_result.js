"use strict";

var setProperty = require('../connection/utils').setProperty
  , getProperty = require('../connection/utils').getProperty
  , getSingleProperty = require('../connection/utils').getSingleProperty;

/**
 * Creates a new CommandResult instance
 * @class
 * @param {object} result CommandResult object
 * @param {Connection} connection A connection instance associated with this result
 * @return {CommandResult} A cursor instance
 */
var CommandResult = function(result, connection) {
  this.result = result;
  this.connection = connection;
}

/**
 * Convert CommandResult to JSON
 * @method
 * @return {object}
 */
CommandResult.prototype.toJSON = function() {
  return this.result;
}

/**
 * Convert CommandResult to String representation
 * @method
 * @return {string}
 */
CommandResult.prototype.toString = function() {
  return JSON.stringify(this.toJSON());
}

module.exports = CommandResult;