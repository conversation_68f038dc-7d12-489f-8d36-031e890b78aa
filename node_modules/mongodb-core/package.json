{"name": "mongodb-core", "version": "1.3.21", "description": "Core MongoDB driver functionality, no bells and whistles and meant for integration not end applications", "main": "index.js", "scripts": {"test": "node test/runner.js -t functional", "coverage": "node_modules/.bin/nyc node test/runner.js -t functional -l && node_modules/.bin/nyc report --reporter=text-lcov | node_modules/.bin/coveralls"}, "repository": {"type": "git", "url": "git://github.com/christkv/mongodb-core.git"}, "keywords": ["mongodb", "core"], "dependencies": {"bson": "~0.4.23", "require_optional": "~1.0.0"}, "devDependencies": {"co": "4.5.4", "coveralls": "^2.11.6", "es6-promise": "^3.0.2", "gleak": "0.5.0", "integra": "0.1.8", "jsdoc": "3.3.0-alpha8", "mkdirp": "0.5.0", "mongodb-topology-manager": "1.0.x", "mongodb-version-manager": "^1.x", "nyc": "^5.5.0", "optimist": "latest", "rimraf": "2.2.6", "semver": "4.1.0"}, "peerOptionalDependencies": {"kerberos": "~0.0"}, "author": "<PERSON>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/christkv/mongodb-core/issues"}, "homepage": "https://github.com/christkv/mongodb-core"}