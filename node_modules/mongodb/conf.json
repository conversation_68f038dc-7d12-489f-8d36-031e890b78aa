{"plugins": ["plugins/markdown", "docs/lib/jsdoc/examples_plugin.js"], "source": {"include": ["test/functional/operation_example_tests.js", "test/functional/operation_promises_example_tests.js", "test/functional/operation_generators_example_tests.js", "test/functional/authentication_tests.js", "test/functional/gridfs_stream_tests.js", "lib/admin.js", "lib/collection.js", "lib/cursor.js", "lib/aggregation_cursor.js", "lib/command_cursor.js", "lib/db.js", "lib/mongo_client.js", "lib/mongos.js", "lib/read_preference.js", "lib/replset.js", "lib/server.js", "lib/bulk/common.js", "lib/bulk/ordered.js", "lib/bulk/unordered.js", "lib/gridfs/grid_store.js", "node_modules/mongodb-core/lib/error.js", "lib/gridfs-stream/index.js", "lib/gridfs-stream/download.js", "lib/gridfs-stream/upload.js", "node_modules/mongodb-core/lib/connection/logger.js", "node_modules/bson/lib/bson/binary.js", "node_modules/bson/lib/bson/code.js", "node_modules/bson/lib/bson/db_ref.js", "node_modules/bson/lib/bson/double.js", "node_modules/bson/lib/bson/long.js", "node_modules/bson/lib/bson/objectid.js", "node_modules/bson/lib/bson/symbol.js", "node_modules/bson/lib/bson/timestamp.js", "node_modules/bson/lib/bson/max_key.js", "node_modules/bson/lib/bson/min_key.js"]}, "templates": {"cleverLinks": true, "monospaceLinks": true, "default": {"outputSourceFiles": true}, "applicationName": "Node.js MongoDB Driver API", "disqus": true, "googleAnalytics": "***********-1", "openGraph": {"title": "", "type": "website", "image": "", "site_name": "", "url": ""}, "meta": {"title": "", "description": "", "keyword": ""}, "linenums": true}, "markdown": {"parser": "gfm", "hardwrap": true, "tags": ["examples"]}, "examples": {"indent": 4}}