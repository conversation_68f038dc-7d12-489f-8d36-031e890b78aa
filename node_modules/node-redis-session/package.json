{"name": "node-redis-session", "version": "0.2.7", "description": "express middleware to save session with redis", "main": "./lib/index", "dependencies": {"redis": "~0.11.0", "debug": "2.2.0"}, "devDependencies": {"mocha": "1.11.0", "debug": "2.2.0"}, "keywords": ["express", "redis", "session"], "author": "<PERSON><PERSON>", "license": "MIT", "scripts": {"test": "mocha test/test-node-redis-session.js"}, "repository": {"type": "git", "url": "https://github.com/albin3/express-redis-session.git"}, "bugs": {"url": "https://github.com/albin3/express-redis-session/issues"}, "homepage": "https://github.com/albin3/express-redis-session"}