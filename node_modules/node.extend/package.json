{"name": "node.extend", "version": "1.1.8", "description": "A port of jQuery.extend that actually works on node.js", "keywords": ["extend", "j<PERSON><PERSON><PERSON>", "jQuery extend", "clone", "copy", "inherit"], "author": "dreamerslab <<EMAIL>>", "dependencies": {"has": "^1.0.3", "is": "^3.2.1"}, "devDependencies": {"@ljharb/eslint-config": "^13.0.0", "covert": "^1.1.0", "eslint": "^5.8.0", "jscs": "^3.0.7", "safe-publish-latest": "^1.1.2", "tape": "^4.9.1"}, "repository": {"type": "git", "url": "https://github.com/dreamerslab/node.extend.git"}, "contributors": [{"name": "<PERSON>", "twitter": "https://twitter.com/ljharb"}], "main": "index", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run --silent tests-only", "posttest": "npm run coverage-quiet", "tests-only": "node test", "coverage": "covert test/index.js", "coverage-quiet": "covert test/index.js --quiet", "lint": "npm run jscs && npm run eslint .", "jscs": "jscs *.js */*.js", "eslint": "eslint"}, "engines": {"node": ">=0.4.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}, "license": "(MIT OR GPL-2.0)"}