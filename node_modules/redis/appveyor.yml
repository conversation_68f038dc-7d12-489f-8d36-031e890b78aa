# http://www.appveyor.com/docs/appveyor-yml

# Test against these versions of Node.js.
environment:
  matrix:
    - nodejs_version: "0.10"
    - nodejs_version: "0.12"
    - nodejs_version: "4"
    - nodejs_version: "5"

pull_requests:
  do_not_increment_build_number: true

platform: Any CPU
shallow_clone: true

# Install scripts. (runs after repo cloning)
install:
  # Install the Redis
  - nuget install redis-64 -excludeversion
  - redis-64\tools\redis-server.exe --service-install
  - redis-64\tools\redis-server.exe --service-start
  - '@ECHO Redis Started'
  # Get the latest stable version of Node 0.STABLE.latest
  - ps: Install-Product node $env:nodejs_version
  # Typical npm stuff. Use msvs 2013 for the hiredis parser
  - npm install
  - npm install hiredis --msvs_version=2013

# Post-install test scripts.
test_script:
  # Output useful info for debugging.
  - node --version
  - npm --version
  - cmd: npm t

os:
  - Default Azure
  - Windows Server 2012 R2

# Don't actually build using MSBuild
build: off

# Set build version format here instead of in the admin panel.
version: "{build}"
