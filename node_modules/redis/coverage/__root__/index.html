<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for __root__/</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="../prettify.css">
    <link rel="stylesheet" href="../base.css">
    <style type='text/css'>
        div.coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class="header high">
    <h1>Code coverage report for <span class="entity">__root__/</span></h1>
    <h2>
        Statements: <span class="metric">99.26% <small>(805 / 811)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Branches: <span class="metric">98.81% <small>(416 / 421)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Functions: <span class="metric">98.72% <small>(77 / 78)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Lines: <span class="metric">99.26% <small>(802 / 808)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Ignored: <span class="metric">13 statements, 2 functions, 4 branches</span> &nbsp;&nbsp;&nbsp;&nbsp;
    </h2>
    <div class="path"><a href="../index.html">All files</a> &#187; __root__/</div>
</div>
<div class="body">
<div class="coverage-summary">
<table>
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="index.js"><a href="index.js.html">index.js</a></td>
	<td data-value="99.26" class="pic high"><span class="cover-fill" style="width: 99px;"></span><span class="cover-empty" style="width:1px;"></span></td>
	<td data-value="99.26" class="pct high">99.26%</td>
	<td data-value="811" class="abs high">(805&nbsp;/&nbsp;811)</td>
	<td data-value="98.81" class="pct high">98.81%</td>
	<td data-value="421" class="abs high">(416&nbsp;/&nbsp;421)</td>
	<td data-value="98.72" class="pct high">98.72%</td>
	<td data-value="78" class="abs high">(77&nbsp;/&nbsp;78)</td>
	<td data-value="99.26" class="pct high">99.26%</td>
	<td data-value="808" class="abs high">(802&nbsp;/&nbsp;808)</td>
	</tr>

</tbody>
</table>
</div>
</div>
<div class="footer">
    <div class="meta">Generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Mon Nov 23 2015 23:43:02 GMT+0100 (CET)</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
