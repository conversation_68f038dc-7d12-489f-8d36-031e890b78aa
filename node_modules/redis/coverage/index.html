<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="prettify.css">
    <link rel="stylesheet" href="base.css">
    <style type='text/css'>
        div.coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class="header high">
    <h1>Code coverage report for <span class="entity">All files</span></h1>
    <h2>
        Statements: <span class="metric">99.38% <small>(962 / 968)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Branches: <span class="metric">98.95% <small>(471 / 476)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Functions: <span class="metric">98.94% <small>(93 / 94)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Lines: <span class="metric">99.38% <small>(959 / 965)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Ignored: <span class="metric">13 statements, 2 functions, 4 branches</span> &nbsp;&nbsp;&nbsp;&nbsp;
    </h2>
    <div class="path"></div>
</div>
<div class="body">
<div class="coverage-summary">
<table>
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="__root__/"><a href="__root__/index.html">__root__/</a></td>
	<td data-value="99.26" class="pic high"><span class="cover-fill" style="width: 99px;"></span><span class="cover-empty" style="width:1px;"></span></td>
	<td data-value="99.26" class="pct high">99.26%</td>
	<td data-value="811" class="abs high">(805&nbsp;/&nbsp;811)</td>
	<td data-value="98.81" class="pct high">98.81%</td>
	<td data-value="421" class="abs high">(416&nbsp;/&nbsp;421)</td>
	<td data-value="98.72" class="pct high">98.72%</td>
	<td data-value="78" class="abs high">(77&nbsp;/&nbsp;78)</td>
	<td data-value="99.26" class="pct high">99.26%</td>
	<td data-value="808" class="abs high">(802&nbsp;/&nbsp;808)</td>
	</tr>

<tr>
	<td class="file high" data-value="lib/"><a href="lib/index.html">lib/</a></td>
	<td data-value="100" class="pic high"><span class="cover-fill cover-full" style="width: 100px;"></span><span class="cover-empty" style="width:0px;"></span></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="37" class="abs high">(37&nbsp;/&nbsp;37)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="10" class="abs high">(10&nbsp;/&nbsp;10)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="5" class="abs high">(5&nbsp;/&nbsp;5)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="37" class="abs high">(37&nbsp;/&nbsp;37)</td>
	</tr>

<tr>
	<td class="file high" data-value="lib/parsers/"><a href="lib/parsers/index.html">lib/parsers/</a></td>
	<td data-value="100" class="pic high"><span class="cover-fill cover-full" style="width: 100px;"></span><span class="cover-empty" style="width:0px;"></span></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="120" class="abs high">(120&nbsp;/&nbsp;120)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="45" class="abs high">(45&nbsp;/&nbsp;45)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">(11&nbsp;/&nbsp;11)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="120" class="abs high">(120&nbsp;/&nbsp;120)</td>
	</tr>

</tbody>
</table>
</div>
</div>
<div class="footer">
    <div class="meta">Generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Mon Nov 23 2015 23:43:02 GMT+0100 (CET)</div>
</div>
<script src="prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="sorter.js"></script>
</body>
</html>
