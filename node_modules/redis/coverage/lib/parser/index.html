<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for lib/parser/</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="../../prettify.css">
    <link rel="stylesheet" href="../../base.css">
    <style type='text/css'>
        div.coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class="header high">
    <h1>Code coverage report for <span class="entity">lib/parser/</span></h1>
    <h2>
        Statements: <span class="metric">96.4% <small>(134 / 139)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Branches: <span class="metric">93.1% <small>(54 / 58)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Functions: <span class="metric">100% <small>(12 / 12)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Lines: <span class="metric">96.4% <small>(134 / 139)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Ignored: <span class="metric">1 statement, 1 branch</span> &nbsp;&nbsp;&nbsp;&nbsp;
    </h2>
    <div class="path"><a href="../../index.html">All files</a> &#187; lib/parser/</div>
</div>
<div class="body">
<div class="coverage-summary">
<table>
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="hiredis.js"><a href="hiredis.js.html">hiredis.js</a></td>
	<td data-value="100" class="pic high"><span class="cover-fill cover-full" style="width: 100px;"></span><span class="cover-empty" style="width:0px;"></span></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="19" class="abs high">(19&nbsp;/&nbsp;19)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">(8&nbsp;/&nbsp;8)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">(3&nbsp;/&nbsp;3)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="19" class="abs high">(19&nbsp;/&nbsp;19)</td>
	</tr>

<tr>
	<td class="file high" data-value="javascript.js"><a href="javascript.js.html">javascript.js</a></td>
	<td data-value="95.83" class="pic high"><span class="cover-fill" style="width: 95px;"></span><span class="cover-empty" style="width:5px;"></span></td>
	<td data-value="95.83" class="pct high">95.83%</td>
	<td data-value="120" class="abs high">(115&nbsp;/&nbsp;120)</td>
	<td data-value="92" class="pct high">92%</td>
	<td data-value="50" class="abs high">(46&nbsp;/&nbsp;50)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">(9&nbsp;/&nbsp;9)</td>
	<td data-value="95.83" class="pct high">95.83%</td>
	<td data-value="120" class="abs high">(115&nbsp;/&nbsp;120)</td>
	</tr>

</tbody>
</table>
</div>
</div>
<div class="footer">
    <div class="meta">Generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Mon Sep 21 2015 02:39:43 GMT+0200 (CEST)</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
