<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for lib/parser/javascript.js</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="../../prettify.css">
    <link rel="stylesheet" href="../../base.css">
    <style type='text/css'>
        div.coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class="header high">
    <h1>Code coverage report for <span class="entity">lib/parser/javascript.js</span></h1>
    <h2>
        Statements: <span class="metric">95.83% <small>(115 / 120)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Branches: <span class="metric">92% <small>(46 / 50)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Functions: <span class="metric">100% <small>(9 / 9)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Lines: <span class="metric">95.83% <small>(115 / 120)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Ignored: <span class="metric">1 statement, 1 branch</span> &nbsp;&nbsp;&nbsp;&nbsp;
    </h2>
    <div class="path"><a href="../../index.html">All files</a> &#187; <a href="index.html">lib/parser/</a> &#187; javascript.js</div>
</div>
<div class="body">
<pre><table class="coverage">
<tr><td class="line-count">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222</td><td class="line-coverage"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">1727</span>
<span class="cline-any cline-yes">1727</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">474</span>
<span class="cline-any cline-yes">474</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">474</span>
<span class="cline-any cline-yes">474</span>
<span class="cline-any cline-yes">474</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">3138</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3138</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1091</span>
<span class="cline-any cline-yes">1091</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1091</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1091</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1091</span>
<span class="cline-any cline-yes">44</span>
<span class="cline-any cline-yes">1047</span>
<span class="cline-any cline-yes">388</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">659</span>
<span class="cline-any cline-yes">2047</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">320</span>
<span class="cline-any cline-yes">320</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">320</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">320</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">320</span>
<span class="cline-any cline-yes">1727</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1452</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1452</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1452</span>
<span class="cline-any cline-yes">64</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1388</span>
<span class="cline-any cline-yes">1388</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1388</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1388</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1387</span>
<span class="cline-any cline-yes">300</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1087</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">275</span>
<span class="cline-any cline-yes">275</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">275</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">272</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">269</span>
<span class="cline-any cline-yes">269</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">269</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">269</span>
<span class="cline-any cline-yes">1043</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1043</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1043</span>
<span class="cline-any cline-yes">1040</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">266</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">1350</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1350</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1350</span>
<span class="cline-any cline-yes">3441</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3441</span>
<span class="cline-any cline-yes">1346</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2095</span>
<span class="cline-any cline-yes">2095</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2095</span>
<span class="cline-any cline-yes">1030</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1030</span>
<span class="cline-any cline-yes">1065</span>
<span class="cline-any cline-yes">39</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">39</span>
<span class="cline-any cline-yes">1026</span>
<span class="cline-any cline-yes">247</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">247</span>
<span class="cline-any cline-yes">779</span>
<span class="cline-any cline-yes">556</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">556</span>
<span class="cline-any cline-yes">223</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">223</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">223</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">219</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1350</span>
<span class="cline-any cline-yes">468</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">468</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">882</span>
<span class="cline-any cline-yes">878</span>
<span class="cline-any cline-yes">878</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">878</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">1727</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1727</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1727</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">3138</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3138</span>
<span class="cline-any cline-yes">9432</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">9432</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3138</span>
<span class="cline-any cline-yes">3138</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">3713</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">'use strict';
&nbsp;
var util   = require("util");
&nbsp;
function Packet(type, size) {
    this.type = type;
    this.size = +size;
}
&nbsp;
function ReplyParser(return_buffers) {
    this.name = exports.name;
    this.return_buffers = return_buffers;
&nbsp;
    this._buffer            = null;
    this._offset            = 0;
    this._encoding          = "utf-8";
}
&nbsp;
function IncompleteReadBuffer(message) {
    this.name = "IncompleteReadBuffer";
    this.message = message;
}
util.inherits(IncompleteReadBuffer, Error);
&nbsp;
ReplyParser.prototype._parseResult = function (type) {
    var start, end, offset, packetHeader;
&nbsp;
    if (type === 43 || type === 45) { // + or -
        // up to the delimiter
        end = this._packetEndOffset() - 1;
        start = this._offset;
&nbsp;
        // include the delimiter
        this._offset = end + 2;
&nbsp;
        <span class="missing-if-branch" title="if path not taken" >I</span>if (end &gt; this._buffer.length) {
<span class="cstat-no" title="statement not covered" >            this._offset = start;</span>
<span class="cstat-no" title="statement not covered" >            throw new IncompleteReadBuffer("Wait for more data.");</span>
        }
&nbsp;
        if (type === 45) {
            return new Error(this._buffer.toString(this._encoding, start, end));
        } else if (this.return_buffers) {
            return this._buffer.slice(start, end);
        }
        return this._buffer.toString(this._encoding, start, end);
    } else if (type === 58) { // :
        // up to the delimiter
        end = this._packetEndOffset() - 1;
        start = this._offset;
&nbsp;
        // include the delimiter
        this._offset = end + 2;
&nbsp;
        <span class="missing-if-branch" title="if path not taken" >I</span>if (end &gt; this._buffer.length) {
<span class="cstat-no" title="statement not covered" >            this._offset = start;</span>
<span class="cstat-no" title="statement not covered" >            throw new IncompleteReadBuffer("Wait for more data.");</span>
        }
&nbsp;
        // return the coerced numeric value
        return +this._buffer.toString('ascii', start, end);
    } else if (type === 36) { // $
        // set a rewind point, as the packet could be larger than the
        // buffer in memory
        offset = this._offset - 1;
&nbsp;
        packetHeader = new Packet(type, this.parseHeader());
&nbsp;
        // packets with a size of -1 are considered null
        if (packetHeader.size === -1) {
            return null;
        }
&nbsp;
        end = this._offset + packetHeader.size;
        start = this._offset;
&nbsp;
        // set the offset to after the delimiter
        this._offset = end + 2;
&nbsp;
        if (end &gt; this._buffer.length) {
            this._offset = offset;
            throw new IncompleteReadBuffer("Wait for more data.");
        }
&nbsp;
        if (this.return_buffers) {
            return this._buffer.slice(start, end);
        }
        return this._buffer.toString(this._encoding, start, end);
    } else { // *
        offset = this._offset;
        packetHeader = new Packet(type, this.parseHeader());
&nbsp;
        if (packetHeader.size &lt; 0) {
            return null;
        }
&nbsp;
        if (packetHeader.size &gt; this._bytesRemaining()) {
            this._offset = offset - 1;
            throw new IncompleteReadBuffer("Wait for more data.");
        }
&nbsp;
        var reply = [];
        var ntype, i, res;
&nbsp;
        offset = this._offset - 1;
&nbsp;
        for (i = 0; i &lt; packetHeader.size; i++) {
            ntype = this._buffer[this._offset++];
&nbsp;
            <span class="missing-if-branch" title="if path not taken" >I</span>if (this._offset &gt; this._buffer.length) {
<span class="cstat-no" title="statement not covered" >                throw new IncompleteReadBuffer("Wait for more data.");</span>
            }
            res = this._parseResult(ntype);
            reply.push(res);
        }
&nbsp;
        return reply;
    }
};
&nbsp;
ReplyParser.prototype.execute = function (buffer) {
    this.append(buffer);
&nbsp;
    var type, ret, offset;
&nbsp;
    while (true) {
        offset = this._offset;
        // at least 4 bytes: :1\r\n
        if (this._bytesRemaining() &lt; 4) {
            break;
        }
&nbsp;
        try {
            type = this._buffer[this._offset++];
&nbsp;
            if (type === 43) { // +
                ret = this._parseResult(type);
&nbsp;
                this.send_reply(ret);
            } else  if (type === 45) { // -
                ret = this._parseResult(type);
&nbsp;
                this.send_error(ret);
            } else if (type === 58) { // :
                ret = this._parseResult(type);
&nbsp;
                this.send_reply(ret);
            } else if (type === 36) { // $
                ret = this._parseResult(type);
&nbsp;
                this.send_reply(ret);
            } else <span class="missing-if-branch" title="else path not taken" >E</span>if (type === 42) { // 42 *
                // set a rewind point. if a failure occurs,
                // wait for the next execute()/append() and try again
                offset = this._offset - 1;
&nbsp;
                ret = this._parseResult(type);
&nbsp;
                this.send_reply(ret);
            }
        } catch (err) {
            // catch the error (not enough data), rewind, and wait
            // for the next packet to appear
            this._offset = offset;
            break;
        }
    }
};
&nbsp;
ReplyParser.prototype.append = function (newBuffer) {
&nbsp;
    // first run
    if (this._buffer === null) {
        this._buffer = newBuffer;
&nbsp;
        return;
    }
&nbsp;
    // out of data
    if (this._offset &gt;= this._buffer.length) {
        this._buffer = newBuffer;
        this._offset = 0;
&nbsp;
        return;
    }
&nbsp;
    this._buffer = Buffer.concat([this._buffer.slice(this._offset), newBuffer]);
    this._offset = 0;
};
&nbsp;
ReplyParser.prototype.parseHeader = function () {
    var end   = this._packetEndOffset(),
        value = this._buffer.toString('ascii', this._offset, end - 1);
&nbsp;
    this._offset = end + 1;
&nbsp;
    return value;
};
&nbsp;
ReplyParser.prototype._packetEndOffset = function () {
    var offset = this._offset;
&nbsp;
    while (this._buffer[offset] !== 0x0d &amp;&amp; this._buffer[offset + 1] !== 0x0a) {
        offset++;
&nbsp;
        /* istanbul ignore if: activate the js parser out of memory test to test this */
        <span class="skip-if-branch" title="if path not taken" >I</span>if (offset &gt;= this._buffer.length) {
<span class="cstat-skip" title="statement not covered" >            throw new IncompleteReadBuffer("didn't see LF after NL reading multi bulk count (" + offset + " =&gt; " + this._buffer.length + ", " + this._offset + ")");</span>
        }
    }
&nbsp;
    offset++;
    return offset;
};
&nbsp;
ReplyParser.prototype._bytesRemaining = function () {
    return (this._buffer.length - this._offset) &lt; 0 ? 0 : (this._buffer.length - this._offset);
};
&nbsp;
exports.Parser = ReplyParser;
exports.name = "javascript";
&nbsp;</pre></td></tr>
</table></pre>

</div>
<div class="footer">
    <div class="meta">Generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Mon Sep 21 2015 02:39:43 GMT+0200 (CEST)</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
