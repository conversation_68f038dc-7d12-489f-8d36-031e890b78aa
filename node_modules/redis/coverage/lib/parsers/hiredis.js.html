<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for lib/parsers/hiredis.js</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="../../prettify.css">
    <link rel="stylesheet" href="../../base.css">
    <style type='text/css'>
        div.coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class="header high">
    <h1>Code coverage report for <span class="entity">lib/parsers/hiredis.js</span></h1>
    <h2>
        Statements: <span class="metric">100% <small>(19 / 19)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Branches: <span class="metric">100% <small>(4 / 4)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Functions: <span class="metric">100% <small>(3 / 3)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Lines: <span class="metric">100% <small>(19 / 19)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Ignored: <span class="metric"><span class="ignore-none">none</span></span> &nbsp;&nbsp;&nbsp;&nbsp;
    </h2>
    <div class="path"><a href="../../index.html">All files</a> &#187; <a href="index.html">lib/parsers/</a> &#187; hiredis.js</div>
</div>
<div class="body">
<pre><table class="coverage">
<tr><td class="line-count">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38</td><td class="line-coverage"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">1074</span>
<span class="cline-any cline-yes">1074</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">17503</span>
<span class="cline-any cline-yes">17503</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">3044</span>
<span class="cline-any cline-yes">3044</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3044</span>
<span class="cline-any cline-yes">14471</span>
<span class="cline-any cline-yes">105</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">14366</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">14459</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">'use strict';
&nbsp;
var hiredis = require('hiredis');
&nbsp;
function HiredisReplyParser(return_buffers) {
    this.name = exports.name;
    this.reader = new hiredis.Reader({
        return_buffers: return_buffers
    });
}
&nbsp;
HiredisReplyParser.prototype.return_data = function () {
    try {
        return this.reader.get();
    } catch (err) {
        // Protocol errors land here
        this.send_error(err);
        return void 0;
    }
};
&nbsp;
HiredisReplyParser.prototype.execute = function (data) {
    this.reader.feed(data);
    var reply = this.return_data();
&nbsp;
    while (reply !== undefined) {
        if (reply &amp;&amp; reply.name === 'Error') {
            this.send_error(reply);
        } else {
            this.send_reply(reply);
        }
        reply = this.return_data();
    }
};
&nbsp;
exports.Parser = HiredisReplyParser;
exports.name = 'hiredis';
&nbsp;</pre></td></tr>
</table></pre>

</div>
<div class="footer">
    <div class="meta">Generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Mon Nov 23 2015 23:43:02 GMT+0100 (CET)</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
