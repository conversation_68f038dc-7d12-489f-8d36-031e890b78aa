<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for lib/queue.js</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="../prettify.css">
    <link rel="stylesheet" href="../base.css">
    <style type='text/css'>
        div.coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class="header low">
    <h1>Code coverage report for <span class="entity">lib/queue.js</span></h1>
    <h2>
        Statements: <span class="metric">30.3% <small>(10 / 33)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Branches: <span class="metric">0% <small>(0 / 6)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Functions: <span class="metric">16.67% <small>(1 / 6)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Lines: <span class="metric">30.3% <small>(10 / 33)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Ignored: <span class="metric"><span class="ignore-none">none</span></span> &nbsp;&nbsp;&nbsp;&nbsp;
    </h2>
    <div class="path"><a href="../index.html">All files</a> &#187; <a href="index.html">lib/</a> &#187; queue.js</div>
</div>
<div class="body">
<pre><table class="coverage">
<tr><td class="line-count">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62</td><td class="line-coverage"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">'use strict';
&nbsp;
// Queue class adapted from Tim Caswell's pattern library
// http://github.com/creationix/pattern/blob/master/lib/pattern/queue.js
&nbsp;
function Queue() {
    this.tail = [];
    this.head = [];
    this.offset = 0;
}
&nbsp;
Queue.prototype.shift = <span class="fstat-no" title="function not covered" >function () {</span>
<span class="cstat-no" title="statement not covered" >    if (this.offset === this.head.length) {</span>
<span class="cstat-no" title="statement not covered" >        var tmp = this.head;</span>
<span class="cstat-no" title="statement not covered" >        tmp.length = 0;</span>
<span class="cstat-no" title="statement not covered" >        this.head = this.tail;</span>
<span class="cstat-no" title="statement not covered" >        this.tail = tmp;</span>
<span class="cstat-no" title="statement not covered" >        this.offset = 0;</span>
<span class="cstat-no" title="statement not covered" >        if (this.head.length === 0) {</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
    }
<span class="cstat-no" title="statement not covered" >    var item = this.head[this.offset];</span>
<span class="cstat-no" title="statement not covered" >    this.head[this.offset] = null;</span>
<span class="cstat-no" title="statement not covered" >    this.offset++;</span>
<span class="cstat-no" title="statement not covered" >    return item;</span>
};
&nbsp;
Queue.prototype.push = <span class="fstat-no" title="function not covered" >function (item) {</span>
<span class="cstat-no" title="statement not covered" >    return this.tail.push(item);</span>
};
&nbsp;
Queue.prototype.forEach = <span class="fstat-no" title="function not covered" >function (fn, thisv) {</span>
<span class="cstat-no" title="statement not covered" >    var array = this.head.slice(this.offset), i, il;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    array.push.apply(array, this.tail);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (thisv) {</span>
<span class="cstat-no" title="statement not covered" >        for (i = 0, il = array.length; i &lt; il; i += 1) {</span>
<span class="cstat-no" title="statement not covered" >            fn.call(thisv, array[i], i, array);</span>
        }
    } else {
<span class="cstat-no" title="statement not covered" >        for (i = 0, il = array.length; i &lt; il; i += 1) {</span>
<span class="cstat-no" title="statement not covered" >            fn(array[i], i, array);</span>
        }
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    return array;</span>
};
&nbsp;
Queue.prototype.getLength = <span class="fstat-no" title="function not covered" >function () {</span>
<span class="cstat-no" title="statement not covered" >    return this.head.length - this.offset + this.tail.length;</span>
};
&nbsp;
Object.defineProperty(Queue.prototype, 'length', {
    get: <span class="fstat-no" title="function not covered" >function () {</span>
<span class="cstat-no" title="statement not covered" >        return this.getLength();</span>
    }
});
&nbsp;
module.exports = Queue;
&nbsp;</pre></td></tr>
</table></pre>

</div>
<div class="footer">
    <div class="meta">Generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Sat Oct 10 2015 19:36:16 GMT+0200 (CEST)</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
