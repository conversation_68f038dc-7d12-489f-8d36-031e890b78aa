<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for lib/utils.js</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="../prettify.css">
    <link rel="stylesheet" href="../base.css">
    <style type='text/css'>
        div.coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class="header high">
    <h1>Code coverage report for <span class="entity">lib/utils.js</span></h1>
    <h2>
        Statements: <span class="metric">100% <small>(30 / 30)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Branches: <span class="metric">100% <small>(10 / 10)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Functions: <span class="metric">100% <small>(4 / 4)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Lines: <span class="metric">100% <small>(30 / 30)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Ignored: <span class="metric"><span class="ignore-none">none</span></span> &nbsp;&nbsp;&nbsp;&nbsp;
    </h2>
    <div class="path"><a href="../index.html">All files</a> &#187; <a href="index.html">lib/</a> &#187; utils.js</div>
</div>
<div class="body">
<pre><table class="coverage">
<tr><td class="line-count">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67</td><td class="line-coverage"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">132</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">132</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">108</span>
<span class="cline-any cline-yes">248</span>
<span class="cline-any cline-yes">248</span>
<span class="cline-any cline-yes">248</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">108</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">33417</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">33417</span>
<span class="cline-any cline-yes">8376</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">25041</span>
<span class="cline-any cline-yes">706</span>
<span class="cline-any cline-yes">706</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6862</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">706</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24335</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">4420</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4420</span>
<span class="cline-any cline-yes">5768</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4420</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">'use strict';
&nbsp;
// hgetall converts its replies to an Object. If the reply is empty, null is returned.
function replyToObject(reply) {
    var obj = {}, j, jl, key, val;
&nbsp;
    if (reply.length === 0 || !Array.isArray(reply)) {
        return null;
    }
&nbsp;
    for (j = 0, jl = reply.length; j &lt; jl; j += 2) {
        key = reply[j].toString('binary');
        val = reply[j + 1];
        obj[key] = val;
    }
&nbsp;
    return obj;
}
&nbsp;
function replyToStrings(reply) {
    var i;
&nbsp;
    if (Buffer.isBuffer(reply)) {
        return reply.toString();
    }
&nbsp;
    if (Array.isArray(reply)) {
        var res = new Array(reply.length);
        for (i = 0; i &lt; reply.length; i++) {
            // Recusivly call the function as slowlog returns deep nested replies
            res[i] = replyToStrings(reply[i]);
        }
        return res;
    }
&nbsp;
    return reply;
}
&nbsp;
function toArray(args) {
    var len = args.length,
        arr = new Array(len), i;
&nbsp;
    for (i = 0; i &lt; len; i += 1) {
        arr[i] = args[i];
    }
&nbsp;
    return arr;
}
&nbsp;
function print (err, reply) {
    if (err) {
        console.log('Error: ' + err);
    } else {
        console.log('Reply: ' + reply);
    }
}
&nbsp;
var redisErrCode = /^([A-Z]+)\s+(.+)$/;
&nbsp;
module.exports = {
    reply_to_strings: replyToStrings,
    reply_to_object: replyToObject,
    to_array: toArray,
    print: print,
    errCode: redisErrCode
};
&nbsp;</pre></td></tr>
</table></pre>

</div>
<div class="footer">
    <div class="meta">Generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Mon Nov 23 2015 23:43:02 GMT+0100 (CET)</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
