{"name": "redis", "version": "2.4.2", "description": "Redis client library", "keywords": ["database", "redis", "transaction", "pipelining", "performance", "queue"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "main": "./index.js", "scripts": {"coveralls": "nyc report --reporter=text-lcov | coveralls", "coverage": "nyc report --reporter=html", "benchmark": "node benchmarks/multi_bench.js", "test": "nyc ./node_modules/.bin/_mocha ./test/*.js ./test/commands/*.js --timeout=8000", "pretest": "optional-dev-dependency hiredis", "posttest": "jshint ."}, "dependencies": {"double-ended-queue": "^2.1.0-0", "redis-commands": "^1.0.1"}, "engines": {"node": ">=0.10.0"}, "devDependencies": {"coveralls": "^2.11.2", "jshint": "^2.8.0", "metrics": "^0.1.9", "mocha": "^2.3.2", "nyc": "^3.2.2", "optional-dev-dependency": "^1.1.0", "tcp-port-used": "^0.1.2", "uuid": "^2.0.1", "win-spawn": "^2.0.0", "bluebird": "^3.0.2"}, "repository": {"type": "git", "url": "git://github.com/NodeRedis/node_redis.git"}, "bugs": {"url": "https://github.com/NodeRedis/node_redis/issues"}, "homepage": "https://github.com/NodeRedis/node_redis", "directories": {"example": "examples", "test": "test"}}