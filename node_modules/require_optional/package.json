{"name": "require_optional", "version": "1.0.1", "description": "Allows you declare optionalPeerDependencies that can be satisfied by the top level module but ignored if they are not.", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/christkv/require_optional.git"}, "keywords": ["optional", "require", "optionalPeerDependencies"], "author": "<PERSON>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/christkv/require_optional/issues"}, "homepage": "https://github.com/christkv/require_optional", "dependencies": {"semver": "^5.1.0", "resolve-from": "^2.0.0"}, "devDependencies": {"bson": "0.4.21", "co": "4.6.0", "es6-promise": "^3.0.2", "mocha": "^2.4.5"}, "peerOptionalDependencies": {"co": ">=5.6.0", "es6-promise": "^3.0.2", "es6-promise2": "^4.0.2", "bson": "0.4.21"}}