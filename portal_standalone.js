#!/usr/bin/env node

const http = require('http');
const fs = require('fs');
const path = require('path');
const querystring = require('querystring');
const url = require('url');

// 简单的MIME类型映射
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.woff': 'font/woff',
    '.woff2': 'font/woff2',
    '.ttf': 'font/ttf',
    '.eot': 'application/vnd.ms-fontobject'
};

// 模拟会话存储
const sessions = new Map();

// 生成会话ID
function generateSessionId() {
    return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
}

// 解析Cookie
function parseCookies(cookieHeader) {
    const cookies = {};
    if (cookieHeader) {
        cookieHeader.split(';').forEach(cookie => {
            const parts = cookie.trim().split('=');
            if (parts.length === 2) {
                cookies[parts[0]] = decodeURIComponent(parts[1]);
            }
        });
    }
    return cookies;
}

// 获取会话
function getSession(req) {
    const cookies = parseCookies(req.headers.cookie);
    const sessionId = cookies.portalUid;
    
    if (sessionId && sessions.has(sessionId)) {
        return sessions.get(sessionId);
    }
    
    return null;
}

// 设置会话
function setSession(res, sessionData) {
    const sessionId = generateSessionId();
    sessions.set(sessionId, sessionData);
    
    // 设置Cookie (10小时过期)
    const expires = new Date(Date.now() + 10 * 60 * 60 * 1000);
    res.setHeader('Set-Cookie', `portalUid=${sessionId}; Path=/; Expires=${expires.toUTCString()}; HttpOnly`);
    
    return sessionId;
}

// 读取文件
function readFile(filePath) {
    try {
        return fs.readFileSync(filePath);
    } catch (error) {
        return null;
    }
}

// 处理静态文件
function serveStaticFile(req, res, filePath) {
    const fullPath = path.join(__dirname, 'public', filePath);
    const content = readFile(fullPath);
    
    if (!content) {
        res.writeHead(404, { 'Content-Type': 'text/plain' });
        res.end('File not found');
        return;
    }
    
    const ext = path.extname(filePath);
    const mimeType = mimeTypes[ext] || 'application/octet-stream';
    
    res.writeHead(200, { 
        'Content-Type': mimeType,
        'Cache-Control': 'public, max-age=86400'
    });
    res.end(content);
}

// 登录页面HTML
function getLoginPage() {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Portal 管理后台 - 登录</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }
        .login-container { background: white; padding: 40px; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); width: 100%; max-width: 400px; }
        .logo { text-align: center; margin-bottom: 30px; }
        .logo h1 { color: #333; margin: 0; font-size: 24px; }
        .logo p { color: #666; margin: 5px 0 0 0; font-size: 14px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; color: #333; font-weight: bold; }
        input[type="text"], input[type="password"] { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box; font-size: 16px; }
        input[type="text"]:focus, input[type="password"]:focus { outline: none; border-color: #667eea; box-shadow: 0 0 5px rgba(102, 126, 234, 0.3); }
        .login-btn { width: 100%; padding: 12px; background: #667eea; color: white; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; transition: background 0.3s; }
        .login-btn:hover { background: #5a6fd8; }
        .login-btn:disabled { background: #ccc; cursor: not-allowed; }
        .error-msg { color: #e74c3c; margin-top: 10px; text-align: center; }
        .success-msg { color: #27ae60; margin-top: 10px; text-align: center; }
        .forgot-password { text-align: center; margin-top: 15px; }
        .forgot-password a { color: #667eea; text-decoration: none; }
        .forgot-password a:hover { text-decoration: underline; }
        .loading { display: none; text-align: center; margin-top: 10px; }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>🏢 Portal 管理后台</h1>
            <p>用户登录</p>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" placeholder="请输入用户名" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" placeholder="请输入密码" value="rongzhilian1819" required>
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">登录</button>
            
            <div class="loading" id="loading">🔄 正在登录...</div>
            <div id="message"></div>
            
            <div class="forgot-password">
                <a href="#" onclick="alert('请联系管理员重置密码')">忘记密码？</a>
            </div>
        </form>
    </div>
    
    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const loading = document.getElementById('loading');
            const message = document.getElementById('message');
            
            if (!username || !password) {
                message.innerHTML = '<div class="error-msg">请输入用户名和密码</div>';
                return;
            }
            
            loginBtn.disabled = true;
            loading.style.display = 'block';
            message.innerHTML = '';
            
            fetch('/login/li', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'username=' + encodeURIComponent(username) + '&password=' + encodeURIComponent(password)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    message.innerHTML = '<div class="success-msg">✅ 登录成功！正在跳转...</div>';
                    setTimeout(() => {
                        window.location.href = '/index';
                    }, 1000);
                } else {
                    message.innerHTML = '<div class="error-msg">❌ ' + (data.message || '登录失败') + '</div>';
                }
            })
            .catch(error => {
                message.innerHTML = '<div class="error-msg">❌ 网络错误，请稍后重试</div>';
            })
            .finally(() => {
                loginBtn.disabled = false;
                loading.style.display = 'none';
            });
        });
        
        // 自动聚焦用户名输入框
        document.getElementById('username').focus();
    </script>
</body>
</html>`;
}

// 首页HTML
function getIndexPage(user) {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Portal 管理后台 - 首页</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background: #f5f5f5; }
        .header { background: #2c3e50; color: white; padding: 15px 20px; display: flex; justify-content: space-between; align-items: center; }
        .header h1 { margin: 0; font-size: 20px; }
        .user-info { display: flex; align-items: center; gap: 15px; }
        .logout-btn { background: #e74c3c; color: white; padding: 8px 15px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; }
        .logout-btn:hover { background: #c0392b; }
        .container { max-width: 1200px; margin: 20px auto; padding: 0 20px; }
        .welcome-card { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .stat-number { font-size: 32px; font-weight: bold; color: #3498db; margin-bottom: 10px; }
        .stat-label { color: #666; font-size: 14px; }
        .menu-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .menu-item { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; cursor: pointer; transition: transform 0.2s; text-decoration: none; color: #333; }
        .menu-item:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.15); }
        .menu-icon { font-size: 32px; margin-bottom: 10px; }
        .menu-title { font-weight: bold; margin-bottom: 5px; }
        .menu-desc { font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏢 Portal 管理后台</h1>
        <div class="user-info">
            <span>👤 ${user.name} (${user.username})</span>
            <span>🏷️ ${user.roles.join(', ')}</span>
            <a href="/login/lo" class="logout-btn">退出登录</a>
        </div>
    </div>
    
    <div class="container">
        <div class="welcome-card">
            <h2>🎉 欢迎回来，${user.name}！</h2>
            <p>当前时间：${new Date().toLocaleString('zh-CN')}</p>
            <p>您的权限级别：${user.permissions.includes('*') ? '超级管理员' : '普通用户'}</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">156</div>
                <div class="stat-label">总用户数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">89</div>
                <div class="stat-label">在线用户</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">1,234</div>
                <div class="stat-label">总订单数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">¥56,789</div>
                <div class="stat-label">今日收入</div>
            </div>
        </div>
        
        <h3>📋 功能菜单</h3>
        <div class="menu-grid">
            <a href="/sysuser" class="menu-item">
                <div class="menu-icon">👥</div>
                <div class="menu-title">用户管理</div>
                <div class="menu-desc">管理系统用户账号</div>
            </a>
            <a href="/customer" class="menu-item">
                <div class="menu-icon">🛍️</div>
                <div class="menu-title">客户管理</div>
                <div class="menu-desc">管理客户信息</div>
            </a>
            <a href="/goods" class="menu-item">
                <div class="menu-icon">📦</div>
                <div class="menu-title">商品管理</div>
                <div class="menu-desc">管理商品信息</div>
            </a>
            <a href="/news" class="menu-item">
                <div class="menu-icon">📰</div>
                <div class="menu-title">新闻管理</div>
                <div class="menu-desc">管理新闻资讯</div>
            </a>
            <a href="/role" class="menu-item">
                <div class="menu-icon">🔐</div>
                <div class="menu-title">角色管理</div>
                <div class="menu-desc">管理用户角色权限</div>
            </a>
            <a href="/department" class="menu-item">
                <div class="menu-icon">🏢</div>
                <div class="menu-title">部门管理</div>
                <div class="menu-desc">管理组织架构</div>
            </a>
        </div>
    </div>
</body>
</html>`;
}

// 创建服务器
const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;
    
    console.log(`${new Date().toISOString()} - ${req.method} ${pathname}`);
    
    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    // 处理静态文件
    if (pathname.startsWith('/plugins/') || pathname.startsWith('/stylesheets/') || 
        pathname.startsWith('/images/') || pathname.startsWith('/module/') ||
        pathname.startsWith('/fonts/') || pathname.startsWith('/fontc/') ||
        pathname === '/favicon.ico') {
        serveStaticFile(req, res, pathname);
        return;
    }
    
    // 登录处理
    if (pathname === '/login/li' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        req.on('end', () => {
            const data = querystring.parse(body);
            const { username, password } = data;
            
            let user, actions;
            
            if (username === 'admin' && password === 'rongzhilian1819') {
                user = {
                    userId: 'admin001',
                    username: 'admin',
                    name: '系统管理员',
                    roles: ['admin', 'superadmin'],
                    permissions: ['*'],
                    accountName: 'admin'
                };
                actions = { actionInfos: [{ accode: 'index' }, { accode: 'sysuserList' }] };
            } else {
                user = {
                    userId: 'testuser123',
                    username: 'testuser',
                    name: '测试用户',
                    roles: ['user'],
                    permissions: ['basic_access'],
                    accountName: '测试账号'
                };
                actions = { actionInfos: [{ accode: 'index' }] };
            }
            
            const sessionData = { sysuser: user, actions: actions };
            setSession(res, sessionData);
            
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                code: 0,
                message: '登录成功',
                data: {
                    token: 'mock_token_' + Date.now(),
                    user: user
                }
            }));
        });
        return;
    }
    
    // 登出处理
    if (pathname === '/login/lo') {
        const cookies = parseCookies(req.headers.cookie);
        const sessionId = cookies.portalUid;
        if (sessionId) {
            sessions.delete(sessionId);
        }
        
        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(getLoginPage());
        return;
    }
    
    // 检查登录状态
    const session = getSession(req);
    
    // 登录页面
    if (pathname === '/' || pathname === '/login') {
        if (session && session.sysuser) {
            // 已登录，重定向到首页
            res.writeHead(302, { 'Location': '/index' });
            res.end();
        } else {
            // 未登录，显示登录页面
            res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
            res.end(getLoginPage());
        }
        return;
    }
    
    // 需要登录的页面
    if (!session || !session.sysuser) {
        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(getLoginPage());
        return;
    }
    
    // 首页
    if (pathname === '/index') {
        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(getIndexPage(session.sysuser));
        return;
    }
    
    // 其他管理页面（简单响应）
    if (pathname.startsWith('/sysuser') || pathname.startsWith('/customer') || 
        pathname.startsWith('/goods') || pathname.startsWith('/news') ||
        pathname.startsWith('/role') || pathname.startsWith('/department')) {
        
        const moduleName = pathname.split('/')[1];
        const moduleTitle = {
            'sysuser': '用户管理',
            'customer': '客户管理', 
            'goods': '商品管理',
            'news': '新闻管理',
            'role': '角色管理',
            'department': '部门管理'
        }[moduleName] || '管理页面';
        
        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(`
<!DOCTYPE html>
<html>
<head>
    <title>${moduleTitle} - Portal 管理后台</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 1px solid #eee; }
        .back-btn { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; }
        .back-btn:hover { background: #2980b9; }
        .info-box { background: #e8f4fd; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 ${moduleTitle}</h1>
            <a href="/index" class="back-btn">← 返回首页</a>
        </div>
        
        <div class="info-box">
            <h3>🚧 功能开发中</h3>
            <p>此模块的完整功能正在开发中。当前显示的是演示页面。</p>
            <p><strong>用户信息：</strong>${session.sysuser.name} (${session.sysuser.username})</p>
            <p><strong>访问权限：</strong>${session.sysuser.permissions.join(', ')}</p>
            <p><strong>当前时间：</strong>${new Date().toLocaleString('zh-CN')}</p>
        </div>
    </div>
</body>
</html>`);
        return;
    }
    
    // 404
    res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
<!DOCTYPE html>
<html>
<head>
    <title>页面未找到 - Portal 管理后台</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background: #f5f5f5; display: flex; align-items: center; justify-content: center; min-height: 100vh; }
        .error-container { text-align: center; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .error-code { font-size: 72px; color: #e74c3c; margin-bottom: 20px; }
        .error-message { font-size: 24px; color: #333; margin-bottom: 20px; }
        .back-btn { background: #3498db; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; font-size: 16px; }
        .back-btn:hover { background: #2980b9; }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-code">404</div>
        <div class="error-message">页面未找到</div>
        <p>您访问的页面不存在或已被移除。</p>
        <a href="/index" class="back-btn">返回首页</a>
    </div>
</body>
</html>`);
});

const PORT = 8088;
server.listen(PORT, '0.0.0.0', () => {
    console.log('🚀 Portal 独立应用程序启动成功！');
    console.log('📍 访问地址: http://localhost:' + PORT);
    console.log('📍 或者访问: http://127.0.0.1:' + PORT);
    console.log('⏰ 启动时间: ' + new Date().toLocaleString('zh-CN'));
    console.log('🔐 管理员账号: admin / rongzhilian1819');
    console.log('👤 测试账号: 任意用户名和密码');
});

server.on('error', (err) => {
    console.error('❌ 服务器错误:', err.message);
});
