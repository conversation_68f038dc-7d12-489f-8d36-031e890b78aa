<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="Content-Type" Content="text/html; charset=utf-8;">
    <title>新闻详情</title>
    <link href="../plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link href="../stylesheets/common.css" rel="stylesheet" type="text/css">
    <link href="../stylesheets/template2/stylesheets/uec-base.css" rel="stylesheet" type="text/css">
    <link href="../stylesheets/template2/stylesheets/uec-main.css" rel="stylesheet" type="text/css">
    <script type="text/javascript" src="../plugins/jquery/jquery-1.11.3.js"></script>
    <script type="text/javascript" src="../plugins/jeromeetienne-jquery/src/jquery.qrcode.js"></script>
    <script type="text/javascript" src="../plugins/jeromeetienne-jquery/src/qrcode.js"></script>
</head>
<body>
<header class="uec-header">
    <div class="uec-head">
        <div class="uec-bar uec-icon icon-shousuo"></div>
        <h2 class="uec-logo">
            <img src="../stylesheets/template2/images/logo.png" class="uec-h-logo">
            <!--<a class="uec-h-logo" href="javascript:;"></a>-->
        </h2>
        <nav class="uec-h-nav uec-nav-js">
            <ul>
                <li class="uec-h-li on"><a class="uec-h-a" href="javascript:;">首页</a></li>
                <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">产品创新</a></li>
                <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">解决方案</a></li>
                <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">行业</a></li>
                <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">商城</a></li>
                <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">新闻中心</a></li>
                <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">投资者关系</a></li>
                <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">关于我们</a></li>
            </ul>
        </nav>
        <div class="uec-right">
            <div class="uec-search">
                <i class="uec-icon icon-fangdajing"></i>
                <input class="uec-input" type="text">
            </div>
            <div class="uec-h-r-div1">
                <a class="uec-h-r-a" href="javascript:;">EN</a>
                <span class="uec-h-r-sp">|</span>
                <a class="uec-h-r-a uec-icon icon-fangdajing" href="javascript:;"></a>
            </div>
            <div class="uec-h-r-div">
                <a class="uec-h-r-a" href="javascript:;">登录</a>
                <span class="uec-h-r-sp">|</span>
                <a class="uec-h-r-a" href="javascript:;">注册</a>
            </div>
        </div>
    </div>
    <div class="uec-h-nCont">
        <div class="uec-h-nCline"></div>
        <nav class="uec-nC-subnav">
            <ul class="uec-nC-ul">
                <li class="uec-subn-li"><i class="uec-icon icon-yunjisuan"></i><span>企业数字化</span></li>
                <li class="uec-subn-li"><i class="uec-icon icon-jichugoujia"></i><span>基础架构</span></li>
                <li class="uec-subn-li"><i class="uec-icon icon-wulianwang"></i><span>物联网</span></li>
                <li class="uec-subn-li"><i class="uec-icon icon-dashujuchanpinzhongxin"></i><span>数据产品</span></li>
                <li class="uec-subn-li"><i class="uec-icon icon-jiyin"></i><span>生物信息</span></li>
            </ul>
        </nav>
        <ul class="uec-nC-ul">
            <li class="uec-sbsb-li">
                <ul>
                    <li><a href="#">云桥</a></li>
                    <li><a href="#">云盘</a></li>
                    <li><a href="#">档案系统</a></li>
                    <li><a href="#">电商平台</a></li>
                    <li><a href="#">iNews</a></li>
                    <li><a href="#">云豆</a></li>
                    <li><a href="#">云客服</a></li>
                </ul>
            </li>
            <li class="uec-sbsb-li">
                <ul>
                    <li><a href="#">云资源管理</a></li>
                    <li><a href="#">APEX监控管理</a></li>
                    <li><a href="#">APEX运维管理</a></li>
                    <li><a href="#">PaaS平台</a></li>
                    <li><a href="#">存储平台</a></li>
                </ul>
            </li>
            <li class="uec-sbsb-li">
                <ul>
                    <li><a href="#">车辆相关</a></li>
                    <li><a href="#">边缘计算</a></li>
                    <li><a href="#">实时数据库</a></li>
                    <li><a href="#">物联网平台</a></li>
                    <li><a href="#">农业物联网</a></li>
                    <li><a href="#">工业物联网</a></li>
                    <li><a href="#">水联网</a></li>
                </ul>
            </li>
            <li class="uec-sbsb-li">
                <ul>
                    <li><a href="#">风报</a></li>
                    <li><a href="#">NLP引擎</a></li>
                    <li><a href="#">大数据垂直应用一体机</a></li>
                    <li><a href="#">外部数据指数</a></li>
                    <li><a href="#">评分卡模型</a></li>
                    <li><a href="#">反欺诈模型</a></li>
                    <li><a href="#">蚂蚁多为检索</a></li>
                </ul>
            </li>
            <li class="uec-sbsb-li">
                <ul>
                    <li><a href="#">数据平台</a></li>
                    <li><a href="#">Helicube</a></li>
                    <li><a href="#">Balsa/Elsa</a></li>
                    <li><a href="#">Database.io</a></li>
                    <li><a href="#">基因浏览器</a></li>
                </ul>
            </li>
        </ul>
    </div>
    <div class="uec-h-nCont"></div>
    <div class="uec-h-nCont"></div>
    <div class="uec-h-nCont"></div>
    <div class="uec-h-nCont"></div>
    <div class="uec-h-nCont">
        <div class="uec-h-nCline"></div>
        <ul class="uec-nC-ul">
            <li class="uec-sbsb-li"><a href="#">公告信息</a></li>
            <li class="uec-sbsb-li"><a href="#">定期报告</a></li>
            <li class="uec-sbsb-li"><a href="#">公司治理</a></li>
            <li class="uec-sbsb-li"><a href="#">投资者服务</a></li>
        </ul>
    </div>
    <div class="uec-h-nCont"></div>
</header>
<section class="uec-section">
    <div class="uec-solution-banner o-overhidden">
        <img class="uec-s-b-img" src="../stylesheets/template2/images/solution.jpg">
    </div>
    <div class="uec-eidt-bg"></div>
    <div class="uec-section-btn1">
        <a href="javascript:;" class="uec-icon icon-shanchu"></a>
        <a href="javascript:;" class="uec-icon icon-xiayi"></a>
        <a href="javascript:;" class="uec-icon icon-shangyi"></a>
        <a href="javascript:;" class="uec-icon icon-bianji"></a>
        <a href="javascript:;" class="uec-icon icon-xianshi"></a>
    </div>
</section>
<section class="uec-nd-tab">
    <div class="uec-s-tab">
        <ul class="uec-head">
            <li><a href="javascript:;" class="uec-s-t-a">首页</a></li>
            <li>></li>
            <li><a href="javascript:;" class="uec-s-t-a">新闻中心</a></li>
            <li>></li>
            <li>新闻详情</li>
        </ul>
    </div>
</section>
<section>
    <div class="uec-head">
        <div class="uec-nd-l">
            <div class="uec-nd-div">
                <h2 class="uec-nd-h2 uec-head">强强联合|荣之联与The Floow共创车联网新时代</h2>
                <div class="uec-nd-suite clear">
                    <div class="uec-nd-data uec-nd-fanart">发布于 2017-05-17<span>新闻来源:ADS</span></div>
                    <div class="uec-n-share">
                        <a href="javascript:;" onclick="uecShare(this)" class="uec-share-btn"><i class="uec-icon icon-icon"></i>分享</a>
                        <ul class="uec-n-link uec-link-js">
                            <li><a class="uec-qZ-js uec-icon icon-iconfontkongjian" onclick="uecShareInfo(this,'qz')" href="javascript:;"></a></li>
                            <li><a id="uec-in-js" class="uec-in-js uec-icon icon-in" onclick="uecShareInfo(this,'in')" href="javascript:;"></a></li>
                            <li><a class="uec-weibo-js uec-icon icon-unie61d" onclick="uecShareInfo(this,'weibo')" href="javascript:;"></a></li>
                            <li><a class="uec-weixin-js uec-icon icon-weixin1" onclick='uecEwmOpen(this)' href="javascript:;"></a>
                                <div class="uec-ewm">
                                    <i class="uec-ewm-close" onclick="uecEwmClose(this)">×</i>
                                    <div class="uec-ewm-js"></div>
                                    <div class="uec-ewm-text">扫二维码分享</div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <script type="text/javascript">
                    function uecShareInfo(elm, type){
                    	var $el = $(elm), openUrl = '';
                    	var options = {
		                    url: window.location.href,
		                    title: $('h2.uec-nd-h2').html(),
		                    content: $('h2.uec-nd-h2').html(),
		                    pic: ''
                    	};
	                    //分享地址
	                    var qzone = 'http://sns.qzone.qq.com/cgi-bin/qzshare/cgi_qzshare_onekey?url={url}&title={title}&pics={pic}&summary={content}';
	                    var sina = 'http://service.weibo.com/share/share.php?url={url}&title={title}&pic={pic}&searchPic=false';
	                    var tqq = 'http://share.v.t.qq.com/index.php?c=share&a=index&url={url}&title={title}&appkey=801cf76d3cfc44ada52ec13114e84a96';
	                    var douban = 'http://www.douban.com/share/service?href={url}&name={title}&text={content}&image={pic}';
	                    var linked = 'http://www.linkedin.com/shareArticle?mini=true&url={enurl}&title={entitle}';
                    	switch (type){
                            case 'qz':
                            	openUrl = qzone;
                            	break;
		                    case 'weibo':
			                    openUrl = sina;
			                    break;
		                    case 'in':
			                    openUrl = linked;
			                    break;
                        }
                        if(openUrl){
	                        window.open(replaceAPI(openUrl,options));
                        }
                    }

                    function replaceAPI (api,options) {
	                    api = api.replace('{url}', encodeURIComponent(options.url));
	                    api = api.replace('{enurl}', encodeURIComponent(options.url));
	                    api = api.replace('{title}', options.title);
	                    api = api.replace('{entitle}', encodeURIComponent(options.title));
	                    api = api.replace('{content}', options.content);
	                    api = api.replace('{pic}', options.pic);
	                    return api;
                    }

                    function uecEwmOpen(elm){
	                    $('.uec-ewm').show().find('.uec-ewm-js').html('');
	                    $('.uec-ewm-js').qrcode({width:100,height:100, text: window.location.href});
                    };
                    function uecEwmClose(elm) {
	                    $('.uec-ewm').hide().find('.uec-ewm-js').html('');
                    };
                    function uecShare(elm){
                        var $this=$(elm);
                        if(!$this.hasClass('on')){
	                        $this.addClass('on');
                            $this.next('.uec-link-js').stop(true,false).slideDown();
                        }else{
	                        $this.removeClass('on');
	                        uecEwmClose();
	                        $this.next('.uec-link-js').stop(true,false).slideUp();
                        }
                    };
                </script>
            </div>
            <img src="../stylesheets/template2/images/news-detail-img1.jpg" class="uec-nd-img">
            <div class="uec-nd-p">日前，国内前沿的信息技术公司北京荣之联科技股份有限公司（以下简称“荣之联”, 002642.sz）与全球前沿的动力管理公司伊顿在上海隆重举行“伊带E路
                战略合作协议签约仪式。荣之联战略联盟部总经理张雷、伊顿电能质量伊顿品牌及数据中心解决方案业务大中华区总经理曲颖(Lisa Kuk) 出席了签约仪式，此次
                协议的签订标志着双方长期战略合作发展关系的正式确立。
                通过本次合作，今后双方将共同探索适合中国数据中心基地型项目的建设方案和数据中心降耗节能、便利部署、模块化设计与建设，以及贴近客户诉求的定制化服务模式，共同推动中国数据中心、生物云、物联网产业绿色节能高效化发展，创造更大的商业价值和社会价值。 </div>
        </div>
        <div class="uec-nd-r">
            <h3 class="uec-nd-h3">资讯中心<a class="uec-nd-r-a" href="javascript:;">更多>></a></h3>
            <ul class="uec-nd-ul">
                <li class="uec-nd-li on">
                    <a href="javascript:;">
                        <div class="uec-n-time">
                            <span class="uec-n-span">5</span>201703
                        </div>
                        <div class="uec-nd-r-div">
                            <h4 class="uec-nd-h4">强强联合|荣之联与The Floow共创车联网新时代</h4>
                            <p class="uec-nd-r-p">日前，荣之联车联网平台-车网互联的合作伙伴The Floow完成了1300万英镑股权投资交易，此交易由复星集团领投，车网互联总公司荣之联和Direct Line Group跟投。据悉，此轮融资将用于在大众市场推广保险行业的最佳预测分析。</p>
                        </div>
                    </a>
                </li>
                <li class="uec-nd-li">
                    <a href="javascript:;">
                        <div class="uec-n-time">
                            <span class="uec-n-span">5</span>201703
                        </div>
                        <div class="uec-nd-r-div">
                            <h4 class="uec-nd-h4">强强联合|荣之联与The Floow共创车联网新时代</h4>
                            <p class="uec-nd-r-p">日前，荣之联车联网平台-车网互联的合作伙伴The Floow完成了1300万英镑股权投资交易，此交易由复星集团领投，车网互联总公司荣之联和Direct Line Group跟投。据悉，此轮融资将用于在大众市场推广保险行业的最佳预测分析。</p>
                        </div>
                    </a>
                </li>
                <li class="uec-nd-li">
                    <a href="javascript:;">
                        <div class="uec-n-time">
                            <span class="uec-n-span">5</span>201703
                        </div>
                        <div class="uec-nd-r-div">
                            <h4 class="uec-nd-h4">强强联合|荣之联与The Floow共创车联网新时代</h4>
                            <p class="uec-nd-r-p">日前，荣之联车联网平台-车网互联的合作伙伴The Floow完成了1300万英镑股权投资交易，此交易由复星集团领投，车网互联总公司荣之联和Direct Line Group跟投。据悉，此轮融资将用于在大众市场推广保险行业的最佳预测分析。</p>
                        </div>
                    </a>
                </li>
                <li class="uec-nd-li">
                    <a href="javascript:;">
                        <div class="uec-n-time">
                            <span class="uec-n-span">5</span>201703
                        </div>
                        <div class="uec-nd-r-div">
                            <h4 class="uec-nd-h4">强强联合|荣之联与The Floow共创车联网新时代</h4>
                            <p class="uec-nd-r-p">日前，荣之联车联网平台-车网互联的合作伙伴The Floow完成了1300万英镑股权投资交易，此交易由复星集团领投，车网互联总公司荣之联和Direct Line Group跟投。据悉，此轮融资将用于在大众市场推广保险行业的最佳预测分析。</p>
                        </div>
                    </a>
                </li>
                <li class="uec-nd-li">
                    <a href="javascript:;">
                        <div class="uec-n-time">
                            <span class="uec-n-span">5</span>201703
                        </div>
                        <div class="uec-nd-r-div">
                            <h4 class="uec-nd-h4">强强联合|荣之联与The Floow共创车联网新时代</h4>
                            <p class="uec-nd-r-p">日前，荣之联车联网平台-车网互联的合作伙伴The Floow完成了1300万英镑股权投资交易，此交易由复星集团领投，车网互联总公司荣之联和Direct Line Group跟投。据悉，此轮融资将用于在大众市场推广保险行业的最佳预测分析。</p>
                        </div>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</section>
<footer class="uec-footer clear">
    <div class="uec-f-upline">
        <div class="uec-f-line1"></div>
        <div class="uec-f-line2"></div>
    </div>
    <div class="uec-foot clear">
        <dl class="uec-f-dl">
            <dt class="uec-f-dt">快速链接</dt>
            <dd class="uec-f-dd"><a href="javascript:;">车网互联</a></dd>
            <dd class="uec-f-dd"><a href="javascript:;">泰和佳通</a></dd>
            <dd class="uec-f-dd"><a href="javascript:;">荣之联加速器</a></dd>
            <dd class="uec-f-dd"><a href="javascript:;">子公司网站链接</a></dd>
        </dl>
        <dl class="uec-f-dl">
            <dt class="uec-f-dt">新闻中心</dt>
            <dd class="uec-f-dd"><a href="javascript:;">新闻动态</a></dd>
            <dd class="uec-f-dd"><a href="javascript:;">新闻动态</a></dd>
        </dl>
        <dl class="uec-f-dl">
            <dt class="uec-f-dt">加入我们</dt>
            <dd class="uec-f-dd"><a href="javascript:;">用人之道</a></dd>
            <dd class="uec-f-dd"><a href="javascript:;">社会招聘</a></dd>
            <dd class="uec-f-dd"><a href="javascript:;">企业招聘</a></dd>
        </dl>
        <div class="uec-xian"></div>
        <div class="uec-f-r">
            <ul class="uec-f-r-ul">
                <li class="uec-f-r-li1"></li>
                <li class="uec-f-r-li2"></li>
            </ul>
            <div class="uec-f-r-d"></div>
            <p class="uec-f-r-p">官方微信</p>
        </div>
        <dl class="uec-f-dl1">
            <dt class="uec-f-dt">联系我们</dt>
            <dd class="uec-f-dd">电话：010-62602000</dd>
            <dd class="uec-f-dd">传真：010-62602100</dd>
            <dd class="uec-f-dd">邮箱：<EMAIL></dd>
            <dd class="uec-f-dd">北京市朝阳区酒仙桥北路甲10号院106楼荣联数讯大厦</dd>
        </dl>
        <ul class="uec-f-m">
            <li><a href="javascript:;">网站地图</a><span>|</span></li>
            <li><a href="javascript:;">举报信息</a><span>|</span></li>
            <li><a href="javascript:;">法律保护</a></li>
        </ul>
    </div>
    <ul class="uec-font1">
        <li>CopyRight©2015-2016</li>
        <li>北京荣之联科技股份有限公司 版权所有</li>
        <li class="uec-f-bot">京ICP备16042301号</li>
    </ul>
</footer>
</body>
</html>