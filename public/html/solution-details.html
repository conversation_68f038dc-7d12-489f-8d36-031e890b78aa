<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="Content-Type" Content="text/html; charset=utf-8;">
    <title>解决方案详情页</title>
    <link href="../plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link href="../stylesheets/template2/stylesheets/uec-base.css" rel="stylesheet" type="text/css">
    <link href="../stylesheets/template2/stylesheets/uec-main.css" rel="stylesheet" type="text/css">
</head>
<body>
    <header class="uec-header">
        <div class="uec-head">
            <div class="uec-bar uec-icon icon-shousuo"></div>
            <h2 class="uec-logo">
                <img src="../stylesheets/template2/images/logo.png" class="uec-h-logo">
                <!--<a class="uec-h-logo" href="javascript:;"></a>-->
            </h2>
            <nav class="uec-h-nav uec-nav-js">
                <ul>
                    <li class="uec-h-li on"><a class="uec-h-a" href="javascript:;">首页</a></li>
                    <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">产品创新</a></li>
                    <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">解决方案</a></li>
                    <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">行业</a></li>
                    <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">商城</a></li>
                    <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">新闻中心</a></li>
                    <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">投资者关系</a></li>
                    <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">关于我们</a></li>
                </ul>
            </nav>
            <div class="uec-right">
                <div class="uec-search">
                    <i class="uec-icon icon-fangdajing"></i>
                    <input class="uec-input" type="text">
                </div>
                <div class="uec-h-r-div1">
                    <a class="uec-h-r-a" href="javascript:;">EN</a>
                    <span class="uec-h-r-sp">|</span>
                    <a class="uec-h-r-a uec-icon icon-fangdajing" href="javascript:;"></a>
                </div>
                <div class="uec-h-r-div">
                    <a class="uec-h-r-a" href="javascript:;">登录</a>
                    <span class="uec-h-r-sp">|</span>
                    <a class="uec-h-r-a" href="javascript:;">注册</a>
                </div>
            </div>
        </div>
        <div class="uec-h-nCont">
            <div class="uec-h-nCline"></div>
            <nav class="uec-nC-subnav">
                <ul class="uec-nC-ul">
                    <li class="uec-subn-li"><i class="uec-icon icon-yunjisuan"></i><span>企业数字化</span></li>
                    <li class="uec-subn-li"><i class="uec-icon icon-jichugoujia"></i><span>基础架构</span></li>
                    <li class="uec-subn-li"><i class="uec-icon icon-wulianwang"></i><span>物联网</span></li>
                    <li class="uec-subn-li"><i class="uec-icon icon-dashujuchanpinzhongxin"></i><span>数据产品</span></li>
                    <li class="uec-subn-li"><i class="uec-icon icon-jiyin"></i><span>生物信息</span></li>
                </ul>
            </nav>
            <ul class="uec-nC-ul">
                <li class="uec-sbsb-li">
                    <ul>
                        <li><a href="#">云桥</a></li>
                        <li><a href="#">云盘</a></li>
                        <li><a href="#">档案系统</a></li>
                        <li><a href="#">电商平台</a></li>
                        <li><a href="#">iNews</a></li>
                        <li><a href="#">云豆</a></li>
                        <li><a href="#">云客服</a></li>
                    </ul>
                </li>
                <li class="uec-sbsb-li">
                    <ul>
                        <li><a href="#">云资源管理</a></li>
                        <li><a href="#">APEX监控管理</a></li>
                        <li><a href="#">APEX运维管理</a></li>
                        <li><a href="#">PaaS平台</a></li>
                        <li><a href="#">存储平台</a></li>
                    </ul>
                </li>
                <li class="uec-sbsb-li">
                    <ul>
                        <li><a href="#">车辆相关</a></li>
                        <li><a href="#">边缘计算</a></li>
                        <li><a href="#">实时数据库</a></li>
                        <li><a href="#">物联网平台</a></li>
                        <li><a href="#">农业物联网</a></li>
                        <li><a href="#">工业物联网</a></li>
                        <li><a href="#">水联网</a></li>
                    </ul>
                </li>
                <li class="uec-sbsb-li">
                    <ul>
                        <li><a href="#">风报</a></li>
                        <li><a href="#">NLP引擎</a></li>
                        <li><a href="#">大数据垂直应用一体机</a></li>
                        <li><a href="#">外部数据指数</a></li>
                        <li><a href="#">评分卡模型</a></li>
                        <li><a href="#">反欺诈模型</a></li>
                        <li><a href="#">蚂蚁多为检索</a></li>
                    </ul>
                </li>
                <li class="uec-sbsb-li">
                    <ul>
                        <li><a href="#">数据平台</a></li>
                        <li><a href="#">Helicube</a></li>
                        <li><a href="#">Balsa/Elsa</a></li>
                        <li><a href="#">Database.io</a></li>
                        <li><a href="#">基因浏览器</a></li>
                    </ul>
                </li>
            </ul>
        </div>
        <div class="uec-h-nCont"></div>
        <div class="uec-h-nCont"></div>
        <div class="uec-h-nCont"></div>
        <div class="uec-h-nCont"></div>
        <div class="uec-h-nCont">
            <div class="uec-h-nCline"></div>
            <ul class="uec-nC-ul">
                <li class="uec-sbsb-li"><a href="#">公告信息</a></li>
                <li class="uec-sbsb-li"><a href="#">定期报告</a></li>
                <li class="uec-sbsb-li"><a href="#">公司治理</a></li>
                <li class="uec-sbsb-li"><a href="#">投资者服务</a></li>
            </ul>
        </div>
        <div class="uec-h-nCont"></div>
    </header>
    <section class="uec-section">
        <div class="uec-solution-banner">
            <img class="uec-s-b-img" src="../stylesheets/template2/images/solution.jpg">
        </div>
        <div class="uec-section-btn1">
            <a href="javascript:;" class="uec-icon icon-shanchu"></a>
            <a href="javascript:;" class="uec-icon icon-xiayi"></a>
            <a href="javascript:;" class="uec-icon icon-shangyi"></a>
            <a href="javascript:;" class="uec-icon icon-bianji"></a>
            <a href="javascript:;" class="uec-icon icon-xianshi"></a>
        </div>
    </section>
        <section class="uec-solu-details">
            <div class="uec-s-tab">
                <ul class="uec-head">
                    <li><a href="javascript:;" class="uec-s-t-a">首页</a></li>
                    <li>></li>
                    <li>解决方案</li>
                    <li>></li>
                    <li>生物云PaaS管理平台</li>
                </ul>
                <h2 class="uec-head uec-solu-dt-h2">生物云PaaS管理平台解决方案</h2>
            </div>
            <div class="uec-ind-tab">
                <div class="uec-ind-list">
                    <div class="uec-s-t-i"><i class="uec-icon icon-fangkuai"></i></div>
                    <div class="uec-s-tab-css">
                        <div class="uec-s-tab1 uec-s-tab-js">
                            <ul class="uec-s-ul-js on">
                                <li><a href="javascript:;">IT服务</a></li><span>|</span>
                                <li><a href="javascript:;">云计算</a></li><span>|</span>
                                <li><a href="javascript:;">云计算</a></li><span>|</span>
                                <li><a href="javascript:;">云计算</a></li><span>|</span>
                                <li><a href="javascript:;">云计算</a></li><span>|</span>
                                <li><a href="javascript:;">云计算</a></li><span>|</span>
                                <li><a href="javascript:;">大数据</a></li><span>|</span>
                            </ul>
                            <ul class="uec-s-ul-js">
                                <li><a href="javascript:;">大数据</a></li><span>|</span>
                                <li><a href="javascript:;">大数据</a></li><span>|</span>
                                <li><a href="javascript:;">大数据</a></li><span>|</span>
                                <li><a href="javascript:;">大数据</a></li><span>|</span>
                                <li><a href="javascript:;">大数据</a></li><span>|</span>
                                <li><a href="javascript:;">大数据</a></li><span>|</span>
                                <li><a href="javascript:;">大数据</a></li><span>|</span>
                            </ul>
                            <ul class="uec-s-ul-js">
                                <li><a href="javascript:;">大数据</a></li><span>|</span>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="uec-s-t-r">
                    <i class="uec-icon icon-sanjiao"></i>
                    <i class="uec-icon icon-right"></i>
                </div>
            </div>
        </section>
            <section class="uec-section ">
                <div class="uec-head">
                    <h3 class="uec-sd-h3"><span></span><em>产品概述</em><b>PRODUCT DESCRIPTION</b></h3>
                    <p class="uec-sd-p">
                        荣之联PaaS管理平台是一个自主研发、具有自主知识产权的面向应用的快速开发、部署以及运维管理的云计算平台。该平台以Docker容器技术为应用载体，基于广泛应用并被大规模实践验证过的开源项目Kubernetes为调度核心，为应用的快速开发部署、弹性伸缩、高可用、高扩展、高性能、高安全的运维管理平台。</p>
                    <p class="uec-sd-p">
                        平台将服务于两类主要的用户，一类是PaaS管理平台的运维管理人员，通过平台管理门户管理SaaS应用对资源的使用许可及限额等、监控各类计算资源、PaaS自身的健康状况以及运行在其上的各类SaaS应用的资源使用及健康状况， 可以大大地降低运维管理成本，提高管理效率以及为应用的高稳定、高可靠性等提供平台级的保障；另一类用户是各个SaaS服务的管理员，通过应用管理门户提交部署SaaS应用并监控自有应用的健康状况，为应用快速开发与发布，持续集成以及应用的安全可靠性提供保障。
                    </p>
                </div>
                <!--<div class="uec-section-btn">-->
                    <!--<a href="javascript:;" class="uec-icon icon-shanchu"></a>-->
                    <!--<a href="javascript:;" class="uec-icon icon-xiayi"></a>-->
                    <!--<a href="javascript:;" class="uec-icon icon-shangyi"></a>-->
                    <!--<a href="javascript:;" class="uec-icon icon-bianji"></a>-->
                    <!--<a href="javascript:;" class="uec-icon icon-xianshi"></a>-->
                <!--</div>-->
            </section>
            <section class="uec-section">
                <div class="uec-head">
                    <h3 class="uec-sd-h3"><span></span><em>产品功能</em><b>PRODUCT FEATURES</b></h3>
                    <ul class="uec-sd-ul o-overhidden">
                        <li class="uec-sd-li"><i class="uec-icon icon-yingyong"></i><em class="uec-sd-em">应用管理</em><small class="uec-sd-sm">基于微服务架构的服务编排及发布管理</small></li>
                        <li class="uec-sd-li"><i class="uec-icon icon-rongliangguanli"></i><em class="uec-sd-em">容量管理</em><small class="uec-sd-sm">将用户下的所有容器进行统一管理</small></li>
                        <li class="uec-sd-li"><i class="uec-icon icon-ziyuanguanli"></i><em class="uec-sd-em">资源池管理</em><small class="uec-sd-sm">为平台的工作负载节点，提供资源，资源池即主机的集合。</small></li>
                        <li class="uec-sd-li"><i class="uec-icon icon-hexinzhibiao"></i><em class="uec-sd-em">核心功能区</em><small class="uec-sd-sm">Node节点之间建立路由、弹性伸缩、故障检测、负载均衡、
                            容器编排、容错自愈、服务发现、网络管理和存储管理</small></li>
                        <li class="uec-sd-li"><i class="uec-icon icon-yonghuguanli"></i><em class="uec-sd-em">租户管理</em><small class="uec-sd-sm">平台的服务对象</small></li>
                        <li class="uec-sd-li"><i class="uec-icon icon-icon-yxj-public"></i><em class="uec-sd-em">公共服务</em><small class="uec-sd-sm">公共服务包括mysql（虚机版和容器版），mongodb
                            （虚机版和容器版），redis等。</small></li>
                    </ul>
                </div>
                <!--<div class="uec-section-btn">-->
                    <!--<a href="javascript:;" class="uec-icon icon-shanchu"></a>-->
                    <!--<a href="javascript:;" class="uec-icon icon-xiayi"></a>-->
                    <!--<a href="javascript:;" class="uec-icon icon-shangyi"></a>-->
                    <!--<a href="javascript:;" class="uec-icon icon-bianji"></a>-->
                    <!--<a href="javascript:;" class="uec-icon icon-xianshi"></a>-->
                <!--</div>-->
            </section>
            <section class="uec-section">
                <div class="uec-head">
                    <h3 class="uec-sd-h3"><span></span><em>产品优势/特点</em><b>PRODUCT Advantages / Features</b></h3>
                    <h4 class="uec-sd-h4">支持通用的IaaS平台</h4>
                    <p class="uec-sd-p">荣之联PaaS管理平台总体设计是以容器技术为核心，基于基础架构构建主机资源池，可以来自虚机，物理机甚至是裸机，虚机可以自有提供也可以来自于第三方云平台比如AWS、Azure等公有云平台。基础架构资源池同时提供可插拔的网络和硬盘资源。</p>
                    <div class="uec-sd-img1">
                        <img src="../stylesheets/template2/images/solution-details-img1.jpg">
                    </div>
                </div>
                <div class="uec-section-btn">
                    <a href="javascript:;" class="uec-icon icon-shanchu"></a>
                    <a href="javascript:;" class="uec-icon icon-xiayi"></a>
                    <a href="javascript:;" class="uec-icon icon-shangyi"></a>
                    <a href="javascript:;" class="uec-icon icon-bianji"></a>
                    <a href="javascript:;" class="uec-icon icon-xianshi"></a>
                    </div>
            </section>
            <section class="uec-section">
                <div class="uec-head">
                    <h3 class="uec-sd-h3"><span></span><em>系统架构</em><b>system structure</b></h3>
                    <div class="uec-sd-img2">
                        <img src="../stylesheets/template2/images/solution-details-img2.jpg">
                    </div>
                </div>
                <div class="uec-section-btn">
                    <a href="javascript:;" class="uec-icon icon-shanchu"></a>
                    <a href="javascript:;" class="uec-icon icon-xiayi"></a>
                    <a href="javascript:;" class="uec-icon icon-shangyi"></a>
                    <a href="javascript:;" class="uec-icon icon-bianji"></a>
                    <a href="javascript:;" class="uec-icon icon-xianshi"></a>
                </div>
            </section>
            <section class="uec-section">
                <div class="uec-head">
                    <h3 class="uec-sd-h3"><span></span><em>相关案例</em><b>Related cases</b></h3>
                    <ul class="uec-rc-ul">
                        <li class="uec-related-case"><a href="javascript:;"><img src="../stylesheets/template2/images/case_logo01.jpg"></a></li>
                        <li class="uec-related-case"><a href="javascript:;"><img src="../stylesheets/template2/images/case_logo02.jpg"></a></li>
                        <li class="uec-related-case"><a href="javascript:;"><img src="../stylesheets/template2/images/case_logo03.jpg"></a></li>
                        <li class="uec-related-case"><a href="javascript:;"><img src="../stylesheets/template2/images/case_logo04.jpg"></a></li>
                        <li class="uec-related-case"><a href="javascript:;"><img src="../stylesheets/template2/images/case_logo05.jpg"></a></li>
                        <li class="uec-related-case"><a href="javascript:;"><img src="../stylesheets/template2/images/case_logo06.jpg"></a></li>
                        <li class="uec-related-case"><a href="javascript:;"><img src="../stylesheets/template2/images/case_logo07.jpg"></a></li>
                    </ul>
                </div>
                <div class="uec-section-btn">
                    <a href="javascript:;" class="uec-icon icon-shanchu"></a>
                    <a href="javascript:;" class="uec-icon icon-xiayi"></a>
                    <a href="javascript:;" class="uec-icon icon-shangyi"></a>
                    <a href="javascript:;" class="uec-icon icon-bianji"></a>
                    <a href="javascript:;" class="uec-icon icon-xianshi"></a>
                </div>
            </section>
    <footer class="uec-footer clear">
        <div class="uec-f-upline">
            <div class="uec-f-line1"></div>
            <div class="uec-f-line2"></div>
        </div>
        <div class="uec-foot clear">
            <dl class="uec-f-dl">
                <dt class="uec-f-dt">快速链接</dt>
                <dd class="uec-f-dd"><a href="javascript:;">车网互联</a></dd>
                <dd class="uec-f-dd"><a href="javascript:;">泰和佳通</a></dd>
                <dd class="uec-f-dd"><a href="javascript:;">荣之联加速器</a></dd>
                <dd class="uec-f-dd"><a href="javascript:;">子公司网站链接</a></dd>
            </dl>
            <dl class="uec-f-dl">
                <dt class="uec-f-dt">新闻中心</dt>
                <dd class="uec-f-dd"><a href="javascript:;">新闻动态</a></dd>
                <dd class="uec-f-dd"><a href="javascript:;">新闻动态</a></dd>
            </dl>
            <dl class="uec-f-dl">
                <dt class="uec-f-dt">加入我们</dt>
                <dd class="uec-f-dd"><a href="javascript:;">用人之道</a></dd>
                <dd class="uec-f-dd"><a href="javascript:;">社会招聘</a></dd>
                <dd class="uec-f-dd"><a href="javascript:;">企业招聘</a></dd>
            </dl>
            <div class="uec-xian"></div>
            <div class="uec-f-r">
                <ul class="uec-f-r-ul">
                    <li class="uec-f-r-li1"></li>
                    <li class="uec-f-r-li2"></li>
                </ul>
                <div class="uec-f-r-d"></div>
                <p class="uec-f-r-p">官方微信</p>
            </div>
            <dl class="uec-f-dl1">
                <dt class="uec-f-dt">联系我们</dt>
                <dd class="uec-f-dd">电话：010-62602000</dd>
                <dd class="uec-f-dd">传真：010-62602100</dd>
                <dd class="uec-f-dd">邮箱：<EMAIL></dd>
                <dd class="uec-f-dd">北京市朝阳区酒仙桥北路甲10号院106楼荣联数讯大厦</dd>
            </dl>
            <ul class="uec-f-m">
                <li><a href="javascript:;">网站地图</a><span>|</span></li>
                <li><a href="javascript:;">举报信息</a><span>|</span></li>
                <li><a href="javascript:;">法律保护</a></li>
            </ul>
        </div>
        <ul class="uec-font1">
            <li>CopyRight©2015-2016</li>
            <li>北京荣之联科技股份有限公司 版权所有</li>
            <li class="uec-f-bot">京ICP备16042301号</li>
        </ul>
    </footer>
</body>
</html>