<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="Content-Type" Content="text/html; charset=utf-8;">
    <title>解决方案</title>
    <link href="../plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link href="../stylesheets/common.css" rel="stylesheet" type="text/css">
    <link href="../stylesheets/template2/stylesheets/uec-base.css" rel="stylesheet" type="text/css">
    <link href="../stylesheets/template2/stylesheets/uec-main.css" rel="stylesheet" type="text/css">
    <script type="text/javascript" src="../plugins/jquery/jquery-1.11.3.js"></script>
    <script type="text/javascript" src="../plugins/jquery/jquery.mobile-1.4.5.js"></script>
</head>
<body>
    <header class="uec-header">
        <div class="uec-head">
            <div class="uec-bar uec-icon icon-shousuo"></div>
            <h2 class="uec-logo">
                <img src="../stylesheets/template2/images/logo.png" class="uec-h-logo">
                <!--<a class="uec-h-logo" href="javascript:;"></a>-->
            </h2>
            <nav class="uec-h-nav uec-nav-js">
                <ul>
                    <li class="uec-h-li on"><a class="uec-h-a" href="javascript:;">首页</a></li>
                    <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">产品创新</a></li>
                    <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">解决方案</a></li>
                    <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">行业</a></li>
                    <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">商城</a></li>
                    <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">新闻中心</a></li>
                    <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">投资者关系</a></li>
                    <li class="uec-h-li"><a class="uec-h-a" href="javascript:;">关于我们</a></li>
                </ul>
            </nav>
            <div class="uec-right">
                <div class="uec-search">
                    <i class="uec-icon icon-fangdajing"></i>
                    <input class="uec-input" type="text">
                </div>
                <div class="uec-h-r-div1">
                    <a class="uec-h-r-a" href="javascript:;">EN</a>
                    <span class="uec-h-r-sp">|</span>
                    <a class="uec-h-r-a uec-icon icon-fangdajing" href="javascript:;"></a>
                </div>
                <div class="uec-h-r-div">
                    <a class="uec-h-r-a" href="javascript:;">登录</a>
                    <span class="uec-h-r-sp">|</span>
                    <a class="uec-h-r-a" href="javascript:;">注册</a>
                </div>
            </div>
        </div>
        <div class="uec-h-nCont">
            <div class="uec-h-nCline"></div>
            <nav class="uec-nC-subnav">
                <ul class="uec-nC-ul">
                    <li class="uec-subn-li"><i class="uec-icon icon-yunjisuan"></i><span>企业数字化</span></li>
                    <li class="uec-subn-li"><i class="uec-icon icon-jichugoujia"></i><span>基础架构</span></li>
                    <li class="uec-subn-li"><i class="uec-icon icon-wulianwang"></i><span>物联网</span></li>
                    <li class="uec-subn-li"><i class="uec-icon icon-dashujuchanpinzhongxin"></i><span>数据产品</span></li>
                    <li class="uec-subn-li"><i class="uec-icon icon-jiyin"></i><span>生物信息</span></li>
                </ul>
            </nav>
            <ul class="uec-nC-ul">
                <li class="uec-sbsb-li">
                    <ul>
                        <li><a href="#">云桥</a></li>
                        <li><a href="#">云盘</a></li>
                        <li><a href="#">档案系统</a></li>
                        <li><a href="#">电商平台</a></li>
                        <li><a href="#">iNews</a></li>
                        <li><a href="#">云豆</a></li>
                        <li><a href="#">云客服</a></li>
                    </ul>
                </li>
                <li class="uec-sbsb-li">
                    <ul>
                        <li><a href="#">云资源管理</a></li>
                        <li><a href="#">APEX监控管理</a></li>
                        <li><a href="#">APEX运维管理</a></li>
                        <li><a href="#">PaaS平台</a></li>
                        <li><a href="#">存储平台</a></li>
                    </ul>
                </li>
                <li class="uec-sbsb-li">
                    <ul>
                        <li><a href="#">车辆相关</a></li>
                        <li><a href="#">边缘计算</a></li>
                        <li><a href="#">实时数据库</a></li>
                        <li><a href="#">物联网平台</a></li>
                        <li><a href="#">农业物联网</a></li>
                        <li><a href="#">工业物联网</a></li>
                        <li><a href="#">水联网</a></li>
                    </ul>
                </li>
                <li class="uec-sbsb-li">
                    <ul>
                        <li><a href="#">风报</a></li>
                        <li><a href="#">NLP引擎</a></li>
                        <li><a href="#">大数据垂直应用一体机</a></li>
                        <li><a href="#">外部数据指数</a></li>
                        <li><a href="#">评分卡模型</a></li>
                        <li><a href="#">反欺诈模型</a></li>
                        <li><a href="#">蚂蚁多为检索</a></li>
                    </ul>
                </li>
                <li class="uec-sbsb-li">
                    <ul>
                        <li><a href="#">数据平台</a></li>
                        <li><a href="#">Helicube</a></li>
                        <li><a href="#">Balsa/Elsa</a></li>
                        <li><a href="#">Database.io</a></li>
                        <li><a href="#">基因浏览器</a></li>
                    </ul>
                </li>
            </ul>
        </div>
        <div class="uec-h-nCont"></div>
        <div class="uec-h-nCont"></div>
        <div class="uec-h-nCont"></div>
        <div class="uec-h-nCont"></div>
        <div class="uec-h-nCont">
            <div class="uec-h-nCline"></div>
            <ul class="uec-nC-ul">
                <li class="uec-sbsb-li"><a href="#">公告信息</a></li>
                <li class="uec-sbsb-li"><a href="#">定期报告</a></li>
                <li class="uec-sbsb-li"><a href="#">公司治理</a></li>
                <li class="uec-sbsb-li"><a href="#">投资者服务</a></li>
            </ul>
        </div>
        <div class="uec-h-nCont"></div>
    </header>
    <section class="uec-section">
        <div class="uec-solution-banner">
            <img class="uec-s-b-img" src="../stylesheets/template2/images/solution.jpg">
            <a href="javascript:;" class="uec-cursor-d uec-banner-textimg uec-solution-textimg"></a>
        </div>
        <div class="uec-eidt-bg"></div>
        <div class="uec-section-btn1">
            <a href="javascript:;" class="uec-icon icon-shanchu"></a>
            <a href="javascript:;" class="uec-icon icon-xiayi"></a>
            <a href="javascript:;" class="uec-icon icon-shangyi"></a>
            <a href="javascript:;" class="uec-icon icon-bianji"></a>
            <a href="javascript:;" class="uec-icon icon-xianshi"></a>
        </div>
    </section>
    <section class="uec-solu-sect">
        <div class="uec-s-tab">
            <ul class="uec-head">
                <li><a href="javascript:;" class="uec-s-t-a">首页</a></li>
                <li>></li>
                <li>解决方案</li>
            </ul>
        </div>
        <div class="uec-head">
            <div class="uec-ind-tab wes-s-sub-js">
                    <div class="uec-s-t-i"><i class="uec-icon icon-fangkuai"></i></div>
                    <div class="uec-s-tab-css">
                        <div class="uec-s-tab1 uec-s-tab-js">
                            <ul id="uec-s-ul-js" class="uec-s-ul-js clear">
                                <li><a href="javascript:;">云计算</a><span>|</span></li>
                                <li ><a href="javascript:;">云计算</a><span>|</span></li>
                                <li><a href="javascript:;">IT咨询、测试和培训服务</a><span>|</span></li>
                                <li><a href="javascript:;">云计算</a><span>|</span></li>
                                <li><a href="javascript:;">IT咨询、测试和培训服务</a><span>|</span></li>
                                <li><a href="javascript:;">云计算</a><span>|</span></li>
                                <li><a href="javascript:;">大数据</a><span>|</span></li>
                                <li><a href="javascript:;">1大数据</a><span>|</span></li>
                                <li><a href="javascript:;">大数据</a><span>|</span></li>
                                <li><a href="javascript:;">大数据</a><span>|</span></li>
                                <li><a href="javascript:;">大数据</a><span>|</span></li>
                                <li class="on"><a href="javascript:;">大数据</a><span>|</span></li>
                                <li><a href="javascript:;">大数据</a><span>|</span></li>
                                <li><a href="javascript:;">大数据</a><span>|</span></li>
                                <li><a href="javascript:;">大数据</a><span>|</span></li>
                                <li><a href="javascript:;">大数据</a><span>|</span></li>
                            </ul>
                        </div>
                    </div>
                    <div id="uec-operate-tab" class="uec-s-t-r">
                        <i class="uec-icon icon-sanjiao" onclick="clickSolutionTabLeft()"></i>
                        <i class="uec-icon icon-right" onclick="clickSolutionTabRight()"></i>
                    </div>
            </div>
            <script type="text/javascript">
                var tablicked = null;
                $(function () {
                	initCustumTab('#uec-s-ul-js');
                    touchMove();
                });
                $(window).resize(function () {
	                initCustumTab('#uec-s-ul-js');
                });

                function initCustumTab(elm) {
	                var $ul = $(elm),
                        tabSize = $ul.children('li').size(),
                        liWidth = getTabliWidth($ul),
                        tabContentWidth = $ul.parent().width();
	                var ulWidth = liWidth + 1;
	                    $ul.width(ulWidth);
                };

                function getTabliWidth(elm) {
                    var sum = 0, tabContentWidth = elm.parent().width();
                    var sum2 = 0;
	                elm.children('li').each(function (index, el) {
                       var $el = $(el);
                       sum += $el.width();
		                if(sum >= tabContentWidth){
			                $el.addClass('v-hidden');
			                $('#uec-operate-tab').show();
		                }else{
			                $('#uec-operate-tab').hide();
		                }
	                });
	                return sum;
                };

                function clickSolutionTabLeft() {
	                clearTimeout(tablicked);
	                tablicked = setTimeout(function () {
                        var $ul = $('#uec-s-ul-js'),shiftLeft = 0;
                        var liSize = $ul.children('li').size() - 1;
                        var lastVisLi = $ul.children('li:not(.v-hidden)').last().index();
                        var firstVisLi = $ul.children('li:not(.v-hidden)').first().index();
                        var ulLeft = $ul.position().left;
                        if(firstVisLi == 0){
                            return;
                        }else{
                        	var firstVisNextLiw = $ul.children('li:eq('+(firstVisLi-1)+')').removeClass('v-hidden').width();
                        	var lastVisPrevLiw = $ul.children('li:eq('+(lastVisLi-1)+')').width();
                        	if(firstVisNextLiw > lastVisPrevLiw){
		                        shiftLeft = ulLeft + firstVisNextLiw;
                            }else{
		                        shiftLeft = ulLeft + lastVisPrevLiw;
                            }
                            if(shiftLeft > 0){
                                shiftLeft = 0;
                            }
                            $ul.children('li:eq('+lastVisLi+')').addClass('v-hidden');
                            $ul.stop(true, false).animate({left:shiftLeft}, 300);
                        }
                        clearTimeout(tablicked);
                    },200);
                }

                function clickSolutionTabRight() {
	                clearTimeout(tablicked);
	                tablicked = setTimeout(function () {
		                var $ul = $('#uec-s-ul-js'), shiftLeft = 0;
		                var liSize = $ul.children('li').size() - 1;
		                var lastVisLi = $ul.children('li:not(.v-hidden)').last().index();
		                var firstVisLi = $ul.children('li:not(.v-hidden)').first().index();
		                var ulLeft = $ul.position().left;
		                if(liSize == lastVisLi){
			                return;
		                }else{
		                	var lastVisPrevLiw = $ul.children('li:eq('+(lastVisLi+1)+')').removeClass('v-hidden').width();
		                	var firstVisNextLiw = $ul.children('li:eq('+(firstVisLi)+')').width();
                            shiftLeft = ulLeft - firstVisNextLiw;
			                $ul.children('li:eq('+firstVisLi+')').addClass('v-hidden');
			                $ul.stop(true, false).animate({left:shiftLeft},300);
		                }
		                clearTimeout(tablicked);
	                },200);
                }

                function touchMove() {
                    $(".wes-s-sub-js").on("swipeleft",function(){
                        $('.uec-s-t-r .icon-right').stop(false,true).click();
                    });
                    $(".wes-s-sub-js").on("swiperight",function(){
                        $('.uec-s-t-r .icon-sanjiao').stop(false,true).click();
                    });
                }
            </script>
        </div>

        <ul class="uec-s-ul o-overhidden">
            <li class="uec-s-li">
                <a href="javascript:;">
                    <div class="uec-s-img1"><img src="../stylesheets/template2/images/fe6bb76fa2a543e2917cef84dea9ec03.jpg" /></div>
                    <div class="uec-s-bg"></div>
                    <h3 class="uec-s-h3">
                        <em class="uec-s-em">物联网</em>
                        <div class="uec-s-content">
                            sjddddddddddddddddddddddddddddddddddddddddddddddddddd
                        </div>
                    </h3>
                </a>
                <!--<div class="uec-s-cover">-->
                    <!--<a href="javascript:;" class="uec-icon icon-shanchu"></a>-->
                    <!--<a href="javascript:;" class="uec-icon icon-moban1"></a>-->
                    <!--<a href="javascript:;" class="uec-icon icon-bianji"></a>-->
                <!--</div>-->
            </li>
            <li class="uec-s-li">
                <a href="javascript:;">
                    <div class="uec-s-img1"><img src="../stylesheets/template2/images/fe6bb76fa2a543e2917cef84dea9ec03.jpg" /></div>
                    <div class="uec-s-bg"></div>
                    <h3 class="uec-s-h3">
                        <em class="uec-s-em">物联网</em>
                        <div class="uec-s-content">
                            sjddddddddddddddddddddddddddddddddddddddddddddddddddd
                        </div>
                    </h3>
                </a>
            </li>
            <li class="uec-s-li">
                <a href="javascript:;">
                    <div class="uec-s-img1"><img src="../stylesheets/template2/images/fe6bb76fa2a543e2917cef84dea9ec03.jpg" /></div>
                    <div class="uec-s-bg"></div>
                    <h3 class="uec-s-h3">
                        <em class="uec-s-em">物联网</em>
                        <div class="uec-s-content">
                            sjddddddddddddddddddddddddddddddddddddddddddddddddddd
                        </div>
                    </h3>
                </a>
            </li>
            <li class="uec-s-li">
                <a href="javascript:;">
                    <div class="uec-s-img1"><img src="../stylesheets/template2/images/fe6bb76fa2a543e2917cef84dea9ec03.jpg" /></div>
                    <div class="uec-s-bg"></div>
                    <h3 class="uec-s-h3">
                        <em class="uec-s-em">物联网</em>
                        <div class="uec-s-content">
                            sjddddddddddddddddddddddddddddddddddddddddddddddddddd
                        </div>
                    </h3>
                </a>
            </li>
            <li class="uec-s-li">
                <a href="javascript:;" class="uec-icon icon-jia"></a>
            </li>
        </ul>
    </section>
    <footer class="uec-footer clear">
        <div class="uec-f-upline">
            <div class="uec-f-line1"></div>
            <div class="uec-f-line2"></div>
        </div>
        <div class="uec-foot clear">
            <dl class="uec-f-dl">
                <dt class="uec-f-dt">快速链接</dt>
                <dd class="uec-f-dd"><a href="javascript:;">车网互联</a></dd>
                <dd class="uec-f-dd"><a href="javascript:;">泰和佳通</a></dd>
                <dd class="uec-f-dd"><a href="javascript:;">荣之联加速器</a></dd>
                <dd class="uec-f-dd"><a href="javascript:;">子公司网站链接</a></dd>
            </dl>
            <dl class="uec-f-dl">
                <dt class="uec-f-dt">新闻中心</dt>
                <dd class="uec-f-dd"><a href="javascript:;">新闻动态</a></dd>
                <dd class="uec-f-dd"><a href="javascript:;">新闻动态</a></dd>
            </dl>
            <dl class="uec-f-dl">
                <dt class="uec-f-dt">加入我们</dt>
                <dd class="uec-f-dd"><a href="javascript:;">用人之道</a></dd>
                <dd class="uec-f-dd"><a href="javascript:;">社会招聘</a></dd>
                <dd class="uec-f-dd"><a href="javascript:;">企业招聘</a></dd>
            </dl>
            <div class="uec-xian"></div>
            <div class="uec-f-r">
                <ul class="uec-f-r-ul">
                    <li class="uec-f-r-li1"></li>
                    <li class="uec-f-r-li2"></li>
                </ul>
                <div class="uec-f-r-d"></div>
                <p class="uec-f-r-p">官方微信</p>
            </div>
            <dl class="uec-f-dl1">
                <dt class="uec-f-dt">联系我们</dt>
                <dd class="uec-f-dd">电话：010-62602000</dd>
                <dd class="uec-f-dd">传真：010-62602100</dd>
                <dd class="uec-f-dd">邮箱：<EMAIL></dd>
                <dd class="uec-f-dd">北京市朝阳区酒仙桥北路甲10号院106楼荣联数讯大厦</dd>
            </dl>
            <ul class="uec-f-m">
                <li><a href="javascript:;">网站地图</a><span>|</span></li>
                <li><a href="javascript:;">举报信息</a><span>|</span></li>
                <li><a href="javascript:;">法律保护</a></li>
            </ul>
        </div>
        <ul class="uec-font1">
            <li>CopyRight©2015-2016</li>
            <li>北京荣之联科技股份有限公司 版权所有</li>
            <li class="uec-f-bot">京ICP备16042301号</li>
        </ul>
    </footer>
</body>
</html>