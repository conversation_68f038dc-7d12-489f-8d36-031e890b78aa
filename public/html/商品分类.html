<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7; IE=EmulateIE10">
    <title>商品分类</title>
    <link href="../plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../plugins/bootstrap/css/bootstrap-theme.min.css" rel="stylesheet" type="text/css" />
    <link href="../stylesheets/common.css" rel="stylesheet" type="text/css" />
    <link href="../stylesheets/shopm.css" rel="stylesheet" type="text/css" />
    <link href="../stylesheets/template2/stylesheets/uec-main.css" rel="stylesheet" type="text/css">
    <script type="text/javascript" src="../plugins/jquery/jquery-1.11.3.js"></script>
    <script type="text/javascript" src="../plugins/jquery-ui/jquery-ui-1.11.4.js"></script>
</head>
<body>
<!--top结构开始-->
<div class="sm-top-box">
     <div class="sm-top-logo float-l">
      <a href="#" class="sm-logo-img"><img src="../images/logo.png" /><i></i>官网管理后台</a>
     </div>
     <ul class="sm-top-right float-r">
      <li><a class="sm-urse">Admin</a>|</li>
      <li><a>个人信息</a>|</li>
      <li><a class="sm-exit">退出登录</a></li>
     </ul>
</div>
<!--top结构end-->
<div class="sm-conent-box clear">
 <!--content-left结构开始-->
   <div class="sm-content-left">
   <ul class="menu">
         <li class="menu-li menu-li-home">
           <!-- 点击之后a添加class="current"-->
             <a href="#"><i class="iconfont icon-home"></i>首页</a>
         </li>
		 <li class="menu-li">
			<a href="#"><i class="iconfont icon-inquiry"></i>商城询价</a>
		</li>
        <li class="menu-li">
           <em class="iconfont icon-sanj icon-dianjs"></em>
			<a href="#" class="current"><i class="iconfont icon-wangzhanicon"></i>网站管理</a>
			<ul class="menu-li-ul">
				<li><a href="#" ><i class="iconfont icon-yuan1"></i>模板管理</a></li>
				<li><a href="#"><i class="iconfont icon-yuan1"></i>自定义设置</a></li>
				<li><a href="#" class="current"><i class="iconfont icon-yuan1"></i>页面管理</a></li>
			</ul>
		</li>
         <li class="menu-li">
           <em class="iconfont icon-sanj"></em>
			<a href="#"><i class="iconfont icon-wej"></i>新闻管理</a>
			<ul class="menu-li-ul">
				<li><a href="#"><i class="iconfont icon-yuan1"></i>文章分类</a></li>
			</ul>

		</li>
		 <li class="menu-li">
           <a href="#"><i class="iconfont icon-nexusone"></i>投资者关系管理</a>
		</li>
        <li class="menu-li">
           <em class="iconfont  icon-sanj"></em>
			<a href="#"><i class="iconfont icon-content"></i>其他内容管理</a>
		</li>
        <li class="menu-li">
			<a href="#"><i class="iconfont icon-iconfontuser"></i>会员管理</a>
		</li>
        <li class="menu-li">
			<a href="#"><i class="iconfont icon-auditing"></i>审核管理</a>
		</li>        
        <li class="menu-li">
           <em class="iconfont icon-sanj"></em>
			<a href="#"><i class="iconfont icon-shezhi"></i>系统管理</a>
		</li>
	</ul>
   </div>

 <!--content-left结构end-->
 <!--content-right结构开始-->
 <div class="sm-content-right">
     <div class="sm-content-right-inner">
      <p class="location">当前位置：<a href="#">首页</a>&gt;<a href="#">商品管理</a>&gt;<span>商品分类</span></p>
      <p class="sm-according">Hi,分类管理帮助您有效归类商品！</p>
      <div class="sm-according-box clear">
            <div class="sm-listbox-accord clear">
              <dl class="sm-accord-dl">
                <dt>添加一级菜单：</dt>
                <dd><input type="text" class="input-medium input-box w200"  placeholder="请输入一级菜单的名称"/></dd>
                <dd><a href="#" title="添加" class="sm-accord-a"><i class="iconfont icon-add"></i></a></dd>
              </dl>
            </div>
      
            <div class="sm-listbox-accord clear">
              <dl class="sm-accord-dl">
                <dt>添加二级菜单：</dt>
                <dd><input type="text" class="input-medium input-box w200"  placeholder="请输入一级菜单的名称"/></dd>
                <dd class="ml10">所属一级分类：</dd>
                 <dd>
                     <div class="btn-group btn-group-box open">
                          <span type="button" class="btn-group-wenb">服务器1</span>
                          <button type="button" class="btn-group-btn" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <span class="caret"></span>
                          </button>
                          <ul class="dropdown-menu rwgl-menu">
                            <li><a href="#">X86服务器</a></li>
                            <li><a href="#">Unix服务器</a></li>
                          </ul>
                      </div>
                </dd>
                <dd><a href="#" title="添加" class="sm-accord-a"><i class="iconfont icon-add"></i></a></dd>
              </dl>
            </div>
              <ul class="uec-pL-ul">
                  <li class="sm-one-level ">
                      <dl class="sm-accord-dl uec-pL-dl">
                          <dt><span class="iconfont icon-move"></span>一级菜单：</dt>
                          <dd><input type="text" class="input-medium input-box w200"  value="服务器" /></dd>
                          <dd><a href="#" title="删除" class="sm-accord-a"><i class="iconfont icon-jian"></i></a></dd>
                      </dl>
                      <div class="uec-pL-js">
                          <dl class="sm-accord-dl sm-two-level uec-pL-item-js">
                              <dt><span class="iconfont icon-move"></span>二级菜单：</dt>
                              <dd><input type="text" class="input-medium input-box w200"  value="X86服务"/></dd>
                              <dd><a href="#" title="删除" class="sm-accord-a"><i class="iconfont icon-jian"></i></a></dd>
                          </dl>
                          <dl class="sm-accord-dl sm-two-level uec-pL-item-js">
                              <dt><span class="iconfont icon-move"></span>二级菜单：</dt>
                              <dd><input type="text" class="input-medium input-box w200"  value="Unix服务器"/></dd>
                              <dd><a href="#" title="删除" class="sm-accord-a"><i class="iconfont icon-jian"></i></a></dd>
                          </dl>
                          <dl class="sm-accord-dl sm-two-level uec-pL-item-js">
                              <dt><span class="iconfont icon-move"></span>二级菜单：</dt>
                              <dd><input type="text" class="input-medium input-box w200"  value="融合基础架构服务器"/></dd>
                              <dd><a href="#" title="删除" class="sm-accord-a"><i class="iconfont icon-jian"></i></a></dd>
                          </dl>
                      </div>
                  </li>
                  <li class="sm-one-level">
                      <dl class="sm-accord-dl">
                          <dt><span class="iconfont icon-move"></span>一级菜单：</dt>
                          <dd><input type="text" class="input-medium input-box w200"  value="存储" /></dd>
                          <dd><a href="#" title="删除" class="sm-accord-a"><i class="iconfont icon-jian"></i></a></dd>
                      </dl>
                      <div class="uec-pL-js">
                          <dl class="sm-accord-dl sm-two-level uec-pL-item-js">
                              <dt><span class="iconfont icon-move"></span>二级菜单：</dt>
                              <dd><input type="text" class="input-medium input-box w200"  value="荣之联云存储"/></dd>
                              <dd><a href="#" title="删除" class="sm-accord-a"><i class="iconfont icon-jian"></i></a></dd>
                          </dl>
                          <dl class="sm-accord-dl sm-two-level uec-pL-item-js">
                              <dt><span class="iconfont icon-move"></span>二级菜单：</dt>
                              <dd><input type="text" class="input-medium input-box w200"  value="VTL"/></dd>
                              <dd><a href="#" title="删除" class="sm-accord-a"><i class="iconfont icon-jian"></i></a></dd>
                          </dl>
                          <dl class="sm-accord-dl sm-two-level uec-pL-item-js">
                              <dt><span class="iconfont icon-move"></span>二级菜单：</dt>
                              <dd><input type="text" class="input-medium input-box w200"  value="NAS"/></dd>
                              <dd><a href="#" title="删除" class="sm-accord-a"><i class="iconfont icon-jian"></i></a></dd>
                          </dl>
                      </div>
                  </li>
                  <li class="sm-one-level uec-pL-js">
                      <dl class="sm-accord-dl">
                          <dt><span class="iconfont icon-move"></span>一级菜单：</dt>
                          <dd><input type="text" class="input-medium input-box w200"  value="存储" /></dd>
                          <dd><a href="#" title="删除" class="sm-accord-a"><i class="iconfont icon-jian"></i></a></dd>
                      </dl>
                  </li>
              </ul>
                 
         <div class="sm-btn clear align-center">
             <button class="btn btnw btn-box">保存</button>
             <button class="btn btnw btn-box btn-box-primary">取消</button>
           </div>
          <script type="text/javascript">
              $( ".uec-pL-ul" ).sortable({
                    revert: true});
              $( ".uec-pL-js" ).sortable({
                  revert: true
              });
              $( ".uec-pL-ul" ).disableSelection();
              $( ".uec-pL-js" ).disableSelection();
          </script>
    </div>
      
    </div>
</div>
<!--content结构end-->

</body>
</html>
