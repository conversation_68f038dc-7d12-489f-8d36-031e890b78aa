/**
 * Created by r<PERSON><PERSON> on 2017/5/4.
 */

var agencyPageNo = 1; //当前页码
var agencyPageSize = $('#agency-content input.hiddenId').val();
var agencyStr;
var dataJson = {
    data: [{"id": "", "name": "全部"}, {"id": "1", "name": "北京市"}, {"id": "2", "name": "上海市"}, {
        "id": "3",
        "name": "天津市"
    }, {"id": "4", "name": "重庆市"}, {"id": "5", "name": "河北省"}, {"id": "6", "name": "山西省"}, {
        "id": "7",
        "name": "辽宁省"
    }, {"id": "8", "name": "吉林省"}, {"id": "9", "name": "黑龙江省"}, {"id": "10", "name": "江苏省"}, {
        "id": "11",
        "name": "浙江省"
    }, {"id": "12", "name": "安徽省"},
        {"id": "13", "name": "福建省"}, {"id": "14", "name": "江西省"}, {"id": "15", "name": "山东省"}, {
            "id": "16",
            "name": "河南省"
        }, {"id": "17", "name": "湖北省"}, {"id": "18", "name": "湖南省"}, {"id": "19", "name": "广东省"}, {
            "id": "20",
            "name": "海南省"
        }
        , {"id": "21", "name": "四川省"}, {"id": "22", "name": "贵州省"}, {"id": "23", "name": "云南省"}, {
            "id": "24",
            "name": "陕西省"
        }, {"id": "25", "name": "甘肃省"}, {"id": "26", "name": "青海省"}, {"id": "27", "name": "台湾省"}, {
            "id": "28",
            "name": "新疆"
        }, {"id": "29", "name": "西藏"}, {"id": "30", "name": "内蒙古"}
        , {"id": "31", "name": "宁夏"}, {"id": "32", "name": "广西"}, {"id": "33", "name": "香港"}, {
            "id": "34",
            "name": "澳门"
        }]
};

var dataAddJson = {
    data: [{"id": "1", "name": "北京市"}, {"id": "2", "name": "上海市"}, {
        "id": "3",
        "name": "天津市"
    }, {"id": "4", "name": "重庆市"}, {"id": "5", "name": "河北省"}, {"id": "6", "name": "山西省"}, {
        "id": "7",
        "name": "辽宁省"
    }, {"id": "8", "name": "吉林省"}, {"id": "9", "name": "黑龙江省"}, {"id": "10", "name": "江苏省"}, {
        "id": "11",
        "name": "浙江省"
    }, {"id": "12", "name": "安徽省"},
        {"id": "13", "name": "福建省"}, {"id": "14", "name": "江西省"}, {"id": "15", "name": "山东省"}, {
            "id": "16",
            "name": "河南省"
        }, {"id": "17", "name": "湖北省"}, {"id": "18", "name": "湖南省"}, {"id": "19", "name": "广东省"}, {
            "id": "20",
            "name": "海南省"
        }
        , {"id": "21", "name": "四川省"}, {"id": "22", "name": "贵州省"}, {"id": "23", "name": "云南省"}, {
            "id": "24",
            "name": "陕西省"
        }, {"id": "25", "name": "甘肃省"}, {"id": "26", "name": "青海省"}, {"id": "27", "name": "台湾省"}, {
            "id": "28",
            "name": "新疆"
        }, {"id": "29", "name": "西藏"}, {"id": "30", "name": "内蒙古"}
        , {"id": "31", "name": "宁夏"}, {"id": "32", "name": "广西"}, {"id": "33", "name": "香港"}, {
            "id": "34",
            "name": "澳门"
        }]
};

//页面初始
$(document).ready(function () {

    $('#category').selectlist({topPosition: false, dataJson: dataJson});
    //改变查看页大小
    $('#agency-content .in-pageNo ul').delegate('li', 'click', function () {
        if ($(this).text() != agencyPageSize) {
            agencyPageSize = $(this).text();
            setPageSizeCookie(agencyPageSize);
            agencySearch(agencyPageNo, agencyPageSize);
        }
    });
    agencySearch(agencyPageNo, agencyPageSize);
    //添加加载列表
    $('#addAgency').on('click', function () {
        addAgency();
    });
    //详情按钮
    $('#indexData').delegate('a[name=agencyDetail]', 'click', function () {
        var thisId = $(this).attr('data-id');
        showAgencyDetail(thisId);
    });
    //编辑
    $('#indexData').delegate('a[name=agencyEdit]', 'click', function () {
        var thisId = $(this).attr('data-id');
        showAgencyEdit(thisId);
    });
    //删除办事处
    $('#indexData').delegate('a[name=agencyDelete]', 'click', function () {
        var thisId = $(this).attr('data-id');
        var agencyVo = {};
        agencyVo.agencyId = thisId;
        $.confirm({
            animation: 'opacity',
            closeAnimation: 'scale',
            confirmButton: '确定',
            title: '办事处删除',
            content: alertWarning('确定要删除此办事处吗？'),
            confirm: function () {
                deleteAgency(agencyVo);
            }
        });
    });
    //查询按钮
    $('#btnQuery').on('click', function () {
        //收集查询条件
        agencySearch(agencyPageNo, agencyPageSize);
    });
    /**
     * 键盘事件
     */
    $("body").keydown(function (e) {
        var curKey = e.which;
        if (curKey == 13) {
            $("#btnQuery").click();
        }
    });
});
//执行新增办事处
function addAgency() {
    agencyStr = $.confirm({
        confirmButtonPlural: "保存",
        animation: 'opacity',
        confirmTop: '10%',
        closeAnimation: 'scale',
        columnClass: 'col-md-6 col-md-offset-3',
        title: '添加办事处',
        confirmButton: false,
        content: function ($obj) {
            var sysuser = {};
            sysuser.agencyId = null;
            return $.post('/agency/s', sysuser, function (data) {
                $obj.setContent(data);
                $('#categoryEdit').selectlist({topPosition: false, dataJson: dataAddJson});
            });
        },
        confirmPlural: function () {
            if (!validAgencyEdit()) {
                return false;
            } else {
                var region = $('#categoryEdit').find('li.selected').attr('data-value');
                if(region == undefined){
                    $.alert({
                        animation: 'opacity',
                        closeAnimation: 'scale',
                        title: '提示',
                        content: alertWarning('请选择所在区域!')
                    });
                    return false;
                }
                return doSave();
            }
        }
    });
}

/**
 * 办事处详情
 */
function showAgencyDetail(id) {
    $.confirm({
        animation: 'opacity',
        confirmTop: '4.5%',
        closeAnimation: 'scale',
        columnClass: 'col-md-6 col-md-offset-3',
        title: '办事处详情',
        content: function ($obj) {
            var dataRequest = {};
            dataRequest.pageSize = 1;
            dataRequest.pageNo = 1;
            dataRequest.agencyId = id;
            dataRequest.ticker = new Date().getTime();
            return $.post('/agency/detail', dataRequest, function (data) {
                $obj.setContent(data);
            });
        },
        confirmButton: '关闭',
        cancelButton: null,
    });
}

/**
 * 弹出修改办事处页面
 * @param agencyId
 */
function showAgencyEdit(agencyId) {

    var jsonStr = {};
    jsonStr.agencyId = agencyId;
    $.confirm({
        confirmButtonPlural: '保存',
        animation: 'opacity',
        confirmTop: '10%',
        closeAnimation: 'scale',
        columnClass: 'col-md-6 col-md-offset-3',
        title: '修改办事处',
        confirmButton: false,
        content: function ($obj) {
            return $.post('/agency/edit', jsonStr, function (data) {
                $obj.setContent(data);
                $('#categoryEdit').selectlist({topPosition: false, dataJson: dataAddJson});
                $('#categoryEdit').find('li[data-value="' + $("#categoryHid").val() + '"]').click();
            });
        },
        confirmPlural: function () {
            if (!$('#agencyEditForm').isValid()) {
                return false;
            }
            doSave();
        }
    });
}
/**
 * 删除办事处
 * @param
 */
function deleteAgency(agencyVo) {
    $.post('/agency/d', agencyVo, function (data) {
        if (doCallBackAlert('办事处删除成功', data)) {
            agencySearch(agencyPageNo, agencyPageSize);
        }
    });
}
/**
 * 校验办事处
 */
function validAgencyEdit() {
    if ($('#agencyAddForm').isValid()) {
        return true;
    }
}
/**
 * 办事处保存和修改
 */
function doSave() {
    var agencyId = $('#agencyId').val();
    if (agencyId) {//修改操作
        var userData = collectAgencyInfos(agencyId);
        userData.agencyId = agencyId;
        $.post('/agency/u', userData, function (data) {
            if (doCallBackAlert('办事处修改成功', data)) {
                agencySearch(agencyPageNo, agencyPageSize);
            }
        });
    } else {
        var userData = collectAgencyInfos('');

        $.post('/agency/a', userData, function (data) {
            if (doCallBackAlert('办事处添加成功', data)) {
                agencySearch(agencyPageNo, agencyPageSize);
                agencyStr.close();
            }
        });
    }
    return false;
}
/**
 * 收集添加信息
 */
function collectAgencyInfos(userId) {
    //办事处名称
    var agencyName = $('#agencyName').val();
    //联系人
    var linkman = $('#linkman').val();
    //职务
    var position = $('#position').val();
    //手机号
    var telephone = $('#telephone').val();
    //电子邮件
    var email = $('#email').val();
    //所在区域
    if (userId == '') {
        var region = $('#categoryEdit').find('li.selected').attr('data-value');
    } else {
        var region = $('#categoryEdit').find('li.selected').attr('data-value');
    }
    //详细地址
    var address = $('#address').val();

    var agencys = {};
    agencys.agencyName = agencyName;
    agencys.linkman = linkman;
    agencys.position = position;
    agencys.telephone = telephone;
    agencys.email = email;
    agencys.region = region;
    agencys.address = address;
    return agencys;
}
//查询办事处
function agencySearch(pageNo, pageSize) {
    showTableLoading('indexData');
    var agencyName = $.trim($('#q_agencyName').val());
    var linkman = $.trim($('#q_linkman').val());
    // var region = $.trim($('#category').val());
    var region = $('#category').find('li.selected').attr('data-value');
    var agencyConditionVo = {};
    agencyConditionVo.pageSize = pageSize == null ? $('#agency-content input.hiddenId').val() : pageSize;
    agencyConditionVo.pageNo = pageNo;
    agencyConditionVo.agencyName = agencyName;
    agencyConditionVo.linkman = linkman;
    agencyConditionVo.region = region;
    agencyConditionVo.ticker = new Date().getTime();
    $.post('/agency/q', agencyConditionVo, function (data) {
        $('#indexData').empty().html(data);
        var totalPages = $("#cusPageCount").val();
        var options = {
            currentPage: pageNo,  //直接跳转到特定的page
            totalPages: totalPages,//总页数
            useBootstrapTooltip: false,
            onPageChanged: function (event, oldPage, newPage) {
                agencySearch(newPage, pageSize);
            }
        }
        $('#example').bootstrapPaginator(options);
    });
}

