/**
 * Created by d<PERSON><PERSON> on 2017/5/3.
 */
var approvedPageNo = 1; //当前页码
var approvedPageSize = $('#approved-content input.hiddenId').val();
var sortIndex = "a.create_on desc";//处理排序
//页面初始
$(function () {
    //初始化日期控件
    initDateTimerPicker("startDateTmp", "YYYY-MM-DD", "day");
    initDateTimerPicker("endDateTmp", "YYYY-MM-DD", "day");
    addDateLinster();
    //改变查看页大小
    $('#approved-content .in-pageNo ul').delegate('li', 'click', function () {
        if ($(this).text() != approvedPageSize) {
            approvedPageSize = $(this).text();
            setPageSizeCookie(approvedPageSize);
            approvedSearch(approvedPageNo, approvedPageSize);
        }
    });

    //查询按钮
    $('#btnQuery').on('click', function () {
        //收集查询条件
        approvedSearch(approvedPageNo, approvedPageSize);
    });
    $('#indexData').delegate('a[name=btnView]', 'click', function () {
        previewPage(this);
    });
    $('#indexData').delegate('a[name=btnAudit]', 'click', function () {
        update(this, 'audit',1);
    });
    $('#indexData').delegate('a[name=btnRegectAudit]', 'click', function () {
        update(this, 'regectAudit',2);
    });
    $('#indexData').delegate('a[name=btnRelease]', 'click', function () {
        update(this, 'release',3);
    });
    $('#indexData').delegate('a[name=btnRegectRelease]', 'click', function () {
        update(this, 'regectRelease',4);
    });
    //排序改变
    $('#sortOrders').on('click', function () {
        var $this = $(this);
        var btn =  $this.find('btn-a');
        if(btn.hasClass('clickon-a')){
            btn.removeClass('clickon-a');
            btn.addClass('click-a');
            sortIndex = 'a.create_on asc';//正序
        }else {
            btn.removeClass('click-a');
            btn.addClass('clickon-a');
            sortIndex = 'a.create_on desc';//倒序
        }
        approvedSearch(approvedPageNo, approvedPageSize);
    });
    if($("#statusShow").val()!='') {
        $('#status').find('li[data-value="' + $("#statusShow").val() + '"]').click();
    }
    //查询加载列表
    approvedSearch(approvedPageNo, approvedPageSize);

});
//预览
function previewPage(obj) {
    var type=$(obj).parents('tr').attr('data-type');
    if(type!='INVESTOR1'&&type!='INVESTOR2'&&type!='INVESTOR3'&&type!='INVESTOR4'&&type!='INVESTOR5'&&type!='INVESTOR6'&&type!='INVESTOR7'){
        if(type!='INVESTOR8')
        {
            window.open($("#shoppingUrl").val() + $(obj).attr("data-id"));
        }else{
            window.open($(obj).attr("data-id"));
        }
    }
}

//查询
function approvedSearch(pageNo, pageSize) {
    showTableLoading('indexData');
    var approvedConditionVo = {};
    approvedConditionVo.pageSize = pageSize == null ? $('#approved-content input.hiddenId').val() : pageSize;
    approvedConditionVo.pageNo = pageNo;
    approvedConditionVo.creatorName = $.trim($('#creatorName').val());
    approvedConditionVo.status = $('#status').find('li.selected').attr('data-value');
    approvedConditionVo.type = $('#change_type').find('li.selected').attr('data-value');
    approvedConditionVo.createOnStart = $.trim($('#createOnStart').val());
    approvedConditionVo.createOnEnd = $.trim($('#createOnEnd').val());
    approvedConditionVo.ticker = new Date().getTime();
    approvedConditionVo.sortIndex=sortIndex;//处理排序字段
    $.post('/approved/q', approvedConditionVo, function (data) {
        $('#indexData').empty().html(data);
        var totalPages = $("#cusPageCount").val();
        var options = {
            currentPage: pageNo,  //直接跳转到特定的page
            totalPages: totalPages,//总页数
            useBootstrapTooltip: false,
            onPageChanged: function (event, oldPage, newPage) {
                approvedSearch(newPage, pageSize);
            }
        }
        $('#example').bootstrapPaginator(options);
    });
}

function update(obj, type,status) {
    var title = '';
    var content = '';
    if (type == "audit") {
        title = '审核';
        content = '确定要审核?';
    } else if(type=='regectAudit'){
        title = '拒绝审核';
        content = '确定要拒绝审核?';
    } else if(type=='release'){
        title = '发布';
        content = '确定要发布?';
    } else if(type=='regectRelease'){
        title = '拒绝发布';
        content = '确定要拒绝发布?';
    }
    $.confirm({
        confirmButtonPlural: '确定',
        confirmButton: false,
        animation: 'opacity',
        closeAnimation: 'scale',
        title: title,
        content: alertWarning(content),
        confirmPlural: function () {
            var dataRequest = {};
            dataRequest.approvedId = $(obj).attr("data-id");
            dataRequest.status = status;
            $.post('/approved/u', dataRequest, function (data) {
                var dataObj = JSON.parse(data);
                if (dataObj.result != undefined) {
                    approvedSearch(1, approvedPageSize);
                    alertSucceed(title+"成功");
                }
                else {
                    $.alert({
                        animation: 'opacity',
                        closeAnimation: 'scale',
                        title: '提示',
                        content: alertError("修改失败")
                    });
                }
            });
        }
    });
}