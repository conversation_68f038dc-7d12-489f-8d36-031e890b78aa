/**
 * @name         :base.js
 * @date         :2015-12-24
 * @updateTime   :()
 * <AUTHOR>
 * @version      :1.0
 **/
/**
 * Tab 页动态内容
 * @param  {[type]} cId tabId
 * @param  {[type]} url tabContent load url
 * @return {[type]}     [description]
 */

$(function () {
    try {
        //公共select
        $('.in-select').selectlist({topPosition: false, width: 150});
        //table显示条数
        $('.in-pageNo').selectlist({width: 70});

    } catch (e) {
    }

    hoverTableOption();

    // 左侧导航 动画 begin ---chendongjie
    $('ul.menu .menu-li-ul').find('a.current').each(function (index, el) {
        $(el).parents('ul.menu-li-ul').show().siblings('em.icon-sanj').addClass('icon-dianjs');
    });
    //去掉不可见元素，如果下级没有 可见菜单 隐藏掉
    $('ul.menu').find('li.menu-li').each(function (index, el) {

        if ($(el).is(':visible')) {
            $(el).siblings('ul').show();
            var em = $(el).find('em');
            var emptyLI = (em.length > 0 && ($(el).find('ul.menu-li-ul li').length === 0));
            if (emptyLI === true) {
                //alert(emptyLI);
                $(el).hide();
            }
        } else {
            $(el).hide();
        }

    });

    //展开 折叠
    $('ul.menu').delegate('li', 'click', function (event) {
        event.stopPropagation();
        var $this = $(this), $em = $this.find('em');
        if ($em.length > 0) {
            if (!$em.hasClass('icon-dianjs')) {
                $em.siblings('ul.menu-li-ul').stop().slideDown();
                $em.addClass('icon-dianjs');
            } else if ($em.hasClass('icon-sanj')) {
                $em.siblings('ul.menu-li-ul').stop().slideUp();
                $em.removeClass('icon-dianjs');
            }
        } else {
            $this.parents('ul.menu').find('a').removeClass('current');
            $this.children('a').addClass('current');
        }
    });

    //选中高亮处理
    //$('ul.menu').delegate('li.menu-li a', 'click', function(event) {
    //    var em =  $(this).parent('li.menu-li').find('em');
    //    if( em.length!=0){//有em,下面有子节点，不显示高亮
    //         return;
    //    }
    //    $(this).parents('ul.menu').find('a').removeClass('current');
    //    $(this).addClass('current');
    //});
    // 左侧导航 动画 end  --chendongjie

    //搜索工具收起or展开
    $(document).delegate('.content-title .icondowm', 'click', function () {
        var $this = $(this),
            animateDiv = $this.parent().siblings('.animate-search');
        if (!$this.hasClass('icon-down1')) {
            animateDiv.stop().slideUp();
            $this.addClass('icon-down1');
        } else {
            animateDiv.stop().slideDown();
            $this.removeClass('icon-down1');
        }
    });

});

/**
 * 先用serializeArray序列化为数组，再封装为Json对象
 */
(function ($) {
    $.fn.serializeJson = function () {
        var serializeObj = {};
        $(this.serializeArray()).each(function () {
            serializeObj[this.name] = this.value;
        });
        return serializeObj;
    };
})(jQuery);

//table option hover begin
function hoverTableOption() {
    $('.table .zxmsh-a .shbtn').hover(function () {
        $(this).find('.tooltip').show();
    }, function () {
        $(this).find('.tooltip').hide();
    });

    $('.table .shbtn .tooltip').hover(function () {
        $(this).hide();
    }, function () {
        $(this).show();
    });
}
//table option hover end

/*
 * 判断图片大小
 * @param ths type="file"的javascript对象
 * @param width 需要符合的宽
 * @param height 需要符合的高
 * @return true-符合要求,false-不符合
 */
function checkImgPX(ths, width, height) {
    if (FileReader) {
        var reader = new FileReader(),
            file = ths.files[0];
        reader.onload = function (e) {
            var image = new Image();
            image.src = e.target.result;
            image.onload = function () {
                if (image.width > width || image.height > height) {
                    //alert("头像尺寸应在128x128之间");
                    return false;
                }
            }
        }
        reader.readAsDataURL(file);
    }
};

/**
 * 失败 alert.content = data
 * @param data
 * @returns {string}
 */
function alertError(data) {
    var htmlTemp = '<div class="modal-p"><em class="modal-p-delete"></em><em class="modal-p-inner">' + decodeURI(data) + '</em></div>';
    return htmlTemp;
}
/**
 * 成功 alert.content = data
 * @param data
 * @returns {string}
 */
function alertSuccess(data) {
    var htmlTemp = '<div class="modal-p"><em class="modal-p-succeed"></em><em class="modal-p-inner">' + decodeURI(data) + '</em></div>';
    return htmlTemp;
}

/**
 * 警告 alert.content = data
 * @param data
 * @returns {string}
 */
function alertWarning(data) {
    var htmlTemp = '<div class="modal-p"><em class="modal-p-warning"></em><em class="modal-p-inner">' + decodeURI(data) + '</em></div>';
    return htmlTemp;
}

function showPage(cId, url) {
    if ($('#' + cId).html().length < 20) { // 当tab页面内容小于20个字节时ajax加载新页面
        $('#' + cId).html('页面加载中，请稍后...'); // 设置页面加载时的loading图片
        $('#' + cId).load(url); // ajax加载页面
    }
};

function popoverTempHtml() {
    var template = new Array();
    template.push('<div class="popover">');
    template.push('   <div class="arrow"></div>');
    template.push('   <h3 class="popover-title"></h3>');
    template.push('   <div class="popover-content"></div>');
    template.push('   <div class="modal-footer">');
    template.push('       <button class="btn btnw btn-box">确定</button>');
    template.push('       <button class="btn btn-box btnw btn-box-primary">取消</button>');
    template.push('   </div>');
    template.push('</div>');

    return template.join(' ');
};


function tooltipTempHtml() {
    var template = new Array();
    template.push('<div class="tooltip" role="tooltip">');
    template.push('   <div class="tooltip-arrow"></div>');
    template.push('   <div class="popover-content tooltip-inner w200"></div>');
    template.push('</div>');

    return template.join(' ');
};

/**
 * 滚动条顶部到网页顶部的距离(body)
 * @returns {number}
 */
function getScrollTopOfBody() {
    var scrollTop = 0;
    if (typeof window.pageYOffset != 'undefined') { //pageYOffset指的是滚动条顶部到网页顶部的距离
        scrollTop = window.pageYOffset;
    } else if (typeof document.compatMode != 'undefined' && document.compatMode != 'BackCompat') {
        scrollTop = document.documentElement.scrollTop;
    } else if (typeof document.body != 'undefined') {
        scrollTop = document.body.scrollTop;
    }
    return scrollTop;
};

/**
 * 滚动条顶部到网页顶部的距离(div) author:<EMAIL>
 * @returns {number}
 */
function getScrollTopOfDiv(obj) {
    var scrollTop = 0;
    if (typeof $('.' + obj + '')[0].pageYOffset != 'undefined') { //pageYOffset指的是滚动条顶部到网页顶部的距离
        scrollTop = $('.' + obj + '')[0].pageYOffset;
    } else if (typeof document.compatMode != 'undefined' && document.compatMode != 'BackCompat') {
        scrollTop = $('.' + obj + '')[0].scrollTop;
    } else if (typeof document.body != 'undefined') {
        scrollTop = $('.' + obj + '')[0].scrollTop;
    }
    return scrollTop;
};

/**
 * 滚动条顶部到网页顶部的距离 author:<EMAIL>
 * */
function getScrollTopOfBody() {
    var scrollTop = 0;
    if (typeof window.pageYOffset != 'undefined') { //pageYOffset指的是滚动条顶部到网页顶部的距离
        scrollTop = window.pageYOffset;
    } else if (typeof document.compatMode != 'undefined' && document.compatMode != 'BackCompat') {
        scrollTop = document.documentElement.scrollTop;
    } else if (typeof document.body != 'undefined') {
        scrollTop = document.body.scrollTop;
    }
    return scrollTop;
}

/**
 * 悬浮保存、取消置顶 body
 * @param sNum 设定某高度时出现
 * @param hNum 设定某高度时消失
 * @param obj  被设定的对象
 */
function setScrollTop(sNum, hNum, obj) {
    var sNum = sNum;
    var hNum = hNum;
    var obj = $(obj);
    var objLeft = obj.offset().left;

    $(window).scroll(function () {
        var scrollTopNum = getScrollTopOfBody();
        if (scrollTopNum > sNum) {
            obj.addClass("fixed-toolbar-body");
            if (obj.hasClass("fixed-toolbar-body")) {
                obj.stop().animate({
                        top: 0
                    },
                    300);
            }
        } else if (scrollTopNum < hNum) {
            obj.removeClass("fixed-toolbar-body");
            if (!obj.hasClass("fixed-toolbar-body")) {
                obj.stop().animate({
                        top: 0
                    },
                    0);
            }
        }

    });
};

/**
 * 悬浮保存、取消置顶 div
 * @param sNum 设定某高度时出现
 * @param hNum 设定某高度时消失
 * @param obj  被设定的对象
 */
function setScrollTopToolbar(sNum, hNum, obj) {
    try {
        var sNum = sNum;
        var hNum = hNum;
        var obj = $(obj);
        var objLeft = obj.offset().left;
        $('.sm-content-right').scroll(function () {
            var scrollTopNum = getScrollTopOfDiv('sm-content-right');
            if (scrollTopNum > sNum) {
                obj.addClass("fixed-toolbar");
                if (obj.hasClass("fixed-toolbar")) {
                    obj.stop().animate({
                            top: 60
                        },
                        300);
                }
            } else if (scrollTopNum < hNum) {
                obj.removeClass("fixed-toolbar");
                if (!obj.hasClass("fixed-toolbar")) {
                    obj.stop().animate({
                            top: 0
                        },
                        0);
                }
            }

        });
    } catch (e) {

    }
};

/**
 * 设置数据条数到cookie
 * @param cookieVal 参数 val
 */
function setPageSizeCookie(cookieVal) {
    $.cookie('pageSizeCookie', cookieVal, {expires: 7, path: '/'});
};

/**
 * 保存成功后关闭
 * @param str 提示语
 * @param columnClass 窗口的宽、位移
 * @param timeout  关闭时间
 */
function alertSucceed(str, columnClass, timeout, callback) {
    var htmlTemp = [], _timeout = 0;
    if (timeout) {
        _timeout = timeout;
    } else {
        _timeout = 1000;
    }
    if (columnClass == null) {
        columnClass = 'col-md-2 col-md-offset-7';
    }
    htmlTemp.push('<div class="prompt">');
    htmlTemp.push('	<em class="prompt-img"></em>');
    htmlTemp.push('	<p>' + str + '</p>');
    htmlTemp.push('</div>');

    var $confirm = $.confirm({
        title: false,
        confirmTop: '40px',
        animation: 'opacity',
        animationSpeed: 1000,
        content: htmlTemp.join(' '),
        columnClass: '' + columnClass + ' succeed-box',
        cancelButton: false,
        confirmButton: false,
        backgroundOpacity: 0,
        closeIcon: false
    });

    setTimeout(function () {
        $confirm.close();
        if (typeof callback == 'function') {
            callback();
        }
    }, _timeout);

};


var defaultColumnClass = 'col-md-2 col-md-offset-7';//默认宽 和 位置
/**
 *陈东杰------------------------
 * 从node返回后的提示
 * @param successMsg 成功的提示
 * @param data 回调数据
 * @param columnClass 窗口的宽、位移
 * @param timeout  关闭时间
 */
function doCallBackAlert(successMsg, data, columnClass, timeout) {
    if (columnClass == undefined) {
        columnClass = defaultColumnClass;
    }
    try {
        if (data) {
            var dataObj = JSON.parse(data);
            if (dataObj.nopermission != undefined && dataObj.nopermission) {//权限校验失败
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '提示',
                    content: alertError(dataObj.message)
                });
                return false;
            }
            if (dataObj.name != undefined && dataObj.name == 'WeChatAPIError') {//微信错误
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '提示',
                    content: alertError('调用微信接口出错')
                });
                return false;
            }
            if (dataObj.code == 2) {//服务端返回错误
                var message = dataObj.message[ERROR_KEY][0];
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '提示',
                    content: alertError(message)
                });
                return false;
            }
            //成功
            alertSucceed(successMsg, columnClass, timeout);

        } else {
            //成功
            alertSucceed(successMsg, columnClass, timeout);
        }
    } catch (e) {
        //成功
        alertSucceed(successMsg, columnClass, timeout);
    }

    return true;
}

/**
 * 添加日期控件的监听；
 * 开始日期、结束日期 合理性控制
 *  如果时间选择不合理，自动同步为同一时间
 *
 */
function addDateLinster(options) {
    if (options == undefined) {
        options = {};
        options.drops = 'down';
        options.showDropdowns = true;
    }
    //开始日期选择
    $('#startDateTmp').daterangepicker({
        startDate: moment().startOf('hour'),
        endDate: moment().startOf('hour'),
        drops: options.drops,
        showDropdowns: options.showDropdowns
    }, function (selectedDate) {

        //获取enddate ，如果大于enddate 设置当前为值为endDate
        var selectedDateStr = selectedDate.format('YYYY-MM-DD');
        var endDateStr = $('#endDateTmp input').val();
        if (endDateStr != '' && selectedDateStr > endDateStr) {
            selectedDateStr = endDateStr;
        }

        $('#startDateTmp input').val(selectedDateStr).focus();
    });
    //结束日期选择
    $('#endDateTmp').daterangepicker({
        startDate: moment().startOf('hour'),
        endDate: moment().startOf('hour'),
        drops: options.drops,
        showDropdowns: true
    }, function (selectedDate) {
        //alert(selectedDate);
        //获取startdate ，如果小于startdate 设置当前为值为startdate
        var selectedDateStr = selectedDate.format('YYYY-MM-DD');
        var startdateStr = $('#startDateTmp input').val();
        if (startdateStr != '' && selectedDateStr < startdateStr) {
            selectedDateStr = startdateStr;
        }
        $('#endDateTmp input').val(selectedDateStr).focus();
    });
}
/**
 * 添加特殊字符验证；
 * @param message 提示信息
 * @param data 验证数据
 */
function specialCharValidate(message, data) {
    var specialCharReg = /[`~!@#\$%\^\*\(\)\+<>\?:"\{\},\.\\\/;'\[\]]/im;
    var specialCharRegone = /[^\a-\z\A-\Z0-9\u4E00-\u9FA5]/g;//只含有汉字、数字、字母
    var result = '';
    if (specialCharReg.test(data)) {
        result = '不能含有特殊字符';
    }
    if (specialCharRegone.test(data)) {
        result = '请输入中文、英文或数字';
    }
    if (result != '') {

        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '信息提示',
            content: alertError(result)
        });
        return true;
    }
}

/**
 *字符串转date（parserDate("2015-03-19 12::00:00")
 * @param 2015-03-19 12::00:00
 * @returns Date
 */
var parserDate = function (date) {
    return new Date(Date.parse(date.replace(/-/g, "/")));
};
/**
 *date转String  formatDate(Date())
 * @param date
 * @returns 2015-03-19
 */
var formatDate = function (date) {
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    m = m < 10 ? '0' + m : m;
    var d = date.getDate();
    d = d < 10 ? ('0' + d) : d;
    return y + '-' + m + '-' + d;
};

/**
 *date转String  formatDate(Date())
 * @param date
 * @returns 2015-03-19 12:00
 */
var formatDateTime = function (date) {
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    m = m < 10 ? ('0' + m) : m;
    var d = date.getDate();
    d = d < 10 ? ('0' + d) : d;
    var h = date.getHours();
    var minute = date.getMinutes();
    minute = minute < 10 ? ('0' + minute) : minute;
    return y + '-' + m + '-' + d + ' ' + h + ':' + minute;
};

/**
 * 返回loading效果
 * @param type 容器是div or table ->tbody
 * @param colspan table 合并列数
 * @returns {string}
 */
function loadinglst(type, colspan) {
    var tmp = [];
    var divTmp = [];
    divTmp.push(' 		<div class="dropload-load">');
    divTmp.push('			<span class="loading"></span>');
    divTmp.push('			加载中...');
    divTmp.push('		</div>');
    if (type == 'div') {
        tmp.push(divTmp.join(' '));
    } else {
        tmp.push('<tr>');
        tmp.push(' <td colspan="' + colspan + '">');
        tmp.push(divTmp.join(' '));
        tmp.push('	</td>');
        tmp.push('</tr>');
    }
    return tmp.join(' ');
};

/**
 * 判断浏览器版本
 */
(function (jQuery) {

    if (jQuery.browser) return;

    jQuery.browser = {};
    jQuery.browser.mozilla = false;
    jQuery.browser.webkit = false;
    jQuery.browser.opera = false;
    jQuery.browser.msie = false;
    jQuery.browser.netscape = false;

    var nAgt = navigator.userAgent;
    jQuery.browser.name = navigator.appName;
    jQuery.browser.fullVersion = '' + parseFloat(navigator.appVersion);
    jQuery.browser.majorVersion = parseInt(navigator.appVersion, 10);
    var nameOffset, verOffset, ix;
    // In Opera, the true version is after "Opera" or after "Version"
    if ((verOffset = nAgt.indexOf("Opera")) != -1) {
        jQuery.browser.opera = true;
        jQuery.browser.name = "Opera";
        jQuery.browser.fullVersion = nAgt.substring(verOffset + 6);
        if ((verOffset = nAgt.indexOf("Version")) != -1)
            jQuery.browser.fullVersion = nAgt.substring(verOffset + 8);
    }
    // In MSIE, the true version is after "MSIE" in userAgent
    else if ((verOffset = nAgt.indexOf("MSIE")) != -1) {
        jQuery.browser.msie = true;
        jQuery.browser.name = "Microsoft Internet Explorer";
        jQuery.browser.fullVersion = nAgt.substring(verOffset + 5);

    }
    // In Netscape, the true version is after "rv" in userAgent
    else if ((verOffset = nAgt.indexOf("rv")) != -1) {
        jQuery.browser.netscape = true;
        jQuery.browser.name = "Netscape";
        jQuery.browser.fullVersion = nAgt.substring(verOffset + 3);
    }
    // In Chrome, the true version is after "Chrome"
    else if ((verOffset = nAgt.indexOf("Chrome")) != -1) {
        jQuery.browser.webkit = true;
        jQuery.browser.name = "Chrome";
        jQuery.browser.fullVersion = nAgt.substring(verOffset + 7);

    }
    // In Safari, the true version is after "Safari" or after "Version"
    else if ((verOffset = nAgt.indexOf("Safari")) != -1) {
        jQuery.browser.webkit = true;
        jQuery.browser.name = "Safari";
        jQuery.browser.fullVersion = nAgt.substring(verOffset + 7);
        if ((verOffset = nAgt.indexOf("Version")) != -1)
            jQuery.browser.fullVersion = nAgt.substring(verOffset + 8);
    }
    // In Firefox, the true version is after "Firefox"
    else if ((verOffset = nAgt.indexOf("Firefox")) != -1) {
        jQuery.browser.mozilla = true;
        jQuery.browser.name = "Firefox";
        jQuery.browser.fullVersion = nAgt.substring(verOffset + 8);
    }
    // In most other browsers, "name/version" is at the end of userAgent
    else if ((nameOffset = nAgt.lastIndexOf(' ') + 1) < (verOffset = nAgt.lastIndexOf('/'))) {
        jQuery.browser.name = nAgt.substring(nameOffset, verOffset);
        jQuery.browser.fullVersion = nAgt.substring(verOffset + 1);
        if (jQuery.browser.name.toLowerCase() == jQuery.browser.name.toUpperCase()) {
            jQuery.browser.name = navigator.appName;
        }
    }
    // trim the fullVersion string at semicolon/space if present
    if ((ix = jQuery.browser.fullVersion.indexOf(";")) != -1)
        jQuery.browser.fullVersion = jQuery.browser.fullVersion.substring(0, ix);
    if ((ix = jQuery.browser.fullVersion.indexOf(" ")) != -1)
        jQuery.browser.fullVersion = jQuery.browser.fullVersion.substring(0, ix);

    jQuery.browser.majorVersion = parseInt('' + jQuery.browser.fullVersion, 10);
    if (isNaN(jQuery.browser.majorVersion)) {
        jQuery.browser.fullVersion = '' + parseFloat(navigator.appVersion);
        jQuery.browser.majorVersion = parseInt(navigator.appVersion, 10);
    }
    jQuery.browser.version = jQuery.browser.majorVersion;
})(jQuery);
