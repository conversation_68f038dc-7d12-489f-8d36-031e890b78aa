/*
 * 公共js
 */

var confirm = null;
//勾选checkbox处理
function checkBoxClick() {
    var chks = $('input[name="ids"]:checked');
    if (chks.size() > 1) {
        $('#deleteBtn').removeClass('content-chaxba');
        $('#updateBtn,#checkBtn').addClass('content-chaxba');
    } else {
        if (chks.size() == 1) {
            $('#updateBtn,#deleteBtn,#checkBtn').removeClass('content-chaxba');
        } else {
            $('#updateBtn,#deleteBtn,#checkBtn').addClass('content-chaxba');
        }
    }
}


/**
 * 日期控件初始化
 * @param divId 标签Id
 * @param formatString 日期格式化（例：YYYY/MM/DD）
 * @param startOf 显示到年月日时分秒（例：years、months、week、day）
 * @param separator 日期之间的分隔符
 */
function initDateTimerPicker(divId, formatString, startOf) {
    $('#' + divId).daterangepicker({
        startDate: moment().startOf(startOf),
        endDate: moment().startOf(startOf),
        //timePicker12Hour: false,
        showDropdowns: true
    }, function (start, end, label) {
        $('#' + divId + ' input').val(start.format(formatString));
        constraintDate();
    });
}

/**
 * 约束日期控件
 */
function constraintDate() {
    var startDate = $("#startDate input").val();
    var endDate = $("#endDate input").val();
    if (endDate < startDate) {
        var setDate = startDate != "" ? startDate : endDate != "" ? endDate : "";
        $("#startDate input").val(setDate);
        $("#endDate input").val(setDate);
    }
}

//添加按钮点击事件
function createBtnClick(obj) {
    var title = $(obj).text().substr(1, $(obj).text().length)
    confirm = $.confirm({
        title: title,
        content: 'url:a',
        animation: 'opacity',
        animationSpeed: 100,
        animationBounce: 1.2,
        columnClass: 'col-md-12', //大于992px窗体
        closeAnimation: 'scale',
        keyboardEnabled: true, //点击遮罩层关闭窗口回调和取消是同一个
        confirm: function () {
            $('form').submit();
            return false;
        }, onAction: function () {
        }
    });
}

//修改按钮点击事件
function updateBtnClick(obj, needChecked) {
    var checkBox = $("input:checked");
    if (checkBox.length == 1) {
        var id = checkBox.attr("value");
        if (needChecked) {
            $.ajax({
                url: "qcount?id=" + id,
                type: 'GET',
                dataType: 'json',
                contentType: 'application/json;charset=utf-8',
                success: function (data, textStatus, jqXHR) {
                    if (data.count > 0) {
                        $.alert({
                            animation: 'opacity',
                            closeAnimation: 'scale',
                            title: '提示',
                            content: data.msg
                        });
                    } else {
                        var title = $(obj).text().substr(1, $(obj).text().length)
                        confirm = $.confirm({
                            title: title,
                            content: 'url:q/id?id=' + id + '&type=0',
                            animation: 'opacity',
                            animationSpeed: 100,
                            animationBounce: 1.2,
                            columnClass: 'col-md-12', //大于992px窗体
                            closeAnimation: 'scale',
                            keyboardEnabled: true, //点击遮罩层关闭窗口回调和取消是同一个
                            confirm: function () {
                                $('form').submit();
                                return false;
                            }, onAction: function () {
                            }
                        });
                    }


                },
                error: function (er) {
                    $.alert({
                        animation: 'opacity',
                        closeAnimation: 'scale',
                        title: '错误提示',
                        content: '<div>调用后台服务出错</div>'
                    });
                }
            });
        } else {
            var title = $(obj).text().substr(1, $(obj).text().length)
            confirm = $.confirm({
                title: title,
                content: 'url:q/id?id=' + id + '&type=0',
                animation: 'opacity',
                animationSpeed: 100,
                animationBounce: 1.2,
                columnClass: 'col-md-12', //大于992px窗体
                closeAnimation: 'scale',
                keyboardEnabled: true, //点击遮罩层关闭窗口回调和取消是同一个
                confirm: function () {
                    $('form').submit();
                    return false;
                }, onAction: function () {
                }
            });
        }

    } else {
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content: '请选择一条记录！'
        });
    }

}

//查看按钮点击事件
function checkBtnClick(obj) {
    var checkBox = $("input:checked");
    if (checkBox.length == 1) {
        var id = checkBox.attr("value");
        var title = $(obj).text().substr(1, $(obj).text().length)
        $.confirm({
            title: title,
            content: 'url:q/id?id=' + id + '&type=1',
            animation: 'opacity',
            animationSpeed: 100,
            animationBounce: 1.2,
            columnClass: 'col-md-12', //大于992px窗体
            closeAnimation: 'scale',
            keyboardEnabled: true, //点击遮罩层关闭窗口回调和取消是同一个
            confirm: function () {
            }
        });
    } else {
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content: '请选择一条记录！'
        });
    }

}

//删除按钮点击事件
function deleteBtnClick(obj) {
    var checkBox = $("input:checked");
    if (checkBox.length > 0) {
        var idArray = new Array();
        for (i = 0; i < checkBox.length; i++) {
            if (checkBox[i].checked == true) {
                idArray.push(checkBox[i].value);
            }
        }
        //var json = JSON.parse(idArray)
        var idArrStr = idArray.join(",");
        //console.log(idArrStr);
        $.confirm({
            title: $(obj).text(),
            content: "确认删除吗？",
            animation: 'opacity',
            animationSpeed: 100,
            animationBounce: 1.2,
            closeAnimation: 'scale',
            keyboardEnabled: true, //点击遮罩层关闭窗口回调和取消是同一个
            confirm: function () {
                $.ajax({
                    url: "d",
                    type: "GET",
                    cache: false,
                    data: {"idArray": idArrStr},
                    //beforeSend: function () { },//$("#publish").attr("disabled", "disabled"); },
                    success: function (data) {
                        $.alert({
                            animation: 'opacity',
                            closeAnimation: 'scale',
                            title: '提示',
                            content: JSON.parse(data).msg
                        });
                        search(1);
                    }
                });
            }
        });

    } else {
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content: '请至少选择一条记录！'
        });
    }

}

/**
 * 分页查询
 * @param pageNo 第几页
 * @param obj    查询条件对象
 * @param tableId table标签id
 */
function gotoPage(pageNo, obj, tableId, url) {
    var urlParam = jsonToUrlParam(obj, null);
    $.ajax({
        url: url + "?" + urlParam,
        type: 'GET',
        dataType: 'text',
        contentType: 'application/json;charset=utf-8',
        success: function (data, textStatus, jqXHR) {
            $('#' + tableId).html(data);
            //设置页面控件
            initPaginator(pageNo);

        },
        error: function (er) {
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '错误提示',
                content: '<div>调用后台服务出错</div>'
            });
        }
    });
}

/**
 * 将json对象转换成URL参数格式
 * @param param json对象
 * @param key   键（用于对象含有内子元素）
 * @returns  url参数字符串
 */
function jsonToUrlParam(param, key) {
    var paramStr = '';
    if (isEmpty(param)) {
        return paramStr;
    }

    if (param instanceof String || param instanceof Number || param instanceof Boolean) {

        paramStr += "&" + key + "=" + encodeURIComponent(param);
    }
    else {
        $.each(param, function (i) {
            var k = key == null ? i : key + (param instanceof Array ? "[" + i + "]" : "." + i);
            paramStr += '&' + jsonToUrlParam(this, k);

        });
    }
    return paramStr.substr(1);
};

/**
 * 将url参数转换成json
 * @param url url地址
 * @returns json对象
 */
function urlParamToJSON(url) {
    var obj = {};
    var keyvalue = [];
    var key = "", value = "";
    var paraString = url.substring(url.indexOf("?") + 1, url.length).split("&");
    for (var i in paraString) {
        keyvalue = paraString[i].split("=");
        key = keyvalue[0];
        value = keyvalue[1];
        obj[key] = value;
    }
    return obj;
}

/**
 * 判断字符串是否为空
 * @param str 需要判断的字符串
 * @returns {boolean}
 */
function isEmpty(str) {
    if (str == null || str == "" || str.length < 1 || str == undefined) {
        return true;
    } else {
        return false;
    }
}
/**
 * 初始化分页控件
 */
function initPaginator(pageNo) {
    var options = {
        currentPage: pageNo,  //直接跳转到特定的page
        totalPages: $("#pageCount").val(),
        listContainerClass: 'pagination', //设置分页list容器 class
        useBootstrapTooltip: false,
        onPageClicked: function (event, originalEvent, type, page) {
            search(page);
        }
    };
    $("#pageDiv").bootstrapPaginator(options);
}
/**
 * 初始化弹窗中的分页
 * @param pageNo 页数
 * @param pageCountTabId 总也数标签id
 * @param pageDivId 分页组件id
 */
function initPaginatorDialog(pageNo, pageCountTabId, pageDivId) {
    var options = {
        currentPage: pageNo,  //直接跳转到特定的page
        totalPages: $("#" + pageCountTabId).val(),
        listContainerClass: 'pagination', //设置分页list容器 class
        useBootstrapTooltip: false,
        onPageClicked: function (event, originalEvent, type, page) {
            search(page);
        }
    };
    $("#" + pageDivId).bootstrapPaginator(options);
}

//绑定每页显示条数选择框改变事件
function bindPageSizeSelectChanged() {
    $('#pageSizeSelect ul li').on('click', function () {
        if ($(this).text() != pageSize) {
            pageSize = $(this).text();
            search(1);
        }
    });
}

//checkbox全选反选事件
function bindCheckBoxSelectAll(obj) {
    //console.log($(obj).is(':checked'))
    $(":checkbox").prop("checked", $(obj).is(':checked'))
    $(obj).prop("checked", $(obj).is(':checked'))
}

//省市县三级联动
function initSelect() {
    $("#provinceSelect").citySelect({
        prov: "北京",
        nodata: "none"
    });
}

/**
 * 保存
 * @param formId form表单id
 */
function saveOrUpdateSubmit(url, formId) {
    var url = $("form").attr("action");
    var json = $("form").serialize();
    if (isEmpty(url) || isEmpty(json)) {
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '错误提示',
            content: '参数不合法！'
        });
        return false;
    }
    $.ajax({
        url: url,
        type: 'POST',
        async: false,
        data: json,
        dataType: 'json',
        success: function (data, textStatus, jqXHR) {
            successHandlePrompt(data);
        },
        error: function (er) {
            var msg = "<div>调用后台服务出错[" + er.status + "]: " + er.statusText + "</div>";
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '错误提示',
                content: msg
            });
        }
    });


}

/**
 * 操作提示
 * @param data
 */
function successHandlePrompt(data) {
    if (!isEmpty(data.id)) {
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content: '<div>操作成功！</div>'
        });
        confirm.close();
    } else {
        var msg = "<div>";
        for (key in data.errorMsg) {
            msg += key + ": " + data.errorMsg[key] + "<br>";
        }
        msg += "</div>";

        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '错误提示',
            content: msg
        });
    }
}


/**
 * Simple Map
 *
 *
 * var m = new Map();
 * m.put('key','value');
 * ...
 * var s = "";
 * m.each(function(key,value,index){
 *         s += index+":"+ key+"="+value+"\n";
 * });
 * alert(s);
 *
 * <AUTHOR>
 * @date 2008-05-24
 */
function Map() {
    /** 存放键的数组(遍历用到) */
    this.keys = new Array();
    /** 存放数据 */
    this.data = new Object();

    /**
     * 放入一个键值对
     * @param {String} key
     * @param {Object} value
     */
    this.put = function (key, value) {
        if (this.data[key] == null) {
            this.keys.push(key);
        }
        this.data[key] = value;
    };

    /**
     * 获取某键对应的值
     * @param {String} key
     * @return {Object} value
     */
    this.get = function (key) {
        return this.data[key];
    };

    /**
     * 删除一个键值对
     * @param {String} key
     */
    this.remove = function (key) {
        this.keys.remove(key);
        this.data[key] = null;
    };

    /**
     * 遍历Map,执行处理函数
     *
     * @param {Function} 回调函数 function(key,value,index){..}
     */
    this.each = function (fn) {
        if (typeof fn != 'function') {
            return;
        }
        var len = this.keys.length;
        for (var i = 0; i < len; i++) {
            var k = this.keys[i];
            fn(k, this.data[k], i);
        }
    };

    /**
     * 获取键值数组(类似Java的entrySet())
     * @return 键值对象{key,value}的数组
     */
    this.entrys = function () {
        var len = this.keys.length;
        var entrys = new Array(len);
        for (var i = 0; i < len; i++) {
            entrys[i] = {
                key: this.keys[i],
                value: this.data[i]
            };
        }
        return entrys;
    };

    /**
     * 判断Map是否为空
     */
    this.isEmpty = function () {
        return this.keys.length == 0;
    };

    /**
     * 获取键值对数量
     */
    this.size = function () {
        return this.keys.length;
    };

    /**
     * 重写toString
     */
    this.toString = function () {
        var s = "{";
        for (var i = 0; i < this.keys.length; i++, s += ',') {
            var k = this.keys[i];
            s += k + "=" + this.data[k];
        }
        s += "}";
        return s;
    };
}

//初始化富文本编辑器
function initEditor() {
    if ($('.summernote').length > 0) {
        $('.summernote').summernote({
            lang: 'zh-CN', width: '100%', height: 300, disableResizeEditor: true,
            onkeydown: function (e) {
                $('textarea[name="content"]').text($(this).code());
            }
        });
    }
}
//显示tableloading
function showTableLoading(id) {

    $("#" + id).html("<tr> <td colspan='20'><div class='loding-div'><p><img width='82' height='82' src='/images/logoding.gif'>加载中，请稍候...</p> </div></td>")
    //$("#"+id).html("<div class='loding-div'><p><img width='82' height='82' src='../images/logoding.gif'>加载中，请稍候...</p> </div>")
}
//显示层loading
function showDivLoading(id) {
    $("#" + id).html("<div class='loding-div'><p><img width='82' height='82' src='/images/logoding.gif'>加载中，请稍候...</p> </div>")
}
