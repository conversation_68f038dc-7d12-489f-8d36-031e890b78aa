/**
 * Created by r<PERSON><PERSON> on 2016/4/14.
 */
var provCityMap = {};
var provDistMap = {};
var distObj={};
var cityObj={};
var provObj={};
/**
 * 地区选择的级联操作
 */
function cascadeDist(){
    distObj = $('#dist');
    cityObj = $('#city');
    provObj = $('#prov');
    //下拉框的基本操作
    addDistEvent(distObj);
    addDistEvent(cityObj);
    addDistEvent(provObj);
}
/**
 * 地区的选择
 */
function addDistEvent(obj){
    //菜单选中事件
    obj.delegate(' .select-list ul li','click', function(event) {
        //选中的子菜单
        var $this = $(this);
        var dataid = $this.attr('data-value');
        var selectedBtn = obj.find('.select-button');
        var orgnDataId = selectedBtn.attr('data-id');
        if(dataid!==orgnDataId){//如果发生了变化
            selectedBtn.attr('data-id',dataid==undefined?'':dataid);
            var objectid = obj.attr('id');
            if(objectid=='city'){
                doSelectChange(dataid,provDistMap,distObj);
            }else if(objectid=='prov'){
                doSelectChange(dataid,provCityMap,cityObj);
            }
        }
    });
}
/**
 *执行选中操作
 * @param key 当前选中的菜单Id
 * @param dataMap 客户端缓存map
 * @param nextObj 要处理的下一级 下拉框
 */
function doSelectChange(key,dataMap,nextObj){
    //重置“city”，和“dist”
    var dataArray=dataMap[key];//从缓存取出数据
    if(dataArray){//如果缓存里没有数据，从库里取出最新值，同时替换html
        claerSelectMenu(distObj);
        claerSelectMenu(nextObj);
        nextObj.find('.select-list ul').html(liHtml(dataArray));
    }else{
        var ticker = new Date().getTime();
        var url='/district?ticker='+ticker+'&parentId='+key;
        $.get(url,function (data) {
            var jsonObject = JSON.parse(data);
            dataMap[key]=jsonObject;
            claerSelectMenu(distObj);
            claerSelectMenu(nextObj);
            nextObj.find('.select-list ul').html(liHtml(jsonObject));
        });
    }
}
/**
 * 清空下拉控件
 */
function claerSelectMenu(obj){
    var selectedBtn = obj.find('.select-button');
    selectedBtn.attr('data-id','');
    selectedBtn.val('请选择');
    obj.find('.select-list ul').html('<li class="selected" data-value="" title="请选择">请选择</li>');
}
/**
 * 根据数据集拼装html
 * @param dataArray
 * @returns {string}
 */
function  liHtml(dataArray){
    var liArray = new Array();
    for(var i=0;i<dataArray.length;i++){
        var lidata = dataArray[i];
        liArray.push('<li data-value="' +lidata.id+'" title="' +lidata.name+ '">' +lidata.name+'</li>');
    }
    return liArray.join('');
}
/**
 * 对外提供数据（当前选中的 省、市、区县）
 * @returns {{}}
 */
function  fetchDistrict(){
    var district = {};
    var provBtn = provObj.find('.select-button');
    var cityBtn = cityObj.find('.select-button');
    var distBtn = distObj.find('.select-button');
    district.prov={};
    district.prov.id= provBtn.attr('data-id');
    district.prov.name= $.trim(provBtn.val());
    district.city={};
    district.city.id= cityBtn.attr('data-id');
    district.city.name= $.trim(cityBtn.val());
    district.dist={};
    district.dist.id= distBtn.attr('data-id');
    district.dist.name= $.trim(distBtn.val());
    return district;
}