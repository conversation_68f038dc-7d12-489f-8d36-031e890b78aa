$(function () {
    $('#goodsDetail').summernote({
        lang: 'zh-CN', width: 908, height: 300, disableResizeEditor: true,
        callbacks: {
            onImageUpload: function (files, editor, editable) {
                uploadImage(editor, editable);
            }
        }
    });
    $('#postitionReqirmentDetail').summernote({
        lang: 'zh-CN', width: 684 , height: 200, disableResizeEditor: true,
        callbacks: {
            onImageUpload: function (files, editor, editable) {
                uploadImage(editor, editable);
            }
        }
    });
    $('#summernote').summernote({
        lang: 'zh-CN', width: 950 , height: 200, disableResizeEditor: true,
        callbacks: {
            onImageUpload: function (files, editor, editable) {
                uploadImage(editor, editable);
            }
        }
    });
});

function uploadImage(editor, editable) {
    var obj = $(".note-modal-form.nice-validator.n-default");
    $(obj).attr("enctype", "multipart/form-data");

    var formData = new FormData();
    var fileInput = $(".note-image-input")[0];
    if (fileInput.files.length > 0) {
        formData.append('file', fileInput.files[0]);
    }

    $.ajax({
        type: 'POST',
        url: updImgDomain + "/uploadImages",
        data: formData,
        processData: false,  // 告诉jQuery不要处理发送的数据
        contentType: false,  // 告诉jQuery不要设置内容类型头
        success: function (data) {/*url为上传成功后返回的图片路径*/
            var result = data
            if (result.status == "SUCCESS") {
                editor.insertImage(imgDomain+result.data);
            }
            else {
                var message=result.message==""?"上传失败":result.message;
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '信息提示',
                    content: alertError(message)
                });
            }

        },
        error: function (msg) {
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '信息提示',
                content: alertError('上传失败。')
            });
        }

    });
    return false;
}

