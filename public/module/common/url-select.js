/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2016-09-18 .
 */

(function($) {
    $.fn.urlSelect = function(options) {
        var _this = this;
        var defaults = {
            data : '', /*域名url ，如：http://weixin.bio.ronglian.com，如果没有给出的是相对地址 */
            onClick : function() {   /* 回调函数 给出url等需要的数据*/
                $.noop();
            }
        };
        var setOption = $.extend(defaults, options || {});
        function foderOrExpand( $this){
            var dropdownUL = $this.parent('.sm-opts.dropdown.hover').find('ul.dropdown-menu');
            if(dropdownUL.attr('style').trim()=='display:none;'||dropdownUL.attr('style').trim()=='display: none;'){
                dropdownUL.attr('style','display:block;');
            }else{
                dropdownUL.attr('style','display:none;');
            }
        };
        var _pageNo=1;
        /**
         * 初始化
         */
        function initDataSelectionLinster(dataType){
            //添加选中监听
            $('#indexData').delegate('a[name="selection"]','click',function(e){
                var $this = $(this);
                $('a[name="selection"]').each(function(i, el){
                    $(el).removeClass('sm-waix-aon');
                });
                $this.addClass('sm-waix-aon');
            });
            //初始化分页方法
            initPaginator(_pageNo);
            //查询操作
            $('#queryBtn').on('click',function(){
                _dataSearch(_pageNo);
            });
            _dataSearch(_pageNo);
        };

        /**
         * 收集选中的数据主键
         */
        function getSelectedData(){
            var selectionData = {};
            $('a[name="selection"]').each(function(i, el){
                if($(el).hasClass('sm-waix-aon')){
                    selectionData.id = $(el).attr('data-id');
                    selectionData.name = $(el).attr('data-name');
                    selectionData.obj = JSON.parse($(el).attr('data-obj'));
                }
            });
            return selectionData;
        };

        /**
         * 初始化分页方法
         */
        function initPaginator(pageNo){
            var totalPages = $("#_cusPageCount").val();
            var options = {
                currentPage: pageNo,  //直接跳转到特定的page
                totalPages: totalPages,//总页数
                useBootstrapTooltip: false,
                onPageChanged: function (event, oldPage, newPage) {
                    //_pageNo=pageNo;
                    _dataSearch(newPage);
                }
            }
            $('#example').bootstrapPaginator(options);
        };


        /**
         * 执行查询操作
         * @private
         */
        function _dataSearch(pageNo){
            var condiVo = {};
            condiVo.dataName = $.trim($('#dataName').val());
            condiVo.pageNo=pageNo;
            condiVo.dataType =$.trim($('#dataPage').attr('data-id'));
            showTableLoading('indexData');
            $.post('/us/q', condiVo, function (data) {
                //_pageNo=pageNo;
                $('#indexData').empty().html(data);
                initPaginator(pageNo);
            });
        };
        /**
         * 数据选择
         *
         */
        function dataSelection($this ,title,selectUrl,singleMenuData){
            $.confirm({
                animation: 'opacity',
                closeAnimation: 'scale',
                columnClass: 'col-md-12',
                title:title,
                confirmTop:'4.5%',
                confirmButton:'确定',
                content: function ($obj) {
                    return $.get(selectUrl, function (data) {
                        $obj.setContent(data);
                        //添加选择监听
                        initDataSelectionLinster(singleMenuData.type);
                    });
                },
                confirm: function (){//确定选择
                    var selectData = getSelectedData();
                    if(selectData.id){
                        singleMenuData.data.id=selectData.id;
                        singleMenuData.data.name=selectData.name;
                        singleMenuData.data.obj=selectData.obj;
                        singleMenuData.url=singleMenuData.url+'/'+selectData.id;
                        setOption.onClick($this,singleMenuData);
                        return true;
                    }else{
                        $.alert({
                            animation: 'opacity',
                            closeAnimation: 'scale',
                            title: '提示',
                            content:alertWarning('请选择一条记录')
                        });
                        return false;
                    }
                }
            });
        };

        /**
         * 其他链接，站外链接
         *
         */
        function otherUrl($this ,singleMenuData){
            $.confirm({
                animation: 'opacity',
                closeAnimation: 'scale',
                columnClass: 'col-md-6',
                title:'站外链接设置',
                confirmTop:'4.5%',
                confirmButton:'确定',
                content: function ($obj) {
                    return $.get('/us?dataType=OTHERS&dataTime='+new Date().getTime(), function (data) {
                        $obj.setContent(data);
                    });
                },
                confirm: function (){//确定设置
                    var selectData = $.trim($('#otherUrl').val());
                    if(selectData==''){
                        $.alert({
                            animation: 'opacity',
                            closeAnimation: 'scale',
                            title: '提示',
                            content:alertWarning('请填写站外URL')
                        });
                        return false;
                    }else if(!(selectData.indexOf('http://')==0||selectData.indexOf('https://')==0)){//格式不正确
                        $.alert({
                            animation: 'opacity',
                            closeAnimation: 'scale',
                            title: '提示',
                            content:alertWarning('请输入完整的站外链接')
                        });
                        return false;
                    }else{//
                        singleMenuData.data.id=selectData;
                        singleMenuData.url=selectData;
                        setOption.onClick($this,singleMenuData);
                        return true;
                    }
                }
            });
        };
        function openSelect($this,dataType){
            var domainUrl = '';
            if(typeof setOption.data !='object') {
                domainUrl = setOption.data;
            }
            /**
             * {
                      "type":"GOODS",
                      "url":"/goods",
                      "data":"e47f69a982034930ad198e8e2f07ae30"//数据
                    }
             * @type {{}}
             */
            var singleMenuData = {};
            singleMenuData.type=dataType;
            singleMenuData.data = {};
            switch (dataType){
                case 'GOODS'://商品
                    singleMenuData.url = domainUrl+'/goods/qd';
                    dataSelection($this, '商品选择','/us?dataType=GOODS&dataTime='+new Date().getTime(),singleMenuData);
                    break;
                case 'GOODS_LIST'://商品列表
                    singleMenuData.url = domainUrl+'/goods/index';
                    setOption.onClick($this,singleMenuData);
                    break;
                case 'NEWS'://新闻
                    singleMenuData.url = domainUrl+'/news/qd';
                    dataSelection($this, '新闻选择','/us?dataType=NEWS&dataTime='+new Date().getTime(),singleMenuData);
                    break;
                case 'NEWS_LIST'://新闻列表
                    singleMenuData.url = domainUrl+'/news/index';
                    setOption.onClick($this,singleMenuData);
                    break;
                case 'SAMPLE_BIND'://样品绑定
                    singleMenuData.url = domainUrl+'/sampling/s';
                    setOption.onClick($this,singleMenuData);
                    break;
                case 'REPORT_PROCESS'://报告进度
                    singleMenuData.url = domainUrl+'/sampling/ss';
                    setOption.onClick($this,singleMenuData);
                    break;
                case 'CUST_PAGE'://自定义页面
                    singleMenuData.url = domainUrl+'/custpage/by/page';
                    dataSelection($this, '自定义页面选择','/us?dataType=CUST_PAGE&dataTime='+new Date().getTime(),singleMenuData);
                    break;
                case 'PAGE_MGR'://页面管理
                    singleMenuData.url = domainUrl+'/websitecontent/index';
                    dataSelection($this, '页面选择','/us?dataType=PAGE_MGR&dataTime='+new Date().getTime(),singleMenuData);
                    break;
                case 'REPORT_CENTER'://报告中心
                    singleMenuData.url = domainUrl+'/report/index';
                    setOption.onClick($this,singleMenuData);
                    break;
                case 'MY_ORDER'://我的订单
                    singleMenuData.url = domainUrl+'/order';
                    setOption.onClick($this,singleMenuData);
                    break;
                case 'OTHERS'://其他链接
                    otherUrl($this,singleMenuData);
                    break;

                default://说明是控制折叠展开的
                    foderOrExpand($this);
            }

        };
        if(setOption.dataType!=undefined&&setOption.dataType!=''){
            return  openSelect( $(this),setOption.dataType);
        }else{
            return  $(this).each(function(index,el) {
                $(this).undelegate('.sm-select-link','mouseleave');//.sm-opts.dropdown.hover
                $(this).delegate('.sm-select-link','mouseleave',function(){
                    var $this = $(this);
                    var dropdownUL = $this.find('.sm-opts.dropdown.hover ul.dropdown-menu');
                    dropdownUL.attr('style','display:none;');
                });
                $(this).undelegate('a[name="url-select"]','click');
                $(this).delegate('a[name="url-select"]','click',function(e){
                    var $this = $(this), dataType = $this.attr('data-type');
                    openSelect($this,dataType);
                });
            });
        }
    }

    /**
     * 构件选择器的html
     * @returns {string}
     */
    $.fn.urlSelect.bulidSelectHtml= function(){
        var selectHtml =    '<div class="sm-select-link">' +
            '<span class="sm-change-txt float-l">链接：</span><span class="sm-main-link">' +
            '<a name="url-select"  data-type="GOODS" href="javascript:void(0);">商品</a><a  name="url-select" data-type="GOODS_LIST" href="javascript:void(0);">商品列表</a>' +
            '<a  name="url-select" data-type="NEWS" href="javascript:void(0);">新闻</a><a  name="url-select" data-type="NEWS_LIST" href="javascript:void(0);">新闻列表</a><a name="url-select" data-type="SAMPLE_BIND" href="javascript:void(0);">样品绑定</a></span>' +
            '<div class="sm-opts dropdown hover"><a  name="url-select" class="dropdown-toggle" href="javascript:void(0);">其他<i class="caret"></i></a>' +
            '<ul class="dropdown-menu" style=" display:none;"><li><a  name="url-select" data-type="REPORT_PROCESS" href="javascript:void(0);">报告进度</a></li>' +
            '<li><a name="url-select" data-type="CUST_PAGE" href="javascript:void(0);">自定义页面</a></li><li><a  name="url-select" data-type="PAGE_MGR" href="javascript:void(0);">页面管理</a></li>' +
            '<li><a name="url-select" data-type="REPORT_CENTER" href="javascript:void(0);">报告中心</a></li><li><a name="url-select" data-type="MY_ORDER"  href="javascript:void(0);">我的订单</a></li>' +
            '<li><a  name="url-select" data-type="OTHERS"  href="javascript:void(0);">其他链接</a></li></ul></div> </div> ' +
            '</div> ' ;
        return selectHtml;
    }

})(jQuery);