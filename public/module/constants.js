/**
 * Created by yubin on 2016/2/3.
 */
/**
 * 后台接口传回的错误信息Key，对映一个错误信息字符串数组
 */
var ERROR_KEY = 'object.error.message';

//图片域名
var imgDomain="https://im1.ronglian.com:8103";
//var imgDomain="http://10.0.99.26:8103";
var updImgDomain="http://10.0.99.26:8103";
//备注类型
var REMARK_TYPE_ORDER = 0;
var REMARK_TYPE_ACCOUNT_CHECK = 1;
/**
 * 备注每页行数限制
 * @type {number}
 */
var REMARK_COUNT_LIMIT = 5;
/**
 * 页面栏目数量限制
 * @type {number}
 */
var PAGE_TABS_COUNT_LIMIT = 8;

var CONST_SUCCESS = 'SUCCESS';
var CONST_FAILURE = 'FAILURE';

//组件url
//产品详情页模板
var MU_PRODUCT_INDEX = 'product/index';
//自定义空模版
var MU_CONTENT_INDEX = 'content/index';
