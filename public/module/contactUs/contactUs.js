/**
 * Created by r<PERSON><PERSON> on 2017/5/8.
 */
var contactUsPageNo = 1; //当前页码
var contactUsPageSize = $('#contactUs-content input.hiddenId').val();
var contactUsStr;
//页面初始
$(document).ready(function () {
    //改变查看页大小
    $('#contactUs-content .in-pageNo ul').delegate('li', 'click', function () {
        if ($(this).text() != contactUsPageSize) {
            contactUsPageSize = $(this).text();
            setPageSizeCookie(contactUsPageSize);
            contactUsSearch(contactUsPageNo, contactUsPageSize);
        }
    });
    //查询加载列表
    contactUsSearch(contactUsPageNo, contactUsPageSize);
    $('#addContactUs').on('click', function () {
        addContactUs();
    });
    //编辑e
    $('#indexData').delegate('a[name=contactUsEdit]', 'click', function () {
        var thisId = $(this).attr('data-id');
        showContactUsEdit(thisId);
    });
    //删除联系我们
    $('#indexData').delegate('a[name=contactUsDelete]', 'click', function () {
        var thisId = $(this).attr('data-id');
        var contactUsVo = {};
        contactUsVo.contactId = thisId;
        var companyName = $('#companyNameVo').val();
        contactUsVo.companyName = companyName;
        $.confirm({
            animation: 'opacity',
            closeAnimation: 'scale',
            confirmButton: '确定',
            title: '联系我们删除',
            content: alertWarning('确定要删除此公司吗？'),
            confirm: function () {
                deleteContactUs(contactUsVo);
            }
        });
    });
    //查询按钮
    $('#btnQuery').on('click', function () {
        //收集查询条件
        contactUsSearch(contactUsPageNo, contactUsPageSize);
    });
    /**
     * 键盘事件
     */
    $("body").keydown(function(e){
        var curKey = e.which;
        if(curKey == 13){
            $("#btnQuery").click();
        }
    });
});

//执行新增联系我们
function addContactUs() {
    contactUsStr=$.confirm({
        confirmButtonPlural: "保存",
        animation: 'opacity',
        confirmTop: '10%',
        closeAnimation: 'scale',
        columnClass: 'col-md-6 col-md-offset-3',
        title: '添加联系我们',
        confirmButton: false,
        content: function ($obj) {
            var contactUsVo = {};
            contactUsVo.contactId = null;
            return $.post('/contactUs/s', contactUsVo, function (data) {
                $obj.setContent(data);
            });
        },
        confirmPlural: function () {
            if(!validContactUsEdit()){
                return false;
            }else{
            return doSave();
            }
        }
    });
}

/**
 * 校验联系我们
 */
function validContactUsEdit() {
    if ($('#contactUsAddForm').isValid()) {
        return true;
    }
}

/**
 * 弹出修改联系我们
 * @param agencyId
 */
function showContactUsEdit(contactId) {
    var currentContactUs = {};
    var currentTR = $('#tr' + contactId);
    var tds = currentTR.children('td');
    currentContactUs.contactId = contactId;
    currentContactUs.sort = tds.eq(0).text();
    currentContactUs.companyName   = tds.eq(1).text();
    currentContactUs.address = tds.eq(2).text();
    currentContactUs.postcode = tds.eq(3).text();
    currentContactUs.phone = tds.eq(4).text();
    currentContactUs.fax = tds.eq(5).text();
    $.confirm({
        confirmButtonPlural: '保存',
        animation: 'opacity',
        confirmTop: '10%',
        closeAnimation: 'scale',
        columnClass: 'col-md-6 col-md-offset-3',
        title: '修改联系我们',
        confirmButton: false,
        content: function ($obj) {
            return $.post('/contactUs/s',currentContactUs, function (data) {
                $obj.setContent(data);
            });
        },
        confirmPlural: function () {
            if (!$('#contactUsEditForm').isValid()) {
                return false;
            }
            doSave();
        }
    });
}

/**
 * 联系我们保存和修改
 */
function doSave() {
    var contactId = $('#contactId').val();
    if (contactId) {//修改操作
        var userData = collectContactUsInfos(contactId);
        userData.contactId = contactId;
        $.post('/contactUs/u', userData, function (data) {
            if (doCallBackAlert('联系我们修改成功', data)) {
                contactUsSearch(contactUsPageNo, contactUsPageSize);
                contactUsStr.close();
            }
        });
    } else {
        var userData = collectContactUsInfos('');
        $.post('/contactUs/a', userData, function (data) {
            if (doCallBackAlert('联系我们添加成功', data)) {
                contactUsSearch(contactUsPageNo, contactUsPageSize);
                contactUsStr.close();
            }
        });
    }
    return false;
}

/**
 * 删除联系我们
 * @param
 */
function deleteContactUs(agencyVo) {
    $.post('/contactUs/d', agencyVo, function (data){
        if(doCallBackAlert('联系我们删除成功',data)){
            contactUsSearch(contactUsPageNo, contactUsPageSize);
        }
    });
}

/**
 * 收集添加信息
 */
function collectContactUsInfos(contactUsId) {
    //顺序
    var sort = $('#sort').val();
    //公司名称
    var companyName = $('#companyName').val();
    //地址
    var address = $('#address').val();
    //邮编
    var postcode = $('#postcode').val();
    //电话
    var phone = $('#phone').val();
    //传真
    var fax = $('#fax').val();

    var contactUsStr = {};
    contactUsStr.sort = sort;
    contactUsStr.companyName = companyName;
    contactUsStr.address = address;
    contactUsStr.postcode = postcode;
    contactUsStr.phone = phone;
    contactUsStr.fax = fax;
    return contactUsStr;
}





//查询办事处
function contactUsSearch(pageNo, pageSize) {
    showTableLoading('indexData');
    var companyName = $.trim($('#q_contactUsName').val());
    var contactUsConditionVo = {};
    contactUsConditionVo.pageSize = pageSize == null ? $('#contactUs-content input.hiddenId').val() : pageSize;
    contactUsConditionVo.pageNo = pageNo;
    contactUsConditionVo.companyName = companyName;
    contactUsConditionVo.ticker = new Date().getTime();
    $.post('/contactUs/q', contactUsConditionVo, function (data) {
        $('#indexData').empty().html(data);
        var totalPages = $("#cusPageCount").val();
        var options = {
            currentPage: pageNo,  //直接跳转到特定的page
            totalPages: totalPages,//总页数
            useBootstrapTooltip: false,
            onPageChanged: function (event, oldPage, newPage) {
                contactUsSearch(newPage, pageSize);
            }
        }
        $('#example').bootstrapPaginator(options);
    });
}