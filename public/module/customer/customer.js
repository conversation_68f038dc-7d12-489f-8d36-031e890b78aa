/**
 * Created by r<PERSON><PERSON> on 2016/2/3.
 */
var pageNo = 1; //当前页码
var pageSize = $('#customer-content input.hiddenId').val();
var sortIndex='create_on desc';
$(document).ready(function () {
    $('input[type="text"]').val("");
    initPage(pageNo, pageSize);
    $("#iSearch").attr("onclick", "customerSearch(1)");
    //全选、全清
    $('#customerAll').on('click', function () {
        var $this = $(this);
        var checked = $this.is(':checked');
        $('input[type="checkbox"][name="customerCheck"]').each(function(i, el){
            $(el).prop('checked', checked);
        });
    });
    //改变查看页大小
    $('#customer-content .in-pageNo ul').delegate('li', 'click', function () {
        if ($(this).text() != pageSize) {
            pageSize = $(this).text();
            setPageSizeCookie(pageSize);
            customerSearch(pageNo, pageSize);
        }
    });
    $('#indexData').delegate('a[name=operation-a]', 'click', function () {
        showDelete(this,'publish');
    });
    $('#indexData').delegate('a[name=operation-ab]', 'click', function () {
        showCustomerInfo(this);
    });
    $('#indexData').delegate('a[name=operation-ac]', 'click', function () {
        showDelete(this,'pause');
    });
    //创建注册时间排序
    $('#sortCreateTime').on('click', function () {
        sortClick('create_on',this)
    });
    //登录时间排序
    $('#sortLoginTime').on('click', function () {
        sortClick('login_time',this);
    });

    //会员导出
    $('#customerExport').on('click', function () {
        //收集选中IDs
        var selectedIds = collectSelected();
        if(selectedIds.length==0){//导出全部
            $.confirm({
                animation: 'opacity',
                closeAnimation: 'scale',
                confirmButton: true,
                confirmButton:'确定',
                title: '导出全部会员',
                content: alertWarning('没有选中的会员，确定导出当前所有会员吗？'),
                confirm: function () {
                    doExportExcel(null);
                }
            });
        }else{//导出选中记录
            doExportExcel(selectedIds);
        }
    });

});

/**
 *执行导出
 */
function  doExportExcel(selectedIds){
    var customerConditionVo =collectCustomerConditionVo(pageNo, pageSize);
    customerConditionVo.ids=JSON.stringify(selectedIds);
    customerConditionVo.ticker=new Date().getTime();
    var url=$.param(customerConditionVo);
    window.location.href = '/excel/customers?'+url;
}

/**
 * 收集选中的会员
 */
function collectSelected(){
    var array = new Array();
    $('input[type="checkbox"][name="customerCheck"]').each(function(i, el){
        if( $(el).is(':checked')){
            array.push($(el).attr('data-id'));
        }
    });
    return array;
}
//排序触发
function sortClick(sortDate,obj) {
    var btn = $(obj).find('btn-a');
    if (btn.hasClass('clickon-a')) {
        btn.removeClass('clickon-a');
        btn.addClass('click-a');
        sortIndex = sortDate + ' asc';//正序
    } else {
        btn.removeClass('click-a');
        btn.addClass('clickon-a');
        sortIndex = sortDate + ' desc';//倒序
    }
    customerSearch(pageNo, pageSize);
}
/**
 *弹出删除提示
 * @param status
 */
function showDelete(obj,type) {
    var title='';
    var content='';
    if(type=="publish")
    {
        title='启用';
        content='确定要启用此用户?';
    }else{
        title='禁用';
        content='确定要禁用此用户?';
    }
    $.confirm({
        confirmButtonPlural:'确定',
        confirmButton: false,
        animation: 'opacity',
        closeAnimation: 'scale',
        title: title,
        content: alertWarning(content),
        confirmPlural: function () {
            var dataRequest = {};
            dataRequest.id = $(obj).attr("data-id");
            dataRequest.status = $(obj).parents("tr").find(".td-status").attr("data-status") == 1 ? 0 : 1;
            $.post('/customer/u/s', dataRequest, function (data) {
                var dataObj = JSON.parse(data);
                if (dataObj.result != undefined) {
                    customerSearch(1, pageSize);
                    alertSucceed("修改成功");
                }
                else {
                    $.alert({
                        animation: 'opacity',
                        closeAnimation: 'scale',
                        title: '提示',
                        content: alertError("修改失败")
                    });
                }
            });
        }
    });
}
function collectCustomerConditionVo(pageNo, pageSize) {
    var dataRequest = {};
    dataRequest.pageSize = pageSize == null ? $('#customer-content input.hiddenId').val() : pageSize;
    dataRequest.pageNo = pageNo;
    dataRequest.account = $.trim($("#account").val());
    dataRequest.custMobile = $.trim($("#custMobile").val());
    dataRequest.custEmail = $.trim($("#custEmail").val());
    dataRequest.custName = $.trim($("#custName").val());
    dataRequest.company = $.trim($("#company").val());
    dataRequest.ticker = new Date().getTime();
    dataRequest.sortIndex=sortIndex;
    return dataRequest;
}
//页数量
function customerSearch(pageNo, pageSize) {
    showTableLoading('indexData');
    var dataRequest = {};
    dataRequest.pageSize = pageSize == null ? $('#customer-content input.hiddenId').val() : pageSize;
    dataRequest.pageNo = pageNo;
    dataRequest.account = $.trim($("#account").val());
    dataRequest.custMobile = $.trim($("#custMobile").val());
    dataRequest.custEmail = $.trim($("#custEmail").val());
    dataRequest.custName = $.trim($("#custName").val());
    dataRequest.company = $.trim($("#company").val());
    dataRequest.ticker = new Date().getTime();
    dataRequest.sortIndex=sortIndex;
    $.post('/customer/q/ac', dataRequest, function (data) {
        $('#indexData').empty().html(data);
        initPage(pageNo, pageSize);
    });
}
//初始化分页
function initPage(pageNo, pageSize) {
    var totalPages = $("#cusPageCount").val();
    var options = {
        currentPage: pageNo,  //直接跳转到特定的page
        totalPages: totalPages,//总页数
        useBootstrapTooltip: false,
        onPageChanged: function (event, oldPage, newPage) {
            customerSearch(newPage, pageSize);
        }
    }
    $('#example').bootstrapPaginator(options);
}
function showCustomerInfo(obj) {
    $.confirm({
        animation: 'opacity',
        confirmTop:'4.5%',
        closeAnimation: 'scale',
        columnClass: 'col-md-12',
        title: '会员详情',
        content: function ($obj) {
            var dataRequest = {};
            dataRequest.pageSize = 1;
            dataRequest.pageNo = 1;
            dataRequest.custId = $(obj).attr("data-id");
            dataRequest.ticker = new Date().getTime();
            return $.post('/customer/q', dataRequest, function (data) {
                $obj.setContent(data);
            });
        },
        confirmButton: '关闭',
        cancelButton: null,
    });
}

