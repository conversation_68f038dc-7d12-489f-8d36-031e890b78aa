var pageNo = 1; //当前页码
var pageSize = 10; //默认10条
var sortIndex = " em.create_on desc";//处理排序
//页面初始
$(document).ready(function () {
    //初始类别
    $('#category').selectlist(
        {
            dataJson:{"data":[{"id":"","name":"全部"},{"id":"0","name":"社会招聘"},{"id":"1","name":"校园招聘"}]}
        }
    )
    //初始化日期控件
    initDateTimerPicker("startDateTmp", "YYYY-MM-DD", "day");
    initDateTimerPicker("endDateTmp", "YYYY-MM-DD", "day");
    //查询加载列表
    conditionSearch(pageNo, pageSize);
    //查询按钮
    $('#btnQuery').on('click', function () {
        conditionSearch(pageNo, pageSize);
    });
    //新增招聘信息
    $('#addEmployment').on('click', function () {
        gotoEmploymentDetail();
    });
    //职能分类维护
    $('#maintainFuns').on('click', function () {
        gotoMaintainFuns();
    });
    //编辑招聘信息
    $('#indexData').delegate('a[name=operation-a]', 'click', function() {
        var employmentId = $(this).parents('td').attr('data-id');
        gotoEmploymentDetail(employmentId);
    });
    //删除招聘信息
    $('#indexData').delegate('a[name=operation-ac]', 'click', function() {
        var employmentId = $(this).parents('td').attr('data-id');
        delEmployment(employmentId);
    });
    //排序改变
    $('#sortOrders').on('click', function () {
        var $this = $(this);
        var btn =  $this.find('btn-a');
        if(btn.hasClass('clickon-a')){
            btn.removeClass('clickon-a');
            btn.addClass('click-a');
            sortIndex = ' em.create_on desc';//倒序
        }else {
            btn.removeClass('click-a');
            btn.addClass('clickon-a');
            sortIndex = ' em.create_on asc';//正序
        }
        conditionSearch(pageNo, pageSize);
    });
    //添加日期的监听
    addDateLinster();
});
// 新增||编辑弹窗
function gotoEmploymentDetail(employmentId){
    var title='新增招聘信息';
    if(employmentId){
        title='修改招聘信息';
    }
    $.confirm({
        confirmButtonPlural: "保存",
        animation: 'opacity',
        closeAnimation: 'scale',
        columnClass: 'col-md-8 col-md-offset-2',
        title: title,
        confirmTop:'8%',
        confirmButton:'',
        content: function ($obj) {
            return $.get('/employment/qd?employmentId='+employmentId,function (data) {
                $obj.setContent(data);
            });
        },
        confirmPlural: function () {//保存
            if(doValidation()){
                doSave(employmentId);
                return true;
            }
            return false;
        }
    });
}

// 职能分类维护
function gotoMaintainFuns(employmentId){
    $.confirm({
        confirmButtonPlural: "",
        cancelButton:'返回',
        animation: 'opacity',
        closeAnimation: 'scale',
        columnClass: 'col-md-10 col-md-offset-1',
        title: "职能分类维护",
        confirmTop:'8%',
        confirmButton:'',
        content: function ($obj) {
            return $.get('/funt',{},function (data) {
                $obj.setContent(data);
            });
        }
    });
}
//删除招聘信息
function delEmployment(employmentId) {
    $.confirm({
        animation: 'opacity',
        closeAnimation: 'scale',
        confirmButton: '确定',
        title: '删除招聘信息',
        content: alertWarning("确定删除此招聘信息"),
        confirm: function () {
            $.post('/employment/d?employmentId='+employmentId, function (data) {
                var dataObj = JSON.parse(data);
                if (dataObj.result != undefined) {
                    alertSucceed("删除成功");
                    conditionSearch(pageNo, pageSize);
                }
                else {
                    var message = dataObj.message[ERROR_KEY][0];
                    $.alert({
                        animation: 'opacity',
                        closeAnimation: 'scale',
                        title: '提示',
                        content: alertWarning(message)
                    });
                }
            });
        }
    });
}
//验证表单
function doValidation(){
    if(! $('#employmentForm').isValid()){
        return false;
    }
    var funs = $.trim($('#funsDetail .hiddenId').val());
    if(funs == undefined || funs == null || funs == ''){
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content: alertWarning('请选择职能')
        });
        return false;
    }
    var hiredNumberDetail = parseInt($.trim($('#hiredNumberDetail').val()));
    if(hiredNumberDetail < 0){
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content: alertWarning('请填写正确的招聘人数')
        });
        return false;
    }
    return true;
}
function doSave(employmentId){
    if(employmentId){//修改
        var goodsData = collectEmploymentVo(employmentId);
        goodsPageNo = parseInt($('#currentPage').val());
        $.post('/employment/u', goodsData, function (resultData) {
            if(doCallBackAlert('修改招聘信息成功',resultData)) {
                conditionSearch(pageNo, pageSize);
            }
        });
    }else{//新增
        var goodsData = collectEmploymentVo();
        $.post('/employment/a', goodsData, function (resultData) {
            if(doCallBackAlert('增加招聘信息成功',resultData)) {
                conditionSearch(pageNo, pageSize);
            }
        });
    }

}

//收集要添加或修改的数据
function collectEmploymentVo(employmentId){
    var contentVo = {};
    contentVo.employmentId=employmentId;//ID
    contentVo.position= $.trim($('#positionDetail').val());//职位
    contentVo.category= parseInt($.trim($('#categoryDetailHidden').val()));//分类
    if($('#onYes').hasClass('radioemon')){
        contentVo.category = 0;
    }else{
        contentVo.category = 1;
    }
    contentVo.functionsId= $.trim($('#funsDetail .hiddenId').val());//职能ID
    contentVo.hiredNumber=parseInt($.trim($('#hiredNumberDetail').val()));//招聘人数
    contentVo.department=$.trim($('#departmentDetail').val());//所属部门
    contentVo.workplace=$.trim($('#workplaceDetail').val());//工作地点
    contentVo.postitionReqirment=$('#postitionReqirmentDetail').summernote('code');//职位描述/要求
    return contentVo;
}
/**
 * 收集查询条件
 * @returns {{}}
 */
function collectOrderConditionVo(pageNo, pageSize){
    var conditionVo = {};
    conditionVo.sortIndex = sortIndex;
    conditionVo.pageNo = pageNo;
    conditionVo.pageSize = pageSize;
    conditionVo.position=$.trim($('#position').val());//职位
    conditionVo.category= $.trim($('#category .hiddenId').val());//分类
    conditionVo.startDateTmp= $.trim($('#startDate').val());//开始时间
    conditionVo.endDateTmp= $.trim($('#endDate').val());//结束时间
    return conditionVo;
}

//查询
function conditionSearch(pageNo, pageSize) {
    showTableLoading('indexData');
    var orderConditionVo =collectOrderConditionVo(pageNo, pageSize);
    $.post('/employment/q', orderConditionVo, function (data) {
        $('#indexData').empty().html(data);
        var totalPages = $("#cusPageCount").val();
        var options = {
            currentPage: pageNo,  //直接跳转到特定的page
            totalPages: totalPages,//总页数
            useBootstrapTooltip: false,
            onPageChanged: function (event, oldPage, newPage) {
                conditionSearch(newPage, pageSize);
            }
        }
        $('#example').bootstrapPaginator(options);
    });
}
