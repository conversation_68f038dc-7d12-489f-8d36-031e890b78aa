var pageNo = 1; //当前页码
var pageSize = 10; //默认10条
var sortIndex = " en.create_on desc";//处理排序
//页面初始
$(document).ready(function () {
    //初始化办事处
    $.get("/agency/qa",function(rst){
        $('#agencyId').selectlist(
            {
                dataJson:JSON.parse(rst)
            }
        )
    });
    //初始化日期控件
    initDateTimerPicker("startDateTmp", "YYYY-MM-DD", "day");
    initDateTimerPicker("endDateTmp", "YYYY-MM-DD", "day");
    //查询加载列表
    ordersSearch(pageNo, pageSize);
    //查询按钮
    $('#btnQuery').on('click', function () {
        ordersSearch(pageNo, pageSize);
    });
    //订单导出
    $('#orderExport').on('click', function () {
        //收集选中IDs
        var selectedIds = collectSelectedOrders();
        if(selectedIds.length==0){//导出全部
            $.confirm({
                animation: 'opacity',
                closeAnimation: 'scale',
                confirmButton: true,
                confirmButton:'确定',
                //columnClass: 'col-md-5',
                title: '导出全部询价记录',
                content: alertWarning('没有选中的记录，确定导出当前所有记录吗？'),
                confirm: function () {
                    doExportExcel(null);
                }
            });
        }else{//导出选中记录
            doExportExcel(selectedIds);
        }
    });
    //全选、全清
    $('#orderAll').on('click', function () {
        var $this = $(this);
        var checked = $this.is(':checked');
        $('input[type="checkbox"][name="orderCheck"]').each(function(i, el){
            $(el).prop('checked', checked);
        });
    });
    //排序改变
    $('#sortOrders').on('click', function () {
        var $this = $(this);
        var btn =  $this.find('btn-a');
        if(btn.hasClass('clickon-a')){
            btn.removeClass('clickon-a');
            btn.addClass('click-a');
            sortIndex = ' en.create_on desc';//倒序
        }else {
            btn.removeClass('click-a');
            btn.addClass('clickon-a');
            sortIndex = ' en.create_on asc';//正序
        }
        ordersSearch(pageNo, pageSize);
    });
    //询价详情
    $('#indexData').delegate('a[name=operation-ab]', 'click', function() {
        var enquiryId = $(this).parents('tr').find('input').attr('data-id');
        $.confirm({
            confirmButton:'',
            animation: 'opacity',
            closeAnimation: 'scale',
            columnClass: 'col-md-6 col-md-offset-3',
            title: '询价详情',
            confirmTop:'20%',
            cancelButton:'返回',
            content: function ($obj) {
                var ticker = new Date().getTime();
                var url = '/enquiry/qd/'+enquiryId+'?'+ticker;
                return $.get(url, function (data) {
                    $obj.setContent(data);
                });
            }
        });
    });
    //添加日期的监听
    addDateLinster();
});
//是否取消全选按钮
function isCancelAllCheck(obj)
{
    var isChecked =$(obj).prop("checked");
    if(!isChecked)
    {
        $("#orderAll").prop("checked",false);
    }
}

/**
 *执行发货操作
 */
function doShipping(isDetai){
    var sendGoodsRequestVo = {};
    sendGoodsRequestVo.orderId=$.trim($('#shippingOrderId').val());
    sendGoodsRequestVo.shippingId=$('#shipping').find('li.selected').attr('data-value');
    sendGoodsRequestVo.sendId=$.trim($('#sendId').val());
    sendGoodsRequestVo.receiveId=$.trim($('#receiveId').val());
    sendGoodsRequestVo.boxIds=$.trim($('#boxIds').val());
    $.post('/order/sp', sendGoodsRequestVo, function (data) {
        if(doCallBackAlert('发货成功',data)){
            if(isDetai){//刷新详情页面
                $('#shippingDetail').html(data);
            }else{
                ordersSearch(pageNo, pageSize);
            }
        }
    });
}

/**
 *执行导出
 */
function  doExportExcel(selectedIds){
    var orderConditionVo = {};
    orderConditionVo.ids=JSON.stringify(selectedIds);
    orderConditionVo.ticker=new Date().getTime();
    var url=$.param(orderConditionVo);
    window.location.href = '/excel/ens?'+url;
}

/**
 * 收集选中的订单
 */
function collectSelectedOrders(){
    var orders = new Array();
    $('input[type="checkbox"][name="orderCheck"]:checked').each(function(i, el){
        orders.push($.trim($(el).attr('data-id')));
    });
    return orders;
}
/**
 * 收集查询条件
 * @returns {{}}
 */
function collectOrderConditionVo(pageNo, pageSize){
    var conditionVo = {};
    conditionVo.pageNo = pageNo;
    conditionVo.pageSize = pageSize;
    conditionVo.companyName=$.trim($('#companyName').val());//公司名称
    conditionVo.phone= $.trim($('#phone').val());//手机号
    conditionVo.agencyId= $.trim($('#agencyId .hiddenId').val());//所在办事处ID
    conditionVo.name= $.trim($('#name').val());//询价者名称
    conditionVo.startDateTmp= $.trim($('#startDate').val());//开始时间
    conditionVo.endDateTmp= $.trim($('#endDate').val());//结束时间
    conditionVo.sortIndex = sortIndex;//处理排序
    return conditionVo;
}

//查询
function ordersSearch(pageNo, pageSize) {
    showTableLoading('indexData');
    var orderConditionVo =collectOrderConditionVo(pageNo, pageSize);
    $.post('/enquiry/q', orderConditionVo, function (data) {
        $('#indexData').empty().html(data);
        var totalPages = $("#cusPageCount").val();
        var options = {
            currentPage: pageNo,  //直接跳转到特定的page
            totalPages: totalPages,//总页数
            useBootstrapTooltip: false,
            onPageChanged: function (event, oldPage, newPage) {
                ordersSearch(newPage, pageSize);
            }
        }
        $('#example').bootstrapPaginator(options);
    });
}
