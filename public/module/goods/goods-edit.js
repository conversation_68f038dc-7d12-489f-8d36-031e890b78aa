$(document).ready(function () {
    var goodsVId = $.trim($('#goodsId').val());
    var firstCategoryId = $.trim($('#firstCategoryId').val());
    var secondCategoryId = $.trim($('#secondCategoryId').val());
    //编辑商品级联分类
    $('#secondCategoryChild').selectlist({});
    $.get("/goods/ggc", function (rst) {
        $('#firstCategory').selectlist(
            {
                dataJson: JSON.parse(rst),
                onChange: function () {
                    $.get("/goods/ggc?parentId=" + $('#firstCategory .hiddenId').val(), function (rst1) {
                        $('#secondCategoryChild').selectlist({dataJson: JSON.parse(rst1)});
                        $('#secondCategoryChild').find('ul li:eq(0)').click();
                        if(goodsVId){
                            $('#secondCategoryChild').find('li[data-value="'+secondCategoryId+'"]').click();
                        }
                    });
                }
            }
        )
        if (goodsVId) {
            $('#firstCategory').find('li[data-value="'+firstCategoryId+'"]').click();
        }
    });
    //厂商下拉框
    $.get("/vendor/qa", function (rst) {
        $('#vendor').selectlist( {dataJson: JSON.parse(rst)} )
        if(goodsVId){
            var vendorId = $.trim($('#vendorId').val());
            $('#vendor').find('li[data-value="'+ vendorId +'"]').click();
        }
    });
    //初始化附件列表
    $('#fileMsg').hide();
    var goodsFileClone = $('#goodsFile').clone();
    //提交商品附件
    $('#goodsFile').on('change',function(){
        uploadGoodsFiles();
    });
    //加载资源
    if (goodsVId) {
        var res = $.trim($('#resourceId').val());//资源
        var resArr = res.split('\;');
        for (var i = 0; i < resArr.length-1; i++) {
            var itemArr = resArr[i].split('\,');
            var fileName = itemArr[0];
            var fileId = itemArr[1];
            $('#fileMsg').append('<div class="fileCell">' +
                '<input class="uploadGoodFile input-medium input-box" value= "' + fileName + '" uploadFileId = ' + fileId + '>' +
                '<a class="ml10 sm-updelete-a" onclick="javascript:deleteFileMsg(this)">删除</a>' +
                '</div>');
            $('#fileMsg').show();
        }
    }
});
function uploadGoodsFiles(){
    //验证上传文件控件
    var rf = $('#goodsFile');
    var uploadGoodFileName = '';
    var uploadGoodFileNameArr = [];
    var uploadFileVal = rf.val();
    var fileSize = parseInt(document.getElementById("goodsFile").files[0].size);
    var fileMaxSize = 40*1024*1024;//40M
    if (uploadFileVal == '') {
        $('#msgEm').html('请选择上传文件');
        return false;
    }
    var nameLength = "", fileName = "";
    if (uploadFileVal.indexOf('\\') > 0) {
        var fileNameArr = uploadFileVal.split('\\');
        fileName = fileNameArr[fileNameArr.length - 1];
        nameLength = fileNameArr[fileNameArr.length - 1].length;
    } else {
        nameLength = uploadFileVal.length;
    }
    //不能上传重复的文件
    $('.uploadGoodFile').each(function (key, value) {
        var fileValueName = $.trim($(value).val());
        uploadGoodFileNameArr.push(fileValueName);
        uploadGoodFileName = uploadGoodFileName + ',' + fileValueName;
    });
    if(uploadGoodFileName.indexOf(fileName) > -1){
        $('#msgEm').html('附件不能重复上传');
        return false;
    }
    if(uploadGoodFileNameArr.length > 4){
        $('#msgEm').html('最多上传5个附件');
        return false;
    }
    if (nameLength > 100) {
        $('#msgEm').html('文件名长度超过100字长度限制');
        return false;
    }
    if(fileSize > fileMaxSize){
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content: alertWarning('上传附件最大为40M')
        });
        return false;
    }
    //data-type="plural"

    //提交到node服务端
    var uploadOpts = {
        success: function (rst) {
            var data = JSON.parse(rst);
            var code = data.code;
            if (code === '0') {
                $('#fileMsg').append('<div class="fileCell">' +
                    '<input class="uploadGoodFile input-medium input-box" value= "' + fileName + '" uploadFileId = ' + data.uploadFileId + '>' +
                    '<a class="ml10 sm-updelete-a" onclick="javascript:deleteFileMsg(this)">删除</a>' +
                    '</div>');
                $('#fileMsg').show();
                $('#goodsFile').remove();//清空
                $('#goodsFileInput').append('<input id = "goodsFile" onchange="uploadGoodsFiles()" name = "goodsFile", type = "file",value="选择文件", multiple = "true">');
                $('#msgEm').html('上传成功');
            } else {
                $('#msgEm').html('上传失败');
            }
            $('[data-type="plural"]').removeAttr("disabled");
        }
    };
    $('#msgEm').html('开始上传附件');
    //data-type="plural"
    //禁用确定按钮
    $('[data-type="plural"]').attr('disabled','disabled');

    var uploadForm = $('<form></form>');
    uploadForm.attr('action', '/goods/uf');
    uploadForm.attr('enctype', 'multipart/form-data');
    uploadForm.attr('method', 'POST');
    uploadForm.append(rf);
    uploadForm.ajaxSubmit(uploadOpts);
}
function deleteFileMsg(linkObj){
    $(linkObj).parent().remove();
}
