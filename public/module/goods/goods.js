/**
 * Created by ch<PERSON><PERSON><PERSON><PERSON> on 2016/3/8.
 */
var goodsPageNo = 1; //当前页码
var goodsPageSize = $('#goods-content input.hiddenId').val();
var goodsStatus=1;//默认设置选中"是"
var goodsIdes = '';//默认商品ID缓存
var goodsId= ''; //默认商品ID
var qstnaireRule=0;//默认绑定后
//页面初始
$(document).ready(function () {
    //加载分类
    $('#categoryChild').selectlist({});
    $.get("/goods/ggc",function(rst){
        $('#category').selectlist(
            {
                dataJson:JSON.parse(rst),
                onChange:function(){
                    $.get("/goods/ggc?parentId=" + $('#category .hiddenId').val(),function(rst1){
                        $('#categoryChild').selectlist({dataJson:JSON.parse(rst1)});
                        $('#categoryChild').find('ul li:eq(0)').click();
                    });
                }
            }
        )
    });

    //改变查看页大小
    $('#goods-content .in-pageNo ul').delegate('li', 'click', function () {
        if ($(this).text() != goodsPageSize) {
            goodsPageSize = $(this).text();
            setPageSizeCookie(goodsPageSize);
            goodsSearch(goodsPageNo, goodsPageSize);
        }
    });
    //查询加载列表
    goodsSearch(goodsPageNo, goodsPageSize);
    //新增商品，商品元素比较多，且较为复杂 所以 跳转到新的页面
    $('#addGoods').on('click', function () {
        gotoEditGoodsPage(null);
    });
    //查询按钮
    $('#btnQuery').on('click', function () {
        //收集查询条件
        //alert('111');
        goodsSearch(goodsPageNo, goodsPageSize);
    });
    //上架
    $('#putOn').on('click', function () {
        //上架操作
        var goodses = collectSelectedGoodses();
        if(goodses.length>0){
            $.confirm({
                animation: 'opacity',
                closeAnimation: 'scale',
                confirmButton: '确定',
                //columnClass: 'col-md-5',
                title: '商品上架',
                content: alertWarning('确定要上架所有选中的商品吗？'),
                confirm: function () {
                    doPutOn(goodses);
                }
            });
        }else{
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '提示',
                content: alertWarning('请选择要上架的商品')
            });
        }
    });
    //下架
    $('#pullOff').on('click', function () {
        //下架操作
        var goodses = collectSelectedGoodses();
        if(goodses.length>0){
            $.confirm({
                animation: 'opacity',
                closeAnimation: 'scale',
                confirmButton: '确定',
                //columnClass: 'col-md-5',
                title: '商品下架',
                content: alertWarning('确定要下架所有选中的商品吗？'),
                confirm: function () {
                    doPullOff(goodses);
                }
            });
        }else{
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '提示',
                content: alertWarning('请选择要下架的商品')
            });
        }
    });

    //-----------添加 列表动作监听-------------------------------------
    //全选、全清
    $('#goodsAll').on('click', function () {
        var $this = $(this);
        var checked = $this.is(':checked');
        $('input[type="checkbox"][name="goodsCheck"]').each(function(i, el){
            $(el).prop('checked', checked);
        });
    });
    //编辑
    $('#indexData').delegate('a[name=operation-a]', 'click', function() {
        var thisId = $(this).attr('data-id');
        gotoEditGoodsPage(thisId);
    });
    //预览
    $('#indexData').delegate('a[name=operation-ab]', 'click', function() {
        previewPage(this);
    });
    //预览
    function previewPage(obj) {
        //跳到前台预览
        window.open($("#shoppingUrl").val() + '/goods/index/qd?goodsId=' + $.trim($(obj).attr("data-id")) + "&isShow=1");
    }
    //删除
    $('#indexData').delegate('a[name=operation-ac]', 'click', function() {
        var thisId = $(this).attr('data-id');
        var goodsVo = {} ;
        goodsVo.goodsId=thisId;
        $.confirm({
            animation: 'opacity',
            closeAnimation: 'scale',
            confirmButton: '确定',
            //columnClass: 'col-md-5',
            title: '商品删除',
            content: alertWarning('确定要删除商品吗？'),
            confirm: function () {
                doDeleteGoods(goodsVo);
            }
        });
    });
});
//是否取消全选按钮
function isCancelAllCheck(obj)
{
    var isChecked =$(obj).prop("checked");
    if(!isChecked)
    {
        $("#goodsAll").prop("checked",false);
    }
}
/**
 *
 * 执行上架操作
 */
function doPutOn(goodses){
    var putDatas={};
    putDatas.goodses=JSON.stringify(goodses);
    $.post('/goods/put/on',  putDatas, function (data){
        if(doCallBackAlert('商品上架成功',data)){
            goodsSearch(goodsPageNo, goodsPageSize);

        }
    });
}

/**
 *
 * 执行下架操作
 */
function doPullOff(goodses){
    var pullDatas={};
    pullDatas.goodses=JSON.stringify(goodses);
    $.post('/goods/pull/off', pullDatas, function (data){
        if(doCallBackAlert('商品下架成功',data)){
            goodsSearch(goodsPageNo, goodsPageSize);
        }
    });
}

/**
 * 收集选中的商品
 */
function collectSelectedGoodses(){
    var goodses = new Array();
    $('input[type="checkbox"][name="goodsCheck"]').each(function(i, el){
        if( $(el).is(':checked')){
            goodses.push($(el).attr('data-id'));
        }
    });
    return goodses;
}

/**
 * 执行删除操作
 * @param goodsVo
 */
function doDeleteGoods(goodsVo){
    goodsPageNo = parseInt($('#currentPage').val());
    $.post('/goods/d', goodsVo, function (data){
        if(doCallBackAlert('商品删除成功',data)){
            goodsSearch(goodsPageNo, goodsPageSize);
        }
    });
}

function reViewGoods(){

}

/**
 * 单选多选处理
 */
function  addRedioEvent(){
    goodsStatus = parseInt($('#goodsStatusValue').val())!=0?1:0;
    $('#onYes').on('click', function () {
        if( $(this).find('radioemon').length<=0){
            goodsStatus=1;
            $('#onNo').removeClass('radioemon');
            $('#onYes').addClass('radioemon');
        }
    });

    $('#onNo').on('click', function () {

        if( $(this).find('radioemon').length<=0){
            goodsStatus=0;
            $('#onYes').removeClass('radioemon');
            $('#onNo').addClass('radioemon');
        }
    });
}

/**
 * 绑定样品单选处理
 */
function  qstnaireRuleEvent(){
    $('#bindingAgo').on('click', function () {
        if( $(this).find('radioemon').length<=0){
            qstnaireRule=1;
            $('#bindingAfter').removeClass('radioemon');
            $('#bindingAgo').addClass('radioemon');
        }
    });
    $('#bindingAfter').on('click', function () {
        if( $(this).find('radioemon').length<=0){
            qstnaireRule=0;
            $('#bindingAgo').removeClass('radioemon');
            $('#bindingAfter').addClass('radioemon');
        }
    });
}

//查询
function goodsSearch(pageNo, pageSize) {
    showTableLoading('indexData');
    var name = $.trim($('#goodsName').val());//商品名称
    //获取选中的商品分类ID
    var firstCategory = $.trim($('#category .hiddenId').val());//第一级商品分类ID
    var secondCategory = $.trim($('#categoryChild .hiddenId').val());//第二级商品分类ID
    var vendorName = $.trim($('#vendorName').val())//厂商

    var goodsConditionVo = {};
    goodsConditionVo.name = name;
    goodsConditionVo.firstCategory = firstCategory;
    goodsConditionVo.secondCategory = secondCategory;
    goodsConditionVo.vendorName = vendorName;

    goodsConditionVo.pageSize =
        pageSize == null ?
            $('#goods-content input.hiddenId').val()
            : pageSize;
    goodsConditionVo.pageNo = pageNo;
    $.post('/goods/q', goodsConditionVo, function (data) {
        $('#indexData').empty().html(data);
        var totalPages = $("#cusPageCount").val();
        var options = {
            currentPage: pageNo,  //直接跳转到特定的page
            totalPages: totalPages,//总页数
            useBootstrapTooltip: false,
            onPageChanged: function (event, oldPage, newPage) {
                goodsSearch(newPage, pageSize);
            }
        }
        $('#example').bootstrapPaginator(options);
    });
    $('#goodsAll').attr('checked',false);
}

/**
 *跳转到新增页面
 */
var goodsAlert;
function gotoEditGoodsPage(goodsId){

    goodsIdes = '';
    var title='添加商品';
    if(goodsId){
        title='修改商品';
    }

goodsAlert = $.confirm({
        confirmButtonPlural: "保存",
        animation: 'opacity',
        closeAnimation: 'scale',
        columnClass: 'col-md-12',
        title: title,
        confirmTop:'4.5%',
        confirmButton:'',
        content: function ($obj) {
            var goodsVo = {};
            goodsVo.goodsId=goodsId;
            goodsVo.name='';
            goodsVo.promotionTitle='';
            goodsVo.goodsSn='';
            goodsVo.price='';
            goodsVo.sellPrice='';
            goodsVo.amount='';
            return $.post('/goods/s', goodsVo, function (data) {
                $obj.setContent(data);
                //处理单选的监听
                addRedioEvent();
            });
        },
        confirmPlural: function () {//保存
            if(doValidation()){
                doSave();
                return false;
            }
            return false;
        }
    });
}
/**
 * 显示“新增分类”的全部元素
 */
function showAddCategory(){
    $('#add-category').find('i').removeClass('add');
    $('#add-category').find('i').addClass('ec-show');
    $('#add-category').find('p').removeClass('ec-show');
}
/**
 * 隐藏“新增分类”的全部元素
 */
function hiddenAddCategory(){
    $('#add-category').find('i').removeClass('ec-show');
    $('#add-category').find('i').addClass('add');
    $('#add-category').find('p').addClass('ec-show');
    $('#categoryName').val('');//清空输入框内容
}
//执行增加商品分类
function doAddCategory(goodsCategoryVo){
    $.post('/goodsCategory/a', goodsCategoryVo, function (data){
        if(doCallBackAlert('商品分类添加成功',data)){
            //加入下拉框
            var categoryData=JSON.parse(data);
            $('#editCategory').find('ul').append('<li title="'+goodsCategoryVo.name+'" style="height: 34px;" data-value="' +categoryData.id+'">' +goodsCategoryVo.name+'</li>');
            $('#editCategory').find('.select-button').val(goodsCategoryVo.name);
            $('#selectOfID').val(categoryData.id);
            //退出
            hiddenAddCategory();
        }
    });
}
function  validCategoryName(categoryName){
    //var msg='按钮触发成功'+roleName;
    if(categoryName==''){
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '错误提示',
            content:alertWarning('请输入商品分类名称')
        });
        return false;
    }
    if(specialCharValidate('商品分类名称不能含有',categoryName)){
        return false;
    }
    if(categoryName.length>12){
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '错误提示',
            content:alertWarning('商品分类名称长度不能超过12个字<br><center>请重新输入</center>')
        });
        return false;
    }
    return true;
}
/**
 * 验证表单
 * @returns {boolean}
 */
function doValidation(){
    if(! $('#goodsForm').isValid()){
      return false;
    }
    var firstCategory = $.trim($('#firstCategory .hiddenId').val());//第一级分类
    var secondCategoryChild = $.trim($('#secondCategoryChild .hiddenId').val());//第二级分类
    if(firstCategory == '' || secondCategoryChild == ''){
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content: alertWarning('请选择商品分类')
        });
        return false;
    }
    //验证厂商
    var vendor =$.trim($('#vendor .hiddenId').val());
    if(vendor == '' || vendor == undefined ){
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content: alertWarning('请选择厂商')
        });
        return false;
    }
    var picDetail = $("#picDetail").val();
    var pic =  $("#goodsPicImg").attr("src");
    if(!pic || pic == '../images/tub.jpg'){
        //提示
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content: alertWarning('请上传商品的图片')
        });
        return false;
    }
    return true;
}
/**
 * 验证商品上下架状态
 * @returns {boolean}
 */
function doValidationGoodsStatus() {
    $.alert({
        animation: 'opacity',
        closeAnimation: 'scale',
        title: '提示',
        content: alertWarning('在预览修改时必须将商品上下架状态改为下架状态')
    });
    return false;
}
/**
 * 执行商品预览
 */
function doPrivew(){
    if(goodsIdes){
        goodsId = goodsIdes;
    }else{
        goodsId = $.trim($('#goodsId').val());
    }
    if(goodsId){
        var goodsData = collectGoodsVo(goodsId);
        $.post('/goods/u', goodsData, function (resultData) {
            goodsSearch(goodsPageNo, goodsPageSize);
            window.open($("#shoppingUrl").val() + '/goods/q/'+goodsId);
        });
    }else{
        var goodsData = collectGoodsVo('');
        $.post('/goods/a', goodsData, function (resultData) {
            var result = JSON.parse(resultData);
            var goodsIdDate = result.id;
            goodsIdes = goodsIdDate;
            goodsSearch(goodsPageNo, goodsPageSize);
            if(goodsIdDate){
                window.open($("#shoppingUrl").val() + '/goods/q/'+goodsIdDate);
            }else{
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '预览提示',
                    content: alertWarning('商品名称重复预览失败')
                });
            }

        });
    }
}
/**
 * 执行保存操作
 */
function doSave(){
    if(goodsIdes){
        goodsId= goodsIdes;
    }else{
        goodsId= $.trim($('#goodsId').val());
    }
    if(goodsId){//修改
        var goodsData = collectGoodsVo(goodsId);
        goodsPageNo = parseInt($('#currentPage').val());
        $.post('/goods/u', goodsData, function (resultData) {
            if(doCallBackAlert('商品修改成功',resultData)) {
                goodsAlert.close();
                goodsSearch(goodsPageNo, goodsPageSize);
            }
        });
    }else{//新增
        var goodsData = collectGoodsVo('');
        $.post('/goods/a', goodsData, function (resultData) {
            if(doCallBackAlert('商品添加成功',resultData)) {
                goodsAlert.close();
                goodsSearch(goodsPageNo, goodsPageSize);
            }
        });
    }

}
/**
 * 收集商品最新数据
 * @param goodsId
 */
function collectGoodsVo(goodsId){
    var goodsVo = {};
    goodsVo.goodsId=goodsId;//商品ID
    goodsVo.firstCategory= $.trim($('#firstCategory .hiddenId').val());//第一级分裂ID
    goodsVo.secondCategory=$.trim($('#secondCategoryChild .hiddenId').val());//第二级分类ID
    goodsVo.name=$.trim($('#goodsNameDetail').val());//商品名称
    goodsVo.promotionTitle=$.trim($('#promotionTitle').val());//促销标题
    goodsVo.defaultPic=getDefaultlPic();//商品图片
    goodsVo.picDetail=$("#picDetail").val();//商品图片alt
    goodsVo.vendorId=$.trim($('#vendor .hiddenId').val());//厂商ID
    goodsVo.goodsIntro= $.trim($('#goodsIntro').val());//商品简介
    goodsVo.goodsDetail= $('#goodsDetail').summernote('code');//商品详情 包括技术规格、关键特性
    var res = '';
    $('.uploadGoodFile').each(function(key,val){
        var fileName = $.trim($(val).val());
        var fileId = $.trim($(val).attr('uploadFileId'));
        res = res + fileName + ',' + fileId + ';';
    });
    goodsVo.resource= res;//相关资源
    goodsVo.goodsStatus=goodsStatus;//是否上架
    goodsVo.keywords=$.trim($('#keywords').val());//关键字
    goodsVo.description=$.trim($('#description').val());//关键字描述
    return goodsVo;
}

//处理图片
function getDefaultlPic(){
    return $("#goodsPicImg").attr("src").replace(imgDomain,"");
}
/**
 * 上传图片
 * @returns {boolean}
 */
function uploadGoodsImage() {
        var orgnHtml = $("#goodsPic").parent().html();//获取input file 没写value前的html；解决IE同路径文件无法重复上传问题
        $("#goodsForm").ajaxSubmit({
            type: 'POST',
            async: false,
            url: "/imageUpload/up/file?uptype=GoodsConfig", /*填写后台上传文件的路径*/
            cache: false,
            success: function (data) {/*url为上传成功后返回的图片路径*/
                var result = JSON.parse(data);
                if (result.status == "SUCCESS") {
                    //$("#goodsPic").parent().html(orgnHtml);//重写html
                    $("#goodsPicImg").attr("src",imgDomain+result.fileUrl);
                }
                else {
                    var message = result.message == "" ? "上传失败" : result.message;
                    $.alert({
                        animation: 'opacity',
                        closeAnimation: 'scale',
                        title: '信息提示',
                        content: alertError(message)
                    });
                    $("#goodsPic").parent().html(orgnHtml);//重写html
                }

            },
            error: function (msg) {
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '信息提示',
                    content: alertError('上传失败。')
                });
                $("#goodsPic").parent().html(orgnHtml);//重写html
            }
    });
    return false;
}