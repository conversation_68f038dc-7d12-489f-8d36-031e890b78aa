/**
 * Created by ch<PERSON><PERSON><PERSON><PERSON> on 2016/3/7.
 */
var secondHtml={};
//页面初始
$(document).ready(function () {
    //头部新增第一级分类
    $('#btnAddFirstCategory').on('click', function () {
        var categoryName = $.trim($('#firstGCInput').val());
        if(validCategoryName(categoryName)){
            var goodsCategoryVo = {};
            goodsCategoryVo.name=categoryName;
            goodsCategoryVo.parentId = '0';
            $.post('/goodsCategory/a', goodsCategoryVo, function (data){
                if(doCallBackAlert('商品分类添加成功',data)){
                    categorySerch();
                    $.get('/goodsCategory/qp',function(data){
                        $('#firstGCSelect').selectlist({dataJson:JSON.parse(data)});
                    });
                    $('#firstGCInput').val('');
                }
            });
        }
    });
    //头部新增第二级分类
    $('#btnAddSecondCategory').on('click', function () {
        var categoryName = $('#secondGCInput').val();
        var parentId = $.trim($('#firstGCSelect .hiddenId').val());
        if(validCategoryName(categoryName)){
            if(parentId == undefined || parentId == '' || parentId == 'undefined' || parentId == null){
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '提示',
                    content:alertWarning('请选择第一级分类，如果没有第一级分类，请先创建第一级分类')
                });
            }else{
                var goodsCategoryVo = {};
                goodsCategoryVo.name=categoryName;
                goodsCategoryVo.parentId = parentId;
                doAddCategory(goodsCategoryVo);
                $('#secondGCInput').val('');
            }
        }
    });
});

/**
 * 删除商品分类
 * @param goodsCategoryVo
 */
function  doDeleteCategory(goodsCategoryVo){
    $.post('/goodsCategory/d', goodsCategoryVo, function (data){
        if(doCallBackAlert('商品分类删除成功',data)){
            categorySerch();
            $.get('/goodsCategory/qp', function (data) {
                $('#firstGCSelect').selectlist({dataJson: JSON.parse(data)});
            });
        }
    });
}

//添加事件监听,编辑的“保存”按钮
function  addEditSaveLinster(){
    //保存编辑后的商品分类
    $('#editSave').on('click',function(){
        var categoryName=$('#editTxt').val();
        var categoryId=$('#editTxt').attr('id_data');
        //校验通过，执行修改
        if(validCategoryName(categoryName)){
            var goodsCategoryVo = {};
            goodsCategoryVo.goodsCatId = categoryId;
            goodsCategoryVo.name=categoryName;
            doUpdateCategory(goodsCategoryVo);
            hoverTableOption();
        }
    });
}
/**
 * 执行修改操作
 * @param goodsCategoryVo
 */
function  doUpdateCategory(goodsCategoryVo){
    $.post('/goodsCategory/u', goodsCategoryVo, function (data){
        if(doCallBackAlert('商品分类修改成功',data)){
            categorySerch();
            $.get('/goodsCategory/qp', function (data) {
                $('#firstGCSelect').selectlist({dataJson: JSON.parse(data)});
            });
        }
    });
}
//添加事件监听，编辑的“取消”按钮
function addEditCancelLinster(){
//取消编辑
    $('#editCancel').on('click',function(){
        var categoryName=$('#editTxt').attr('hidden-value');
        var categoryId=$('#editTxt').attr('id_data');
        var curTr = $('#tr'+categoryId);
        cancelEditHtml(curTr.children('td').eq(0),curTr.children('td').eq(1),categoryName);
    });
}

/**
 * 点击“编辑“插入编辑框
 * @param fistTd
 * @param secendTd
 * @param categoryId
 * @param categoryName
 */
function  editHtml(fistTd,secendTd,categoryId,categoryName){
    secondHtml=secendTd.html();
    fistTd.html(
        '<input type="text" id="editTxt" id_data="' +categoryId+'" class="input-medium input-box" value="'+categoryName+'" hidden-value="' +categoryName+
        '" />');
    secendTd.html(
        '<a href="javascript:void(0);" id="editSave" class="input-btnfile rolelist-btnfile float-l">保存</a> ' +
        '<a href="javascript:void(0);" id="editCancel" class="input-btnfile rolelist-btnfile float-l">取消</a>');
}
/**
 * 点击”取消“按钮 还原html
 * @param fistTd
 * @param secendTd
 * @param categoryName
 */
function  cancelEditHtml(fistTd,secendTd,categoryName){
    fistTd.html(categoryName);
    secendTd.html(secondHtml);
}


/**
 * 刷新查询列表
 */
function categorySerch(){
    var ticker = new Date().getTime();
    $.get('/goodsCategory/qt?'+ticker, function (data) {
        $('#indexData').empty().html(data);
    });
}

//执行增加商品分类
function doAddCategory(goodsCategoryVo){
    $.post('/goodsCategory/a', goodsCategoryVo, function (data){
        if(doCallBackAlert('商品分类添加成功',data)){
            categorySerch();
        }
    });
}

/**
 * 校验商品分类名称
 * @param categoryName
 * @returns {boolean}
 */
function  validCategoryName(categoryName){
    if(categoryName == ''){
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content:alertWarning('商品分类名称不能为空')
        });
        return false;
    }else if(categoryName.length>20){
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content:alertWarning('商品分类名称长度不能超过20个字<br><center>请重新输入</center>')
        });
        $('#categoryName').val('');
        return false;
    }
    return true;
}

//保存数据
function saveData(){
    var k = 0;
    var dataArr = [];
    var gCTreeVo = {};
    $('#indexData dl').each(function () {
        var goodsCategoryVo = {};
        goodsCategoryVo.goodsCatId = $(this).attr('goodsCatId');
        goodsCategoryVo.categorySort = k;
        dataArr.push(goodsCategoryVo);
        k++;
    })
    gCTreeVo.gcs = JSON.stringify(dataArr);
    $.post('/goodsCategory/s', gCTreeVo, function (data) {
        doCallBackAlert('保存成功', data)
    });
}
