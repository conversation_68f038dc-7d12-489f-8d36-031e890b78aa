$(function(){
    $('#type').selectlist({topPosition:false});
    initDateTimerPicker('releaseTimeS', 'YYYY-MM-DD', 'day');
    var typeId = $.trim($('#typeId').val());
    if(typeId){
        $('#type').find('li[data-value="'+ typeId +'"]').click();
    }
    dateYear();
});


function dateYear() {
    var options = {};
    options.drops = 'down';
    options.showDropdowns = true;
    //开始日期选择
    $('#releaseTimeS').daterangepicker({
        startDate: moment().startOf('hour'),
        endDate: moment().startOf('hour'),
        drops: options.drops,
        showDropdowns: options.showDropdowns
    }, function (selectedDate) {
        //获取enddate ，如果大于enddate 设置当前为值为endDate
        var selectedDateStr;
        if (selectedDate > new Date()) {
            selectedDateStr = formatDate(new Date());
        }
        else {
            selectedDateStr = selectedDate.format('YYYY-MM-DD');
        }
        $('#releaseTimeS input').val(selectedDateStr).focus();

    });
}