/**
 * Created by d<PERSON><PERSON> on 2017/5/12.
 */
var honorPageNo = 1; //当前页码
var honorPageSize = $('#honor-content input.hiddenId').val();
var sortIndex = "release_time desc";//处理排序
//页面初始
$(function () {
    //改变查看页大小
    $('#honor-content .in-pageNo ul').delegate('li', 'click', function () {
        if ($(this).text() != honorPageSize) {
            honorPageSize = $(this).text();
            setPageSizeCookie(honorPageSize);
            honorSearch(honorPageNo, honorPageSize);
        }
    });
    //查询加载列表
    honorSearch(honorPageNo, honorPageSize);
    //新增投资者关系
    $('#addHonor').on('click', function () {
        doAddHonor();
    });
    //查询按钮
    $('#btnQuery').on('click', function () {
        //收集查询条件
        honorSearch(honorPageNo, honorPageSize);
    });
    //编辑
    $('#indexData').delegate('a[name=btnHonorUpdate]', 'click', function () {
        var thisId = $(this).attr('data-id');
        showEdit(thisId);
    });
    //删除无效投资者关系
    $('#indexData').delegate('a[name=btnHonorDel]', 'click', function () {
        var thisId = $(this).attr('data-id');
        var honorVo = {};
        honorVo.honorId = thisId;
        $.confirm({
            animation: 'opacity',
            closeAnimation: 'scale',
            confirmButton: '确定',
            title: '企业荣誉删除',
            content: alertWarning('确定要删除此企业荣誉吗？'),
            confirm: function () {
                deleteHonor(honorVo);
            }
        });
    });
    //新增投资者关系
    $('#addIMAGE').on('click', function () {
        doAddHonorInfo();
    });
    //排序改变
    $('#sortOrders').on('click', function () {
        var $this = $(this);
        var btn = $this.find('btn-a');
        if (btn.hasClass('clickon-a')) {
            btn.removeClass('clickon-a');
            btn.addClass('click-a');
            sortIndex = 'release_time asc';//正序
        } else {
            btn.removeClass('click-a');
            btn.addClass('clickon-a');
            sortIndex = 'release_time desc';//倒序
        }
        honorSearch(honorPageNo, honorPageSize);
    });
});

/**
 * 删除无效用户
 * @param userId
 */
function deleteHonor(vo) {
    $.post('/honor/d', vo, function (data) {
        if (doCallBackAlert('企业荣誉删除成功', data)) {
            honorSearch(honorPageNo, honorPageSize);
        }
    });
}

//查询
function honorSearch(pageNo, pageSize) {
    showTableLoading('indexData');
    var name = $.trim($('#q_name').val());
    var honorConditionVo = {};
    honorConditionVo.pageSize = pageSize == null ? $('#honor-content input.hiddenId').val() : pageSize;
    honorConditionVo.pageNo = pageNo;
    honorConditionVo.name = name;
    honorConditionVo.type = $('#qtype').find('li.selected').attr('data-value');
    honorConditionVo.ticker = new Date().getTime();
    honorConditionVo.sortIndex = sortIndex;//处理排序字段
    $.post('/honor/q', honorConditionVo, function (data) {
        $('#indexData').empty().html(data);
        var totalPages = $("#cusPageCount").val();
        var options = {
            currentPage: pageNo,  //直接跳转到特定的page
            totalPages: totalPages,//总页数
            useBootstrapTooltip: false,
            onPageChanged: function (event, oldPage, newPage) {
                honorSearch(newPage, pageSize);
            }
        }
        $('#example').bootstrapPaginator(options);
    });
}

//执行新增投资者关系
function doAddHonor() {
    $.confirm({
        confirmButtonPlural: "保存",
        animation: 'opacity',
        confirmTop: '10%',
        closeAnimation: 'scale',
        columnClass: 'col-md-6 col-md-offset-3',
        title: '添加企业荣誉',
        confirmButton: false,
        content: function ($obj) {
            var honor = {};
            honor.honorId = null;
            return $.post('/honor/s', honor, function (data) {
                $obj.setContent(data);
            });
        },
        confirmPlural: function () {
            if (doValidation()) {
                doSave();
                return true;
            }
            return false;
        }
    });
}

function doSave() {
    var honorData = {};
    var honorId = $('#honorId').val();
    honorData.name = $('#name').val();
    honorData.releaseTime = $('#releaseTime').val();
    honorData.type = $('#type').find('li.selected').attr('data-value');
    if (honorData.releaseTime == "") {
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '信息提示',
            content: alertError('发布日期不能为空')
        });
        return false;
    }
    else {
        if (honorId) {//修改操作
            honorData.honorId = honorId;
            $.post('/honor/u', honorData, function (data) {
                if (doCallBackAlert('企业荣誉修改成功', data)) {
                    honorSearch(honorPageNo, honorPageSize);
                }
            });
        } else {
            $.post('/honor/a', honorData, function (data) {
                if (doCallBackAlert('企业荣誉添加成功', data)) {
                    honorSearch(honorPageNo, honorPageSize);
                }
            });
        }
        return true;
    }
}
function doValidation() {
    if (!$('#honorForm').isValid()) {
        return false;
    }
    return true;
}
/**
 * 弹出修改用户页面
 * @param userId
 */
function showEdit(honorId) {
    var currentUser = {};
    var currentTR = $('#tr' + honorId);
    var tds = currentTR.children('td');
    currentUser.honorId = honorId;
    currentUser.name = tds.eq(0).text();
    currentUser.typeId = currentTR.attr('data-type');
    currentUser.releaseTime = tds.eq(2).text();
    $.confirm({
        confirmButtonPlural: '保存',
        animation: 'opacity',
        confirmTop: '10%',
        closeAnimation: 'scale',
        columnClass: 'col-md-6 col-md-offset-3',
        title: '修改企业荣誉',
        confirmButton: false,
        content: function ($obj) {
            return $.post('/honor/s', currentUser, function (data) {
                $obj.setContent(data);
            });
        },
        confirmPlural: function () {
            if (doValidation()) {
                doSave();
                return true;
            }
            return false;
        }
    });
}

//执行新增投资者关系
function doAddHonorInfo() {
    $.confirm({
        confirmButtonPlural: "保存",
        animation: 'opacity',
        confirmTop: '10%',
        closeAnimation: 'scale',
        columnClass: 'col-md-6 col-md-offset-3',
        title: '图片更新',
        confirmButton: false,
        content: function ($obj) {
            return $.get('/honor/info/s', function (data) {
                $obj.setContent(data);
            });
        },
        confirmPlural: function () {
            if (doValidation2()) {
                if (doSave2())
                    return true;
            }
            return false;
        }
    });
}


function doValidation2() {
    if (!$('#honorInfoForm').isValid()) {
        return false;
    }
    return true;
}

function doSave2() {
    if ($('ul.sm-carousel-ul li').length != 2) {
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '信息提示',
            content: alertError('必须上传两张图片。')
        });
        return false;
    }
    var obj = {};
    obj.filePath1 = $('ul.sm-carousel-ul li:first img').attr('fileUrl');
    obj.honorInfoId1 = $('ul.sm-carousel-ul li:first img').attr('data-id');
    obj.filePath2 = $('ul.sm-carousel-ul li:last img').attr('fileUrl');
    obj.honorInfoId2 = $('ul.sm-carousel-ul li:last img').attr('data-id');
    $.post('/honor/info/a', obj, function (data) {
        doCallBackAlert('图片更新成功', data);
    });

    return true;
}

function htmlDiv(imgSrc, fileUrl) {
    var html = [];
    html.push('<li>');
    html.push('<div class="sm">');
    html.push('		<div class="sm-lubimg">');
    // html.push('			<span><i class="iconfont sm-move"></i></span>');
    html.push('			<span class="sm-carousel-spanimg"><img src="' + imgSrc + '" width="287" height="101" fileUrl="' + fileUrl + '"><em class="hid"></em></span>');
    html.push('		</div>');
    html.push('		<em class="sm-down sm-imgcarousel-del"><i class="iconfont icon-cha"></i></em>');
    html.push('</div>');
    html.push('</li>');
    return html.join(' ');
}