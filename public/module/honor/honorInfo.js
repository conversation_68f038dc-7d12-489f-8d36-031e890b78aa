$(function () {
    $('.sm-imgboxtu').delegate('#imgCarouselFileLabel','click',function(){
        return $('#imgCarouselFile').click();
    });
    //删除一张轮播图
    $('#set-module-imgCarousel').delegate('.sm-imgcarousel-del', 'click', function(){
        var $this = $(this);
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content: alertWarning('确定删除该组图片吗？'),
            confirm : function(){
                $this.parents('li').remove();
            }
        });
    });
});
/**
 * 上传图片
 * @param formName
 * @param upType
 * @returns {boolean}
 */
function uploadImage(formName, upType, obj) {
    if($('ul.sm-carousel-ul li').length>1){
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '信息提示',
            content: alertError('只能上传两张图片。')
        });
        return false;
    }
    if (typeof obj != 'undefined' && obj != null) {
        var $obj = $(obj);
        var dataType = $obj.attr('data-type');
        if (typeof dataType != 'undefined' && dataType != null && dataType == 'img') {
            upType = 'HonorInfoConfig';
        }
    }
    var carouseHtml = $("#imgCarouselFile").parent().html();
    $("#" + formName).ajaxSubmit({
        type: 'POST',
        async: false,
        url: "/imageUpload/up/file?uptype=" + upType, /*填写后台上传文件的路径*/
        cache: false,
        success: function (data) {/*url为上传成功后返回的图片路径*/
            var result = JSON.parse(data);
            if (result.status == CONST_SUCCESS) {
                $('#set-module-imgCarousel ul.sm-carousel-ul').append(htmlDiv(imgDomain + result.fileUrl,result.fileUrl));
            }
            else {
                var message = result.message == "" ? "上传失败" :result.message;
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '信息提示',
                    content: alertError(message)
                });
                $("#imgCarouselFile").parent().html(carouseHtml);
            }

        },
        error: function (msg) {
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '信息提示',
                content: alertError('上传失败。')
            });
        }

    });
    return false;
}

