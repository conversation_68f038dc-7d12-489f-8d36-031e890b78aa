/**
 * Created by r<PERSON><PERSON> on 2016/2/2.
 */


function loadIndexDate(index, obj) {
    //showDivLoading('indexData');
    var ticker = new Date().getTime();
    $(obj).addClass('on').parent().siblings('li').find('a').removeClass('on');
    // $.get('/index/q/' + index + "?" + ticker, function (data) {
    //     $('#indexData').empty().html(data);
    // })
}
/**
 *
 * @param status 状态
 * @param begTime 开始时间
 * @param endTime 结束时间
 */
function samplingBoxUrl(status) {
    var time=$("#sBoxHid").val();
    var begTime="";
    var endTime="";
    if(time!="")
    {
        var arrTime =time.split("|");
        begTime=arrTime[0];
        endTime=arrTime[1];
    }
    window.location.href = "/samplingTesting/sts/" + status + "?msDate=" + begTime + "&meDate=" + endTime;
}
$(document).ready(function () {
    var firstLogin = $('#isFirstLogin').val();
    if (firstLogin == 1) {
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示信息',
            content: '<p class="input-tis">您第一次登录，请立即在右上角"个人信息"中修改密码！</p>'
        });
        $('#isFirstLogin').val(0);
    }
    loadIndexDate('0');
});