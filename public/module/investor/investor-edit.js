$(function () {
    $('#category-sel').selectlist({topPosition: false});
    //初始化附件列表
    $('#fileMsg').hide();
    //提交附件
    $('#investorFile').on('change', function () {
        uploadInvestorFiles();
    });
    dateYear()
});
function dateYear() {
    var options = {};
    options.drops = 'down';
    options.showDropdowns = true;
    //开始日期选择
    $('#releaseTimeS').daterangepicker({
        startDate: moment().startOf('hour'),
        endDate: moment().startOf('hour'),
        drops: options.drops,
        showDropdowns: options.showDropdowns
    }, function (selectedDate) {
        //获取enddate ，如果大于enddate 设置当前为值为endDate
        var selectedDateStr;
        if (selectedDate > new Date()) {
            selectedDateStr = formatDate(new Date());
        }
        else {
            selectedDateStr = selectedDate.format('YYYY-MM-DD');
        }
        $('#releaseTimeS input').val(selectedDateStr).focus();

    });
}
function uploadInvestorFiles() {
    if ($("#hidfile").val() == 1) {
        $('#msgEm').html('正在上传,请勿重复上传');
        return false;
    }
    //验证上传文件控件
    var rf = $('#investorFile');
    var uploadInvestorFileName = '';
    var uploadInvestorFileNameArr = [];
    var uploadFileVal = rf.val();
    var fileSize = parseInt(document.getElementById("investorFile").files[0].size);
    var fileMaxSize = 40 * 1024 * 1024;//40M
    if (uploadFileVal == '') {
        $('#msgEm').html('请选择上传文件');
        return false;
    }
    var nameLength = "", fileName = "";
    if (uploadFileVal.indexOf('\\') > 0) {
        var fileNameArr = uploadFileVal.split('\\');
        fileName = fileNameArr[fileNameArr.length - 1];
        nameLength = fileNameArr[fileNameArr.length - 1].split('\.')[0].length;
    } else {
        nameLength = uploadFileVal.split('\.')[0].length;
    }
    //不能上传重复的文件
    $('.prompt-border-box').each(function (key, value) {
        var fileValueName = $.trim($(value).val());
        uploadInvestorFileNameArr.push(fileValueName);
        uploadInvestorFileName = uploadInvestorFileName + ',' + fileValueName;
    });
    if (uploadInvestorFileName.indexOf(fileName) > -1) {
        $(".icon-tannhao").text("附件不能重复上传");
        $("#msgEm").show();
        return false;
    }
    if (uploadInvestorFileNameArr.length > 0) {
        $(".icon-tannhao").text("最多上传1个附件");
        $("#msgEm").show();
        return false;
    }
    if (fileName.length > 100) {
        $(".icon-tannhao").text("文件名长度超过100字长度限制");
        $("#msgEm").show();
        return false;
    }
    if (fileSize > fileMaxSize) {
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content: alertWarning('上传附件最大为40M')
        });
        return false;
    }
    $("#hidfile").val("1");
    //提交到node服务端
    var uploadOpts = {
        success: function (rst) {
            var data = JSON.parse(rst);
            var code = data.code;
            $("#hidfile").val("0");
            if (code === '0') {
                $('#fileMsg').append('<div class="fileCell">' +
                    '<input class="h30 input-medium input-box  prompt-border-box" value= "' + fileName + '" filePath = ' + data.uploadFileId + '>' +
                    '<a class="prompt-btn" onclick="javascript:deleteFileMsg(this)" title="删除"><i class="iconfont icon-cha1"></i>' +
                    '</div>');
                $('#fileMsg').show();
                $('#investorFile').remove();//清空
                $('#investorFileInput').append('<input id = "investorFile" onchange="uploadInvestorFiles()" name = "investorFile", type = "file",value="选择文件", multiple = "true">');
            } else {
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '提示信息',
                    content: alertWarning('文件不得大于40M')
                });
            }
        }
    };
    var urForm = $('#investorForm');
    urForm.ajaxSubmit(uploadOpts);
}
function deleteFileMsg(linkObj) {
    $("#msgEm").hide();
    $('#investorFile').remove();
    $("#hidfile").val("0")
    $('#investorFileInput').append('<input id = "investorFile" onchange="uploadInvestorFiles()" name = "investorFile", type = "file",value="选择文件", multiple = "true">');
    $(linkObj).parent().remove();
}