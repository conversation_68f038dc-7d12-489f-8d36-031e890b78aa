/**
 * Created by d<PERSON><PERSON> on 2017/5/3.
 */
var investorPageNo = 1; //当前页码
var investorPageSize = $('#investor-content input.hiddenId').val();
var sortIndex = "release_time desc";//处理排序
//页面初始
$(function () {
    //初始化日期控件
    initDateTimerPicker("startDateTmp", "YYYY-MM-DD", "day");
    initDateTimerPicker("endDateTmp", "YYYY-MM-DD", "day");
    addDateLinster();
    //改变查看页大小
    $('#investor-content .in-pageNo ul').delegate('li', 'click', function () {
        if ($(this).text() != investorPageSize) {
            investorPageSize = $(this).text();
            setPageSizeCookie(investorPageSize);
            investorSearch(investorPageNo, investorPageSize);
        }
    });
    //查询加载列表
    investorSearch(investorPageNo, investorPageSize);
    //新增投资者关系
    $('#addInvestor').on('click', function () {
        doAddInvestor();
    });
    $('#impInvestor').on('click', function () {

        doImpInvestor();
    });
    //查询按钮
    $('#btnQuery').on('click', function () {
        //收集查询条件
        investorSearch(investorPageNo, investorPageSize);
    });
    //删除无效投资者关系
    $('#indexData').delegate('a[name=btnInvestorDel]', 'click', function () {
        var thisId = $(this).attr('data-id');
        var investorVo = {};
        investorVo.investorId = thisId;
        $.confirm({
            animation: 'opacity',
            closeAnimation: 'scale',
            confirmButton: '确定',
            title: '投资者关系删除',
            content: alertWarning('确定要删除此投资者关系吗？'),
            confirm: function () {
                deleteInvestor(investorVo);
            }
        });
    });
    //排序改变
    $('#sortOrders').on('click', function () {
        var $this = $(this);
        var btn = $this.find('btn-a');
        if (btn.hasClass('clickon-a')) {
            btn.removeClass('clickon-a');
            btn.addClass('click-a');
            sortIndex = 'release_time asc';//正序
        } else {
            btn.removeClass('click-a');
            btn.addClass('clickon-a');
            sortIndex = 'release_time desc';//倒序
        }
        investorSearch(investorPageNo, investorPageSize);
    });
});

/**
 * 导入全景网公告
 * @param userId
 */
function doImpInvestor() {

    var sysPara={};
    var pageNo=$.trim($('#title').val());
    if (pageNo!=""){
        sysPara.page=parseInt(pageNo);
    }else{
        sysPara.page=1;
    }

    sysPara.size=30;
    //console.log("start..."+sysPara.page);

    var flag=0;

    $.post('/investor/imp', sysPara, function (data) {
        var dataObj = JSON.parse(data);
        //console.log(dataObj.rows);

        var i = 0;
        if (dataObj.rows.length>0) {
            for ( i = 0; i < dataObj.rows.length; i++) {

                //console.log("insert start...");

                var investorData = {};
                investorData.category = "1";
                var titlen=dataObj.rows[i].s3;
                if(titlen.indexOf("：")>-1){
                    titlen=titlen.substring(titlen.indexOf("：")+1,100);
                }
                investorData.title = titlen;
                investorData.status = 3;
                investorData.content = "全景网导入";
                investorData.releaseTime = dataObj.rows[i].s4;
                investorData.fileName = dataObj.rows[i].s2;
                investorData.filePath = dataObj.rows[i].s2;

                $.ajaxSettings.async = false;
                $.post('/investor/impa', investorData, function (data) {
                    //console.log(investorData.title+"导入：" + data);
                    if(data != "0"){
                        flag++;
                    }

                });
                $.ajaxSettings.async = true;

            }
            //console.log("flag=" + flag);
            if (flag > 0){
                if (doCallBackAlert(flag+'条导入成功', dataObj)) {
                    investorSearch(investorPageNo, investorPageSize);
                }
            }else {
                doCallBackAlert('没有可导入的新数据', dataObj);
            }
        }else{
            doCallBackAlert('没有可导入的新数据', dataObj);
        }

    });

}

/**
 * 删除无效用户
 * @param userId
 */
function deleteInvestor(sysUserVo) {
    $.post('/investor/d', sysUserVo, function (data) {
        if (doCallBackAlert('投资者关系删除成功', data)) {
            investorSearch(investorPageNo, investorPageSize);
        }
    });
}

//查询
function investorSearch(pageNo, pageSize) {
    showTableLoading('indexData');
    var investorConditionVo = {};
    investorConditionVo.pageSize = pageSize == null ? $('#investor-content input.hiddenId').val() : pageSize;
    investorConditionVo.pageNo = pageNo;
    investorConditionVo.title = $.trim($('#title').val());
    investorConditionVo.status = $('#status').find('li.selected').attr('data-value');
    investorConditionVo.category = $('#category').find('li.selected').attr('data-value');
    investorConditionVo.releaseTimeStart = $.trim($('#releaseTimeStart').val());
    investorConditionVo.releaseTimeEnd = $.trim($('#releaseTimeEnd').val());
    investorConditionVo.ticker = new Date().getTime();
    investorConditionVo.sortIndex = sortIndex;//处理排序字段
    $.post('/investor/q', investorConditionVo, function (data) {
        $('#indexData').empty().html(data);
        var totalPages = $("#cusPageCount").val();
        var options = {
            currentPage: pageNo,  //直接跳转到特定的page
            totalPages: totalPages,//总页数
            useBootstrapTooltip: false,
            onPageChanged: function (event, oldPage, newPage) {
                investorSearch(newPage, pageSize);
            }
        }
        $('#example').bootstrapPaginator(options);
    });
}

//执行新增投资者关系
function doAddInvestor() {
    $.confirm({
        confirmButtonPlural: "保存",
        animation: 'opacity',
        confirmTop: '10%',
        closeAnimation: 'scale',
        columnClass: 'col-md-10 col-md-offset-1',
        title: '添加投资者关系',
        confirmButton: false,
        content: function ($obj) {
            return $.get('/investor/s', function (data) {
                $obj.setContent(data);
            });
        },
        confirmPlural: function () {
            if (doValidation()) {
                doSave();
                return true;
            }
            return false;
        }
    });
}
/**
 * 验证表单
 * @returns {boolean}
 */
function doValidation() {
    if (!$('#investorForm').isValid()) {
        return false;
    }

    //验证上传文件控件
    var uploadFileVal = $('.prompt-border-box').attr('filePath');
    if (uploadFileVal == undefined) {
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '信息提示',
            content: alertWarning('请选择上传文件')
        });
        return false;
    }
    if ($('#releaseTimeS input').val() == "") {
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '信息提示',
            content: alertWarning('请选择发布时间')
        });
        return false;
    }

    return true;
}
function doSave() {
    var investorData = {};
    investorData.category = $('#category-sel').find('li.selected').attr('data-value');
    investorData.title = $('input[name="title"]').val();
    investorData.content = $('#content').val();
    investorData.releaseTime = $('#releaseTime').val();
    investorData.fileName = $('.prompt-border-box').val();
    investorData.filePath = $('.prompt-border-box').attr('filePath');
    $.post('/investor/a', investorData, function (data) {
        if (doCallBackAlert('投资者关系添加成功', data)) {
            investorSearch(investorPageNo, investorPageSize);
        }
    });
    return true;
}
