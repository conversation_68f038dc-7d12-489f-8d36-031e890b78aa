/**
 * Created by yubin on 2016/1/27.
 */
$('#commitBtn').on('click',function(){
    var username = $('#username').val();
    if(username==''){
       $('#fpem').text('请输入您的账号、邮箱或手机号');
        return false;
    }
    $.ajax({
        url : '/login/fp',
        data : {'username':username},
        async: false,
        dataType:'json',
        type : "POST",
        success : function(data) {
            if(data['result'] == CONST_SUCCESS){
                var url=location.host;
                window.location.href="http://"+url+'/login/sfpr';
            }else{
                $('#fpem').text(data['message'][ERROR_KEY][0]);
            }
        },
        error:function(er) {
            //TODO 跳转到错误页面
            var msg = "<div>调用后台服务出错[" + er.status + "]: " + er.statusText + "</div>";
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '错误提示',
                content: msg
            });
        }
    });
})

$('#backBtn').on('click',function(){
    window.location.href="/login/lo";
})

$('#reValiBtn').on('click',function(){
    window.location.href="/login/sfpp";
})