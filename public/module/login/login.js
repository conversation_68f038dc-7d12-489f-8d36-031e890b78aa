/**
 * Created by yubin on 2016/1/27.
 */
$(function(){
    //光标定位用户名输入框
    $('#username').focus();
});
$('#loginBtn').on('click', function () {
    var username = $('#username').val();
    var pwd = $('#password').val();
    if (username == '') {
        $('#loginem').text('请输入用户名');
        return false;
    }
    if (pwd == '') {
        $('#loginem').text('请输入密码');
        return false;
    }
    var loginVo = {};
    loginVo['username'] = username;
    loginVo['password'] = pwd;
    $.post("/login/li", loginVo, function (data) {
        var dataResult = JSON.parse(data);
        if (dataResult.userId) {
            window.location.href = "http://" + location.host + "/index";
        } else {
            $('#loginem').text(dataResult.message['object.error.message'][0]);
        }
    });

});