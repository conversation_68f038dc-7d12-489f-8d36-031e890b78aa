/**
 * Created by <PERSON><PERSON><PERSON> on 2016/1/28.
 */
$('#tijiao').on('click',function(){
    var userId=$('#userId').val();
    var token=$('#token').val();
    var newPassword = $('#newPassword').val();
    var confirmPassword = $('#confirmPassword').val();
    if(userId==''){
        $('#rpem').text('用户ID为空，请检查后重新发起请求');
        return false;
    }
    if(token==''){
        $('#rpem').text('token为空，请检查后重新发起请求');
        return false;
    }
    if(confirmPassword==''){
        $('#rpem').text('请输入确认密码');
        return false;
    }
    if(newPassword==''){
        $('#rpem').text('请输入密码');
        return false;
    }
    if(confirmPassword==''){
        $('#rpem').text('请输入确认密码');
        return false;
    }
    if(newPassword!=confirmPassword){
        $('#rpem').text('新密码与确认密码不一致');
        return false;
    }
    var rpVo = {};
    rpVo['userId'] = userId;
    rpVo['token'] = token;
    rpVo['password'] = newPassword;
    $.ajax({
        url : '/login/rp',
        data : rpVo,
        async: false,
        dataType:'json',
        type : "POST",
        success : function(data) {
            if(data['result'] == CONST_SUCCESS){
                $('#rpem').text('成功重置密码,页面跳转中...');
                $('#tijiao').attr('disabled',"true");
                setTimeout(function () {
                    var url=location.host;
                    window.location.href="/login/lo";
                }, 2000);

            }else{
                $('#rpem').text(data['message'][ERROR_KEY][0]);
            }
        },
        error:function(er) {
            //TODO 跳转到错误页面
            var msg = "<div>调用后台服务出错[" + er.status + "]: " + er.statusText + "</div>";
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '错误提示',
                content: msg
            });
        }
    });
});