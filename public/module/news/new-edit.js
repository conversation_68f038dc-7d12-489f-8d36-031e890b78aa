$(document).ready(function () {
    //初始化发布状态
    initFunction();
    //初始化发布时间
    initDateTimerPicker("publishTime", "YYYY-MM-DD", "day");
});


function initFunction() {
    $('#onYes').on('click', function () {
        if ($(this).find('radioemon').length <= 0) {
            $("#statusHidden").val("1");
            $('#onNo').removeClass('radioemon');
            $('#onYes').addClass('radioemon');
        }
    });
    $('#onNo').on('click', function () {
        if ($(this).find('radioemon').length <= 0) {
            $("#statusHidden").val("0");
            $('#onYes').removeClass('radioemon');
            $('#onNo').addClass('radioemon');
        }
    });
}


