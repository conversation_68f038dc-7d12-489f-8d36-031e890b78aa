var newsPageNo = 1; //当前页码
var newsPageSize = 10;
var sortIndex = 'n.create_on desc';
$(document).ready(function () {
    $('input[type="text"]').val("");
    initPage(newsPageNo, newsPageSize);
    //初始化日期控件
    initDateTimerPicker("startDateTmp", "YYYY-MM-DD", "day");
    initDateTimerPicker("endDateTmp", "YYYY-MM-DD", "day");
    $("#iSearch").attr("onclick", "newsSearch(1)");
    //改变查看页大小
    $('#news-content .in-pageNo ul').delegate('li', 'click', function () {
        if ($(this).text() != newsPageSize) {
            newsPageSize = $(this).text();
            setPageSizeCookie(newsPageSize);
            newsSearch(newsPageNo, newsPageSize);
        }
    });
    $('#news-content .in-pageNo ul').find('li:eq(1)').click();//默认每页显示10条数据
    newsSearch(newsPageNo, newsPageSize);//加载列表
    $('#indexData').delegate('a[name=operation-ab]', 'click', function () {
        showUpdateNews(this);
    });
    $('#indexData').delegate('a[name=operation-ac]', 'click', function () {
        deleteNews(this);
    });
    $('#indexData').delegate('a[name=operation-ae]', 'click', function () {
        previewPage(this);
    });
    //创建时间排序
    $('#sortCreateTime').on('click', function () {
        sortClick('n.create_on', this)
    });
    //修改时间排序
    $('#sortUpdateTime').on('click', function () {
        sortClick('n.create_on', this);
    });
    addDateLinster();
});
//排序触发
function sortClick(sortDate, obj) {
    var btn = $(obj).find('btn-a');
    if (btn.hasClass('clickon-a')) {
        btn.removeClass('clickon-a');
        btn.addClass('click-a');
        sortIndex = sortDate + ' asc';//正序
    } else {
        btn.removeClass('click-a');
        btn.addClass('clickon-a');
        sortIndex = sortDate + ' desc';//倒序
    }
    newsSearch(newsPageNo, newsPageSize);
}
//初始化分页
function initPage(pageNo, pageSize) {
    var totalPages = $("#cusPageCount").val();
    var options = {
        currentPage: pageNo,  //直接跳转到特定的page
        totalPages: totalPages,//总页数
        useBootstrapTooltip: false,
        onPageChanged: function (event, oldPage, newPage) {
            newsSearch(newPage, pageSize);
        }
    };
    $('#example').bootstrapPaginator(options);
}
/**
 * 弹出修改页面
 */
var newsUpdateAlert;
function showUpdateNews(obj) {
    newsUpdateAlert = $.confirm({
        confirmButtonPlural: "修改",
        animation: 'opacity',
        confirmTop: '4.5%',
        closeAnimation: 'scale',
        columnClass: 'col-md-12',
        title: '修改新闻',
        confirmButton: false,
        content: function ($obj) {
            var result = {};
            result.newsId = $(obj).attr("data-id");
            return $.post('/news/u/l', result, function (data) {
                $obj.setContent(data);
                addCategoryLinster();
            });
        },
        confirmPlural: function () {//修改
            updateNews();
            return false;
        }
    });
}
/**
 * 修改新闻
 * @returns {boolean}
 */
function updateNews() {
    if (!$('#newsForm').isValid()) {
        return false;
    } else {
        if ($('#summernote').summernote("isEmpty")) {
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '提示',
                content: alertWarning('请输入新闻内容')
            });
            return false;
        }
        var news = {};
        news.newsId = $.trim($("#newsIdHidden").val());
        news.title = $.trim($("#popTitle").val());
        news.newsSource = $.trim($("#popNewsSource").val());
        news.categoryName = $.trim($('#popNewsCateId input.hiddenName').val());
        news.createOn = $.trim($('#publishTimeInput').val());
        if (news.createOn == undefined || news.createOn == null || news.createOn == '') {
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '提示',
                content: alertWarning('请选择发布日期')
            });
            return false;
        }
        var picMgid = $("#newsPicImg").attr("src");
        var picDetail = $("#newsPicDetail").val();
        if (picMgid == "../images/upimg.png" || picMgid == "") {
            news.picMgid = "/images/newsbig.jpg";
        } else {
            news.picMgid = picMgid.replace(imgDomain, "");
        }
        news.picDetail = picDetail;
        news.content = $('#summernote').summernote('code');
        news.status = $("#statusHidden").val();
        news.description = $.trim($('#description').val());
        news.keywords = $.trim($('#keywords').val());
        news.sortIndex = sortIndex;
        $.post("/news/u", news, function (data) {
            var dataObj = JSON.parse(data);
            newsPageNo = parseInt($('#currentPage').val());
            if (dataObj.result != undefined) {
                newsUpdateAlert.close();
                newsSearch(newsPageNo, newsPageSize);
                alertSucceed("编辑成功");
                return true;
            }
            else {
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '提示',
                    content: alertError("编辑失败")
                });
                return true;
            }

        });
    }
}
//删除新闻
function deleteNews(obj) {
    $.confirm({
        animation: 'opacity',
        closeAnimation: 'scale',
        confirmButton: '确定',
        title: '删除新闻',
        content: alertWarning("确定删除新闻"),
        confirm: function () {
            var news = {};
            news.newsId = $(obj).attr("data-id");
            news.deleteMark = 1;
            $.post("/news/d", news, function (data) {
                var dataObj = JSON.parse(data);
                newsPageNo = parseInt($('#currentPage').val());
                if (dataObj.result != undefined) {
                    newsSearch(newsPageNo, newsPageSize);
                    alertSucceed("删除成功");
                }
                else {
                    $.alert({
                        animation: 'opacity',
                        closeAnimation: 'scale',
                        title: '提示',
                        content: alertError("删除失败")
                    });
                }
            });
        }
    });
}
/**
 * 弹出保存页面
 */
var newsSaveAlert;
function showSaveNews() {
    newsSaveAlert = $.confirm({
        confirmButtonPlural: "保存",
        animation: 'opacity',
        confirmTop: '4.5%',
        closeAnimation: 'scale',
        columnClass: 'col-md-12',
        title: '添加新闻',
        confirmButton: false,
        content: function ($obj) {
            return $.get('/news/a/l?' + new Date().getMilliseconds(), function (data) {
                $obj.setContent(data);
                addCategoryLinster();
            });
        },
        confirmPlural: function () {//保存
            saveNews();
            return false;
        }
    });
}
/**
 * 保存新闻
 * @returns {boolean}
 */
function saveNews() {
    if (!$('#newsForm').isValid()) {
        return false;
    } else {
        if ($('#summernote').summernote("isEmpty")) {
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '提示',
                content: alertWarning('请输入新闻内容')
            });
            return false;
        }
        var news = {};
        news.title = $.trim($("#popTitle").val());
        news.newsSource = $.trim($("#popNewsSource").val());
        news.categoryName = $.trim($('#popNewsCateId input.hiddenName').val());
        var picMgid = $("#newsPicImg").attr("src");
        if (picMgid == "../images/upimg.png" || picMgid == "") {
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '提示',
                content: alertWarning('请上传新闻图片')
            });
            return false;
        } else {
            news.picMgid = picMgid.replace(imgDomain, "");
        }
        news.content = $('#summernote').summernote('code');
        news.picDetail = $("#newsPicDetail").val();
        news.createOn = $.trim($('#publishTimeInput').val());
        if (news.createOn == undefined || news.createOn == null || news.createOn == '') {
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '提示',
                content: alertWarning('请选择发布日期')
            });
            return false;
        }
        news.status = $("#statusHidden").val();
        news.description = $.trim($('#description').val());
        news.keywords = $.trim($('#keywords').val());
        news.sortIndex = sortIndex;
        $.post("/news/a", news, function (data) {
            var dataObj = JSON.parse(data);
            if (dataObj.result != undefined) {
                newsSaveAlert.close();
                newsSearch(newsPageNo, newsPageSize);
                alertSucceed("保存成功");
            } else {
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '提示',
                    content: alertWarning('保存失败')
                });
            }
        });
    }
}

//上传图片
function uploadNewsImage() {
    var orgnHtml = $("#newsPic").parent().html();//获取input file 没写value前的html；解决IE同路径文件无法重复上传问题
    var formData = new FormData();
    var fileInput = $('#newsPic')[0];
    
    if (fileInput.files.length > 0) {
        formData.append('file', fileInput.files[0]);
    }

    $.ajax({
        type: 'POST',
        url: updImgDomain + "/uploadImages", /*填写后台上传文件的路径*/
        data: formData,
        processData: false,  // 告诉jQuery不要处理发送的数据
        contentType: false,  // 告诉jQuery不要设置内容类型头
        success: function (data) {/*url为上传成功后返回的图片路径*/
            var result = data;
            if (result.status == "SUCCESS") {
                //$("#newsPic").parent().html(orgnHtml);//重写html
                $("#newsPicImg").attr("src", imgDomain + result.data);
            }
            else {
                var message = result.message == "" ? "上传失败" : result.message;
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '信息提示',
                    content: alertError(message)
                });
                $("#newsPic").parent().html(orgnHtml);//重写html
            }

        },
        error: function (msg) {
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '信息提示',
                content: alertError('上传失败。')
            });
            $("#newsPic").parent().html(orgnHtml);//重写html
        }

    });
    return false;
}
/**
 * 新闻搜索
 * @param pageNo
 * @param pageSize
 */
function newsSearch(pageNo, pageSize) {
    showTableLoading('indexData');
    var dataRequest = {};
    dataRequest.pageSize = pageSize == null ? $('#news-content input.hiddenId').val() : pageSize;
    dataRequest.pageNo = pageNo;
    dataRequest.title = $.trim($("#title").val());
    dataRequest.begTime = $.trim($("#begTime").val());
    dataRequest.endTime = $.trim($("#endTime").val());
    dataRequest.status = $.trim($('#status input[name="status"]').val());
    dataRequest.sortIndex = sortIndex;
    dataRequest.ticker = new Date().getTime();
    $.post('/news/q/ac', dataRequest, function (data) {
        $('#indexData').empty().html(data);
        initPage(pageNo, pageSize);
    });
}

/**
 * 显示“新增分类”的全部元素
 */
function showAddCategory() {
    $('#add-category').find('i').removeClass('add');
    $('#add-category').find('i').addClass('ec-show');
    $('#add-category').find('p').removeClass('ec-show');
}
/**
 * 隐藏“新增分类”的全部元素
 */
function hiddenAddCategory() {
    $('#add-category').find('i').removeClass('ec-show');
    $('#add-category').find('i').addClass('add');
    $('#add-category').find('p').addClass('ec-show');
}

/**
 * 增加新增分类的监听
 */
function addCategoryLinster() {
    $('#add-category').delegate('i', 'click', function () {//点击+号时 显示新增分类项
        showAddCategory();
    });
    $('#btnAddCategory').on('click', function () {//点击"保存"时，保存并隐藏新增商品分类的元素
        var categoryName = $.trim($('#categoryName').val());
        if (categoryName == "") {
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '错误提示',
                content: alertWarning('请输入新闻分类名称')
            });
        }
        else {
            addCategory();
        }
    });
    $('#btnCancel').on('click', function () {//点击"取消"时，保存并隐藏新增商品分类的元素
        hiddenAddCategory();
    });
}

/**
 * 添加分类
 */
function addCategory() {
    var dataRequest = {};
    dataRequest.categoryname = $.trim($("#categoryName").val());
    $.post('/newscategory/a', dataRequest, function (data) {
        var dataObj = JSON.parse(data);
        if (dataObj.result != undefined) {
            alertSucceed("添加成功");
            $('#popNewsCateId').find('ul').append('<li data-value="' + dataObj.newsCateId + '">' + dataObj.name + '</li>').find('li[data-value="' + dataObj.newsCateId + '"]').click();
            $('#popNewsCateId').find('.select-button').val(dataObj.name);
            $("#categoryName").val("");
            hiddenAddCategory();
        }
        else {
            var message = dataObj.message[ERROR_KEY][0];
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '提示',
                content: alertError(message)
            });
        }
    });
}
//预览
function previewPage(obj) {
    window.open($("#shoppingUrl").val() + '/news/index/qd/' + $(obj).attr("data-id"));
}
