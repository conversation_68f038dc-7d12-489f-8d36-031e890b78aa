var pageNo = 1; //当前页码
var pageSize = 100;
$(document).ready(function () {
    $('input[type="text"]').val("");
    initPage();
});
/**
 * 初始化
 */
function initPage() {
    var totalPages = $("#cusPageCount").val();
    var options = {
        currentPage: pageNo,  //直接跳转到特定的page
        totalPages: totalPages,//总页数
        useBootstrapTooltip: false,
        onPageChanged: function (event, oldPage, newPage) {
            categorySearch(newPage, pageSize);
        }
    }
    $('#indexData').delegate('a[name=operation-a]', 'click', function() {
        editCategoryHtml(this);
    });
    $('#indexData').delegate('a[name=operation-ab]', 'click', function() {
        delCategory(this);
    });
    $('#example').bootstrapPaginator(options);
};
/**
 * 生成编辑样式
 * @param obj
 */
function editCategoryHtml(obj) {
    var parent = $(obj).parents('tr');
    var val = parent.find('.td1').text();
    //var id = parent.attr('id');
    var txt = "<td class='td1' width='50%'><input type='text' name='txt' class='input-medium input-box' value='" + val + "'></td>";
    var editbt = "<td class='td2' width='50%'><a class='input-btnfile rolelist-btnfile float-l' href='javascript:;' onclick=\"editCategory(this,'" + val + "');\">保存</a><a class='input-btnfile rolelist-btnfile float-l' href='javascript:;' onclick=\"cancelCategoryHtml(this,'" + val + "');\">取消</a></td>";
    parent.html(txt + editbt);
}
/**
 * 删除分类
 * @param obj
 */
function delCategory(obj) {
    var parent = $(obj).parents('tr');
    var id = parent.attr('id');
    $.confirm({
        animation: 'opacity',
        closeAnimation: 'scale',
        confirmButton: '确定',
        title: '删除分类',
        content: alertWarning("确定删除此分类"),
        confirm: function () {
            var dataRequest = {};
            dataRequest.newsCateId = id;
            $.post('/newscategory/d', dataRequest, function (data) {
                var dataObj = JSON.parse(data);
                if (dataObj.result != undefined) {
                    alertSucceed("删除成功");
                    categorySearch(1, pageSize);
                }
                else {
                    var message = dataObj.message[ERROR_KEY][0];
                    $.alert({
                        animation: 'opacity',
                        closeAnimation: 'scale',
                        title: '提示',
                        content: alertWarning(message)
                    });
                }
            });
        }
    });
}

/**
 * 添加分类
 */
function addCategory() {
    var name = $.trim($("#categoryname").val());
    if (name != "") {
        if(specialCharValidate('新闻分类名称不能含有',name)){
            $('#categoryname').val('');
            return false;
        }
        var dataRequest = {};
        dataRequest.categoryname = $.trim($("#categoryname").val());
        $.post('/newscategory/a', dataRequest, function (data) {
            var dataObj = JSON.parse(data);
            if (dataObj.result != undefined) {
                $("#categoryname").val("");
                alertSucceed('添加成功');
                categorySearch(1, pageSize);
            }
            else {
                var message = dataObj.message[ERROR_KEY][0];
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '提示',
                    content: alertWarning(message)
                });
            }
        });
    } else {
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content: alertWarning('请输入分类名称')
        });
    }


}

/**
 * 取消按钮事件
 * @param obj
 * @param val
 */
function cancelCategoryHtml(obj, val) {
    var parent = $(obj).parents('tr');
    //var txt1=parent.find('.td1 input').val();
    var txt = "<td width='50%' class='td1'>" + val + "</td>";
    var editbt = "<td width='50%' class='td2'><div class='zxmsh-a'><i class='iconfont shbtn' onclick='editCategoryHtml(this)'>&#xe619;<div class='tooltip top in'><div class='tooltip-arrow'></div><div class='tooltip-inner'>编辑</div></div></i></div><div class='zxmsh-a zxmsh-b'><i class='iconfont shbtn' onclick='delCategory(this)'>&#xe61f;<div class='tooltip top in'><div class='tooltip-arrow'></div><div class='tooltip-inner'>删除</div></div></i></div></td>";
    parent.html(txt + editbt);
}

/**
 * 修改按钮时间
 * @param obj
 * @param val
 */
function editCategory(obj, val) {
    var parent = $(obj).parents('tr');
    var txt = parent.find('.td1 input').val();//分类名称
    var warn = "";
    if (txt == val) {
        warn = "<div class='modal-p'> <em class='modal-p-warning'></em><em class='modal-p-inner'>未做任何修改</em></div>";
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '警告',
            content: warn,
            confirm: function () {
                return true;
            }
        });
    }
    else {
        var dataRequest = {};
        dataRequest.name = txt;
        dataRequest.newsCateId = parent.attr('id');//分类id
        $.post('/newscategory/u', dataRequest, function (data) {
            var dataObj = JSON.parse(data);
            if (dataObj.result != undefined) {
                cancelCategoryHtml(obj,parent.find('.td1 input').val());
                //categorySearch(1, pageSize);
            }
            else {
                var message = dataObj.message[ERROR_KEY][0];
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '提示',
                    content: '<div class="modal-p"><em class="modal-p-warning"></em><em class="modal-p-inner">' + message + '</em></div>'
                });
            }

        });
    }


}
/**
 * 分类查找
 * @param pageNo
 * @param pageSize
 */
function categorySearch(pageNo, pageSize) {
    var dataRequest = {};
    dataRequest.pageSize = pageSize;
    dataRequest.pageNo = pageNo;
    $.post('/newscategory/q/l', dataRequest, function (data) {
        $('#indexData').empty().html(data);
        var totalPages = $("#cusPageCount").val();
        var options = {
            currentPage: pageNo,  //直接跳转到特定的page
            totalPages: totalPages,//总页数
            useBootstrapTooltip: false,
            onPageChanged: function (event, oldPage, newPage) {
                categorySearch(newPage, pageSize);
            }
        }
        $('#example').bootstrapPaginator(options);
    });
}

