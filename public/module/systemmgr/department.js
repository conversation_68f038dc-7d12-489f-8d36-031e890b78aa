/**
 * Created by d<PERSON><PERSON> on 2017/5/3.
 */
var departmentPageNo = 1; //当前页码
var departmentPageSize = $('#department-content input.hiddenId').val();
//页面初始
$(function () {
    //改变查看页大小
    $('#department-content .in-pageNo ul').delegate('li', 'click', function () {
        if ($(this).text() != departmentPageSize) {
            departmentPageSize = $(this).text();
            setPageSizeCookie(departmentPageSize);
            departmentSearch(departmentPageNo, departmentPageSize);
        }
    });
    //查询加载列表
    departmentSearch(departmentPageNo, departmentPageSize);
    //新增部门
    $('#addDepartment').on('click', function () {
        doAddDepartment();
    });
    //查询按钮
    $('#btnQuery').on('click', function () {
        //收集查询条件
        departmentSearch(departmentPageNo, departmentPageSize);
    });
    //编辑
    $('#indexData').delegate('a[name=btnDepartmentUpdate]', 'click', function () {
        var thisId = $(this).attr('data-id');
        showEdit(thisId);
    });
    //删除无效部门
    $('#indexData').delegate('a[name=btnDepartmentDel]', 'click', function () {
        var thisId = $(this).attr('data-id');
        var dataIDs = thisId.split('@@');
        var deptId = '';
        var deptName = '';
        if(dataIDs.length > 1){
         deptId = dataIDs[0];
         deptName = dataIDs[1];
        }else {
            deptId = thisId;
        }
        var departmentVo = {};
        departmentVo.deptId = deptId;
        departmentVo.name = deptName;
        $.confirm({
            animation: 'opacity',
            closeAnimation: 'scale',
            confirmButton: '确定',
            title: '部门删除',
            content: alertWarning('确定要删除此部门吗？'),
            confirm: function () {
                deleteDepartment(departmentVo);
            }
        });
    });
});

/**
 * 删除无效用户
 * @param userId
 */
function deleteDepartment(sysUserVo) {
    $.post('/department/d', sysUserVo, function (data){
        if(doCallBackAlert('部门删除成功',data)){
            departmentSearch(departmentPageNo, departmentPageSize);
        }
    });
}

//查询
function departmentSearch(pageNo, pageSize) {
    showTableLoading('indexData');
    var name = $.trim($('#q_name').val());
    var departmentConditionVo = {};
    departmentConditionVo.pageSize = pageSize == null ? $('#department-content input.hiddenId').val() : pageSize;
    departmentConditionVo.pageNo = pageNo;
    departmentConditionVo.name = name;
    departmentConditionVo.ticker = new Date().getTime();
    $.post('/department/q', departmentConditionVo, function (data) {
        $('#indexData').empty().html(data);
        var totalPages = $("#cusPageCount").val();
        var options = {
            currentPage: pageNo,  //直接跳转到特定的page
            totalPages: totalPages,//总页数
            useBootstrapTooltip: false,
            onPageChanged: function (event, oldPage, newPage) {
                departmentSearch(newPage, pageSize);
            }
        }
        $('#example').bootstrapPaginator(options);
    });
}

//执行新增部门
function doAddDepartment() {
    $.confirm({
        confirmButtonPlural: "保存",
        animation: 'opacity',
        confirmTop: '10%',
        closeAnimation: 'scale',
        columnClass: 'col-md-6 col-md-offset-3',
        title: '添加部门',
        confirmButton: false,
        content: function ($obj) {
            var department = {};
            department.deptId = null;
            return $.post('/department/s', department, function (data) {
                $obj.setContent(data);
            });
        },
        confirmPlural: function () {
            if(doValidation()){
                doSave();
                return true;
            }
            return false;
        }
    });
}

function doSave() {
    var userData = {};
    var deptId = $('#deptId').val();
    userData.name = $('#name').val();
    if (deptId) {//修改操作
        userData.deptId = deptId;
        $.post('/department/u', userData, function (data) {
            if (doCallBackAlert('部门修改成功', data)) {
                departmentSearch(departmentPageNo, departmentPageSize);
            }
        });
    } else {
        $.post('/department/a', userData, function (data) {
            if (doCallBackAlert('部门添加成功', data)) {
                departmentSearch(departmentPageNo, departmentPageSize);
            }
        });
    }
    return true;
}

/**
 * 弹出修改用户页面
 * @param userId
 */
function showEdit(deptId) {
    var currentUser = {};
    var currentTR = $('#tr' + deptId);
    var tds = currentTR.children('td');
    currentUser.deptId = deptId;
    currentUser.name = tds.eq(0).text();
    $.confirm({
        confirmButtonPlural: '保存',
        animation: 'opacity',
        confirmTop: '10%',
        closeAnimation: 'scale',
        columnClass: 'col-md-6 col-md-offset-3',
        title: '修改部门',
        confirmButton: false,
        content: function ($obj) {
            return $.post('/department/s', currentUser, function (data) {
                $obj.setContent(data);
            });
        },
        confirmPlural: function () {
            if(doValidation()){
                doSave();
                return true;
            }
            return false;
        }
    });
}

/**
 * 验证表单
 * @returns {boolean}
 */
function doValidation(){
    if(! $('#departmentForm').isValid()){
        return false;
    }
    return true;
}