/**
 * Created by ch<PERSON><PERSON><PERSON><PERSON><PERSON> on 2016/3/29.
 */
var pageNo = 1; //当前页码
var pageSize = $('#log-content input.hiddenId').val();
var sortIndex = "operate_time desc";//处理排序

//页面初始
$(document).ready(function () {
    //初始化日期控件
    initDateTimerPicker("startDateTmp", "YYYY-MM-DD", "day");
    initDateTimerPicker("endDateTmp", "YYYY-MM-DD", "day");
    //改变查看页大小
    $('#log-content .in-pageNo ul').delegate('li', 'click', function () {
        if ($(this).text() != pageSize) {
            pageSize = $(this).text();
            setPageSizeCookie(pageSize);
            logsSearch(pageNo, pageSize);
        }
    });
    //查询加载列表
    logsSearch(pageNo, pageSize);
    //查询按钮
    $('#btnQuery').on('click', function () {
        //收集查询条件
        logsSearch(pageNo, pageSize);
    });
    //排序改变
    $('#sortOrders').on('click', function () {
        var $this = $(this);
        var btn =  $this.find('btn-a');
        if(btn.hasClass('clickon-a')){
            btn.removeClass('clickon-a');
            btn.addClass('click-a');
            sortIndex = 'operate_time asc';//正序
        }else {
            btn.removeClass('click-a');
            btn.addClass('clickon-a');
            sortIndex = 'operate_time desc';//倒序
        }
        logsSearch(pageNo, pageSize);
    });
    //添加日期的监听
    addDateLinster();
});

/**
 * 查询日志
 * @param pageNo
 * @param pageSize
 */
function logsSearch(pageNo, pageSize){
    showTableLoading('indexData');
    var logConditionVo =collectLogConditionVo(pageNo, pageSize);
    $.post('/log/q', logConditionVo, function (data) {
        $('#indexData').empty().html(data);
        var totalPages = $("#cusPageCount").val();
        var options = {
            currentPage: pageNo,  //直接跳转到特定的page
            totalPages: totalPages,//总页数
            useBootstrapTooltip: false,
            onPageChanged: function (event, oldPage, newPage) {
                logsSearch(newPage, pageSize);
            }
        }
        $('#example').bootstrapPaginator(options);
    });
}

/**
 * 收集日志的查询条件
 * @param pageNo
 * @param pageSize
 * @returns {{}}
 */
function collectLogConditionVo(pageNo, pageSize){
    var logConditionVo={};
    logConditionVo.operateTimeStart= $.trim($('#startDate').val());//开始时间
    logConditionVo.operateTimeEnd= $.trim($('#endDate').val());//结束时间
    logConditionVo.mobile=$.trim($('#mobile').val());//操作人手机
    logConditionVo.username =encodeURI($.trim($('#username').val()));//操作人账号
    logConditionVo.remark =  encodeURI($.trim($('#remark').val()));//操作内容
    logConditionVo.sortIndex=sortIndex;//处理排序字段
    logConditionVo.pi=pageNo;
    logConditionVo.ps= pageSize== null ?
        $('#log-content input.hiddenId').val()
        : pageSize;
    return logConditionVo;
}