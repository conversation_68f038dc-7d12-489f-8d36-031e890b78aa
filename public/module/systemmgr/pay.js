/**
 * Created by ch<PERSON><PERSON><PERSON><PERSON> on 2016/3/28.
 */

//页面初始
$(document).ready(function () {
    //新增角色
    $('#btnSave').on('click', function () {
        //校验
        if ($('#payForm').isValid()) {
            var payChannelVo = {};
            payChannelVo.payChannelName='支付宝';
            payChannelVo.partner=$.trim($('#partner').val());
            payChannelVo.accountNum=$.trim($('#accountNum').val());
            payChannelVo.secretKey=$.trim($('#secretKey').val());
            doSave(payChannelVo);
        }
    });

});
/**
 * 执行保存操作
 * @param payChannelVo
 */
function  doSave(payChannelVo){
    $.post('/pay/e', payChannelVo, function (data){

        if(doCallBackAlert('支付方式配置成功',data)){

        }
    });

}