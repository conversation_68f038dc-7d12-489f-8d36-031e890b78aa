/**
 * Created by ch<PERSON><PERSON><PERSON><PERSON> on 2016-06-29 .
 */


//页面初始
$(document).ready(function () {
    // 支付配置 管理
    addPayMgrLinster();

});

/**
 * 添加支付管理监听
 */
function addPayMgrLinster(){
    $(document).delegate('li.sm-payment-manag a.sm-payment-a', 'click', function () {
        var $this = $(this);
        var payType=$this.attr('data-id');
        $.confirm({
            animation: 'opacity',
            closeAnimation: 'scale',
            columnClass: 'col-md-8 col-md-offset-2',
            title: '支付管理',
            confirmTop:'4.5%',
            confirmButton:'确定',
            content: function ($obj) {
                return $.get( '/pay/s/' +payType+'?ticker='+new Date().getTime(), function (data) {
                    $obj.setContent(data);
                    //处理单选的监听
                    addRedioEvent();
                });
            },
            confirm: function (){//保存
                return doSavePay(function(data){
                    //处理保存后的提示
                    if(doCallBackAlert('支付配置成功',data)){
                        refreshPayList();
                    }
                });
            }
        });
    });
}

function refreshPayList(){
    showTableLoading('payChannelTable');
    $.get('/pay/q?ticker='+new Date().getTime(), function (data) {
        $('#payChannelTable').empty().html(data);
    });
}
