/**
 * Created by ch<PERSON><PERSON><PERSON><PERSON> on 2016-06-29 .
 */
var pay_status = 0;//默认是停用状态
/*

//页面初始
$(document).ready(function () {
    //添加单选监听
    addRedioEvent();
});
*/


/**
 * 单选多选处理
 */
function  addRedioEvent(){
    pay_status = $('#payForm').attr('init-status');
    $('#onYes').on('click', function () {
        //alert('yes');
        if( $(this).find('sm-radioon').length<=0){
            pay_status=1;
            $('#onNo').removeClass('sm-radioon');
            $('#onYes').addClass('sm-radioon');
        }
    });

    $('#onNo').on('click', function () {
        //alert('no');
        if( $(this).find('sm-radioon').length<=0){
            pay_status=0;
            $('#onYes').removeClass('sm-radioon');
            $('#onNo').addClass('sm-radioon');
        }
    });
}

/**
 * 执行保存操作
 * @param payChannelVo
 */
function  doSavePay(callback){
    //校验
    if ($('#payForm').isValid()) {
        var payChannelVo = {};
        payChannelVo.status=pay_status;
        payChannelVo.payChannelName=$('#payForm').attr('data-id');
        payChannelVo.partner=$.trim($('#partner').val());
        payChannelVo.accountNum=$.trim($('#accountNum').val());
        payChannelVo.secretKey=$.trim($('#secretKey').val());
        $.post('/pay/e', payChannelVo, function (data){
            callback(data);
        });
        return true;
    }
  return false;
}