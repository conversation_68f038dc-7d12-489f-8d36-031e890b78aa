/**
 * Created by r<PERSON><PERSON> on 2016/2/22.
 */
var currentRoleId ='';
var rolePresetStatus=0;
var groupActions=['orderMgr','goodsList','customerList','roleList','sysuserList','goodsCategoryList',
    'newsCategoryMgr','newsMgr','shippingMgr','customerMgr','websiteContentMgr','questionnaireMgr',
    'couponMgr','departmentList','agencyList','honortList','approvedList','investorList','vendorList',
    'contactUsMgr','contactUsList','employmentList','modelMgr','modelChange','sysuserDelete','newsPublish'];
//页面初始
$(document).ready(function () {
    var sumItem = $('#tabData').find('li');
    var indexLi = sumItem.length;
    var maxLen = 0;
    for(var i=0;i<indexLi;i++){
        var widthLi=sumItem.eq(i).width();
        maxLen+=widthLi;
    }
    if(maxLen<$('#tabData').width()){
        $('.rs-tab-scorll').hide();
    }
    currentRoleId = $('#tabData').attr('firstRoleId');
    //loadTab();
    loadActions();
    //页签切换
    $('#tabData').delegate('li a', 'click', function(event) {
        var $this = $(this);
        currentRoleId = $this.attr('id');
        rolePresetStatus = $this.attr('presetStatus');
        //重新刷新界面
        loadActions();
    });
    //保存按钮
    $('#permissionSave').on('click',function(){
        if(rolePresetStatus==1){
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '提示',
                content:alertWarning('内置角色不能修改权限'),
                confirm: function () {
                    //重新刷新界面
                    loadActions();
                }
            });
            return;
        }
        doSave();

    });
    //选中或取消分组自动“选中或取消”下面的全部actions
    $('#indexData').delegate('input[type="checkbox"][name="group"]', 'click', function(event) {
        var $this = $(this);
        var groupId = $this.attr('data-id');
        var checked = $this.is(':checked');
        $('input[type="checkbox"][name="action"][group-id="' +groupId+ '"]').each(function(i, el){
            $(el).prop('checked', checked);
        });
    });

    /**
     * tab 右箭头逻辑
     * <EMAIL> 2016/3/1 add
     */
    $('.arrowRight').click(function(){
        if($(this).hasClass('no')) return;
        var $tabsRole = $('.nav-bigbox ul.nav-tabs');
        var liLen = $tabsRole.find('li').length;
        var indexL=$tabsRole.find('li:visible').last().index();
        var indexF=$tabsRole.find('li:visible').first().index();
        var activeIndex = $tabsRole.find('li.active').index();
        var sum = indexL + 1;
        var navBigBoxW =$('.nav-bigbox').width() - 35;

        if(nextLiRightSize == 0 || sum == liLen){
            $(this).addClass('no');
            return;
        }else{
            $tabsRole.find('li').eq(indexL+1).show();
            $tabsRole.find('li').eq(indexF).hide();
            var visibleLiWidthSum = getVisibleLiWidthSum();
            if(visibleLiWidthSum > navBigBoxW){
                $tabsRole.find('li').eq(indexF+1).hide();
                if(activeIndex==indexF){
                    $tabsRole.find('li').eq(indexF+2).find('a').click();
                }
            }else{
                if(activeIndex==indexF){
                    $tabsRole.find('li').eq(indexF+1).find('a').click();
                }
            }

            $(this).prev().removeClass('no');
            var nextLiRightSize = $tabsRole.find('li').eq(indexL).nextAll('li').length;
            if(sum  == liLen - 1){
                $(this).addClass('no');
            }
        }
    });

    /**
     * tab 左箭头逻辑
     * <EMAIL> 2016/3/1 add
     */
    $('.arrowLeft').click(function(){
        if($(this).hasClass('no')) return;
        var $tabsRole = $('.nav-bigbox ul.nav-tabs');
        var liLen = $tabsRole.find('li').length;
        var indexL=$tabsRole.find('li:visible').last().index();
        var indexF=$tabsRole.find('li:visible').first().index();
        var activeIndex = $tabsRole.find('li.active').index();
        var sum = indexF + 1;
        var navBigBoxW =$('.nav-bigbox').width() - 35;

        if(nextLiLeftSize == 0){
            $(this).addClass('no');
            return;
        }else{
            $tabsRole.find('li').eq(indexF-1).show();
            var visibleLiWidthSum = getVisibleLiWidthSum();
            if(visibleLiWidthSum > navBigBoxW){
                $tabsRole.find('li').eq(indexL).hide();
                if(activeIndex==indexL){
                    $tabsRole.find('li').eq(indexL-1).find('a').click();
                }
            }else{
                if(activeIndex==indexL){
                    $tabsRole.find('li').eq(indexL-1).find('a').click();
                }
            }

            $(this).next().removeClass('no');
            var nextLiLeftSize = $tabsRole.find('li').eq(indexF).prevAll('li').length;
            if(nextLiLeftSize - 1  == 0){
                $(this).addClass('no');
            }
        }

    });

    //选中action ,自动选中分组 和隐藏的checkbox
    $('#indexData').delegate('input[type="checkbox"][name="action"]', 'click', function(event) {
        var $this = $(this);
        var groupId = $this.attr('group-id');
        var checked = $this.is(':checked');
        if(checked){
            //选中分组
            $('#group_'+groupId).prop('checked', checked);
            //选中隐藏action
            $('input[type="checkbox"][name="action"][group-id="' +groupId+ '"]').each(function(i, el){
                if( $(el).is(':hidden')){
                    $(el).prop('checked', checked);
                }
            });
        }
    });
    initTabs();
});

//隐藏和分组同级的action
function  hiddenGroupAction(){
    var length = groupActions.length;
    for(var i=0;i<length;i++){
        $('#'+groupActions[i]).parent('span').hide();
    }
}
//加载页签
function loadTab(){
    var ticker = new Date().getTime();
    var url = '/permission/roles/tab/'+currentRoleId+'?'+ticker;
    //发送请求
    $.get(url,function(data){
        $('#tabData').empty().html(data);
        rolePresetStatus= $('#'+currentRoleId).attr('presetStatus');
    });
}

//加载actions列表信息
function  loadActions(){
    var ticker = new Date().getTime();
    var url = '/permission/r/actions/'+currentRoleId+'?'+ticker;
    $.get(url, function (data) {
        $('#indexData').empty().html(data);
        hiddenGroupAction();
    });
}
//执行保存操作
function  doSave(){
    //收集数据
    var array = new Array();
    $('input[type="checkbox"][name="action"]').each(function(i, el){
        var $this = $(el);
        if($this.is(':checked')){
            array.push($this.attr('data-id'));
        }
    });
    //alert('ary: '+ array.join(','));
    var url='/permission/u/'+currentRoleId;
    var actions = {};
    actions.actionIds=JSON.stringify(array);
    $.post(url,actions , function (data) {
        doCallBackAlert('权限设置成功',data);
    });
}

/**
 * tab 左箭头逻辑 begin
 */
function initTabs(){
    var $navBigBox = $('.nav-bigbox');
    var tabsLen = $navBigBox.find('ul li').length;
    var navBigBoxW =$navBigBox.width() - 35;
    var sumLiW = 0;
    $navBigBox.find('ul li').each(function (index, el) {
        var $this = $(el),$thisW = $this.width();
        sumLiW += $thisW;
        if(sumLiW > navBigBoxW){
            $this.hide();
        }
    });
};

function getVisibleLiWidthSum(){
    var sumLiW = 0;
    $('.nav-bigbox').find('ul li:visible').each(function (index, el) {
        var $this = $(el),$thisW = $this.width();
        sumLiW += $thisW;
    });
    return sumLiW;
};
/**
 * tab 左箭头逻辑 end
 */

