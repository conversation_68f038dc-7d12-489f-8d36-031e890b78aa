/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2016/2/15.
 */
var secondHtml={};
//页面初始
$(document).ready(function () {
    //新增角色
    $('#addRoleBtn').on('click',function(){
        var roleName=$('#roleName').val();
        //校验
        var isValid= validRoleMaxSize() && validRole(roleName);
        if(isValid){
            var roleInfoVo={};
            roleInfoVo['roleName'] = roleName;
            doAddRole(roleInfoVo);
        }
        else{

        }
    });

    //删除角色
    $('#indexData').delegate('a[name=btnRoleDel]', 'click', function() {
        var roleId =$(this).attr('data-id');
        var roleInfoVo={};
        roleInfoVo.roleId = roleId;
        var curTr = $('#tr'+roleId);
        var fistTd = curTr.children('td').eq(0);
        var roleName =  fistTd.html();
        roleInfoVo.roleName=roleName;
        $.confirm({
            animation: 'opacity',
            closeAnimation: 'scale',
            confirmButton: '确定',
            cancelButton: '取消',
            //columnClass: 'col-md-5',
            title: '角色删除',
            content: alertWarning('确定删除角色吗？'),
            confirm: function () {
                doDeleteRole(roleInfoVo);
            }
        });
    });

    //编辑角色
    $('#indexData').delegate('a[name=btnRoleUpdate]', 'click', function() {
        if($('#editSave').length>0){
            return;
        }
        var roleId =$(this).attr('data-id');
        var curTr = $('#tr'+roleId);
        var fistTd = curTr.children('td').eq(0);
        var roleName =  fistTd.html();

        editHtml(fistTd,curTr.children('td').eq(1),roleId,roleName);
        addEditSaveLinster();
        addEditCancelLinster();
    });

    //设置权限
    $('#indexData').delegate('a[name=btnActionSet]', 'click', function() {
        var roleId =$(this).attr('data-id');
        var url='/permission/r/q/'+roleId;
        window.location.href = url;
    });

});
/**
 * 角色列表刷新
 */
function roleSearch() {
    var ticker = new Date().getTime();
    $.get('/role/q?'+ticker, function (data) {
        $('#indexData').html(data);
    });
}

function  validRole(roleName){
    //var msg='按钮触发成功'+roleName;
    if(roleName==''){
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '警告',
            content:alertWarning('请输入角色名称')
        });
        return false;
    }
    if(specialCharValidate('角色名称不能含有',roleName)){
        $('#roleName').val('');
        return false;
    }
    return true;
}
//校验角色最多数目
function validRoleMaxSize(){
    var rolesize = $('#indexData').children('tr').size();
    if(rolesize>=20){
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '错误提示',
            content:'<div class="modal-p"><em class="modal-p-warning"></em><em class="modal-p-inner">最多只能有20个角色</em></div>'
        });
        return false;
    }
    return true;
}
//执行增加角色
function doAddRole(roleInfoVo){
    $.post('/role/a', roleInfoVo, function (data){
       $('#roleName').val('');
       if(doCallBackAlert('角色添加成功',data)){
            roleSearch();
        }
    });
}
//执行修改角色
function doUpdateRole(roleInfoVo){
    $.post('/role/u', roleInfoVo, function (data){
        if(doCallBackAlert('角色修改成功',data)){
            var curTr = $('#tr'+roleInfoVo.roleId);
            cancelEditHtml(curTr.children('td').eq(0),curTr.children('td').eq(1),roleInfoVo.roleName);
        }
    });
}
//执行删除角色
function doDeleteRole(roleInfoVo){
    $.post('/role/d', roleInfoVo, function (data){
        if(doCallBackAlert('角色删除成功',data)){
            roleSearch();
        }
    });
 }

function  editHtml(fistTd,secendTd,roleId,roleName){
    secondHtml=secendTd.html();
    fistTd.html(
        '<input type="text" id="editTxt" id_data="' +roleId+'" class="input-medium input-box" value="'+roleName+'" hidden-value="' +roleName+
        '" />');
    secendTd.html(
        '<a href="javascript:void(0);" id="editSave" class="input-btnfile rolelist-btnfile float-l">保存</a> ' +
        '<a href="javascript:void(0);" id="editCancel" class="input-btnfile rolelist-btnfile float-l">取消</a>');
}

function  cancelEditHtml(fistTd,secendTd,roleName){
    fistTd.html(roleName);
    secendTd.html(secondHtml);
}

//添加事件监听,编辑的“保存”按钮
function  addEditSaveLinster(){
    //保存编辑后的角色
    $('#editSave').on('click',function(){
        var roleName=$('#editTxt').val();
        var roleId=$('#editTxt').attr('id_data');
        //校验
        var isValid= validRole(roleName);
        if(isValid){
            var roleInfoVo={};
            roleInfoVo['roleId'] = roleId;
            roleInfoVo['roleName'] = roleName;
            doUpdateRole(roleInfoVo);
        }
    });
}
//添加事件监听，编辑的“取消”按钮
function addEditCancelLinster(){
//取消编辑
    $('#editCancel').on('click',function(){
        var roleName=$('#editTxt').attr('hidden-value');
        var roleId=$('#editTxt').attr('id_data');
        var curTr = $('#tr'+roleId);
        cancelEditHtml(curTr.children('td').eq(0),curTr.children('td').eq(1),roleName);
    });
}
