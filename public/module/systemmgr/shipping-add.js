/**
 * Created by ch<PERSON><PERSON><PERSON><PERSON> on 2016-05-30 .
 */
//页面初始
/*$(document).ready(function () {
    _addCheckLinster();
}*/

//-----------------------------------------下面是物流公司添加页面操作的js
var _pageNo=1;
/**
 * 初始化
 */
function initShippingAdd(){
    //添加选中监听
    _addCheckLinster();
//初始化分页方法
    initPaginator(_pageNo);

}

/**
 * 选中监听
 * @private
 */
function _addCheckLinster(){
     $('#shippingCheckAll').on('click', function () {
         var $this = $(this);
         var checked = $this.is(':checked');
         $('input[type="checkbox"][name="shippingCheck"]').each(function(i, el){
              $(el).prop('checked', checked);
         });
     });
}
/**
 * 收集选中的商品
 */
function collectSelectedShippings(){
    var shippings = new Array();
    $('input[type="checkbox"][name="shippingCheck"]').each(function(i, el){
        if( $(el).is(':checked')){
            shippings.push($(el).attr('data-id'));
        }
    });
    return shippings;
}

/**
 * 初始化分页方法
 */
function initPaginator(pageNo){
    var totalPages = $("#_cusPageCount").val();
    var options = {
        currentPage: pageNo,  //直接跳转到特定的page
        totalPages: totalPages,//总页数
        useBootstrapTooltip: false,
        onPageChanged: function (event, oldPage, newPage) {
            _pageNo=pageNo;
            _shippingSearch(newPage);
        }
    }
    $('#shippingPage').bootstrapPaginator(options);
}


/**
 *执行新加物流公司的操作
 * @returns {boolean}
 */
function  _addShipping(callback){
    var shippingRefRequestVo = {};
    var shippings = collectSelectedShippings();
    if(shippings.length <=0 ){
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content: alertWarning('请至少选择一家物流公司')
        });
        return false;
    }
    shippingRefRequestVo.shippingIds = JSON.stringify(shippings);
    $.post('/shipping/batch/a', shippingRefRequestVo, function (data) {
        if(callback!==undefined){
            callback(data);
        }
    });
    //alert(111);
    return true;
}

/**
 * 执行查询操作
 * @private
 */
function _shippingSearch(pageNo){
    var osShippingVo = {};
    osShippingVo.shippingName = $.trim($('#_shippingName').val());
    osShippingVo.pageNo=pageNo;
    showTableLoading('shippingIndexData');
    $.post('/shipping/s/q', osShippingVo, function (data) {
        _pageNo=pageNo;
        $('#shippingIndexData').empty().html(data);
        initPaginator(_pageNo);
    });
    $('#shippingCheckAll').attr('checked',false);
}

//是否取消全选按钮
function _isCancelAllCheck(obj)
{
    var isChecked =$(obj).prop("checked");
    if(!isChecked)
    {
        $("#shippingCheckAll").prop("checked",false);
    }
}