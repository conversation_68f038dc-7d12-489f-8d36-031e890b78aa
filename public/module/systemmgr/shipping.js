/**
 * Created by r<PERSON><PERSON> on 2016-05-26 .
 */

var pageNo = 1; //当前页码
var pageSize = $('#content input.hiddenId').val();

//页面初始
$(document).ready(function () {
    //改变查看页大小
    $('#content .in-pageNo ul').delegate('li', 'click', function () {
        if ($(this).text() != pageSize) {
            pageSize = $(this).text();
            setPageSizeCookie(pageSize);
            shippingSearch(pageNo, pageSize);
        }
    });
    //查询加载列表
    shippingSearch(pageNo, pageSize);
    //查询按钮
    $('#btnQuery').on('click', function () {
        //收集查询条件
        shippingSearch(pageNo, pageSize);
    });
    //删除
    $('#indexData').delegate('a[name=operation-a]', 'click', function() {
        var thisId = $(this).attr('data-id');
        var shippingRefRequestVo = {};
        shippingRefRequestVo.refId=thisId;
        $.confirm({
            animation: 'opacity',
            closeAnimation: 'scale',
            confirmButton: '确定',
            //columnClass: 'col-md-5',
            title: '常用物流删除',
            content: alertWarning('确定要删除物流吗？'),
            confirm: function () {
                doDeleteShipping(shippingRefRequestVo);
            }
        });
    });

});

/**
 * 查询商家的物流
 * @param pageNo
 * @param pageSize
 */
function shippingSearch(pageNo, pageSize){
    showTableLoading('indexData');
    var conditionVo =collectConditionVo(pageNo, pageSize);
    $.post('/shipping/q', conditionVo, function (data) {
        $('#indexData').empty().html(data);
        var totalPages = $("#cusPageCount").val();
        var options = {
            currentPage: pageNo,  //直接跳转到特定的page
            totalPages: totalPages,//总页数
            useBootstrapTooltip: false,
            onPageChanged: function (event, oldPage, newPage) {
                shippingSearch(newPage, pageSize);
            }
        }
        $('#example').bootstrapPaginator(options);
    });
}

/**
 * 收集查询条件
 * @param pageNo
 * @param pageSize
 * @returns {{}}
 */
function collectConditionVo(pageNo, pageSize) {
    var conditionVo = {};
    conditionVo.shippingName = $.trim($('#shippingNameQuery').val());//物流公司名称,encodeURI
    conditionVo.pageNo = pageNo;
    conditionVo.pageSize = (pageSize != null ? pageSize :
        $('#content input.hiddenId').val());
    return conditionVo;
}


/**
 *跳转到新增页面
 */
function gotoAddShippingPage(){
    var title='添加常用物流公司';
    $.confirm({
        animation: 'opacity',
        confirmTop:'4.5%',
        closeAnimation: 'scale',
        columnClass: 'col-md-12',
        title: title,
        confirmButton: true,
        confirmButton:'确定',
        content:function ($obj) {
            return $.get( '/shipping/s?ticker='+new Date().getTime(), function (data) {
                $obj.setContent(data);
                //处理的监听
                initShippingAdd();
            });
        },
        confirm: function (){//确定
            return _addShipping(function (data){
                if(doCallBackAlert('物流公司添加成功',data)) {
                    shippingSearch(pageNo, pageSize);
                }
            });
        }
    });
}

/**
 * 执行删除操作
 * @param shippingRefRequestVo
 */
function  doDeleteShipping(shippingRefRequestVo){
    $.post('/shipping/d', shippingRefRequestVo, function (data) {
        if(doCallBackAlert('物流公司删除成功',data)) {
            shippingSearch(pageNo, pageSize);
        }
    });
    //alert(shippingRefRequestVo.refId);
}