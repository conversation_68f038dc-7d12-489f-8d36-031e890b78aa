/**
 * Created by r<PERSON><PERSON> on 2016/2/24.
 */

var questionnairePageNo = 1; //当前页码
var questionnairePageSize = $('#customer-content input.hiddenId').val();
var sysuserObj;
//页面初始
$(document).ready(function () {
    //$('#customer-content .in-pageNo ul').val(10);
    //改变查看页大小
    $('#customer-content .in-pageNo ul').delegate('li', 'click', function () {
        if ($(this).text() != questionnairePageSize) {
            questionnairePageSize = $(this).text();
            setPageSizeCookie(questionnairePageSize);
            sysuserSearch(questionnairePageNo, questionnairePageSize);
        }
    });
    //查询加载列表
    sysuserSearch(questionnairePageNo, questionnairePageSize);
    //新增用户
    $('#addSysuser').on('click', function () {
        doAddSysuser();
    });
    //查询按钮
    $('#btnQuery').on('click', function () {
        //收集查询条件
        sysuserSearch(questionnairePageNo, questionnairePageSize);
    });
    //全选、全清
    $('#customerAll').on('click', function () {
        var $this = $(this);
        var checked = $this.is(':checked');
        $('input[type="checkbox"][name="sysUserCheck"]').each(function(i, el){
            $(el).prop('checked', checked);
        });
    });
    //启用
    $('#indexData').delegate('a[name=sysuserStart]', 'click', function() {
        var thisId = $(this).attr('data-id');
        changeStatus(thisId, 1);
    });
    //暂停
    $('#indexData').delegate('a[name=sysuserPause]', 'click', function() {
        var thisId = $(this).attr('data-id');
        changeStatus(thisId, 0);
    });
    //编辑
    $('#indexData').delegate('a[name=sysuserEdit]', 'click', function() {
        var thisId = $(this).attr('data-id');
        showEdit(thisId);
    });
    //修改密码
    $('#indexData').delegate('a[name=sysuserPwdEdit]', 'click', function() {
        var thisId = $(this).attr('data-id');
        showPwdEdit(thisId);
    });
    //删除无效用户
    $('#indexData').delegate('a[name=sysuserDelete]', 'click', function() {
        var thisId = $(this).attr('data-id');
        var sysUserVo = {} ;
        sysUserVo.userId=thisId;
        $.confirm({
            animation: 'opacity',
            closeAnimation: 'scale',
            confirmButton: '确定',
            title: '商户删除',
            content: alertWarning('确定要删除此商户吗？'),
            confirm: function () {
                deleteSysUser(sysUserVo);
            }
        });
    });

    //账号导出
    $('#sysUserExport').on('click', function () {
        //收集选中IDs
        var selectedIds = collectSelected();
        if(selectedIds.length==0){//导出全部
            $.confirm({
                animation: 'opacity',
                closeAnimation: 'scale',
                confirmButton: true,
                confirmButton:'确定',
                title: '导出全部账号',
                content: alertWarning('没有选中的账号，确定导出当前所有账号吗？'),
                confirm: function () {
                    doExportExcel(null);
                }
            });
        }else{//导出选中记录
            doExportExcel(selectedIds);
        }
    });
});
/**
 *执行导出
 */
function  doExportExcel(selectedIds){
    var sysUserConditionVo =collectSysUserConditionVo(questionnairePageNo, questionnairePageSize);
    sysUserConditionVo.userIds=JSON.stringify(selectedIds);
    sysUserConditionVo.ticker=new Date().getTime();
    var url=$.param(sysUserConditionVo);
    window.location.href = '/excel/sysUser?'+url;
}

function collectSysUserConditionVo(pageNo, pageSize) {
    var dataRequest = {};
    dataRequest.pageSize = pageSize == null ? $('#customer-content input.hiddenId').val() : pageSize;
    dataRequest.pageNo = pageNo;
    dataRequest.accountName = $.trim($("#q_accountName").val());
    dataRequest.mobile = $.trim($("#q_mobile").val());
    dataRequest.selectedRoleId = $('#status').find('li.selected').attr('data-value');
    dataRequest.ticker = new Date().getTime();
    return dataRequest;
}
/**
 * 收集选中的会员
 */
function collectSelected(){
    var array = new Array();
    $('input[type="checkbox"][name="sysUserCheck"]').each(function(i, el){
        if( $(el).is(':checked')){
            array.push($(el).attr('data-id'));
        }
    });
    return array;
}
//查询
function sysuserSearch(pageNo, pageSize) {
    showTableLoading('indexData');
    var accountName = $.trim($('#q_accountName').val());
    var mobile = $.trim($('#q_mobile').val());
    //获取选中的角色ID
    var selectedRoleId = $('#status').find('li.selected').attr('data-value');
    var sysuserConditionVo = {};
    sysuserConditionVo.pageSize = pageSize == null ? $('#customer-content input.hiddenId').val() : pageSize;
    sysuserConditionVo.pageNo = pageNo;
    sysuserConditionVo.accountName = accountName;
    sysuserConditionVo.mobile = mobile;
    sysuserConditionVo.selectedRoleId = selectedRoleId;
    sysuserConditionVo.ticker = new Date().getTime();
    $.post('/sysuser/q', sysuserConditionVo, function (data) {
        $('#indexData').empty().html(data);
        var totalPages = $("#cusPageCount").val();
        var options = {
            currentPage: pageNo,  //直接跳转到特定的page
            totalPages: totalPages,//总页数
            useBootstrapTooltip: false,
            onPageChanged: function (event, oldPage, newPage) {
                sysuserSearch(newPage, pageSize);
            }
        }
        $('#example').bootstrapPaginator(options);
    });
}

//执行新增用户
function doAddSysuser() {
    sysuserObj=$.confirm({
        confirmButtonPlural: "保存",
        animation: 'opacity',
        confirmTop:'10%',
        closeAnimation: 'scale',
        columnClass: 'col-md-10 col-md-offset-1',
        title: '添加账号',
        confirmButton: false,
        content: function ($obj) {
            var sysuser = {};
            sysuser.userId = null;
            return $.post('/sysuser/s', sysuser, function (data) {
                $obj.setContent(data);
            });
        },
        confirmPlural: function () {
            if(!validUserEdit()){
                return false;
            }else{
               return doSave();
            }
        }
    });
}


/**
 * 执行修改密码操作
 * @returns {boolean}
 */
function doUpdatePwd() {
    if($('#resetPwdForm').isValid()){
        var userId = $('#userId').val();
        var newPassword = $('#newPwd').val();
        var confirmPassword = $('#checkPwd').val();
        var loginVo = {};
        loginVo.userId = userId;
        loginVo.newPassword = newPassword;
        $.post('/sysuser/mrp', loginVo, function (data) {
            doCallBackAlert('密码修改成功', data);
        });
        return true;
    }
    return false;
}
/**
 * 验证新增用户或修改的表单
 */
function validUserEdit(){
    if(!$('#userEditForm').isValid()){
        return false;
    }

    var roles = collectCheckRoles();
    if(roles.length==0){
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content: alertWarning('请至少指定一个角色')
        });
        return false;
    }
    return true;
}

function doSave() {
    var userId = $('#userId').val();
    if (userId) {//修改操作
        var userData = collectSysuserInfos(userId);
        $.post('/sysuser/u', userData, function ( data) {
            if (doCallBackAlert('账号修改成功', data)) {
                sysuserSearch(questionnairePageNo, questionnairePageSize);
                sysuserObj.close();
            }
        });
    } else {
        var userData = collectSysuserInfos('');
        $.post('/sysuser/a', userData, function ( data) {
            if (doCallBackAlert('账号添加成功', data)) {
                sysuserSearch(questionnairePageNo, questionnairePageSize);
                sysuserObj.close();
            }
        });
    }
    return false;

}

function collectSysuserInfos(userId) {
    //登录账号
    var accountName = $('#accountName').val();
    //名称
    var name = $('#name').val();
    //手机
    var mobile = $('#mobile').val();
    //邮件
    var email = $('#email').val();
    //原版权限数量
    var roleNum = $('#roleNum').val();
    //收集权限信息
    var roles = collectCheckRoles();

    var sysuser = {};
    sysuser.userId = userId;
    sysuser.accountName = accountName;
    sysuser.name = name;
    sysuser.mobile = mobile;
    sysuser.email = email;
    sysuser.roleNum = roleNum;
    sysuser.deptId = $('.sp-registered-dlbox').find('input[name="deptId"]').val();
    var userRole = {};
    userRole.sysuser = JSON.stringify(sysuser);
    userRole.roles = JSON.stringify(roles);
    return userRole;
}
//收集选中的角色
function collectCheckRoles() {
    var array = new Array();
    $('input[type="checkbox"][name="role"]').each(function (i, el) {
        var $this = $(el);
        if ($this.is(':checked')) {
            array.push($this.attr('id'));
        }
    });
    return array;
}

/**
 * 弹出修改用户页面
 * @param userId
 */
function showEdit(userId) {
    var currentUser = {};
    var currentTR = $('#tr_' + userId);
    var tds = currentTR.children('td');
    currentUser.userId = userId;
    currentUser.accountName = tds.eq(1).text();
    currentUser.mobile = tds.eq(3).text();
    currentUser.name = tds.eq(4).text();
    currentUser.email = tds.eq(5).text();
    currentUser.deptId = tds.eq(6).find("input[name='deptId']").val();
    $.confirm({
        confirmButtonPlural: '保存',
        animation: 'opacity',
        confirmTop:'10%',
        closeAnimation: 'scale',
        columnClass: 'col-md-10 col-md-offset-1',
        title: '修改账号',
        confirmButton: false,
        content: function ($obj) {

            return $.post('/sysuser/s', currentUser, function (data) {
                $obj.setContent(data);
            });
        },
        confirmPlural: function () {
            if(!validUserEdit()){
                return false;
            }else{
                doSave();
            }
        }
    });
}
/**
 * 修改密码
 * @param userId
 */
function showPwdEdit(userId) {
    var currentUser = {};
    currentUser.userId = userId;
    $.confirm({
        animation: 'opacity',
        confirmTop:'10%',
        closeAnimation: 'scale',
        columnClass: 'col-md-6   col-md-offset-3',
        title: '重置账号密码',
        content: function ($obj) {

            return $.post('/sysuser/s/up', currentUser, function (data) {
                $obj.setContent(data);
            });
        },
        confirm: function () {
            return doUpdatePwd();
        }
    });
}

/**
 * 删除无效用户
 * @param userId
 */
function deleteSysUser(sysUserVo) {
    $.post('/sysuser/d', sysUserVo, function (data){
        if(doCallBackAlert('账号删除成功',data)){
            sysuserSearch(questionnairePageNo, questionnairePageSize);
        }
    });
}

/**
 *弹出修改启动状态提示
 * @param status
 */
function changeStatus(id, status) {
    var alertStr='您确定要暂停此账号吗?';
    var successStr='账号暂停成功';
    if(status==1){
        alertStr='您确定要启动此账号吗?';
        successStr='账号启动成功';
    }
    $.confirm({
        animation: 'opacity',
        closeAnimation: 'scale',
        confirmButton: '确定',
        title: '启动/暂停 账号',
        content: alertWarning(alertStr),
        confirm: function () {
            var sysuserVo = {};
            sysuserVo.userId = id;
            sysuserVo.status = status;
            $.post('/sysuser/u/s', sysuserVo, function (data) {
                if (doCallBackAlert(successStr, data)) {
                    sysuserSearch(1, questionnairePageSize);
                }
            });
        }
    });
}

/**
 * 导入账号
 */
function sysUserImport(){
    var importHtml = '<form id="sysUserImportForm" name="sysUserImportForm" enctype="multipart/form-data" method="POST" action="/sysuser/spi">' +
        '<div class="sm-img-div">'+
        '<em class="file  iconfont icon-shuchu"><input class="note-image-input" id="sysUserImportFile" name="sysUserImportFile" type="file" multiple="true" accept=".xls" onchange="return showText()"/>选择文件</em><i>(提示：只支持EXCEL<.xls>文档,文件不得大于20M)</i>'+
        '<em class="sm-imgnav"><input type="text" id = "fileShow" style="width:100%;border:0;background:transparent;" readonly="true"/></em></div></form>'
        +'<script>function showText(){document.getElementById("fileShow").value = document.getElementById("sysUserImportFile").value; }</script>';
    //var progressConfirm;
    $.confirm({
        animation: 'opacity',
        closeAnimation: 'scale',
        confirmButton:'导入',
        title: '批量导入账号',
        content: importHtml,
        confirm: function () {
            //验证上传文件控件
            var uploadFileVal = $('#sysUserImportFile').val();
            if(uploadFileVal == ''){
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '信息提示',
                    content: alertWarning('请选择上传文件')
                });
                return false;
            }
            if(uploadFileVal.split('.')[1]!='xls'){
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '信息提示',
                    content: alertWarning('请选择后缀是xls的文件')
                });
                return false;
            }
            //禁用确认按钮
            $('#sysUserImportBtn').attr("disable","disable");
            //提交到node服务端
            var uploadOpts = {
                async: false,
                clearForm: true,
                success:function(data){
                    if(doCallBackAlert('账号导入成功',data)){
                        sysuserSearch(questionnairePageNo, questionnairePageSize);
                    }
                }
            };
            var siForm = $('#sysUserImportForm');
            siForm.ajaxSubmit(uploadOpts);
        }
    });
}