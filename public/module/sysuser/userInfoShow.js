/**
 * Created by y<PERSON><PERSON> on 2016/1/27.
 */
var url = location.host;

/**
 * 个人中心tab点击事件
 */
$('#userInfoTab').on('click', function () {
    //切换tab
    $('#userInfoLi').addClass("active");
    $('#editPwdLi').removeClass("active");
    //切换div
    $('#userInfoShowDiv').show();
    $('#userInfoEditDiv').hide();
    $('#userPwdEditDiv').hide();
});

/**
 * 修改密码tab点击事件
 */
$('#editPwdTab').on('click', function () {
    //切换tab
    $('#userInfoLi').removeClass("active");
    $('#editPwdLi').addClass("active");
    //切换div
    $('#userInfoShowDiv').hide();
    $('#userInfoEditDiv').hide();
    $('#userPwdEditDiv').show();

    clearPwd();
});

//返回首页
$('#toHomepageBtn').on('click', function () {
    window.location.href = "http://" + url;
});

/**
 * "编辑信息"按钮点击事件
 */
$('#editUserBtn').on('click', function () {
    //填充编辑值
    $('#mobile').val($('#mobileShow').text());
    $('#username').val($('#nameShow').text());
    $('#email').val($('#emailShow').text());
    $('#accountNameEditShow').html($('#accountNameShow').text());

    $('#uieem').text('');

    $('#userInfoShowDiv').hide();
    $('#userInfoEditDiv').show();
    $('#userPwdEditDiv').hide();
});

//-------------------------修改用户信息相关事件----------------------------
$('#saveUserBtn').on('click', function () {
    if (!$('#sysUserForm').isValid()) {
        return false;
    }
    else {
        var vo = {};
        vo.userId = $.trim($('#userId').val());
        vo.mobile = $.trim($('#mobile').val());
        vo.name = $.trim($('#username').val());
        vo.email = $.trim($('#email').val());
        vo.accountName = $.trim($('#accountName').val());
        $.post('/sysuser/usu', vo, function (data) {
            var dataResult=JSON.parse(data);
            if (dataResult.userId!=null) {
                //用户信息更新
                $('#userId').val(dataResult.userId);
                $('#mobile').val(dataResult.mobile);
                $('#username').val(dataResult.name);
                $('#email').val(dataResult.email);
                $('#accountNameEditShow').html(dataResult.accountName);

                $('#accountNameShow').html(dataResult.accountName);
                $('#mobileShow').html(dataResult.mobile);
                $('#nameShow').html(dataResult.name);
                $('#emailShow').html(dataResult.email);

                //切换div
                $('#userInfoShowDiv').show();
                $('#userInfoEditDiv').hide();
                $('#userPwdEditDiv').hide();
                alertSucceed("修改成功");
            } else {
                var message = '修改失败。'+dataResult['message'][ERROR_KEY][0];
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '提示',
                    content:alertError(message)
                });
            }
        });
    }
});
$('#cancelBtn').on('click', function () {
    $('#userInfoShowDiv').show();
    $('#userInfoEditDiv').hide();
    $('#userPwdEditDiv').hide();
});

//-------------------------修改密码tab相关事件----------------------------
$('#udtPwdBtn').on('click', function () {
    if (!$('#uptPwdForm').isValid()) {
        return false;
    }
    var userId = $('#userId').val();
    var password = $('#password').val();
    var newPassword = $('#newPassword').val();
    var confirmPassword = $('#confirmPassword').val();
    var vo = {};
    vo.userId = $.trim($('#userId').val());
    vo.password = $.trim($('#password').val());;
    vo.newPassword = $.trim($('#newPassword').val());
    $.post('/sysuser/up', vo, function (data) {
        var dataResult=JSON.parse(data);
        if (dataResult.result == CONST_SUCCESS) {
            //清空密码修改框中的旧数据
            clearPwd();
            //切换div
            $('#userInfoShowDiv').show();
            $('#userInfoEditDiv').hide();
            $('#userPwdEditDiv').hide();
            alertSucceed('修改密码成功');
            //切换tab
            $('#userInfoLi').addClass("active");
            $('#editPwdLi').removeClass("active");

        } else {
            $('#peem').text('原密码错误，请重新输入！');
        }
    });

});

$('#upeCancelBtn').on('click', function () {
    //切换tab
    $('#userInfoLi').addClass("active");
    $('#editPwdLi').removeClass("active");
    //切换div
    $('#userInfoShowDiv').show();
    $('#userInfoEditDiv').hide();
    $('#userPwdEditDiv').hide();
});

/**
 * 清空修改密码输入框数据
 */
function clearPwd() {
    $('#password').val('');
    $('#newPassword').val('');
    $('#confirmPassword').val('');
    $('#peem').text('');
}