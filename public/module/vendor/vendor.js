var pageNo = 1; //当前页码
var pageSize = 100;
$(document).ready(function () {

    $('input[type="text"]').val("");
    //新增
    $('#btnAddCategory').on('click', function () {
        var categoryName = $.trim($('#categoryname').val());
        if(validCategoryName(categoryName)){
            var goodsCategoryVo = {};
            goodsCategoryVo.name=categoryName;
            doAddCategory(goodsCategoryVo);
            //清空输入框
            $('input').val('');
        }
    });
    //修改
    $('#indexData').delegate('a[name=operation-a]', 'click', function() {
        editCategoryHtml(this);
    });
    //删除
    $('#indexData').delegate('a[name=operation-ab]', 'click', function() {
        delCategory(this);
    });
});
//执行增加厂商
function doAddCategory(goodsCategoryVo){
    $.post('/vendor/a', goodsCategoryVo, function (data){
        if(doCallBackAlert('厂商添加成功',data)){
            categorySearch();
        }
        $('#categoryName').val('');
    });
}
/**
 * 生成编辑样式
 * @param obj
 */
function editCategoryHtml(obj) {
    var parent = $(obj).parents('tr');
    var val = parent.find('.td1').text();
    var txt = "<td class='td1' width='50%'><input maxlength='200' type='text' name='txt' class='input-medium input-box' value='" + val + "'></td>";
    var editbt = "<td class='td2' width='50%'>" +
        "<a class='input-btnfile rolelist-btnfile float-l' href='javascript:;' " +
        "onclick=\"editCategory(this,'" + val + "');\">保存</a>" +
        "<a class='input-btnfile rolelist-btnfile float-l' href='javascript:;' " +
        "onclick=\"cancelCategoryHtml(this,'" + val + "');\">取消</a></td>";
    parent.html(txt + editbt);
}
/**
 * 删除分类
 * @param obj
 */
function delCategory(obj) {
    var parent = $(obj).parents('tr');
    var id = parent.attr('id');
    $.confirm({
        animation: 'opacity',
        closeAnimation: 'scale',
        confirmButton: '确定',
        title: '删除厂商',
        content: alertWarning("确定删除此厂商"),
        confirm: function () {
            var dataRequest = {};
            dataRequest.vendorId = id;
            $.post('/vendor/d', dataRequest, function (data) {
                var dataObj = JSON.parse(data);
                if (dataObj.result != undefined) {
                    alertSucceed("删除成功");
                    categorySearch();
                }
                else {
                    var message = dataObj.message[ERROR_KEY][0];
                    $.alert({
                        animation: 'opacity',
                        closeAnimation: 'scale',
                        title: '提示',
                        content: alertWarning(message)
                    });
                }
            });
        }
    });
}

/**
 * 添加分类
 */
function addCategory() {
    var name = $.trim($("#categoryname").val());
    if (name != "") {
        if(specialCharValidate('厂商名称不能含有',name)){
            return false;
        }
        var dataRequest = {};
        dataRequest.categoryname = $.trim($("#categoryname").val());
        $.post('/newscategory/a', dataRequest, function (data) {
            var dataObj = JSON.parse(data);
            if (dataObj.result != undefined) {
                $("#categoryname").val("");
                alertSucceed('添加成功');
                categorySearch();
            }
            else {
                var message = dataObj.message[ERROR_KEY][0];
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '提示',
                    content: alertWarning(message)
                });
            }
        });
    } else {
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content: alertWarning('请输入分类名称')
        });
    }


}

/**
 * 取消按钮事件
 * @param obj
 * @param val
 */
function cancelCategoryHtml(obj, val) {
    var parent = $(obj).parents('tr');
    //var txt1=parent.find('.td1 input').val();
    var txt = "<td width='50%' class='td1'>" + val + "</td>";
    var editbt = "<td width='50%' class='td2'>" +
        "<div class='zxmsh-a'><i class='iconfont shbtn' " +
        "onclick='editCategoryHtml(this)'>&#xe619;" +
        "<div class='tooltip top in'><div class='tooltip-arrow'>" +
        "</div><div class='tooltip-inner'>编辑</div></div></i></div>" +
        "<div class='zxmsh-a zxmsh-b'><i class='iconfont shbtn'" +
        " onclick='delCategory(this)'>&#xe61f;<div class='tooltip top in'>" +
        "<div class='tooltip-arrow'></div><div class='tooltip-inner'>删除</div></div></i></div></td>";
    parent.html(txt + editbt);
}

/**
 * 修改按钮时间
 * @param obj
 * @param val
 */
function editCategory(obj, val) {
    var parent = $(obj).parents('tr');
    var txt = parent.find('.td1 input').val();//分类名称
    var warn = "";
    if (txt == val) {
        warn = "<div class='modal-p'> <em class='modal-p-warning'></em><em class='modal-p-inner'>未做任何修改</em></div>";
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '警告',
            content: warn,
            confirm: function () {
                return true;
            }
        });
    }
    else {
        var dataRequest = {};
        dataRequest.name = txt;
        dataRequest.vendorId = parent.attr('id');//分类id
        $.post('/vendor/u', dataRequest, function (data) {
            var dataObj = JSON.parse(data);
            if (dataObj.result != undefined) {
                cancelCategoryHtml(obj,parent.find('.td1 input').val());
                //categorySearch(1, pageSize);
            }
            else {
                var message = dataObj.message[ERROR_KEY][0];
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '提示',
                    content: '<div class="modal-p"><em class="modal-p-warning"></em><em class="modal-p-inner">' + message + '</em></div>'
                });
            }

        });
    }


}
/**
 * 分类查找
 * @param pageNo
 * @param pageSize
 */
function categorySearch() {
    $.post('/vendor/q/l', function (data) {
        $('#indexData').empty().html(data);
    });
}

function  validCategoryName(categoryName){
    //var msg='按钮触发成功'+roleName;
    if(categoryName==''){
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content:alertWarning('请输入厂商名称')
        });
        return false;
    }
    if(categoryName.length>30){
        $.alert({
            animation: 'opacity',
            closeAnimation: 'scale',
            title: '提示',
            content:alertWarning('厂商名称长度不能超过200个字<br><center>请重新输入</center>')
        });
        $('#categoryName').val('');
        return false;
    }
    return true;
}
