/**
 * Created by <PERSON><PERSON><PERSON>@ronglian.com on 2016/3/23.
 */

var customSettings;
var customBottom;
var customNavi;
var customLink;
var customModule;
var customSlide;

/**
 * 网站管理基础业务js
 */
if(typeof  jQuery === 'undefined'){
	throw new Error('custom-settings requires jQuery');
}
(function($){
	var CustomSettings = function () {};
	CustomSettings.prototype ={
		init : function(){
			var that = this, $obj = $('#customTempModule'),len = $obj.find('.cms-temp-c').length;
			var $cmsTempC = $obj.find('.cms-temp-c');

			$cmsTempC.find('.sm-moveup').show();
			$cmsTempC.find('.sm-movedown').show();
			$cmsTempC.eq(0).find('.sm-moveup').hide();
			$cmsTempC.eq(len-1).find('.sm-movedown').hide();
		},
		moveDown : function(obj){
			var that = this;
			var $this = $(obj), $cmsTempC = $this.parents('.cms-temp-c'),$cmsTempCLen = $cmsTempC.parents('#customTempModule').find('.cms-temp-c').length - 1;
			if($cmsTempC.index() != $cmsTempCLen){
				$cmsTempC.next().after($cmsTempC);
				that.init();
			}
		},
		moveUp : function(obj){
			var that = this;
			var $this = $(obj), $cmsTempC = $this.parents('.cms-temp-c');
			if($cmsTempC.index() != 0){
				$cmsTempC.prev().before($cmsTempC);
				that.init();
			}
		},
		deleteHtml : function (obj) {
			var that = this, $this = $(obj),$cmsTempC = $this.parents('.cms-temp-c');
			$cmsTempC.remove();
			that.init();
		}
	}

	customSettings = new CustomSettings();

}(jQuery));

/**
 * 底部编辑js
 */
if (typeof  jQuery === 'undefined') {
	throw new Error('custom-bottom-edit requires jQuery');
}
(function ($) {
	var CustomBottom = function () {};
	CustomBottom.prototype = {
		init : function(){
			var that = this, $obj = $('#sm-bottom-edit');
			$obj.html(that.getStairTemp());
		},
		getStairTemp : function(){
			return $('#create-stair').html();
		},
		getSubTemp: function () {
			return $('#create-sub').html();
		},
		// 获取整体bottom Vo对象
		getVoJson: function() {
			var that = this, $obj = $('#sm-bottom-edit'), jsonAry = [];
			$obj.find('.sm-footer-box').each(function(index, el){
				var $el = $(el);
				var bottomJson = that.getHtmlJson($el, index);
				jsonAry.push(bottomJson);
			})
			return jsonAry;
		},
		// 获取html 数据对象
		getHtmlJson: function(obj, index){
			var that = this, $obj = obj, jsonStr = {};
			jsonStr.orderNum = index;
			jsonStr.btmTabsName = $obj.find('input[name="stair-text"]').val();
			jsonStr.hyperlink = '';
			jsonStr.subList = that.getSubEditorJson($obj);
			return jsonStr;
		},
		// 获取二级链接json
		getSubEditorJson: function(obj){
			var $obj = obj, jsonAry = [];
			$obj.find('li').each(function(index, el){
				var $el = $(el), jsonStr = {};
				jsonStr.btmTabsName = $el.find('input[name="sub-text"]').val();
				jsonStr.orderNum = index;
				jsonStr.hyperlink = $el.attr('data-link');
				jsonAry.push(jsonStr);
			})
			return jsonAry;
		}
	}

	customBottom = new CustomBottom();

}(jQuery));

/**
 * 栏目编辑js
 */
if (typeof  jQuery === 'undefined') {
	throw new Error('custom-navi-edit requires jQuery');
}
(function ($) {
	var CustomNavi = function () {};
	CustomNavi.prototype = {
		init : function(){
			var that = this, $obj = $('#sm-navi-edit');
			$obj.html(that.getStairTemp());
		},
		getStairTemp : function(){
			return $('#create-new-tabs').html();
		},
		// 获取整体Vo对象
		getVoJson: function() {
			var that = this, $obj = $('#sm-navi-edit'), jsonAry = [];
			$obj.find('.list-group-item-new').each(function(index, el){
				var $el = $(el);
				var ptsJson = that.getHtmlJson($el, index);
				jsonAry.push(ptsJson);
			})
			return jsonAry;
		},
		// 获取html 数据对象
		getHtmlJson: function(obj, index){
			var that = this, $obj = obj, jsonStr = {};
			//栏目ID
			jsonStr.tabsId = $obj.find('input[name="tabsId"]').val();
			jsonStr.orderNum = index;
			//栏目状态
			var operType = $obj.find('input[name="operType"]').val();
			if(operType == 'D'){
				jsonStr.status = '2';
			}else{
				jsonStr.status = '1';
			}
			//是否显示
			var ckBtn = $obj.find('input[name="isshow"]').get(0);
			if(ckBtn.checked){
				jsonStr.isshow = '1';
			}else{
				jsonStr.isshow = '0';
			}
			return jsonStr;
		}
	}

	customNavi = new CustomNavi();

}(jQuery));

/**
 * 自定义链接时获取链接js
 */
if (typeof  jQuery === 'undefined') {
	throw new Error('custom-link requires jQuery');
}
(function($){
	var CustomLink = function () {};
	CustomLink.prototype = {
		getLinkTable : function (onSuccess,linkAddress){
			var htmlTemp = '',dateTime = new Date().getTime();
			if(linkAddress==null || linkAddress == ''){
				linkAddress = 'noLinkAddr';
			}
			var url = '/setLink?linkaddress='+linkAddress+'&t='+dateTime;
			$.ajax({
				url: url,
				type: 'GET',
				dataType: 'text',
				contentType: 'application/json;charset=utf-8',
				success: function (data) {
					if (typeof onSuccess == 'function') {
						onSuccess(data);
					}
				},
				error: function (er) {
					$.alert({
						animation: 'opacity',
						closeAnimation: 'scale',
						title: '错误提示',
						content: alertError('调用后台服务出错')
					});
				}
			});
		},
		getLink : function(objDataLink){
			var dateTime = new Date().getTime(),$liItem = objDataLink;
			$.confirm({
				confirmTop:'4.5%',
				animation: 'opacity',
				closeAnimation: 'scale',
				columnClass: 'col-md-10 col-md-offset-2',
				title: '设置链接',
				content: 'url:/setLink?t='+dateTime,
				confirm : function(){
					var vLink = $('#linkHidden').val();
					if(vLink){
						$liItem.attr('data-link', vLink);
						$liItem.find('.sm-option i').attr('title', vLink);
						$liItem.find('i.sm-cont').show();
					}else{
						$liItem.attr('data-link', '');
						$liItem.find('.sm-option i').attr('title', '');
						$liItem.find('i.sm-cont').hide();
					}
				}
			});
		}
	}
	customLink = new CustomLink();
}(jQuery));

/**
 * 自定义内容块js
 */
if (typeof  jQuery === 'undefined') {
	throw new Error('custom-module requires jQuery');
}
(function($){
	var CustomModule = function () {};
	CustomModule.prototype = {
		// 获取轮播图 voJson
		getCarouselVoJson : function (){
			var that = this, $moduleImgCarousel = $('#set-module-imgCarousel'),
				typeId =$('#customModuleTypeId').val(), jsonAry = [];
			$moduleImgCarousel.find('li').each(function(index, el){
				var $el = $(el);
				var jsonStr = that.getCarouselHtmlJson($el, index, typeId);
				jsonAry.push(jsonStr);
			});
			return jsonAry;
		},
		// 获取轮播图 html data json
		getCarouselHtmlJson : function(obj, index, typeId){
			var jsonStr = {}, $obj = obj;
			jsonStr.linkAddress = $obj.attr('data-link');
			jsonStr.hashyperlink = $obj.attr('data-link') ? 1 : 0;
			jsonStr.orderNum = index;
            jsonStr.picDetail = $obj.find('input[name="picDetail"]').val() ? $obj.find('input[name="picDetail"]').val() : '';
			jsonStr.picMgid = $obj.find('.sm-carousel-spanimg').find('img').attr('src').replace(imgDomain,"");
			jsonStr.tabsId = $('#hidTempModuleCbId').val();
			jsonStr.cbTypeId = typeId;
			jsonStr.title = $obj.find('input[name="title"]').val() ? $obj.find('input[name="title"]').val() : '';
			jsonStr.content = $obj.find('textarea[name="content"]').val() ? $obj.find('textarea[name="content"]').val() : '';
			return jsonStr;
		},
		getFileVoJson : function () {
			var that = this, $moduleImgCarousel = $('#set-module-imgCarousel'),
				typeId =$('#customModuleTypeId').val(), jsonAry = [];
			$moduleImgCarousel.find('li').each(function(index, el){
				var $el = $(el);
				var jsonStr = that.getFileHtmlJson($el, index, typeId);
				jsonAry.push(jsonStr);
			});
			return jsonAry;
		},
		getFileHtmlJson : function (obj, index, typeId) {
			var jsonStr = {}, $obj = obj;
			jsonStr.linkAddress = $obj.attr('data-link');
			jsonStr.hashyperlink = $obj.attr('data-link') ? 1 : 0;
			jsonStr.orderNum = index;
			jsonStr.picMgid = $obj.find('.sm-carousel-spanimg').find('a').attr('data-fileid');
			jsonStr.tabsId = $('#hidTempModuleCbId').val();
			jsonStr.cbTypeId = typeId;
			jsonStr.title = $obj.find('.sm-carousel-spanimg').find('a').text();
			return jsonStr;
		},
		//查询模块
		findModule : function(url, jsonData, onSuccess){
			$.post(url, jsonData, function (data, result) {
				if (typeof onSuccess == 'function') {
					onSuccess(data);
				}
			});
		},
		// 保存内容块
		moduleSave : function(url,jsonData,onSuccess){
			$.ajax({
				url: url,
				type: 'POST',
				dataType: 'json',
				data: JSON.stringify(jsonData),
				contentType: 'application/json;charset=utf-8',
				success: function (data) {
					if (typeof onSuccess == 'function') {
						onSuccess(data);
					}
				},
				error: function (er) {
					$.alert({
						animation: 'opacity',
						closeAnimation: 'scale',
						title: '错误提示',
						content: alertError('调用后台服务出错')
					});
				}
			});
		},
		moduleDel : function(url,jsonData,onSuccess){
			var dateTime = new Date().getTime();
			$.ajax({
				url: url+'/'+jsonData,
				type: 'GET',
				dataType: 'json',
				contentType: 'application/json;charset=utf-8',
				success: function (data) {
					if (typeof onSuccess == 'function') {
						onSuccess(data);
					}
				},
				error: function (er) {
					$.alert({
						animation: 'opacity',
						closeAnimation: 'scale',
						title: '错误提示',
						content: alertError('调用后台服务出错')
					});
				}
			});
		},
		// 上传轮播图 js
		setTempHtmlCarousel : function(imgSrc){
			var html = [];
			html.push('<li data-link="">');
			html.push('<div class="sm">');
			html.push('		<div class="sm-lubimg">');
			html.push('			<span><i class="iconfont sm-move"></i></span>');
			html.push('			<span class="sm-carousel-spanimg"><img src="'+imgSrc+'" width="287" height="101"><em class="hid"></em></span>');
			html.push('		</div>');
			html.push('		<em class="sm-down sm-imgcarousel-del"><i class="iconfont icon-cha"></i></em>');
			html.push('		<div class="sm-option">');
			html.push('			<i class="iconfont sm-lup-cont"></i>');
			html.push('			<i class="sm-ilj imgCarouselSetLink">设置链接</i>');
			html.push('		</div>');
			html.push('</div>');
			html.push('</li>');
			return html.join(' ');
		},
		// 上传轮播图 js
		setTempHtmlAllSet : function(imgSrc){
			var html = [];
			html.push('<li data-link="" class="clear">');
			html.push('<div class="sm float-l">');
			html.push('		<div class="sm-lubimg">');
			html.push('			<span><i class="iconfont sm-move"></i></span>');
			html.push('			<span class="sm-carousel-spanimg"><img src="'+imgSrc+'" width="287" height="101"><em class="hid"></em></span>');
			html.push('		</div>');
			html.push('		<em class="sm-down sm-imgcarousel-del"><i class="iconfont icon-cha"></i></em>');
			html.push('		<div class="sm-option">');
			html.push('			<i class="iconfont sm-lup-cont"></i>');
			html.push('			<i class="sm-ilj imgCarouselSetLink">设置链接</i>');
			html.push('		</div>');
			html.push('</div>');
			html.push('<div class="float-l uec-dis-table">');
            html.push('     <div class="sm-span set-module">');
            html.push('	        <span class="sm-background">图片描述：</span>');
            html.push('         <input type="text" name="picDetail" placeholder="" class="input-medium input-box w300">');
            html.push('	    </div>');
			html.push('     <div class="sm-span set-module">');
			html.push('	        <span class="sm-background">产品标题：</span>');
			html.push('         <input type="text" name="title" placeholder="" class="input-medium input-box w300">');
			html.push('	    </div>');
			html.push('     <div class="sm-span set-module">');
			html.push('	        <span class="sm-background v-align">产品描述：</span>');
			html.push('         <textarea name="content" class="smuec-twotextarea"></textarea>');
			html.push('	    </div>');
			html.push(' </div>');
			html.push('</li>');
			return html.join(' ');
		},
		setTempHtmlCaseSet : function(imgSrc){
			var html = [];
			html.push('<li data-link="" class="clear">');
			html.push('<div class="sm float-l">');
			html.push('		<div class="sm-lubimg">');
			html.push('			<span><i class="iconfont sm-move"></i></span>');
			html.push('			<span class="sm-carousel-spanimg"><img src="'+imgSrc+'" width="287" height="101"><em class="hid"></em></span>');
			html.push('		</div>');
			html.push('		<em class="sm-down sm-imgcarousel-del"><i class="iconfont icon-cha"></i></em>');
			html.push('</div>');
			html.push('</li>');
			return html.join(' ');
		},
		setTempHtmlAdjunctSet: function(upFileId, upflieName){
			var html = [];
			html.push('<li data-link="" class="clear">');
			html.push('<div class="sm float-l">');
			html.push('		<div class="sm-lubimg">');
			html.push('			<span><i class="iconfont sm-move"></i></span>');
			html.push('			<span class="sm-carousel-spanimg"><a data-fileid="'+upFileId+'" href="/file/download/'+upFileId+'">'+upflieName+'</a><em class="hid"></em></span>');
			html.push('		</div>');
			html.push('		<em class="sm-down sm-imgcarousel-del"><i class="iconfont icon-cha"></i></em>');
			html.push('		<a href="/file/download/'+upFileId+'"><div class="sp-file sm-payment-file"><span><i class="uec-icon icon-fangdajing"></i>浏览</span></div></a>');
			html.push('</div>');
			html.push('</li>');
			return html.join(' ');
		}

	}
	customModule = new CustomModule();
}(jQuery));

/**
 * 轮播图js
 */
if(typeof  jQuery === 'undefined'){
	throw new Error(' Custom-Slide requires jQuery');
}
(function($){
	var sBoxIndex = 0;
	var moveTimer;
	var CustomSlide = function () {};

	CustomSlide.prototype ={
		init : function(obj){
			var that = this,
					$obj = $(obj),
					$sBoxImg = $obj.find('.sp-banner-img'),
					$size = $obj.find('.bbox-a-level').length;

			that.sBoxHover($obj, $size);
			that.sBoxLstClick($obj);
			/*-- 3.2秒滚动一次 定时器 begin --*/
			moveTimer= setInterval(function(){
				that.sBoxAuto($size);
				that.sBoxMove($obj, sBoxIndex);
			}, 3200);

		},
		/*-- sbox move:图片移动 begin --*/
		sBoxMove : function(obj, index){
			var that = this, $obj = obj,
					$sBoxImg = $obj.find('.sp-banner-img a'),
					$sBoxLst = $obj.find('.sp-banner-lst a');
			$sBoxImg.eq(index).siblings('.bbox-a-level').stop().animate({opacity:0},600).css('z-index','0');
			$sBoxImg.eq(index).stop().animate({opacity:1},600).css('z-index','10').siblings().removeClass('active');
			$sBoxLst.eq(index).addClass('current').siblings().removeClass('current');
		},
		/*-- 图片当前索引 --*/
		sBoxAuto : function(size){
			sBoxIndex == size-1 ? sBoxIndex = 0 : sBoxIndex += 1;
		},
		sBoxHover : function(obj, size){
			var that = this, $obj = obj,
					$sBoxImg = $obj.find('.sp-banner-img a');
			$sBoxImg.hover(function(){
				clearInterval(moveTimer);
			},function(){
				moveTimer = setInterval(function(){
					that.sBoxAuto(size);
					that.sBoxMove($obj, sBoxIndex);
				}, 3200);
			});
		},
		sBoxLstClick : function(obj){
			var that = this, $obj = obj,
					$sBoxLst = $obj.find('.sp-banner-lst a');
			$sBoxLst.click(function(event) {
				sBoxIndex = $(this).index();
				that.sBoxMove($obj, sBoxIndex);
			});
		}
	}

	customSlide = new CustomSlide();

}(jQuery));