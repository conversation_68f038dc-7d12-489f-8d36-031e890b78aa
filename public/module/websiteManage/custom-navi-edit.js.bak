//缺省部门id(市场id)
var defaultDeptId='bb590fd43d554e1390750686bbcd5aae';
//缺省栏目id(产品栏目)
var defalutModulesId='d43c0fa740d84becabc7f0bf9f08b230';
// 首页类型栏目模块ID
var homePageModulesId = '7e16715948c94bbfa123a698aedb86e7';
var solutionListModulesId = 'f5bb9d20a7d8415cac6a47dc6b268952';
var industryListModulesId = 'f5bb9d20a7d8415cac6a47dc6b268953';
/**
 * Created by ronglian on 2016/3/23.
 */
$(function () {
    //拖动
    $('#sm-navi-edit .sm-innerbox-ul').sortable({
        cursor: 'move'
    });

    $('#sm-navi-edit .uecb-level-c').sortable({
        cursor: 'move'
    });

    $('#sm-navi-edit .uecb-level-c-sub').sortable({
       cursor: 'move'
    });

    //展开图标事件
    $('#sm-navi-edit').delegate('.uec-downup', 'click', function () {
        $this = $(this);
        if ($this.hasClass('icon-sanj')) {
            $this.removeClass('icon-sanj');
            $this.addClass('icon-dianjs');
        } else {
            $this.removeClass('icon-dianjs');
            $this.addClass('icon-sanj');
        }

        var pNode = $this.closest('.lgiCls');
        //收起
        if ($this.hasClass('icon-sanj')) {
            pNode.find('.lgiCls').hide();
        }
        //展开
        else {
            pNode.find('.lgiCls').show();
        }
    });

    //添加
    $('#sm-navi-edit').delegate('.stair-add', 'click', function () {
        $tItem = $(this).closest('.lgiCls');
        var pId = $tItem.find("[name='tabsParentId']").val();
        if(pId == 'undefined'){
            pId = '0';
        }
        var tId = $tItem.find("[name='tabsId']").val();
        var orderNum = $tItem.find("[name='orderNum']").val();
        $.confirm({
            animation: 'opacity',
            closeAnimation: 'scale',
            confirmButton: '确定',
            columnClass: 'col-md-6 col-md-offset-3',
            title: '添加栏目',
            content: 'url:/customNaviEdit/af?pId=' + pId + '&tId=' + tId + '&orderNum=' + orderNum + '&time=' + new Date().getTime(),
            confirm: function () {
                var cnaForm = $('#cnaForm');
                if (!cnaForm.isValid()) {
                    return false;
                }
                var reqVo = cnaForm.serializeJson();
                var modulesIdTemp = $.trim($('#modulesId input.hiddenId').val());
                if ( modulesIdTemp != defalutModulesId) {
                    reqVo.deptId = defaultDeptId;
                }
                $.post('/customNaviEdit/ap', reqVo, function (data) {
                    $('.content').html('');
                    $('.content').html(data);
                    alertSucceed('添加成功！');
                });
            }
        });
    });

    //编辑
    $('#sm-navi-edit').delegate('.stair-edit', 'click', function () {
        $tItem = $(this).closest('.lgiCls');
        var tId = $tItem.find("[name='tabsId']").val();
        $.confirm({
            animation: 'opacity',
            closeAnimation: 'scale',
            confirmButton: '确定',
            columnClass: 'col-md-6 col-md-offset-3',
            title: '编辑栏目',
            content: 'url:/customNaviEdit/ef?tId=' + tId + '&time=' + new Date().getTime(),
            confirm: function () {
                var cnaForm = $('#cnaEditForm');
                if (!cnaForm.isValid()) {
                    return false;
                }
                var reqVo = cnaForm.serializeJson();
                if ($.trim($('#modulesId input.hiddenId').val()) != defalutModulesId) {
                    reqVo.deptId = defaultDeptId;
                }
                $.post('/customNaviEdit/ep', reqVo, function (data) {
                    $('.content').html('');
                    $('.content').html(data);
                    alertSucceed('编辑成功！');
                });
            }
        });
    });

    //删除
    $('#sm-navi-edit').delegate('.stair-del', 'click', function (event) {
        var $this = $(event.target), $liItem = $this.closest('.lgiCls');
        $.confirm({
            animation: 'opacity',
            closeAnimation: 'scale',
            confirmButton: '确定',
            title: '提示',
            content: alertWarning('确定删除该栏目吗？'),
            confirm: function () {
                var tabsId = $liItem.find('input[name="tabsId"]').val();
                var reqVo = {};
                reqVo.tabsId = tabsId;
                reqVo.status = 2;
                $.post('/customNaviEdit/ep', reqVo, function (data) {
                    $('.content').html('');
                    $('.content').html(data);
                    alertSucceed('删除成功！');
                });
            }
        });
    });
});
