/**
 * Created by <PERSON><PERSON><PERSON> on 2016/4/28.
 */
var showTypeDataMap={};
function showType(showType){
    if(showType == 'tile'){
        $('#ta').removeClass('active');
        $('#ti').addClass('active');

        var showTypeData = showTypeDataMap['tile'];

        if(showTypeData){
            $('#indexData').empty().html(showTypeData);
        }else{
            $.post('/tempgoods/qg/'+showType,function(data){
                showTypeDataMap['tile'] = data;
                $('#indexData').empty().html(data);
            })
        }
    }else{
        $('#ta').addClass('active');
        $('#ti').removeClass('active');
        var showTypeData = showTypeDataMap['table'];

        if(showTypeData){
            $('#indexData').empty().html(showTypeData);
        }else{
            $.post('/tempgoods/qg/'+showType,function(data){
                showTypeDataMap['table'] = data;
                $('#indexData').empty().html(data);
            })
        }
    }

}