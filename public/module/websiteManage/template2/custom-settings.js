/**
 * Created by <PERSON><PERSON><PERSON>@ronglian.com on 2016/3/17.
 */

$(function () {
    initHeader();
    //initBottom();
    //initFooter();
    $('body').delegate('.shop-lione','mouseover',function(){
        $('.shop-lione-ul').show();
    });
    $('body').delegate('.shop-lione-ul,.sp-navigation-box','mouseout',function(){
        $('.shop-lione-ul').hide();
    });
    $("#tempheaderDiv").delegate('#ptEditBtn', 'click', function () {
        pageTabsClick()
    });
    $("#tempFooterDiv").delegate('#footRecord', 'click', function () {
        showFootRecord()
    });
    $("#tempBootomDiv").delegate('#bottomMerchantMobileInfo', 'click', function () {
        showMerchantMobileInfo()
    });
    // 主内容拖拽
    $('#customTempModule').sortable({
        cursor: 'move',
	    cancel:'.sm-temp-add'
    }).disableSelection();

    //上移
    $('#customTempModule').delegate('.sm-moveup', 'click', function () {
        customSettings.moveUp(this);
    });

    // 下移
    $('#customTempModule').delegate('.sm-movedown', 'click', function () {
        customSettings.moveDown(this);
    });

    //固定模块显示否
	$('#customTempModule').delegate('.sm-movetrigger', 'click', function () {
		var $this = $(this), dataId = $(this).attr('data-id');
		if (dataId) {
			var url = '/customModuleEdit/u/mark';
			var jsonStr = {};
			jsonStr.tabsId = $('#hidTempModuleCbId').val();
			jsonStr.cbId = dataId;
			console.log($this.hasClass('icon-biyan1'))
			if($this.hasClass('icon-biyan1')){
				jsonStr.showMark = 1;
			}else{
				jsonStr.showMark = 0;
			}
			customModule.moduleSave(url, jsonStr, function (data) {
				if (data == CONST_SUCCESS) {
					if($this.hasClass('icon-biyan1')){
						alertSucceed('已设显示');
					}else{
						alertSucceed('已设不显示');
					}
					$this.toggleClass('icon-biyan1');
				}
			})
		}
	});

    //删除
    $('#customTempModule').delegate('.sm-delete', 'click', function () {
        var $this = $(this), dataId = $(this).attr('data-id');
        if (dataId) {
            $.confirm({
                animation: 'opacity',
                closeAnimation: 'scale',
                confirmButton:'确定',
                title: '提示',
                content: alertWarning('确定删除该内容块吗？'),
                confirm: function () {
                    customModule.moduleDel('/tempModule/d', dataId, function (data) {
                        if (data == CONST_SUCCESS) {
                            customSettings.deleteHtml($this);
                        }
                    });
                }
            });

        }
    });

    //添加模块
    $('#customTempModule').delegate('#sm-custom-module-add', 'click', function () {
        var $this = $(this);
        $.confirm({
            animation: 'opacity',
            closeAnimation: 'scale',
            confirmTop: '4.5%',
            columnClass: 'col-md-11 col-md-offset-1',
            title: '添加内容',
            content: 'url:/customModuleEdit?time='+new Date().getTime(),
            confirm: function () {
                if (!$('#moduleConfirmForm').isValid()) {
                    return false;
                }
                var dataModuleType = $('#sm-module-confirm').find('.custom-module-mian-type .sm-cb-imgon').parent('li').attr('data-type'),
                    jsonAry = [], jsonStr = {};
                var url = '/customModuleEdit/a';

                jsonStr.tabsId = $('#hidTempModuleCbId').val();
                jsonStr.moduleTitle = $('input[name="moduleTitle"]').val();
                jsonStr.enTitle = $('input[name="enTitle"]').val();
                if (dataModuleType == 'imgCarousel') {
                    var array=customModule.getCarouselVoJson();
                    jsonStr.data = array;
                } else {
                    jsonAry.push($('#moduleConfirmForm').serializeJson());
                    jsonAry[0].content = $('.summernoteModule').summernote('code');
                    jsonStr.data = jsonAry;
                    jsonStr.colorCode = $('#contentBgColor').val();
                }
                customModule.moduleSave(url, jsonStr, function (data) {
                    if (data) {
                        var queryStr = {};
                        queryStr.cbId = data.cbId;
                        customModule.findModule('/tempModule/q', queryStr, function (data) {
                            $this.parents('.sm-temp-add').before(data);
                            $this.parents('.sm-temp-add').remove();
                            customSettings.init();
                            uecSlider('.uec-slider-banner');
                            customIndexSlider();
                        });
                    }
                })
            }
        });
    });

    //编辑模块
    $('#customTempModule').delegate('.sm-modify', 'click', function () {
        var $this = $(this), dataId = $this.attr('data-id'), jsonStr = {}, time = new Date().getTime();
        jsonStr.tabsId = $('#hidTempModuleCbId').val();
        jsonStr.cbId = dataId;
        var url = '/customModuleEdit/q?time=' + time + '&tabsId=' + jsonStr.tabsId + '&cbId=' + jsonStr.cbId;
        if (dataId) {
            $.confirm({
                confirmTop:'4.5%',
                animation: 'opacity',
                closeAnimation: 'scale',
                columnClass: 'col-md-11 col-md-offset-1',
                title: '编辑内容',
                content: 'url:' + url,
                confirm: function () {
                    if (!$('#moduleConfirmForm').isValid()) {
                        return false;
                    }
                    var dataModuleType = $('#sm-module-confirm').find('.custom-module-mian-type .sm-cb-imgon').parent('li').attr('data-type'),
                        jsonAry = [], jsonStr = {};

                    var url = '/customModuleEdit/u';
                    jsonStr.tabsId = $('#hidTempModuleCbId').val();
                    jsonStr.cbId = $('#contentId').val();
	                jsonStr.moduleTitle = $('input[name="moduleTitle"]').val();
	                jsonStr.enTitle = $('input[name="enTitle"]').val();
                    if (dataModuleType == 'imgCarousel') {
                        var array=customModule.getCarouselVoJson();
                        jsonStr.data = array;
                    } else {
                        jsonAry.push($('#moduleConfirmForm').serializeJson());
                        jsonAry[0].content = $('.summernoteModule').summernote('code');
                        jsonStr.data = jsonAry;
                        jsonStr.colorCode = $('#contentBgColor').val();
                    }
                    customModule.moduleSave(url, jsonStr, function (data) {
                        if (data == CONST_SUCCESS) {
                            var queryStr = {};
                            queryStr.cbId = jsonStr.cbId;
                            customModule.findModule('/tempModule/q', queryStr, function (data) {
                                $this.parents('div[data-id="' + jsonStr.cbId + '"]').replaceWith(data);
                                $('div.cms-temp-c[data-id="' + jsonStr.cbId + '"]').next('.sm-temp-add').remove();
                                //轮播图效果
                                customSettings.init();
                                uecSlider('.uec-slider-banner');
                                customIndexSlider();
                            });
                        }
                    })
                }
            });
        }

    });


    //编辑底部链接
    $('#tempBootomDiv').delegate('#bottomTabs', 'click', function () {
        var dateTime = new Date().getTime();
        var confirmBoxBottom = $.confirm({
            confirmTop:'4.5%',
            animation: 'opacity',
            closeAnimation: 'scale',
            columnClass: 'col-md-5 col-md-offset-4',
            title: '编辑底部链接',
            content: 'url:/customBottomEdit/q?t=' + dateTime,
            confirm: function () {
                var jsonStr = customBottom.getVoJson();
                var jsonStrVo = {};
                jsonStrVo.data = JSON.stringify(jsonStr);
                $.ajax({
                    type: 'post',
                    url: '/customBottomEdit/e',
                    data: jsonStrVo,
                    cache: false,  // 默认为true（当dataType为script时，默认为false），设置为false将不会从浏览器缓存中加载请求信息
                    dataType: 'json',
                    success: function (msg) {
                        var contentStr = '';
                        if (msg == CONST_SUCCESS) {
                            initBottom();
                            alertSucceed('保存成功！');
                        } else {
                            contentStr = alertError('保存失败！');
                            $.alert({
                                animation: 'opacity',
                                closeAnimation: 'scale',
                                title: '提示',
                                content: contentStr
                            });
                        }
                    }
                });
            }
        });
    });
});

/**
 * 模板保存按钮
 * @param type
 * @param isAlert 0不弹窗 ,1 弹窗
 */
function tempLateTopSave(uid, type, isAlert,shortId,topShortId) {
    $("#tempLateSave").unbind("click");
    var ptType = $("#hidTempModulesUrl").val();
    var ptUid = $("#hidPreCbid").val();//上级id
    if (ptUid == uid && isAlert == "0") {
        return;
    } else {
        var result = {};
        result.pageTabsId = uid;
        result.ptType = type;
        switch (ptType) {
            case "content/index":
                //先触发保存功能
                saveContentTemp(uid, type, isAlert,shortId,topShortId);
                $("#tempLateSave").unbind("click");
                //重新绑定保存按钮
                $("#tempLateSave").bind("click", function () {
                    tempLateTopSave(uid, type, "1",shortId,topShortId);
                });
                break;
            case "goods/index":
                saveGoodsPageConfig(uid, type, isAlert);//保存商品展示方式
                $("#tempLateSave").unbind("click");
                $("#tempLateSave").bind("click", function () {
                    tempLateTopSave(uid, type, "1",shortId,topShortId);
                });
                break;
            default:
                saveContentTemp(uid, type, isAlert,shortId,topShortId);
                $("#tempLateSave").unbind("click");
                $("#tempLateSave").bind("click", function () {
                    tempLateTopSave(uid, type, "1",shortId,topShortId);
                });
                $.post('/tempHeader/ptc', result, function (data, resultData) {
                    $("#customTempModule").html(data);
	                customSettings.init();
                    uecSlider('.uec-slider-banner');
                    customIndexSlider();
                });
                break;
        }
        if (type == 'news/index') {
            $(".default-sm-modal-nav").hide();
        } else {
            $(".default-sm-modal-nav").show();
        }
    }

}
//模板关闭
function tempClose() {
    $.confirm({
        confirmButtonPlural: "确定",
        animation: 'opacity',
        closeAnimation: 'scale',
        columnClass: 'col-md-5 col-md-offset-4',
        title: '提示',
        content: alertWarning("确定关闭窗口?"),
        confirmButton: false,
        confirmPlural: function () {
            //$("#tempLateSave").click();
            window.opener = null;
            window.open('', '_self', '');
            window.close();
        }
    });
}
//自定义模板保存
function saveContentTemp(uid, type, isAlert,shortId,topShortId) {
    var resultArr = new Array();
    $("#customTempModule .cms-temp-c").each(function (index, val) {
        var resultData = {};
        resultData.cbId = $(this).attr("data-id");
        resultData.orderNum = index + 1;
        resultArr.push(resultData);
    });
    if (resultArr.length > 0) {

        if(type == 'product/index' || type == 'tempindustryinfo' || type == 'tempsolutioninfo'){
            //提交审核
            var shenheHtml = "<form id = 'shenheForm' name='shenheForm',data-validator-option='{theme:\"yellow_right_effect\",stopOnError:true}'><div class=\"sm-span set-module\"><span class=\"sm-background v-align\">变更内容：</span>"+
                "<textarea id = 'content' name=\"content\" data-rule='变更内容:required' class=\"smuec-twotextarea\"></textarea></div>" +
                "" +
                "</form>";
            $.confirm({
                confirmButtonPlural: "提交",
                animation: 'opacity',
                closeAnimation: 'scale',
                columnClass: 'col-md-8 col-md-offset-2',
                title: '提交审核',
                confirmButton: false,
                content: shenheHtml,
                confirmPlural: function () {//保存
                    if (!$('#shenheForm').isValid()) {
                        return false;
                    }
                    var reqVo = {};
                    var shenheType = '';
                    switch (type) {
                        case 'product/index':
                            shenheType = 'PRODUCT';
                            break;
                        case 'tempindustryinfo':
                            shenheType = 'INDUSTRY';
                            break;
                        case 'tempsolutioninfo':
                            shenheType = 'SOLUTION';
                            break;
                    }
                    reqVo.type = shenheType;
                    reqVo.content = $('#content').val();
                    reqVo.linkAddress = '/content/index/'+shortId+'/'+topShortId+'/1d15845c8';
                    reqVo.refId = uid;
                    $.post("/approved/a", reqVo, function (data, result) {
                        var resultData = JSON.parse(data);
                        if (resultData.result != CONST_FAILURE) {
                            var result = {};
                            result.contentBlocks = JSON.stringify(resultArr);
                            $.post('/customTemplate/ub', result, function (data, result) {
                                var dataObj = JSON.parse(data);
                                //如果容许弹窗
                                if (isAlert == "1") {
                                    if (dataObj.result == CONST_SUCCESS) {
                                        doCallBackAlert("提交审核并保存成功");
                                    }
                                    else {
                                        var message = dataObj.message[ERROR_KEY][0];
                                        $.alert({
                                            animation: 'opacity',
                                            closeAnimation: 'scale',
                                            columnClass: 'col-md-5 col-md-offset-4',
                                            title: '提示',
                                            content: alertError(message)
                                        });
                                    }
                                }
                                var result = {};
                                result.pageTabsId = uid;
                                result.ptType = type;
                                $.post('/tempHeader/ptc', result, function (data, resultData) {
                                    $("#customTempModule").html(data);
                                    //轮播图效果
                                    customSettings.init();
                                    uecSlider('.uec-slider-banner');
                                    customIndexSlider();
                                });
                            });
                        } else {
                            $.alert({
                                animation: 'opacity',
                                closeAnimation: 'scale',
                                title: '提示',
                                content: alertError("提交审核并修改失败")
                            });
                        }
                        return true;
                    });

                }
            });
        }else{
            var result = {};
            result.contentBlocks = JSON.stringify(resultArr);
            $.post('/customTemplate/ub', result, function (data, result) {
                var dataObj = JSON.parse(data);
                //如果容许弹窗
                if (isAlert == "1") {
                    if (dataObj.result == CONST_SUCCESS) {
                        doCallBackAlert("保存成功");
                    }
                    else {
                        var message = dataObj.message[ERROR_KEY][0];
                        $.alert({
                            animation: 'opacity',
                            closeAnimation: 'scale',
                            columnClass: 'col-md-5 col-md-offset-4',
                            title: '提示',
                            content: alertError(message)
                        });
                    }
                }
                var result = {};
                result.pageTabsId = uid;
                result.ptType = type;
                $.post('/tempHeader/ptc', result, function (data, resultData) {
                    $("#customTempModule").html(data);
                    //轮播图效果
                    customSettings.init();
                    uecSlider('.uec-slider-banner');
                    customIndexSlider();
                });
            });
        }
    } else {
        var result = {};
        result.pageTabsId = uid;
        result.ptType = type;
        $.post('/tempHeader/ptc', result, function (data, resultData) {
            $("#customTempModule").html(data);
            //轮播图效果
            customSettings.init();
	        uecSlider('.uec-slider-banner');
	        customIndexSlider();
        });
    }
}


//商城默认展示方式保存
function saveGoodsPageConfig(uid, type, isAlert) {
    var tabsId = $("#hidPreCbid").val();
    var ta = $('#ta');
    var isTable = ta.hasClass('active');
    var configValue = isTable ? 'table' : 'tile';
    var result = {};
    result.tabsId = tabsId;
    result.configValue = configValue;
    $.post('/tempgoods/ui', result, function (data) {
        var dataObj = JSON.parse(data);
        //如果容许弹窗
        if (isAlert == "1") {
            if (dataObj.configId) {
                doCallBackAlert("保存成功");
            } else {
                var message = dataObj.message[ERROR_KEY][0];
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    columnClass: 'col-md-5 col-md-offset-4',
                    title: '提示',
                    content: alertError(message)
                });
            }
        }
        var result = {};
        result.pageTabsId = uid;
        result.ptType = type;
        $.post('/tempHeader/ptc', result, function (data, resultData) {
            $("#customTempModule").html(data);
            //轮播图效果
            customSettings.init();
	        uecSlider('.uec-slider-banner');
	        customIndexSlider();
        });
    });
}


//初始化bottom页面
function initBottom() {
    $.post('/tempbottom/', null, function (data, result) {
        $("#tempBootomDiv").html(data);
    });
}
//初始化Footer页面
function initFooter() {
    $.post('/tempfooter/', null, function (data, result) {
        $("#tempFooterDiv").html(data);
    });
}
//初始化头部
function initHeader() {
    $.post('/tempHeader/', {'topTabsId':getQueryString('topTabsId')}, function (data, result) {
        $("#tempheaderDiv").html(data);
        var $setFixed = $('.set-fixed');
        setScrollTop(1, 1, $setFixed);
        var pageTabsId = getQueryString('hidTempModuleCbId'),
	        ptType = getQueryString('hidTempModulesUrl'),
            shortId = getQueryString('shortId'),
            topShortId = getQueryString('topShortId');
        if(pageTabsId && ptType){
	        initTempModule(pageTabsId, ptType,shortId,topShortId);
        }else{
	        initTempModule();
        }
        mainNav();
    });
}
/**
 * 初始化中部
 * @param uid
 * @param type
 */
function initTempModule(uid, type, shortId,topShortId,obj) {
    var result = {};
    if (uid == null) {
        var arrPageTabs = $("#pageTabshidInit").val();
        if(arrPageTabs){
	        arrPageTabs = arrPageTabs.split(';');
	        result.pageTabsId = arrPageTabs[0];
	        result.ptType = arrPageTabs[1];
	        result.shortId = arrPageTabs[2];
        }else{
            $('#customTempModule').html('<h2 class="uec-s-tipbox">请选择栏目编辑模板！</h2>');
            return;
        }
        $("#hidTempModuleCbId").val(result.pageTabsId);
        $("#hidTempModulesUrl").val(result.ptType);
	    $('#customTempModule').sortable('enable');
        $("#hidPreCbid").val(result.pageTabsId);
        $("#tempLateSave").bind("click", function () {
            tempLateTopSave(result.pageTabsId, result.ptType, "1",result.shortId);
        });
        $.post('/tempHeader/ptc', result, function (data, resultData) {
            $("#customTempModule").html(data);
            //轮播图效果
            customSettings.init();
            uecSlider('.uec-slider-banner');
	        customIndexSlider();
        });
    }
    else {
        $('#customTempModule').sortable('enable');
        result.pageTabsId = uid;
        result.ptType = type;
        var currentPt = $(obj);
        $('.sp-navbig-ul .ptli').removeClass('on');
        $('.sp-postion-span').remove();
        currentPt.addClass('on');
        currentPt.append('<span class= "sp-postion-span"></span>');
        $("#hidTempModuleCbId").val(uid);
        $("#tempLateSave").bind("click", function () {
            tempLateTopSave(result.pageTabsId, result.ptType, "1",shortId,topShortId);
        });
        var result2 = {};
        result2.pageTabsId = result.pageTabsId;
        result2.ptType = result.ptType;
        $.post('/tempHeader/ptc', result, function (data, resultData) {
            $("#customTempModule").html(data);
            //轮播图效果
            customSettings.init();
	        uecSlider('.uec-slider-banner');
	        customIndexSlider();
        });
        $("#hidTempModulesUrl").val(result.ptType);
        $("#hidPreCbid").val(result.pageTabsId);
    }
    $('.shop-lione-ul').hide();
}

/**
 * 编辑版权
 */
function showFootRecord() {
    var footVal = $("#footVal").val().split(';');
    for(var i=0;i<footVal.length;i++)
    {
        if(footVal[i]=='undefined')
        {
            footVal[i]="";
        }
    }
    var footShowDiv = ' <div class="sm-input-box"><em>Copyright©</em> <input  type="text" value="' + footVal[0] + '" class="input-medium input-box w50" placeholder="请输入年份" maxlength="6"/><input  type="text" value="' + footVal[1] + '" placeholder="请输入公司名称" class="input-medium input-box  w200" maxlength="30"/>'
        + '<em>版权所有</em><input  type="text" value="' + footVal[2] + '" placeholder="请输入备案号" class="input-medium input-box w180" maxlength="30"/>'
        + ' </div>';
    $.confirm({
        confirmButtonPlural: "保存",
        animation: 'opacity',
        closeAnimation: 'scale',
        columnClass: 'col-md-7 col-md-offset-2',
        title: '版权信息',
        confirmButton: false,
        content: footShowDiv,
        confirmPlural: function () {//保存
            var result = {};
            var record = "";
            $(".sm-input-box  input[type='text']").each(function () {
                record += $.trim($(this).val()) + ";";
            });
            record = record.substring(0, record.length - 1);
            result.retailersRecord = record;
            $.post("/merchant/u", result, function (data, result) {
                var resultData = JSON.parse(data);
                if (resultData.result == CONST_SUCCESS) {
                    alertSucceed("保存成功");
                    initFooter();
                } else {
                    $.alert({
                        animation: 'opacity',
                        closeAnimation: 'scale',
                        title: '提示',
                        content: alertError("修改失败")
                    });
                }
                return true;
            });

        }
    });
}
/**
 * 修改底部商家电商信息
 */
function showMerchantMobileInfo() {
    var hidMerchantMobileInfo = $("#hidMerchantMobileInfo").val().split(';');
    var showDiv = '  <ul class="sm-contact-ul">'
        + '<li><span>热线电话：</span> <input type="text"  class="input-medium input-box" value="' + hidMerchantMobileInfo[0] + '" maxlength="20"/></li>'
        + '<li><span>联系邮箱：</span> <input type="text"  class="input-medium input-box" value="' + hidMerchantMobileInfo[1] + '" maxlength="50"/></li>'
        + '<li><span class="sm-kef">qq客服：</span> <input type="text"  class="input-medium input-box" value="' + hidMerchantMobileInfo[2] + '" maxlength="50"/></li>'
        + '</ul>';
    $.confirm({
        confirmButtonPlural: "保存",
        animation: 'opacity',
        closeAnimation: 'scale',
        columnClass: 'col-md-7 col-md-offset-2',
        title: '版权信息',
        confirmButton: false,
        content: showDiv,
        confirmPlural: function () {//保存
            var result = {};
            var record = "";
            $(".sm-contact-ul  input[type='text']").each(function () {
                record += $.trim($(this).val()) + ";";
            });
            record = record.substring(0, record.length - 1);
            var resultArr = record.split(';');
            result.mobile = resultArr[0];
            result.email = resultArr[1];
            result.websiteProtocol = resultArr[2];
            $.post("/merchant/u", result, function (data, result) {
                var resultData = JSON.parse(data);
                if (resultData.result == CONST_SUCCESS) {
                    initBottom();
                } else {
                    $.alert({
                        animation: 'opacity',
                        closeAnimation: 'scale',
                        title: '提示',
                        content: alertError("修改失败")
                    });
                }
                return true;
            });

        }
    });
}

/**
 * 上传图片
 * @param formName
 * @param upType
 * @param imgName
 * @returns {boolean}
 */
function uploadCmsImage(formName, upType, imgName, obj) {
    if (typeof obj != 'undefined' && obj != null) {
        var $obj = $(obj);
        var dataType = $obj.attr('data-type');
        if (typeof dataType != 'undefined' && dataType != null && dataType == 'img') {
            upType = 'BigImgConfig';
        }
    }

    var picMgidHtml = $("#picMgid").parent().html();
    var carouseHtml = $("#imgCarouselFile").parent().html();
    var wxMgidHtml = $("#wxMgid").parent().html();
    var fileLogoMgidHtml = $("#fileLogoMgid").parent().html();

    $("#" + formName).ajaxSubmit({
        type: 'POST',
        async: false,
        url: "/imageUpload/up/file?uptype=" + upType, /*填写后台上传文件的路径*/
        cache: false,
        success: function (data) {/*url为上传成功后返回的图片路径*/
            var result = JSON.parse(data);
            if (result.status == CONST_SUCCESS) {
                switch (imgName) {
                    case "ImgwxMgid":
                        wxImgUpdate(result.fileUrl.replace(imgDomain, ""));
                        $("#" + imgName).attr("src", imgDomain + result.fileUrl).next('.imgFileSrcAddress').val(result.fileUrl);
                        break;
                    case "ImglogoMgid":
                        logoImgUpdate(result.fileUrl.replace(imgDomain, ""));
                        $("#" + imgName).attr("src", imgDomain + result.fileUrl).next('.imgFileSrcAddress').val(result.fileUrl);
                        break;
                    case 'carouselFileImg':
                        $('#set-module-imgCarousel ul.sm-carousel-ul').append(customModule.setTempHtmlCarousel(imgDomain + result.fileUrl));
                        break;
                    case 'allSetFileImg':
                        $('#set-module-imgCarousel ul.sm-carousel-ul').append(customModule.setTempHtmlAllSet(imgDomain + result.fileUrl));
                        break;
                    case 'caseSetFileImg':
                        $('#set-module-imgCarousel ul.sm-carousel-ul').append(customModule.setTempHtmlCaseSet(imgDomain + result.fileUrl));
                        break;
                    default:
                        $("#" + imgName).attr("src", imgDomain + result.fileUrl).next('.imgFileSrcAddress').val(result.fileUrl);
                        break;
                }
                $("#imgCarouselFile").parent().html(carouseHtml);
            }
            else {
                var message = result.message == "" ? "上传失败" : result.message;
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '信息提示',
                    content: alertError(message)
                });
                $("#picMgid").parent().html(picMgidHtml);
                $("#imgCarouselFile").parent().html(carouseHtml);
                $("#wxMgid").parent().html(wxMgidHtml);
                $("#fileLogoMgid").parent().html(fileLogoMgidHtml);
            }

        },
        error: function (msg) {
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '信息提示',
                content: alertError('上传失败。')
            });
        }

    });
    return false;
}

function uploadCmsFile(formName, fileName, fileType){
    if(fileType =='adjunctSetFile'){
        var size = $('#set-module-imgCarousel ul.sm-carousel-ul li').size();
        if(size == 5){
	        $.alert({
		        animation: 'opacity',
		        closeAnimation: 'scale',
		        title: '信息提示',
		        content: alertError('上传文件最多不能超过5个！')
	        });
        }else {
	        uploadCmsFileSubmit(formName, fileName, fileType);
        }
    }else{
	    uploadCmsFileSubmit(formName, fileName, fileType);
    }

};

function uploadCmsFileSubmit(formName, fileName, fileType) {
	var upfileHtml = $("#upfile").parent().html();
	$("#" + formName).ajaxSubmit({
		type: 'POST',
		async: false,
		url: "/imageUpload/uf", /*填写后台上传文件的路径*/
		cache: false,
		success: function (data) {/*url为上传成功后返回的图片路径*/
			var result = JSON.parse(data);
			if (result.status == CONST_SUCCESS) {
				alertSucceed('上传成功！');
				switch (fileType) {
					case "adjunctSetFile":
						$('#set-module-imgCarousel ul.sm-carousel-ul').append(customModule.setTempHtmlAdjunctSet(result.uploadFileId, result.fileName));
						break;
					default:
						$('#' + fileName).val(result.uploadFileId);
				}
			} else {
				var message = result.message == "" ? "上传失败" :result.message;
				$.alert({
					animation: 'opacity',
					closeAnimation: 'scale',
					title: '信息提示',
					content: alertError(message)
				});
			}
			$("#upfile").parent().html(upfileHtml);

		},
		error: function (msg) {
			$.alert({
				animation: 'opacity',
				closeAnimation: 'scale',
				title: '信息提示',
				content: alertError('上传失败。')
			});
			$("#upfile").parent().html(upfileHtml);
		}

	});
	return false;
};

/**
 * 修改微信公众号
 * @param url
 */
function wxImgUpdate(url) {
    var result = {};
    result.weixinMgid = url;
    $.post("/merchant/u", result, function (data, result) {
        var resultData = JSON.parse(data);
        if (resultData.result != CONST_SUCCESS) {
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '提示',
                content: alertError("修改失败")
            });
        }
    });
}
/**
 * 修改logo
 * @param url
 */
function logoImgUpdate(url) {
    var result = {};
    result.logoMgid = url;
    $.post("/merchant/u", result, function (data, result) {
        var resultData = JSON.parse(data);
        if (resultData.result != CONST_SUCCESS) {
            $.alert({
                animation: 'opacity',
                closeAnimation: 'scale',
                title: '提示',
                content: alertError("修改失败")
            });
        }
    });
}
//设置栏目
function pageTabsClick() {
    $.confirm({
        confirmTop:'4.5%',
        animation: 'opacity',
        closeAnimation: 'scale',
        columnClass: 'col-md-10 col-md-offset-2',
        title: '编辑页面栏目',
        content: 'url:/customNaviEdit?t=' + new Date().getTime(),
        cancel: function(){
            initHeader();
        },
        confirm: function () {
            var jsonStr = customNavi.getVoJson();
            var isShowLen = 0;
            for (var i = 0; i < jsonStr.length; i++) {
                if (jsonStr[i].isshow == '1' && jsonStr[i].status != '2' && jsonStr[i].parentId == '0') {
                    isShowLen++;
                }
            }
            if (isShowLen > PAGE_TABS_COUNT_LIMIT) {
                $("#emRegister").show();
                return false;
            }
            var jsonStrVo = {};
            jsonStrVo.data = JSON.stringify(jsonStr);
            $.ajax({
                type: 'post',
                url: '/customNaviEdit/e',
                data: jsonStrVo,
                async: false,
                cache: false,  // 默认为true（当dataType为script时，默认为false），设置为false将不会从浏览器缓存中加载请求信息
                dataType: 'json',
                success: function (msg) {
                    var contentStr = '';
                    if (msg.result == CONST_SUCCESS) {
                        alertSucceed('编辑成功！');
                    } else {
                        contentStr = alertError('保存失败！');
                        $.alert({
                            animation: 'opacity',
                            closeAnimation: 'scale',
                            title: '信息提示',
                            content: contentStr
                        });
                    }
                    initHeader();
                }
            });
        }
    });
}

function fixedModify(target,cbId,cbTypeSn){
    var $this = $(target), cbId = cbId, cbTypeSn = cbTypeSn,
	    jsonStr = {}, jsonAry = [];
    var ptType = getQueryString('hidTempModulesUrl');
    var contentUrl = 'url:/tempmodulecommon?cbId='+cbId+'&cbTypeSn='+cbTypeSn+'&tm='+new Date().getTime();
	$.confirm({
		animation: 'opacity',
		closeAnimation: 'scale',
		confirmTop: '4.5%',
		columnClass: 'col-md-11 col-md-offset-1',
		title: '编辑内容块',
		content: contentUrl,
		confirm: function () {
            if (!$('#moduleConfirmForm').isValid()) {
                return false;
            }
            jsonStr.cbId = cbId;
			jsonStr.moduleTitle = $('input[name="moduleTitle"]').val() ? $('input[name="moduleTitle"]').val() : '';
			jsonStr.enTitle = $('input[name="enTitle"]').val() ? $('input[name="enTitle"]').val() : '';
			switch (cbTypeSn){
                case 'fixedsol-richText':
				case 'fixedind-richText':
                case 'fixedind-imgtext':
                case 'fixedPro-imgleft':
					jsonAry.push($('#moduleConfirmForm').serializeJson());
					jsonAry[0].content= $('.summernoteModule').summernote('code');
					jsonStr.data = jsonAry;
					break;
                case 'fixedhp-imgCarousel':
				case 'fixedhp-ind-case':
				case 'fixedhp-sol':
                case 'fixedPro-textColumn':
                case 'fixedPro-mixedColumn':
                case 'fixedPro-imgColumn':
				case 'fixedhp-pro':
				case 'fixedsol-case':
					jsonStr.data = customModule.getCarouselVoJson();
				    break;
                case 'fixedind-adjunct':
	                jsonStr.data = customModule.getFileVoJson();
                    break;
                default :
	                jsonAry.push($('#moduleConfirmForm').serializeJson());
	                jsonStr.data = jsonAry;
            }
			console.log(jsonStr);
			customModule.moduleSave('/tempmodulecommon/u', jsonStr, function (data) {
				if (data) {
				    console.log(data);
					var queryStr = {};
					queryStr.cbId = cbId;
					queryStr.ptType =ptType;
					customModule.findModule('/tempModule/q', queryStr, function (data) {
						$this.parents('section[data-id="' + queryStr.cbId + '"]').replaceWith(data);
						$('section.cms-temp-c[data-id="' + queryStr.cbId + '"]').next('.sm-temp-add').remove();
						$('div.cms-temp-c[data-id="' + queryStr.cbId + '"]').next('.sm-temp-add').remove();
						customSettings.init();
						uecSlider('.uec-slider-banner');
						customIndexSlider();
						alertSucceed('修改成功！');
					});
				}
			});
		}
	});
};