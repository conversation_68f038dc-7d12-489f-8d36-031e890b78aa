/**
 * Created by <PERSON><PERSON><PERSON>@ronglian.com on 2017/5/8.
 */
$(document).ready(function() {
	customIndexSlider();
    indexNewsList();
});
function indexNewsList() {
    $('.uec-s1-hv-js li').hover(function () {
        var $this = $(this),index=$this.index();
        $this.addClass('on').siblings('li').removeClass('on');
        $('.uec-sect1-r li').eq(index).addClass('on').siblings('li').removeClass('on');
    });
}
function customIndexSlider(){
	uecSliderItem('.uec-p-slider');

	$("#slider-roll").mSlider({
		pagination: false,
		navigation : true,
		autoPlay: 3000, //Set AutoPlay to 3 seconds
		items : 3,
        stopOnHover:true,
        navigationText : ["  ", "  "],
		itemsDesktop : [1199,3],
		itemsDesktopSmall : [979,3]
	});
	$("#slider-roll1").mSlider({
		pagination: false,
		navigation : true,
        stopOnHover:true,
        navigationText : ["  ", "  "],
		autoPlay: 3000, //Set AutoPlay to 3 seconds
		items : 3,
		itemsDesktop : [1199,3],
		itemsDesktopSmall : [979,3]
	});

    $("#slider-roll3").mSlider({
        pagination: true,
        navigation : true,
	    prevClass:'m-prev uec-icon icon-iconfonticonfontleft',
	    nextClass:'m-next uec-icon icon-right-copy',
        navigationText : ["  ", "  "],
        stopOnHover:true,
        autoPlay: 3000, //Set AutoPlay to 3 seconds
        items : 3,
        itemsDesktop : [1199,3],
        itemsDesktopSmall : [979,3]
    });

	$("#slider-solution").mSlider({
		pagination: false,
		navigation : true,
        stopOnHover:true,
		prevClass:'m-prev uec-icon icon-iconfonticonfontleft',
		nextClass:'m-next uec-icon icon-right-copy',
		navigationText : ["  ", "  "],
		autoPlay: 5000, //Set AutoPlay to 3 seconds
		items : 4,
		itemsDesktop : [1199,3],
		itemsDesktopSmall : [979,3]
	});
};

function  fnClickL(target) {
	var $this = $(target), $parents = $this.parents('.uec-c-roll');
	$parents.find('.m-prev').mouseup();
};

function  fnClickR(obj) {
	var $this = $(obj), $parents = $this.parents('.uec-c-roll');
	$parents.find('.m-next').mouseup();
};