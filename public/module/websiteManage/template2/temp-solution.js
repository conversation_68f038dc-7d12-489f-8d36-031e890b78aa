/**
 * Created by <PERSON><PERSON><PERSON>@ronglian.com on 2017/5/11.
 */
var tablicked = null;
$(document).ready(function () {

	solutionSolider();

	initCustumTab('#uec-s-ul-js');

	touchMove();
});
$(window).resize(function () {
	initCustumTab('#uec-s-ul-js');
});
// 页签操作begin

function initCustumTab(elm) {
	var $ul = $(elm),
		tabSize = $ul.children('li').size(),
		liWidth = getTabliWidth($ul),
		tabContentWidth = $ul.parent().width();
	var ulWidth = liWidth + 1;
	$ul.width(ulWidth);
};

function getTabliWidth(elm) {
	var sum = 0, tabContentWidth = elm.parent().width();
	var sum2 = 0;
	elm.children('li').each(function (index, el) {
		var $el = $(el);
		sum += $el.width();
		if(sum >= tabContentWidth){
			$el.addClass('v-hidden');
			$('#uec-operate-tab').show();
		}else{
			$('#uec-operate-tab').hide();
		}
	});
	return sum;
};

function clickSolutionTabLeft() {
	clearTimeout(tablicked);
	tablicked = setTimeout(function () {
		var $ul = $('#uec-s-ul-js'),shiftLeft = 0;
		var liSize = $ul.children('li').size() - 1;
		var lastVisLi = $ul.children('li:not(.v-hidden)').last().index();
		var firstVisLi = $ul.children('li:not(.v-hidden)').first().index();
		var ulLeft = $ul.position().left;
		if(firstVisLi == 0){
			return;
		}else{
			var firstVisNextLiw = $ul.children('li:eq('+(firstVisLi-1)+')').removeClass('v-hidden').width();
			var lastVisPrevLiw = $ul.children('li:eq('+(lastVisLi-1)+')').width();
			if(firstVisNextLiw > lastVisPrevLiw){
				shiftLeft = ulLeft + firstVisNextLiw;
			}else{
				shiftLeft = ulLeft + lastVisPrevLiw;
			}
			if(shiftLeft > 0){
				shiftLeft = 0;
			}
			$ul.children('li:eq('+lastVisLi+')').addClass('v-hidden');
			$ul.stop(true, false).animate({left:shiftLeft}, 300);
		}
		clearTimeout(tablicked);
	},200);
}

function clickSolutionTabRight() {
	clearTimeout(tablicked);
	tablicked = setTimeout(function () {
		var $ul = $('#uec-s-ul-js'), shiftLeft = 0;
		var liSize = $ul.children('li').size() - 1;
		var lastVisLi = $ul.children('li:not(.v-hidden)').last().index();
		var firstVisLi = $ul.children('li:not(.v-hidden)').first().index();
		var ulLeft = $ul.position().left;
		if(liSize == lastVisLi){
			return;
		}else{
			var lastVisPrevLiw = $ul.children('li:eq('+(lastVisLi+1)+')').removeClass('v-hidden').width();
			var firstVisNextLiw = $ul.children('li:eq('+(firstVisLi)+')').width();
			shiftLeft = ulLeft - firstVisNextLiw;
			$ul.children('li:eq('+firstVisLi+')').addClass('v-hidden');
			$ul.stop(true, false).animate({left:shiftLeft},300);
		}
		clearTimeout(tablicked);
	},200);
}

function touchMove() {
	$(".wes-s-sub-js").on("swipeleft",function(){
		console.log("You swiped left!");
		$('.uec-s-t-r .icon-right').stop(false,true).click();
	});
	$(".wes-s-sub-js").on("swiperight",function(){
		console.log("You swiped right!");
		$('.uec-s-t-r .icon-sanjiao').stop(false,true).click();
	});
};
// 页签操作bend


/**
 * 解决方案广告图片
 * @param target
 */
function solutionModify(target) {
	var $this = $(target), cbId = $this.parents('.uec-section').attr('data-id'),
		contentUrl = '', url='';
	var jsonStr = {}, jsonAry = [];
	var tabsId =getQueryString('hidTempModuleCbId');
	if(cbId){
		jsonStr.cbId = cbId;
		contentUrl = 'url:/tempsolutionlst/s/m/q?tm='+new Date().getTime()+'&cbId='+cbId+'&tabsId='+tabsId;
		url = '/tempsolutionlst/s/m/u';
	}else{
		contentUrl = 'url:/tempsolutionlst/s/m?tm='+new Date().getTime()+'&cbTypeSn=fixedImg';
	    url = '/tempsolutionlst/s/m/a';
	}
	$.confirm({
		animation: 'opacity',
		closeAnimation: 'scale',
		confirmTop: '4.5%',
		columnClass: 'col-md-11 col-md-offset-1',
		title: '编辑解决方案图片',
		content: contentUrl,
		confirm: function () {
			var picMgid = $('#picMgid').val();
			if(!picMgid){
                $.alert({
                    animation: 'opacity',
                    closeAnimation: 'scale',
                    title: '提示',
                    content: alertWarning('请选择上传的图片')
                });
                return false;
			}else{
                jsonStr.tabsId = tabsId;
                jsonAry.push($('#moduleConfirmForm').serializeJson());
                jsonStr.data = jsonAry;
                customModule.moduleSave(url, jsonStr, function (data) {
                    if (data) {
                        var queryStr = {};
                        if(cbId)
                            queryStr.cbId = cbId;
                        else
                            queryStr.cbId = data.cbId;
                        customModule.findModule('/tempsolutionlst/cb/q', queryStr, function (data) {
                            $this.parents('.uec-section').replaceWith(data);
                        });
                    }
                });
			}
		}
	});
};

function solutionSolider(){
	$("#slider-ban").mSlider({
		autoPlay : 3000,
		stopOnHover : true,
		paginationSpeed : 1000,
		goToFirstSpeed : 2000,
		singleItem : true,
		transitionStyle:"fade"  //transitionStyle属性值有goDown or fade or backSlide or fadeUp
	});
};

function solutionAdd(target, tabsId,topTabsId,topShortId){
	var $this = $(target), tabsId = tabsId;
	$.confirm({
		animation: 'opacity',
		closeAnimation: 'scale',
		confirmTop: '4.5%',
		columnClass: 'col-md-7 col-md-offset-3',
		title: '设置新解决方案',
		content: 'url:/tempsolutionlst/s/c?tm='+new Date().getTime()+'&tabsId='+tabsId,
		confirm: function () {
			var jsonStr = $('#ts-column').serializeJson();
			var url = '/tempsolutionlst/s/c/a?tm='+new Date().getTime()+'&topTabsId='+topTabsId+'&topShortId='+topShortId;
			customModule.findModule(url, jsonStr, function (data) {
				if (data) {
					$this.parents('.uec-s-ul').prepend(data);
				}
			});
		}
	});
};

function solutionEdit(target, tabsId){
	var $this = $(target), tabsId = tabsId;
	$.confirm({
		animation: 'opacity',
		closeAnimation: 'scale',
		confirmTop: '4.5%',
		columnClass: 'col-md-7 col-md-offset-3',
		title: '编辑解决方案',
		content: 'url:/tempsolutionlst/s/c/u/q?tm='+new Date().getTime()+'&tabsId='+tabsId,
		confirm: function () {
			var jsonStr = $('#ts-column').serializeJson();
			var url = '/tempsolutionlst/s/c/u?tm='+new Date().getTime();
			customModule.findModule(url, jsonStr, function (data) {
				if (data) {
					alertSucceed('修改成功');
				}
			});
		}
	});
};

function solutionDel(target, tabsId){
    $.confirm({
        animation: 'opacity',
        closeAnimation: 'scale',
        confirmButton: '确定',
        cancelButton: '取消',
        title: '提示',
        content: alertWarning('确认删除此解决方案吗？'),
        confirm: function () {
            var $this = $(target);
            var url = '/tempsolutionlst/s/c/del';
            customModule.moduleDel(url, tabsId, function (data) {
                if (data.result == 'SUCCESS') {
                    alertSucceed('删除成功');
                    $this.parents('li').remove();
                }
            });
        },
        cancel:function () {
            return;
        }
    });
};


