/**
 * Created by <PERSON><PERSON><PERSON>@ronglian.com on 2017/5/8.
 */
$(document).ready(function() {
	mainNav();
	uecSlider('.uec-slider-banner');
	uecSliderItem('.uec-p-slider');

	companyInit();
});

function companyInit() {
	var itemSize = $('.uec-company-item .company-memberbox').size();
	if(itemSize == 1){
		$('.uec-company-member-btn').hide();
	}
};
function companyLeftbtn(target) {
	var itemSize = $('.uec-company-item .company-memberbox').size();
	var showIndex = $('.uec-company-item .company-memberbox.show').index();
	if(showIndex > 0){
		showIndex-=1;
		$('.uec-company-item .company-memberbox').eq(showIndex).addClass('show').siblings('.company-memberbox').removeClass('show');
		$('.uec-company-item .company-memberbox').eq(showIndex).removeClass('hide').siblings('.company-memberbox').addClass('hide');;
	}

};

function companyRightbtn(target){
	var itemSize = $('.uec-company-item .company-memberbox').size();
	var showIndex = $('.uec-company-item .company-memberbox.show').index();
	if(showIndex < itemSize){
		showIndex+=1;
		$('.uec-company-item .company-memberbox').eq(showIndex).addClass('show').siblings('.company-memberbox').removeClass('show');
		$('.uec-company-item .company-memberbox').eq(showIndex).removeClass('hide').siblings('.company-memberbox').addClass('hide');;

	}

};

function kellegMotion() {
	var mao = $("#" + getQueryString("lump")); //获得锚点
	if (mao.length > 0) {//判断对象是否存在
		setTimeout(function () {
			var pos = mao.position();
			var pose = pos.top;
			$("html,body").animate({ scrollTop: pose }, 800);
		},1);
	}
};

function uecSlider(obj){
	if(!obj) return;
	$(obj).each(function (index, item) {
		var $item = $(item), childrenItemSize = $item.children('div').size();
		if(childrenItemSize == 1){
			$item.mSlider({
				slideSpeed : 300,
				paginationSpeed : 400,
				stopOnHover : true,
				paginationSpeed : 1000,
				goToFirstSpeed : 2000,
				singleItem : true,
				transitionStyle:"fadeUp"  //transitionStyle属性值有goDown or fade or backSlide or fadeUp
			});
		}else{
			$item.mSlider({
				autoPlay : 3000,
				stopOnHover : true,
				paginationSpeed : 1000,
				goToFirstSpeed : 2000,
				singleItem : true,
				transitionStyle:"fadeUp"  //transitionStyle属性值有goDown or fade or backSlide or fadeUp
			});
		}
	});
};

function uecSliderItem(obj){
	if(!obj) return;
	$(obj).each(function (index, item) {
		var $item = $(item), childrenItemSize = $item.children('div').size();

		if(childrenItemSize == 1){
			$item.mSlider({
				pagination: true,
				navigation : true,
				stopOnHover:true,
				prevClass:'m-prev uec-icon icon-iconfonticonfontleft',
				nextClass:'m-next uec-icon icon-right-copy',
				navigationText : ["  ", "  "],
				autoPlay: 3000, //Set AutoPlay to 3 seconds
				items : 3,
				itemsDesktop : [1199,3],
				itemsDesktopSmall : [979,3]
			});
		}else{
			$item.mSlider({
				pagination: true,
				navigation : true,
				stopOnHover:true,
				prevClass:'m-prev uec-icon icon-iconfonticonfontleft',
				nextClass:'m-next uec-icon icon-right-copy',
				navigationText : ["  ", "  "],
				autoPlay: 3000, //Set AutoPlay to 3 seconds
				items : 3,
				itemsDesktop : [1199,3],
				itemsDesktopSmall : [979,3]
			});
		}
	});
};


function uecIPhoneNav(obj){
	var $this = $(obj), $phoneBox = $('.uec-h-iphone'), winWidth = $(window).width();
	if(!$this.hasClass('icon-cha')){
		$phoneBox.addClass('open');
		$this.removeClass('icon-shousuo').addClass('icon-cha');
	}else{
		$phoneBox.removeClass('open');
		$phoneBox.children('ul').removeClass('uec-ip-close');
		$phoneBox.children('ul').removeClass('uec-ip-open');
		$this.removeClass('icon-cha').addClass('icon-shousuo');
	}
};

function closeIPhoneNav(obj){
	var $this = $(obj);
	uecIPhoneNav('#uec-phone-nav');
};

function hrefIPhoneNav(obj) {
	var $this = $(obj), dataSection = $this.attr('data-section');
	if(dataSection){
		$('ul[data-section="'+dataSection+'"]').addClass('uec-ip-open');
		$this.parents('ul').addClass('uec-ip-close');
	}
};

function hrefIPhoneBack(obj){
	var $this = $(obj), dataSection = $this.parents('ul').attr('data-section');
	if(dataSection){
		$('a[data-section="'+dataSection+'"]').parents('ul').removeClass('uec-ip-close');
		$this.parents('ul').removeClass('uec-ip-open');
	}
};

/**
 * custom-<NAME_EMAIL>
 */
function mainNav() {
	var hv = false;
	$('.uec-nav-js li').hoverDelay({
		outDuring :70,
		hoverEvent: function(that){
			var $this = $(that),
				aData = $this.children('a').attr('data-section'),
				findSubNavLen = $('.uec-h-n-content').children('div[data-section='+aData+']').size();
			hv = false;
			if(findSubNavLen > 0){
				$('.uec-h-n-content').children('div[data-section='+aData+']').slideDown(300).addClass('on').siblings().hide();
				//$('.uec-h-n-content').children('div[data-section='+aData+']').children('.uec-h-nCline').animate({width:'100%'})
			}else{
				$('.uec-h-n-content').children('.uec-h-nCont').removeClass('on').hide();
			}
		},
		outEvent:function(that,e){
			var $this = $(that), aData = $this.children('a').attr('data-section');
			if(!hv){
				$('.uec-h-n-content').children('.uec-h-nCont').removeClass('on').hide();
			}
		}
	});

	$('.uec-h-n-content .uec-h-nCont').hover(function(){
		hv=true;
		$(this).addClass('on').show();
	},function(){
		$(this).removeClass('on').hide();
	});
};


function getQueryString(name) {
	var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
	var r = window.location.search.substr(1).match(reg);
	if(r!=null)return  unescape(r[2]); return null;
};

