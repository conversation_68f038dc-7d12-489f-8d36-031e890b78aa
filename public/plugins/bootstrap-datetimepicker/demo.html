<!doctype html>
<html>
<head>
<meta charset="utf-8">
<title>DateTime</title>
<link href="../../stylesheets/common.css" rel="stylesheet" type="text/css" />
<link href="../../stylesheets/scxt.css" rel="stylesheet" type="text/css"/>
<link href="../bootstrap/css/bootstrap.css" rel="stylesheet" type="text/css" />
<link href="../bootstrap/css/bootstrap-theme.css" rel="stylesheet" type="text/css" />

<link href="css/bootstrap-datetimepicker-2.1.9.css" rel="stylesheet" type="text/css" />



</head>
<body>
<h2>DateTime</h2>
<!-- dome1 begin -->
<div class="pl25 pt20">
	<small>显示两个时间：</small>
	<div id="reportrange1" class="btn calendar-default">
	 	
	 	<span></span><i class="glyphicon fa fa-calendar iconfont icon-time"></i>
	</div>
</div>
<!-- dome1 end -->
<!-- dome2 begin -->
<div class="pl25 pt20">
	<small>显示一个时间：</small>
	<div id="reportrange2" class="inline-block relative">
	 	<input type="text" name="birthday" id="birthday" class="w140 h30 input-time input-time-box pr20" value="">
	 	<i class="iconfont icon-time rp-right"></i>
	</div>
</div>
<!-- dome2 end -->
<!-- dome3 begin -->
<div class="pl25 pt20 mt50">
	<small>可选年费月份：</small>
	<div id="reportrange3" class="inline-block relative">
	 	<input type="text" name="birthday" id="birthday" class="w140 h30 input-time input-time-box pr20" value="">
	 	<i class="iconfont icon-time rp-right"></i>
	</div>
</div>
<!-- dome3 end -->

<script type="text/javascript" src="../jquery/jquery-1.11.3.js"></script>
<script type="text/javascript" src="../bootstrap/js/bootstrap.js"></script>
<script type="text/javascript" src="js/moment.js"></script>
<script type="text/javascript" src="js/bootstrap-datetimepicker-2.1.9.js"></script>
<!-- <script type="text/javascript" src="js/locales/bootstrap-datetimepicker.zh-CN.js"></script> -->
<script type="text/javascript">
    $(document).ready(function(){

    	// dome1 begin
	    $('#reportrange1 span').html(moment().startOf('hour').format('YYYY/MM/DD HH:mm') + ' - ' + moment().startOf('hour').format('YYYY/MM/DD HH:mm'));
	    $('#reportrange1').daterangepicker({
	    	startDate: moment().startOf('hour'),
			endDate: moment().startOf('hour'),
			timePicker12Hour:false,
			singleDatePicker: false

	    },function(start, end, label) {
	            //console.log(start.toISOString(), end.toISOString(), label);
	            $('#reportrange1 span').html(start.format('YYYY/MM/DD HH:mm') + ' - ' + end.format('YYYY/MM/DD HH:mm'));
	    });
	    //dome1 end
	    
	    //dome2 begin

	     $('#reportrange2').daterangepicker({ template:'default',timePicker:true,
			 timePicker24Hour:true}, function(start, end, label) {
                    console.log(start.toISOString(), end.toISOString(), label);
                   	$('#reportrange2 input').val(start.format('YYYY-MM-DD'));
                  });
	    //dome2 end
	    
	    //dome3 begin
	    $('#reportrange3').daterangepicker({
	    	startDate: moment().startOf('hour'),
			endDate: moment().startOf('hour'),
			template:'customTmpApply',
			timePicker:true,
			timePicker24Hour:true,
			showDropdowns:true
	    },function(start, end, label) {
	            $('#reportrange3 input').val(start.format('YYYY/MM/DD HH:mm'));
	    });

        $('#reportrange3').on('cancel.daterangepicker', function(ev, picker) {
            $(this).find('input').val('');
        });
	    //dome3 end
	    
	    
                        // $(function() {

                        //     $('input[name="datefilter"]').daterangepicker({
                        //         autoUpdateInput: false,
                        //         locale: {
                        //             cancelLabel: 'Clear'
                        //         }
                        //     });

                        //     $('input[name="datefilter"]').on('apply.daterangepicker', function(ev, picker) {
                        //         $(this).val(picker.startDate.format('MM/DD/YYYY') + ' - ' + picker.endDate.format('MM/DD/YYYY'));
                        //     });

                        //     $('input[name="datefilter"]').on('cancel.daterangepicker', function(ev, picker) {
                        //         $(this).val('');
                        //     });

                      //  });
                        
    });
</script>
</body>
</html>
