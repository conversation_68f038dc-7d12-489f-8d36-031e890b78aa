<!doctype html>
<html>
<head>
<meta charset="utf-8">
<title>Page</title>
<link href="../bootstrap/css/bootstrap.css" rel="stylesheet" type="text/css" />
<link href="../bootstrap/css/bootstrap-theme.css" rel="stylesheet" type="text/css" />
<link href="../../stylesheets/common.css" rel="stylesheet" type="text/css" />
<link href="../../stylesheets/shopm.css" rel="stylesheet" type="text/css" />
<link href="../../stylesheets/template2/stylesheets/uec-main.css" rel="stylesheet" type="text/css" />
</head>
<body>
<h2>Page</h2>

<!-- dome1 begin -->
<div class="pl25 pt20">
	<div id="example"></div> 
</div>
<!-- dome1 end -->

<button class="ds">设置</button>


<script type="text/javascript" src="../jquery/jquery-1.11.3.js"></script>
<script type="text/javascript" src="../bootstrap/js/bootstrap.js"></script>
<script type="text/javascript" src="js/bootstrap-paginator.js"></script>
<script type="text/javascript">
    $(document).ready(function(){    
        var counts = 10;

	    initPage(3,counts);

        $('.ds').click(function(event) {
            /* Act on the event */
            counts = 0;
            initPage(1,counts);
            //console.log('counts: '+ counts);
        });
	    
    });

    function initPage(pageNo,counts){
        var options = {
            currentPage: pageNo,  //直接跳转到特定的page
            totalPages: counts,
            useBootstrapTooltip: false,
            onPageChanged : function(event, oldPage, newPage){
                
                console.log('newPage: '+ newPage);
                
            },
            /*pageUrl: function (type, page, current) {
                console.log('当前的{current}: '+ current);
            }*/
        }

        $('#example').bootstrapPaginator(options);
    }
</script>
</body>
</html>
