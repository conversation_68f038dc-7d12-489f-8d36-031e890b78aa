<!doctype html>
<html>
<head>
<meta charset="utf-8">
<title>menu</title>
<link href="css/bootstrap.css" rel="stylesheet" type="text/css"/>
<link href="css/bootstrap-theme.css" rel="stylesheet" type="text/css"/>
<link href="../../stylesheets/common.css" rel="stylesheet" type="text/css"/>
<link href="../../stylesheets/scxt.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<!-- dome1 begin -->
<div class="menu-main content-left float-l">
    <div class="nav-list">
        <p>
            <a class="float-l icon iconfont">&#xe603;</a><span class="nav-in">客户管理</span><a class="float-r nav-r icon iconfont icon-updown icon-down1"></a>
        </p>
    </div>
    <div class="nav-list">
        <!--第一导航-->
        <p class="nav-pon">
            <!--当点击p给添加class="Nav-pon"-->
            <a class=" float-l icon iconfont nav-pon icon iconfont">&#xe601;</a>
            <span class="nav-in">项目管理</span>
            <a class="float-r nav-r on icon iconfont icon-updown"></a>
            <!--当点击显示二级菜单的时候a添加class为on-->
        </p>
        <ul class="second-nav">
            <!--第二导航-->
            <li>
                <p class="nav-pon">
                    <!--当点击p给换class="second-nav-lion"-->
                    <a href="#">
                    <i class="second-nav-top"></i>
                             总项目管理 
                    <i class=" second-nav-i second-nav-ion icon iconfont icon-updown"></i><!--当点击显示二级菜单的时候i添加class为second-nav-ion-->
                    </a>
                </p>
                <ul class="three-nav">
                    <!--第三导航-->
                    <li>总项目立项管理</li>
                    <li>总项目立项管理</li>
                    <li class="liton">总项目立项管理</li>
                    <li>总项目立项管理</li>
                    <li>总项目立项管理</li>
                    <li>总项目立项管理</li>
                </ul>
            </li>
            <li>
                <p>
                    <a href="#"><i class="second-nav-top"></i>总项目管理 <i class="second-nav-i icon iconfont icon-updown icon-down1"></i></a>
                </p>
                <ul class="three-nav" style=" display:none">
                    <!--第三导航-->
                    <li>总项目立项管理</li>
                    <li>总项目立项管理</li>
                    <li>总项目立项管理</li>
                    <li>总项目立项管理</li>
                    <li>总项目立项管理</li>
                    <li>总项目立项管理</li>
                </ul>
            </li>
            <li>
                <p>
                    <a href="#"><i class="second-nav-top"></i>总项目管理 <i class="second-nav-i icon iconfont icon-updown icon-down1"></i></a>
                </p>
                <ul class="three-nav" style=" display:none">
                    <!--第三导航-->
                    <li>总项目立项管理</li>
                    <li>总项目立项管理</li>
                    <li>总项目立项管理</li>
                    <li>总项目立项管理</li>
                    <li>总项目立项管理</li>
                    <li>总项目立项管理</li>
                </ul>
            </li>
        </ul>
    </div>
    <div class="nav-list">
        <p>
            <a class="float-l nav-l icon iconfont lab">&#xe600;</a><span class="nav-in">实验室管理</span><a class="float-r nav-r icon iconfont icon-updown icon-down1"></a>
        </p>
    </div>
    <div class="nav-list">
        <p>
            <a class="float-l nav-l icon iconfont">&#xe605;</a><span class="nav-in">查询管理</span><a class="float-r nav-r icon iconfont icon-updown icon-down1"></a>
        </p>
    </div>
    <div class="nav-list">
        <p>
            <a class="float-l nav-l icon iconfont">&#xe602;</a><span class="nav-in">系统设置</span><a class="float-r nav-r icon iconfont icon-updown icon-down1"></a>
        </p>
    </div>
</div>
<!-- dome1 end  -->
<script type="text/javascript" src="../jquery/jquery-1.11.3.js"></script>
<script type="text/javascript" src="js/bootstrap.js"></script>
<script type="text/javascript">
    $(function(){
        // 左侧导航 动画 begin
        $('.menu-main').delegate('.nav-list p', 'click', function(event) {
            var $this = $(this);
            if(!$this.hasClass('nav-pon')){
                $this.siblings('ul').stop().slideDown();
                $this.addClass('nav-pon').find('.nav-r,.second-nav-i').removeClass('icon-down1');
            }else{
                $this.siblings('ul').stop().slideUp();
                $this.removeClass('nav-pon').find('.nav-r,.second-nav-i').addClass('icon-down1');
            }
        });

        $('.menu-main').delegate('ul.three-nav li', 'click', function(event) {
            $(this).parents('.menu-main').find('li').removeClass('liton');
            $(this).addClass('liton');
        });
        // 左侧导航 动画 end
    });
</script>
</body>
</html>