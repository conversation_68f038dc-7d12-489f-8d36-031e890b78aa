<!doctype html>
<html>
<head>
<meta charset="utf-8">
<title>弹出框</title>

<link href="css/bootstrap.css" rel="stylesheet" type="text/css" />
<link href="css/bootstrap-theme.css" rel="stylesheet" type="text/css" />
<link href="../../stylesheets/common.css" rel="stylesheet" type="text/css" />
<link href="../../stylesheets/scxt.css" rel="stylesheet" type="text/css" />
<style type="text/css">
  .form-control{display: inline;}
</style>
</head>
<body>
<h2>弹出框</h2>

<!-- dome1 begin -->
<div class="pl25 pt20 zxmlx-conent">
    项目编号：项目编号：项目编号：项目编号：项目编号：项目编号：项目编号：<input type="text" class="my-popover form-control w200 view-port1" data-toggle="popover" placeholder="my table">
    
    <div class="my-poptable" style="display:none;">
        <div class="list-group" style="width:600px;">
          <a href="#" class="list-group-item active">
            Cras justo odio
          </a>
          <a href="#" class="list-group-item">Dapibus ac facilisis in</a>
          <a href="#" class="list-group-item">Morbi leo risus</a>
          <a href="#" class="list-group-item">Porta ac consectetur ac</a>
          <a href="#" class="list-group-item">Vestibulum at eros</a>
        </div>
    </div>
    
</div>
<!-- dome1 end  -->
<table class="table table-striped table-hover-box  table-box-striped">
    <thead>
        <tr>
          <th width="20%">项目经理 </th>
          <th width="20%">总数</th>
          <th width="20%">生产中</th>
          <th width="20%">已结项</th>
          <th width="20%">已终止</th>
         
        </tr>
    </thead>
        <tr>
            <td><label data-toggle="tooltip" title="Tooltip on right" data-placement="bottom" >于丹丹</label></td>
            <td><label data-toggle="tooltip" title="Tooltip on right" data-placement="bottom" >247</label></td>
            <td><label data-toggle="tooltip" title="Tooltip on right" data-placement="bottom" >100</label></td>
            <td title="47">47</td>
            <td>100</td>
        </tr>   
</table>

<!-- dome2 begin -->
<div class="pl25 pt20 zxmlx-conent">
    操作提示 hover：<input type="text" class="form-control w200" data-toggle="tooltip" placeholder="得到焦点有提示" title="Tooltip on right" data-placement="bottom"><br>
    操作提示 focus：<input type="text" class="form-control w200" data-toggle="tooltip-focus" placeholder="得到焦点有提示"><br>
</div>
<!-- dome2 end  -->

<!-- dome3 begin -->
<div class="pl25 pt20 zxmlx-conent">
    操作提示2：<input type="text" class="tooltip-html form-control w200" data-toggle="tooltip-html" placeholder="得到焦点有提示">   
</div>
<!-- dome3 end  -->
<script type="text/javascript" src="../jquery/jquery-1.11.3.js"></script>
<script type="text/javascript" src="js/bootstrap.js"></script>
<script type="text/javascript" src="../../module/base.js"></script>
<script type="text/javascript">
    $(function(){

        $('[data-toggle="popover"]').popover({
            trigger:'click', //触发方式
            template: popoverTempHtml(),
            placement: 'bottom', //top, bottom, left or right
            viewport:{ selector: '.view-port1', padding: 0 },
            title:"标题",//设置 弹出框 的标题
            html: true, // 为true的话，data-content里就能放html代码了
            content: function(){
               return $('.my-poptable').html(); 
            },//这里可以直接写字符串，也可以 是一个函数，该函数返回一个字符
        });

        $('[data-toggle="tooltip"]').tooltip();

        $('[data-toggle="tooltip-focus"]').tooltip({
            trigger:'focus', //触发方式
            placement: 'right', //top, bottom, left or right
            title:"Hi 你得到焦点"//设置 弹出框 的标题
        });

        $('[data-toggle="tooltip-html"]').popover({
            trigger:'focus', //触发方式
            placement: 'right', //top, bottom, left or right
            template: tooltipTempHtml(),
            html: true,
            content:'人员信息请在此添加，添加时请遵守人员信息 填写规范.'
        });
        
    });

    
</script>
</body>
</html>
