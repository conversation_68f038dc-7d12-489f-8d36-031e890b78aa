<!doctype html>
<html>
<head>
<meta charset="utf-8">
<title>tab</title>

<link href="css/bootstrap.css" rel="stylesheet" type="text/css" />
<link href="css/bootstrap-theme.css" rel="stylesheet" type="text/css" />
<link href="../../stylesheets/common.css" rel="stylesheet" type="text/css" />
<link href="../../stylesheets/scxt.css" rel="stylesheet" type="text/css" />
</head>
<body>
<h2>Tab</h2>

<!-- dome1 begin -->
<div class="pl25 pt20 zxmlx-conent">
    <div class="tabbable zxmlx-tabbable"> 
        <ul class="nav nav-tabs is-tab-panl" role="tablist">
            <li role="presentation" class="active">
                <a href="#home" aria-controls="home" role="tab" data-toggle="tab">Home</a>
                <em class="nav-tabs-em"></em>
            </li>
            <li role="presentation">
                <a href="#profile" onclick="showPage('profile','loading.html')" aria-controls="profile" role="tab" data-toggle="tab">Profile</a>
                <em class="nav-tabs-em"></em>
            </li>
            <li role="presentation">
                <a href="#messages" aria-controls="messages" role="tab" data-toggle="tab">Messages</a>
                <em class="nav-tabs-em"></em>
            </li>
            <li role="presentation">
                <a href="#settings" aria-controls="settings" role="tab" data-toggle="tab">Settings</a>
                <em class="nav-tabs-em"></em>
            </li>
        </ul>
        <div class="tab-content zxmlx-tab-content">
            <div role="tabpanel" class="tab-pane active" id="home">default</div>
            <div role="tabpanel" class="tab-pane" id="profile">页面加载中，请稍后...</div>
            <div role="tabpanel" class="tab-pane" id="messages">页面加载中，请稍后...</div>
            <div role="tabpanel" class="tab-pane" id="settings">页面加载中，请稍后...</div>
        </div>
    </div>
</div>
<!-- dome1 end  -->
<script type="text/javascript" src="../jquery/jquery-1.11.3.js"></script>
<script type="text/javascript" src="js/bootstrap.js"></script>
<script type="text/javascript" src="../../module/base.js"></script>
</body>
</html>
