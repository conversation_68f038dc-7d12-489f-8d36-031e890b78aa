<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>无标题文档</title>
<link href="css/bootstrap.css" rel="stylesheet" type="text/css" />
<link href="css/bootstrap-theme.css" rel="stylesheet" type="text/css" />
<link href="../../stylesheets/common.css" rel="stylesheet" type="text/css" />
<link href="../../stylesheets/scxt.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="../jquery/jquery-1.11.3.js"></script>
<script type="text/javascript" src="js/bootstrap.js"></script>
<script type="text/javascript" src="../../module/base.js"></script>
</head>

<body>
 <div class="content-cx">
    <p class="content-title"><em class="content-title-p">样品查询</em><a class="icondowm iconfont icon-updown"></a></p>
    <!--内容区域开始-->       
        <div class="animate-search">
          <div class="content-chax">
               <span><em>样品编号：</em>
               <input type="text" class="input-medium input-box">
            </span>
               <span><em>样品名称：</em>
               <input type="text" class="input-medium input-box">
            </span>
               <span><em>任务名称：</em>
            <input type="text" class="input-medium input-box"></span>
          </div>
          <div class="content-chax">
           <span><em>子项目名称：</em>
            <input type="text" class="input-medium input-box">
           </span>
             <span>
                 <em>任务类型：</em> 
                  <div class="btn-group btn-group-box jk-box">
                      <span type="button" class="btn-group-wenb">全部</span>
                      <button type="button" class="btn-group-btn" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <span class="caret"></span>
                      </button>
                      <ul class="dropdown-menu">
                        <li><a href="#">已完成</a></li>
                        <li><a href="#">未完成</a></li>
                        <li><a href="#">已完成</a></li>
                        <li><a href="#">未完成</a></li>
                      </ul>
                  </div>  
           </span>
           
           <span>
              <em>任务状态：</em>
                  <div class="btn-group btn-group-box  jk-box">
                      <span type="button" class="btn-group-wenb">已完成</span>
                      <button type="button" class="btn-group-btn" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <span class="caret"></span>
                      </button>
                      <ul class="dropdown-menu">
                        <li><a href="#">已完成</a></li>
                        <li><a href="#">未完成</a></li>
                        <li><a href="#">已完成</a></li>
                        <li><a href="#">未完成</a></li>
                      </ul>
                  </div>            
             </span>
            </div>
           
             <div class="content-chax">
             <span class="btn-span"> 
               <button type="button" class="btn searchbtn w100 btn-box"><i class="iconfont searchb mr5">&#xe60d;</i>查询</button>
             </span>
             </div> 
        </div>     
   <!--内容区域结束-->       

</div> 
</body>
</html>
