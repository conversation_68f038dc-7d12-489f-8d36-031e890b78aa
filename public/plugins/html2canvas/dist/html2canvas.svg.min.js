/*
  html2canvas 0.5.0-beta3 <http://html2canvas.hertzen.com>
  Copyright (c) 2016 <PERSON><PERSON>

  Released under  License
*/
!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n;"undefined"!=typeof window?n=window:"undefined"!=typeof global?n=global:"undefined"!=typeof self&&(n=self),(n.html2canvas||(n.html2canvas={})).svg=e()}}(function(){var define,module,exports;return function e(n,f,o){function d(t,l){if(!f[t]){if(!n[t]){var s="function"==typeof require&&require;if(!l&&s)return s(t,!0);if(i)return i(t,!0);var u=new Error("Cannot find module '"+t+"'");throw u.code="MODULE_NOT_FOUND",u}var a=f[t]={exports:{}};n[t][0].call(a.exports,function(e){var f=n[t][1][e];return d(f?f:e)},a,a.exports,e,n,f,o)}return f[t].exports}for(var i="function"==typeof require&&require,t=0;t<o.length;t++)d(o[t]);return d}({1:[function(){},{}],2:[function(e,n,f){function o(e,n,f){if(!(this instanceof o))return new o(e,n,f);var d,i=typeof e;if("number"===i)d=e>0?e>>>0:0;else if("string"===i)d=o.byteLength(e,n);else{if("object"!==i||null===e)throw new TypeError("must start with number, buffer, array or string");"Buffer"===e.type&&K(e.data)&&(e=e.data),d=+e.length>0?Math.floor(+e.length):0}if(d>L)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+L.toString(16)+" bytes");var t;o.TYPED_ARRAY_SUPPORT?t=o._augment(new Uint8Array(d)):(t=this,t.length=d,t._isBuffer=!0);var l;if(o.TYPED_ARRAY_SUPPORT&&"number"==typeof e.byteLength)t._set(e);else if(A(e))if(o.isBuffer(e))for(l=0;d>l;l++)t[l]=e.readUInt8(l);else for(l=0;d>l;l++)t[l]=(e[l]%256+256)%256;else if("string"===i)t.write(e,0,n);else if("number"===i&&!o.TYPED_ARRAY_SUPPORT&&!f)for(l=0;d>l;l++)t[l]=0;return d>0&&d<=o.poolSize&&(t.parent=M),t}function d(e,n,f){if(!(this instanceof d))return new d(e,n,f);var i=new o(e,n,f);return delete i.parent,i}function i(e,n,f,o){f=Number(f)||0;var d=e.length-f;o?(o=Number(o),o>d&&(o=d)):o=d;var i=n.length;if(i%2!==0)throw new Error("Invalid hex string");o>i/2&&(o=i/2);for(var t=0;o>t;t++){var l=parseInt(n.substr(2*t,2),16);if(isNaN(l))throw new Error("Invalid hex string");e[f+t]=l}return t}function t(e,n,f,o){var d=G(C(n,e.length-f),e,f,o);return d}function l(e,n,f,o){var d=G(D(n),e,f,o);return d}function s(e,n,f,o){return l(e,n,f,o)}function u(e,n,f,o){var d=G(F(n),e,f,o);return d}function a(e,n,f,o){var d=G(E(n,e.length-f),e,f,o,2);return d}function p(e,n,f){return I.fromByteArray(0===n&&f===e.length?e:e.slice(n,f))}function c(e,n,f){var o="",d="";f=Math.min(e.length,f);for(var i=n;f>i;i++)e[i]<=127?(o+=H(d)+String.fromCharCode(e[i]),d=""):d+="%"+e[i].toString(16);return o+H(d)}function y(e,n,f){var o="";f=Math.min(e.length,f);for(var d=n;f>d;d++)o+=String.fromCharCode(127&e[d]);return o}function m(e,n,f){var o="";f=Math.min(e.length,f);for(var d=n;f>d;d++)o+=String.fromCharCode(e[d]);return o}function r(e,n,f){var o=e.length;(!n||0>n)&&(n=0),(!f||0>f||f>o)&&(f=o);for(var d="",i=n;f>i;i++)d+=B(e[i]);return d}function v(e,n,f){for(var o=e.slice(n,f),d="",i=0;i<o.length;i+=2)d+=String.fromCharCode(o[i]+256*o[i+1]);return d}function w(e,n,f){if(e%1!==0||0>e)throw new RangeError("offset is not uint");if(e+n>f)throw new RangeError("Trying to access beyond buffer length")}function b(e,n,f,d,i,t){if(!o.isBuffer(e))throw new TypeError("buffer must be a Buffer instance");if(n>i||t>n)throw new RangeError("value is out of bounds");if(f+d>e.length)throw new RangeError("index out of range")}function g(e,n,f,o){0>n&&(n=65535+n+1);for(var d=0,i=Math.min(e.length-f,2);i>d;d++)e[f+d]=(n&255<<8*(o?d:1-d))>>>8*(o?d:1-d)}function h(e,n,f,o){0>n&&(n=4294967295+n+1);for(var d=0,i=Math.min(e.length-f,4);i>d;d++)e[f+d]=n>>>8*(o?d:3-d)&255}function x(e,n,f,o,d,i){if(n>d||i>n)throw new RangeError("value is out of bounds");if(f+o>e.length)throw new RangeError("index out of range");if(0>f)throw new RangeError("index out of range")}function j(e,n,f,o,d){return d||x(e,n,f,4,3.4028234663852886e38,-3.4028234663852886e38),J.write(e,n,f,o,23,4),f+4}function k(e,n,f,o,d){return d||x(e,n,f,8,1.7976931348623157e308,-1.7976931348623157e308),J.write(e,n,f,o,52,8),f+8}function q(e){if(e=z(e).replace(O,""),e.length<2)return"";for(;e.length%4!==0;)e+="=";return e}function z(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function A(e){return K(e)||o.isBuffer(e)||e&&"object"==typeof e&&"number"==typeof e.length}function B(e){return 16>e?"0"+e.toString(16):e.toString(16)}function C(e,n){var f,o=e.length,d=null;n=n||1/0;for(var i=[],t=0;o>t;t++){if(f=e.charCodeAt(t),f>55295&&57344>f){if(!d){if(f>56319){(n-=3)>-1&&i.push(239,191,189);continue}if(t+1===o){(n-=3)>-1&&i.push(239,191,189);continue}d=f;continue}if(56320>f){(n-=3)>-1&&i.push(239,191,189),d=f;continue}f=d-55296<<10|f-56320|65536,d=null}else d&&((n-=3)>-1&&i.push(239,191,189),d=null);if(128>f){if((n-=1)<0)break;i.push(f)}else if(2048>f){if((n-=2)<0)break;i.push(f>>6|192,63&f|128)}else if(65536>f){if((n-=3)<0)break;i.push(f>>12|224,f>>6&63|128,63&f|128)}else{if(!(2097152>f))throw new Error("Invalid code point");if((n-=4)<0)break;i.push(f>>18|240,f>>12&63|128,f>>6&63|128,63&f|128)}}return i}function D(e){for(var n=[],f=0;f<e.length;f++)n.push(255&e.charCodeAt(f));return n}function E(e,n){for(var f,o,d,i=[],t=0;t<e.length&&!((n-=2)<0);t++)f=e.charCodeAt(t),o=f>>8,d=f%256,i.push(d),i.push(o);return i}function F(e){return I.toByteArray(q(e))}function G(e,n,f,o,d){d&&(o-=o%d);for(var i=0;o>i&&!(i+f>=n.length||i>=e.length);i++)n[i+f]=e[i];return i}function H(e){try{return decodeURIComponent(e)}catch(n){return String.fromCharCode(65533)}}var I=e("base64-js"),J=e("ieee754"),K=e("is-array");f.Buffer=o,f.SlowBuffer=d,f.INSPECT_MAX_BYTES=50,o.poolSize=8192;var L=1073741823,M={};o.TYPED_ARRAY_SUPPORT=function(){try{var e=new ArrayBuffer(0),n=new Uint8Array(e);return n.foo=function(){return 42},42===n.foo()&&"function"==typeof n.subarray&&0===new Uint8Array(1).subarray(1,1).byteLength}catch(f){return!1}}(),o.isBuffer=function(e){return!(null==e||!e._isBuffer)},o.compare=function(e,n){if(!o.isBuffer(e)||!o.isBuffer(n))throw new TypeError("Arguments must be Buffers");for(var f=e.length,d=n.length,i=0,t=Math.min(f,d);t>i&&e[i]===n[i];i++);return i!==t&&(f=e[i],d=n[i]),d>f?-1:f>d?1:0},o.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"raw":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},o.concat=function(e,n){if(!K(e))throw new TypeError("Usage: Buffer.concat(list[, length])");if(0===e.length)return new o(0);if(1===e.length)return e[0];var f;if(void 0===n)for(n=0,f=0;f<e.length;f++)n+=e[f].length;var d=new o(n),i=0;for(f=0;f<e.length;f++){var t=e[f];t.copy(d,i),i+=t.length}return d},o.byteLength=function(e,n){var f;switch(e+="",n||"utf8"){case"ascii":case"binary":case"raw":f=e.length;break;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":f=2*e.length;break;case"hex":f=e.length>>>1;break;case"utf8":case"utf-8":f=C(e).length;break;case"base64":f=F(e).length;break;default:f=e.length}return f},o.prototype.length=void 0,o.prototype.parent=void 0,o.prototype.toString=function(e,n,f){var o=!1;if(n>>>=0,f=void 0===f||1/0===f?this.length:f>>>0,e||(e="utf8"),0>n&&(n=0),f>this.length&&(f=this.length),n>=f)return"";for(;;)switch(e){case"hex":return r(this,n,f);case"utf8":case"utf-8":return c(this,n,f);case"ascii":return y(this,n,f);case"binary":return m(this,n,f);case"base64":return p(this,n,f);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return v(this,n,f);default:if(o)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),o=!0}},o.prototype.equals=function(e){if(!o.isBuffer(e))throw new TypeError("Argument must be a Buffer");return 0===o.compare(this,e)},o.prototype.inspect=function(){var e="",n=f.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(e+=" ... ")),"<Buffer "+e+">"},o.prototype.compare=function(e){if(!o.isBuffer(e))throw new TypeError("Argument must be a Buffer");return o.compare(this,e)},o.prototype.get=function(e){return console.log(".get() is deprecated. Access using array indexes instead."),this.readUInt8(e)},o.prototype.set=function(e,n){return console.log(".set() is deprecated. Access using array indexes instead."),this.writeUInt8(e,n)},o.prototype.write=function(e,n,f,o){if(isFinite(n))isFinite(f)||(o=f,f=void 0);else{var d=o;o=n,n=f,f=d}if(n=Number(n)||0,0>f||0>n||n>this.length)throw new RangeError("attempt to write outside buffer bounds");var p=this.length-n;f?(f=Number(f),f>p&&(f=p)):f=p,o=String(o||"utf8").toLowerCase();var c;switch(o){case"hex":c=i(this,e,n,f);break;case"utf8":case"utf-8":c=t(this,e,n,f);break;case"ascii":c=l(this,e,n,f);break;case"binary":c=s(this,e,n,f);break;case"base64":c=u(this,e,n,f);break;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":c=a(this,e,n,f);break;default:throw new TypeError("Unknown encoding: "+o)}return c},o.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},o.prototype.slice=function(e,n){var f=this.length;e=~~e,n=void 0===n?f:~~n,0>e?(e+=f,0>e&&(e=0)):e>f&&(e=f),0>n?(n+=f,0>n&&(n=0)):n>f&&(n=f),e>n&&(n=e);var d;if(o.TYPED_ARRAY_SUPPORT)d=o._augment(this.subarray(e,n));else{var i=n-e;d=new o(i,void 0,!0);for(var t=0;i>t;t++)d[t]=this[t+e]}return d.length&&(d.parent=this.parent||this),d},o.prototype.readUIntLE=function(e,n,f){e>>>=0,n>>>=0,f||w(e,n,this.length);for(var o=this[e],d=1,i=0;++i<n&&(d*=256);)o+=this[e+i]*d;return o},o.prototype.readUIntBE=function(e,n,f){e>>>=0,n>>>=0,f||w(e,n,this.length);for(var o=this[e+--n],d=1;n>0&&(d*=256);)o+=this[e+--n]*d;return o},o.prototype.readUInt8=function(e,n){return n||w(e,1,this.length),this[e]},o.prototype.readUInt16LE=function(e,n){return n||w(e,2,this.length),this[e]|this[e+1]<<8},o.prototype.readUInt16BE=function(e,n){return n||w(e,2,this.length),this[e]<<8|this[e+1]},o.prototype.readUInt32LE=function(e,n){return n||w(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},o.prototype.readUInt32BE=function(e,n){return n||w(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},o.prototype.readIntLE=function(e,n,f){e>>>=0,n>>>=0,f||w(e,n,this.length);for(var o=this[e],d=1,i=0;++i<n&&(d*=256);)o+=this[e+i]*d;return d*=128,o>=d&&(o-=Math.pow(2,8*n)),o},o.prototype.readIntBE=function(e,n,f){e>>>=0,n>>>=0,f||w(e,n,this.length);for(var o=n,d=1,i=this[e+--o];o>0&&(d*=256);)i+=this[e+--o]*d;return d*=128,i>=d&&(i-=Math.pow(2,8*n)),i},o.prototype.readInt8=function(e,n){return n||w(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},o.prototype.readInt16LE=function(e,n){n||w(e,2,this.length);var f=this[e]|this[e+1]<<8;return 32768&f?4294901760|f:f},o.prototype.readInt16BE=function(e,n){n||w(e,2,this.length);var f=this[e+1]|this[e]<<8;return 32768&f?4294901760|f:f},o.prototype.readInt32LE=function(e,n){return n||w(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},o.prototype.readInt32BE=function(e,n){return n||w(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},o.prototype.readFloatLE=function(e,n){return n||w(e,4,this.length),J.read(this,e,!0,23,4)},o.prototype.readFloatBE=function(e,n){return n||w(e,4,this.length),J.read(this,e,!1,23,4)},o.prototype.readDoubleLE=function(e,n){return n||w(e,8,this.length),J.read(this,e,!0,52,8)},o.prototype.readDoubleBE=function(e,n){return n||w(e,8,this.length),J.read(this,e,!1,52,8)},o.prototype.writeUIntLE=function(e,n,f,o){e=+e,n>>>=0,f>>>=0,o||b(this,e,n,f,Math.pow(2,8*f),0);var d=1,i=0;for(this[n]=255&e;++i<f&&(d*=256);)this[n+i]=e/d>>>0&255;return n+f},o.prototype.writeUIntBE=function(e,n,f,o){e=+e,n>>>=0,f>>>=0,o||b(this,e,n,f,Math.pow(2,8*f),0);var d=f-1,i=1;for(this[n+d]=255&e;--d>=0&&(i*=256);)this[n+d]=e/i>>>0&255;return n+f},o.prototype.writeUInt8=function(e,n,f){return e=+e,n>>>=0,f||b(this,e,n,1,255,0),o.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[n]=e,n+1},o.prototype.writeUInt16LE=function(e,n,f){return e=+e,n>>>=0,f||b(this,e,n,2,65535,0),o.TYPED_ARRAY_SUPPORT?(this[n]=e,this[n+1]=e>>>8):g(this,e,n,!0),n+2},o.prototype.writeUInt16BE=function(e,n,f){return e=+e,n>>>=0,f||b(this,e,n,2,65535,0),o.TYPED_ARRAY_SUPPORT?(this[n]=e>>>8,this[n+1]=e):g(this,e,n,!1),n+2},o.prototype.writeUInt32LE=function(e,n,f){return e=+e,n>>>=0,f||b(this,e,n,4,4294967295,0),o.TYPED_ARRAY_SUPPORT?(this[n+3]=e>>>24,this[n+2]=e>>>16,this[n+1]=e>>>8,this[n]=e):h(this,e,n,!0),n+4},o.prototype.writeUInt32BE=function(e,n,f){return e=+e,n>>>=0,f||b(this,e,n,4,4294967295,0),o.TYPED_ARRAY_SUPPORT?(this[n]=e>>>24,this[n+1]=e>>>16,this[n+2]=e>>>8,this[n+3]=e):h(this,e,n,!1),n+4},o.prototype.writeIntLE=function(e,n,f,o){e=+e,n>>>=0,o||b(this,e,n,f,Math.pow(2,8*f-1)-1,-Math.pow(2,8*f-1));var d=0,i=1,t=0>e?1:0;for(this[n]=255&e;++d<f&&(i*=256);)this[n+d]=(e/i>>0)-t&255;return n+f},o.prototype.writeIntBE=function(e,n,f,o){e=+e,n>>>=0,o||b(this,e,n,f,Math.pow(2,8*f-1)-1,-Math.pow(2,8*f-1));var d=f-1,i=1,t=0>e?1:0;for(this[n+d]=255&e;--d>=0&&(i*=256);)this[n+d]=(e/i>>0)-t&255;return n+f},o.prototype.writeInt8=function(e,n,f){return e=+e,n>>>=0,f||b(this,e,n,1,127,-128),o.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),0>e&&(e=255+e+1),this[n]=e,n+1},o.prototype.writeInt16LE=function(e,n,f){return e=+e,n>>>=0,f||b(this,e,n,2,32767,-32768),o.TYPED_ARRAY_SUPPORT?(this[n]=e,this[n+1]=e>>>8):g(this,e,n,!0),n+2},o.prototype.writeInt16BE=function(e,n,f){return e=+e,n>>>=0,f||b(this,e,n,2,32767,-32768),o.TYPED_ARRAY_SUPPORT?(this[n]=e>>>8,this[n+1]=e):g(this,e,n,!1),n+2},o.prototype.writeInt32LE=function(e,n,f){return e=+e,n>>>=0,f||b(this,e,n,4,2147483647,-2147483648),o.TYPED_ARRAY_SUPPORT?(this[n]=e,this[n+1]=e>>>8,this[n+2]=e>>>16,this[n+3]=e>>>24):h(this,e,n,!0),n+4},o.prototype.writeInt32BE=function(e,n,f){return e=+e,n>>>=0,f||b(this,e,n,4,2147483647,-2147483648),0>e&&(e=4294967295+e+1),o.TYPED_ARRAY_SUPPORT?(this[n]=e>>>24,this[n+1]=e>>>16,this[n+2]=e>>>8,this[n+3]=e):h(this,e,n,!1),n+4},o.prototype.writeFloatLE=function(e,n,f){return j(this,e,n,!0,f)},o.prototype.writeFloatBE=function(e,n,f){return j(this,e,n,!1,f)},o.prototype.writeDoubleLE=function(e,n,f){return k(this,e,n,!0,f)},o.prototype.writeDoubleBE=function(e,n,f){return k(this,e,n,!1,f)},o.prototype.copy=function(e,n,f,d){var i=this;if(f||(f=0),d||0===d||(d=this.length),n>=e.length&&(n=e.length),n||(n=0),d>0&&f>d&&(d=f),d===f)return 0;if(0===e.length||0===i.length)return 0;if(0>n)throw new RangeError("targetStart out of bounds");if(0>f||f>=i.length)throw new RangeError("sourceStart out of bounds");if(0>d)throw new RangeError("sourceEnd out of bounds");d>this.length&&(d=this.length),e.length-n<d-f&&(d=e.length-n+f);var t=d-f;if(1e3>t||!o.TYPED_ARRAY_SUPPORT)for(var l=0;t>l;l++)e[l+n]=this[l+f];else e._set(this.subarray(f,f+t),n);return t},o.prototype.fill=function(e,n,f){if(e||(e=0),n||(n=0),f||(f=this.length),n>f)throw new RangeError("end < start");if(f!==n&&0!==this.length){if(0>n||n>=this.length)throw new RangeError("start out of bounds");if(0>f||f>this.length)throw new RangeError("end out of bounds");var o;if("number"==typeof e)for(o=n;f>o;o++)this[o]=e;else{var d=C(e.toString()),i=d.length;for(o=n;f>o;o++)this[o]=d[o%i]}return this}},o.prototype.toArrayBuffer=function(){if("undefined"!=typeof Uint8Array){if(o.TYPED_ARRAY_SUPPORT)return new o(this).buffer;for(var e=new Uint8Array(this.length),n=0,f=e.length;f>n;n+=1)e[n]=this[n];return e.buffer}throw new TypeError("Buffer.toArrayBuffer not supported in this browser")};var N=o.prototype;o._augment=function(e){return e.constructor=o,e._isBuffer=!0,e._get=e.get,e._set=e.set,e.get=N.get,e.set=N.set,e.write=N.write,e.toString=N.toString,e.toLocaleString=N.toString,e.toJSON=N.toJSON,e.equals=N.equals,e.compare=N.compare,e.copy=N.copy,e.slice=N.slice,e.readUIntLE=N.readUIntLE,e.readUIntBE=N.readUIntBE,e.readUInt8=N.readUInt8,e.readUInt16LE=N.readUInt16LE,e.readUInt16BE=N.readUInt16BE,e.readUInt32LE=N.readUInt32LE,e.readUInt32BE=N.readUInt32BE,e.readIntLE=N.readIntLE,e.readIntBE=N.readIntBE,e.readInt8=N.readInt8,e.readInt16LE=N.readInt16LE,e.readInt16BE=N.readInt16BE,e.readInt32LE=N.readInt32LE,e.readInt32BE=N.readInt32BE,e.readFloatLE=N.readFloatLE,e.readFloatBE=N.readFloatBE,e.readDoubleLE=N.readDoubleLE,e.readDoubleBE=N.readDoubleBE,e.writeUInt8=N.writeUInt8,e.writeUIntLE=N.writeUIntLE,e.writeUIntBE=N.writeUIntBE,e.writeUInt16LE=N.writeUInt16LE,e.writeUInt16BE=N.writeUInt16BE,e.writeUInt32LE=N.writeUInt32LE,e.writeUInt32BE=N.writeUInt32BE,e.writeIntLE=N.writeIntLE,e.writeIntBE=N.writeIntBE,e.writeInt8=N.writeInt8,e.writeInt16LE=N.writeInt16LE,e.writeInt16BE=N.writeInt16BE,e.writeInt32LE=N.writeInt32LE,e.writeInt32BE=N.writeInt32BE,e.writeFloatLE=N.writeFloatLE,e.writeFloatBE=N.writeFloatBE,e.writeDoubleLE=N.writeDoubleLE,e.writeDoubleBE=N.writeDoubleBE,e.fill=N.fill,e.inspect=N.inspect,e.toArrayBuffer=N.toArrayBuffer,e};var O=/[^+\/0-9A-z\-]/g},{"base64-js":3,ieee754:4,"is-array":5}],3:[function(e,n,f){var o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";!function(e){"use strict";function n(e){var n=e.charCodeAt(0);return n===t||n===p?62:n===l||n===c?63:s>n?-1:s+10>n?n-s+26+26:a+26>n?n-a:u+26>n?n-u+26:void 0}function f(e){function f(e){u[p++]=e}var o,d,t,l,s,u;if(e.length%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var a=e.length;s="="===e.charAt(a-2)?2:"="===e.charAt(a-1)?1:0,u=new i(3*e.length/4-s),t=s>0?e.length-4:e.length;var p=0;for(o=0,d=0;t>o;o+=4,d+=3)l=n(e.charAt(o))<<18|n(e.charAt(o+1))<<12|n(e.charAt(o+2))<<6|n(e.charAt(o+3)),f((16711680&l)>>16),f((65280&l)>>8),f(255&l);return 2===s?(l=n(e.charAt(o))<<2|n(e.charAt(o+1))>>4,f(255&l)):1===s&&(l=n(e.charAt(o))<<10|n(e.charAt(o+1))<<4|n(e.charAt(o+2))>>2,f(l>>8&255),f(255&l)),u}function d(e){function n(e){return o.charAt(e)}function f(e){return n(e>>18&63)+n(e>>12&63)+n(e>>6&63)+n(63&e)}var d,i,t,l=e.length%3,s="";for(d=0,t=e.length-l;t>d;d+=3)i=(e[d]<<16)+(e[d+1]<<8)+e[d+2],s+=f(i);switch(l){case 1:i=e[e.length-1],s+=n(i>>2),s+=n(i<<4&63),s+="==";break;case 2:i=(e[e.length-2]<<8)+e[e.length-1],s+=n(i>>10),s+=n(i>>4&63),s+=n(i<<2&63),s+="="}return s}var i="undefined"!=typeof Uint8Array?Uint8Array:Array,t="+".charCodeAt(0),l="/".charCodeAt(0),s="0".charCodeAt(0),u="a".charCodeAt(0),a="A".charCodeAt(0),p="-".charCodeAt(0),c="_".charCodeAt(0);e.toByteArray=f,e.fromByteArray=d}("undefined"==typeof f?this.base64js={}:f)},{}],4:[function(e,n,f){f.read=function(e,n,f,o,d){var i,t,l=8*d-o-1,s=(1<<l)-1,u=s>>1,a=-7,p=f?d-1:0,c=f?-1:1,y=e[n+p];for(p+=c,i=y&(1<<-a)-1,y>>=-a,a+=l;a>0;i=256*i+e[n+p],p+=c,a-=8);for(t=i&(1<<-a)-1,i>>=-a,a+=o;a>0;t=256*t+e[n+p],p+=c,a-=8);if(0===i)i=1-u;else{if(i===s)return t?0/0:1/0*(y?-1:1);t+=Math.pow(2,o),i-=u}return(y?-1:1)*t*Math.pow(2,i-o)},f.write=function(e,n,f,o,d,i){var t,l,s,u=8*i-d-1,a=(1<<u)-1,p=a>>1,c=23===d?Math.pow(2,-24)-Math.pow(2,-77):0,y=o?0:i-1,m=o?1:-1,r=0>n||0===n&&0>1/n?1:0;for(n=Math.abs(n),isNaN(n)||1/0===n?(l=isNaN(n)?1:0,t=a):(t=Math.floor(Math.log(n)/Math.LN2),n*(s=Math.pow(2,-t))<1&&(t--,s*=2),n+=t+p>=1?c/s:c*Math.pow(2,1-p),n*s>=2&&(t++,s/=2),t+p>=a?(l=0,t=a):t+p>=1?(l=(n*s-1)*Math.pow(2,d),t+=p):(l=n*Math.pow(2,p-1)*Math.pow(2,d),t=0));d>=8;e[f+y]=255&l,y+=m,l/=256,d-=8);for(t=t<<d|l,u+=d;u>0;e[f+y]=255&t,y+=m,t/=256,u-=8);e[f+y-m]|=128*r}},{}],5:[function(e,n){var f=Array.isArray,o=Object.prototype.toString;n.exports=f||function(e){return!!e&&"[object Array]"==o.call(e)}},{}],6:[function(_dereq_,module,exports){(function(Buffer){var fabric=fabric||{version:"1.4.11"};"undefined"!=typeof exports&&(exports.fabric=fabric),"undefined"!=typeof document&&"undefined"!=typeof window?(fabric.document=document,fabric.window=window):(fabric.document=_dereq_("jsdom").jsdom("<!DOCTYPE html><html><head></head><body></body></html>"),fabric.window=fabric.document.createWindow()),fabric.isTouchSupported="ontouchstart"in fabric.document.documentElement,fabric.isLikelyNode="undefined"!=typeof Buffer&&"undefined"==typeof window,fabric.SHARED_ATTRIBUTES=["display","transform","fill","fill-opacity","fill-rule","opacity","stroke","stroke-dasharray","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width"],fabric.DPI=96;var Cufon=function(){function e(e){var n=this.face=e.face;this.glyphs=e.glyphs,this.w=e.w,this.baseSize=parseInt(n["units-per-em"],10),this.family=n["font-family"].toLowerCase(),this.weight=n["font-weight"],this.style=n["font-style"]||"normal",this.viewBox=function(){var e=n.bbox.split(/\s+/),f={minX:parseInt(e[0],10),minY:parseInt(e[1],10),maxX:parseInt(e[2],10),maxY:parseInt(e[3],10)};return f.width=f.maxX-f.minX,f.height=f.maxY-f.minY,f.toString=function(){return[this.minX,this.minY,this.width,this.height].join(" ")},f}(),this.ascent=-parseInt(n.ascent,10),this.descent=-parseInt(n.descent,10),this.height=-this.ascent+this.descent}function n(){var e={},n={oblique:"italic",italic:"oblique"};this.add=function(n){(e[n.style]||(e[n.style]={}))[n.weight]=n},this.get=function(f,o){var d=e[f]||e[n[f]]||e.normal||e.italic||e.oblique;if(!d)return null;if(o={normal:400,bold:700}[o]||parseInt(o,10),d[o])return d[o];var i,t,l={1:1,99:0}[o%100],s=[];void 0===l&&(l=o>400),500==o&&(o=400);for(var u in d)u=parseInt(u,10),(!i||i>u)&&(i=u),(!t||u>t)&&(t=u),s.push(u);return i>o&&(o=i),o>t&&(o=t),s.sort(function(e,n){return(l?e>o&&n>o?n>e:e>n:o>e&&o>n?e>n:n>e)?-1:1}),d[s[0]]}}function f(){function e(e,n){return e.contains?e.contains(n):16&e.compareDocumentPosition(n)}function n(n){var f=n.relatedTarget;f&&!e(this,f)&&o(this)}function f(){o(this)}function o(e){setTimeout(function(){y.replace(e,w.get(e).options,!0)},10)}this.attach=function(e){void 0===e.onmouseenter?(i(e,"mouseover",n),i(e,"mouseout",n)):(i(e,"mouseenter",f),i(e,"mouseleave",f))}}function o(){function e(e){return e.cufid||(e.cufid=++f)}var n={},f=0;this.get=function(f){var o=e(f);return n[o]||(n[o]={})}}function d(e){var n={},f={};this.get=function(f){return void 0!=n[f]?n[f]:e[f]},this.getSize=function(e,n){return f[e]||(f[e]=new r.Size(this.get(e),n))},this.extend=function(e){for(var f in e)n[f]=e[f];return this}}function i(e,n,f){e.addEventListener?e.addEventListener(n,f,!1):e.attachEvent&&e.attachEvent("on"+n,function(){return f.call(e,fabric.window.event)})}function t(e,n){var f=w.get(e);return f.options?e:(n.hover&&n.hoverables[e.nodeName.toLowerCase()]&&b.attach(e),f.options=n,e)}function l(e){var n={};return function(f){return n.hasOwnProperty(f)||(n[f]=e.apply(null,arguments)),n[f]}}function s(e,n){n||(n=r.getStyle(e));for(var f,o=r.quotedList(n.get("fontFamily").toLowerCase()),d=0,i=o.length;i>d;++d)if(f=o[d],x[f])return x[f].get(n.get("fontStyle"),n.get("fontWeight"));return null}function u(e){return fabric.document.getElementsByTagName(e)}function a(){for(var e,n={},f=0,o=arguments.length;o>f;++f)for(e in arguments[f])n[e]=arguments[f][e];return n}function p(e,n,f,o,d,i){var t=o.separate;if("none"==t)return h[o.engine].apply(null,arguments);var l,s=fabric.document.createDocumentFragment(),u=n.split(k[t]),a="words"==t;a&&v&&(/^\s/.test(n)&&u.unshift(""),/\s$/.test(n)&&u.push(""));for(var p=0,c=u.length;c>p;++p)l=h[o.engine](e,a?r.textAlign(u[p],f,p,c):u[p],f,o,d,i,c-1>p),l&&s.appendChild(l);return s}function c(e,n){for(var f,o,d,i,l=t(e,n).firstChild;l;l=d){if(d=l.nextSibling,i=!1,1==l.nodeType){if(!l.firstChild)continue;if(!/cufon/.test(l.className)){arguments.callee(l,n);continue}i=!0}if(o||(o=r.getStyle(e).extend(n)),f||(f=s(e,o)),f)if(i)h[n.engine](f,null,o,n,l,e);else{var u=l.data;if("undefined"!=typeof G_vmlCanvasManager&&(u=u.replace(/\r/g,"\n")),""!==u){var a=p(f,u,o,n,l,e);a?l.parentNode.replaceChild(a,l):l.parentNode.removeChild(l)}}}}var y=function(){return y.replace.apply(null,arguments)},m=y.DOM={ready:function(){var e=!1,n={loaded:1,complete:1},f=[],o=function(){if(!e){e=!0;for(var n;n=f.shift();n());}};return fabric.document.addEventListener&&(fabric.document.addEventListener("DOMContentLoaded",o,!1),fabric.window.addEventListener("pageshow",o,!1)),!fabric.window.opera&&fabric.document.readyState&&function(){n[fabric.document.readyState]?o():setTimeout(arguments.callee,10)}(),fabric.document.readyState&&fabric.document.createStyleSheet&&function(){try{fabric.document.body.doScroll("left"),o()}catch(e){setTimeout(arguments.callee,1)}}(),i(fabric.window,"load",o),function(n){arguments.length?e?n():f.push(n):o()}}()},r=y.CSS={Size:function(e,n){this.value=parseFloat(e),this.unit=String(e).match(/[a-z%]*$/)[0]||"px",this.convert=function(e){return e/n*this.value},this.convertFrom=function(e){return e/this.value*n},this.toString=function(){return this.value+this.unit}},getStyle:function(e){return new d(e.style)},quotedList:l(function(e){for(var n,f=[],o=/\s*((["'])([\s\S]*?[^\\])\2|[^,]+)\s*/g;n=o.exec(e);)f.push(n[3]||n[1]);return f}),ready:function(){var e=!1,n=[],f=function(){e=!0;for(var f;f=n.shift();f());},o=Object.prototype.propertyIsEnumerable?u("style"):{length:0},d=u("link");return m.ready(function(){for(var e,n=0,i=0,t=d.length;e=d[i],t>i;++i)e.disabled||"stylesheet"!=e.rel.toLowerCase()||++n;fabric.document.styleSheets.length>=o.length+n?f():setTimeout(arguments.callee,10)}),function(f){e?f():n.push(f)}}(),supports:function(e,n){var f=fabric.document.createElement("span").style;return void 0===f[e]?!1:(f[e]=n,f[e]===n)},textAlign:function(e,n,f,o){return"right"==n.get("textAlign")?f>0&&(e=" "+e):o-1>f&&(e+=" "),e},textDecoration:function(e,n){n||(n=this.getStyle(e));for(var f={underline:null,overline:null,"line-through":null},o=e;o.parentNode&&1==o.parentNode.nodeType;){var d=!0;for(var i in f)f[i]||(-1!=n.get("textDecoration").indexOf(i)&&(f[i]=n.get("color")),d=!1);if(d)break;n=this.getStyle(o=o.parentNode)}return f},textShadow:l(function(e){if("none"==e)return null;for(var n,f=[],o={},d=0,i=/(#[a-f0-9]+|[a-z]+\(.*?\)|[a-z]+)|(-?[\d.]+[a-z%]*)|,/gi;n=i.exec(e);)","==n[0]?(f.push(o),o={},d=0):n[1]?o.color=n[1]:o[["offX","offY","blur"][d++]]=n[2];return f.push(o),f}),color:l(function(e){var n={};return n.color=e.replace(/^rgba\((.*?),\s*([\d.]+)\)/,function(e,f,o){return n.opacity=parseFloat(o),"rgb("+f+")"}),n}),textTransform:function(e,n){return e[{uppercase:"toUpperCase",lowercase:"toLowerCase"}[n.get("textTransform")]||"toString"]()}},v=0==" ".split(/\s+/).length,w=new o,b=new f,g=[],h={},x={},j={engine:null,hover:!1,hoverables:{a:!0},printable:!0,selector:fabric.window.Sizzle||fabric.window.jQuery&&function(e){return jQuery(e)}||fabric.window.dojo&&dojo.query||fabric.window.$$&&function(e){return $$(e)}||fabric.window.$&&function(e){return $(e)}||fabric.document.querySelectorAll&&function(e){return fabric.document.querySelectorAll(e)}||u,separate:"words",textShadow:"none"},k={words:/\s+/,characters:""};return y.now=function(){return m.ready(),y},y.refresh=function(){for(var e=g.splice(0,g.length),n=0,f=e.length;f>n;++n)y.replace.apply(null,e[n]);return y},y.registerEngine=function(e,n){return n?(h[e]=n,y.set("engine",e)):y},y.registerFont=function(f){var o=new e(f),d=o.family;return x[d]||(x[d]=new n),x[d].add(o),y.set("fontFamily",'"'+d+'"')},y.replace=function(e,n,f){return n=a(j,n),n.engine?("string"==typeof n.textShadow&&n.textShadow&&(n.textShadow=r.textShadow(n.textShadow)),f||g.push(arguments),(e.nodeType||"string"==typeof e)&&(e=[e]),r.ready(function(){for(var f=0,o=e.length;o>f;++f){var d=e[f];"string"==typeof d?y.replace(n.selector(d),n,!0):c(d,n)}}),y):y},y.replaceElement=function(e,n){return n=a(j,n),"string"==typeof n.textShadow&&n.textShadow&&(n.textShadow=r.textShadow(n.textShadow)),c(e,n)},y.engines=h,y.fonts=x,y.getOptions=function(){return a(j)},y.set=function(e,n){return j[e]=n,y},y}();Cufon.registerEngine("canvas",function(){function e(e,n){var f,o=0,d=0,i=[],t=/([mrvxe])([^a-z]*)/g;e:for(var l=0;f=t.exec(e);++l){var s=f[2].split(",");switch(f[1]){case"v":i[l]={m:"bezierCurveTo",a:[o+~~s[0],d+~~s[1],o+~~s[2],d+~~s[3],o+=~~s[4],d+=~~s[5]]};break;case"r":i[l]={m:"lineTo",a:[o+=~~s[0],d+=~~s[1]]};break;case"m":i[l]={m:"moveTo",a:[o=~~s[0],d=~~s[1]]};break;case"x":i[l]={m:"closePath",a:[]};break;case"e":break e}n[i[l].m].apply(n,i[l].a)}return i}function n(e,n){for(var f=0,o=e.length;o>f;++f){var d=e[f];n[d.m].apply(n,d.a)}}var f=Cufon.CSS.supports("display","inline-block"),o=!f&&("BackCompat"==fabric.document.compatMode||/frameset|transitional/i.test(fabric.document.doctype.publicId)),d=fabric.document.createElement("style");d.type="text/css";var i=fabric.document.createTextNode(".cufon-canvas{text-indent:0}@media screen,projection{.cufon-canvas{display:inline;display:inline-block;position:relative;vertical-align:middle"+(o?"":";font-size:1px;line-height:1px")+"}.cufon-canvas .cufon-alt{display:-moz-inline-box;display:inline-block;width:0;height:0;overflow:hidden}"+(f?".cufon-canvas canvas{position:relative}":".cufon-canvas canvas{position:absolute}")+"}@media print{.cufon-canvas{padding:0 !important}.cufon-canvas canvas{display:none}.cufon-canvas .cufon-alt{display:inline}}");try{d.appendChild(i)}catch(t){d.setAttribute("type","text/css"),d.styleSheet.cssText=i.data}return fabric.document.getElementsByTagName("head")[0].appendChild(d),function(o,d,i,t,l){function s(){T.save();var e=0,n=0,f=[{left:0}];t.backgroundColor&&(T.save(),T.fillStyle=t.backgroundColor,T.translate(0,o.ascent),T.fillRect(0,0,A+10,(-o.ascent+o.descent)*D),T.restore()),"right"===t.textAlign?(T.translate(G[n],0),f[0].left=G[n]*U):"center"===t.textAlign&&(T.translate(G[n]/2,0),f[0].left=G[n]/2*U);for(var d=0,i=z.length;i>d;++d)if("\n"!==z[d]){var l=o.glyphs[z[d]]||o.missingGlyph;if(l){var s=Number(l.w||o.w)+y;t.textBackgroundColor&&(T.save(),T.fillStyle=t.textBackgroundColor,T.translate(0,o.ascent),T.fillRect(0,0,s+10,-o.ascent+o.descent),T.restore()),T.translate(s,0),e+=s,d==i-1&&(f[f.length-1].width=e*U,f[f.length-1].height=(-o.ascent+o.descent)*U)}}else{n++;var u=-o.ascent-o.ascent/5*t.lineHeight,a=f[f.length-1],p={left:0};a.width=e*U,a.height=(-o.ascent+o.descent)*U,"right"===t.textAlign?(T.translate(-A,u),T.translate(G[n],0),p.left=G[n]*U):"center"===t.textAlign?(T.translate(-e-G[n-1]/2,u),T.translate(G[n]/2,0),p.left=G[n]/2*U):T.translate(-e,u),f.push(p),e=0}T.restore(),Cufon.textOptions.boundaries=f}function u(f){T.fillStyle=f||Cufon.textOptions.color||i.get("color");var d=0,l=0;"right"===t.textAlign?T.translate(G[l],0):"center"===t.textAlign&&T.translate(G[l]/2,0);for(var s=0,u=z.length;u>s;++s)if("\n"!==z[s]){var a=o.glyphs[z[s]]||o.missingGlyph;if(a){var p=Number(a.w||o.w)+y;W&&(T.save(),T.strokeStyle=T.fillStyle,T.lineWidth+=T.lineWidth,T.beginPath(),W.underline&&(T.moveTo(0,-o.face["underline-position"]+.5),T.lineTo(p,-o.face["underline-position"]+.5)),W.overline&&(T.moveTo(0,o.ascent+.5),T.lineTo(p,o.ascent+.5)),W["line-through"]&&(T.moveTo(0,-o.descent+.5),T.lineTo(p,-o.descent+.5)),T.stroke(),T.restore()),X&&(T.save(),T.transform(1,0,-.25,1,0,0)),T.beginPath(),a.d&&(a.code?n(a.code,T):a.code=e("m"+a.d,T)),T.fill(),t.strokeStyle&&(T.closePath(),T.save(),T.lineWidth=t.strokeWidth,T.strokeStyle=t.strokeStyle,T.stroke(),T.restore()),X&&T.restore(),T.translate(p,0),d+=p}}else{l++;var c=-o.ascent-o.ascent/5*t.lineHeight;"right"===t.textAlign?(T.translate(-A,c),T.translate(G[l],0)):"center"===t.textAlign?(T.translate(-d-G[l-1]/2,c),T.translate(G[l]/2,0)):T.translate(-d,c),d=0}}var a=null===d,p=o.viewBox,c=i.getSize("fontSize",o.baseSize),y=i.get("letterSpacing");y="normal"==y?0:c.convertFrom(parseInt(y,10));var m=0,r=0,v=0,w=0,b=t.textShadow,g=[];if(Cufon.textOptions.shadowOffsets=[],Cufon.textOptions.shadows=null,b){Cufon.textOptions.shadows=b;for(var h=0,x=b.length;x>h;++h){var j=b[h],k=c.convertFrom(parseFloat(j.offX)),q=c.convertFrom(parseFloat(j.offY));g[h]=[k,q]}}for(var z=Cufon.CSS.textTransform(a?l.alt:d,i).split(""),A=0,B=null,C=0,D=1,E=[],h=0,x=z.length;x>h;++h)if("\n"!==z[h]){var F=o.glyphs[z[h]]||o.missingGlyph;F&&(A+=B=Number(F.w||o.w)+y)}else D++,A>C&&(C=A),E.push(A),A=0;E.push(A),A=Math.max(C,A);for(var G=[],h=E.length;h--;)G[h]=A-E[h];if(null===B)return null;r+=p.width-B,w+=p.minX;var H,I;if(a)H=l,I=l.firstChild;else if(H=fabric.document.createElement("span"),H.className="cufon cufon-canvas",H.alt=d,I=fabric.document.createElement("canvas"),H.appendChild(I),t.printable){var J=fabric.document.createElement("span");
J.className="cufon-alt",J.appendChild(fabric.document.createTextNode(d)),H.appendChild(J)}var K=H.style,L=I.style||{},M=c.convert(p.height-m+v),N=Math.ceil(M),O=N/M;I.width=Math.ceil(c.convert(A+r-w)*O),I.height=N,m+=p.minY,L.top=Math.round(c.convert(m-o.ascent))+"px",L.left=Math.round(c.convert(w))+"px";var P=Math.ceil(c.convert(A*O)),Q=P+"px",R=c.convert(o.height),S=(t.lineHeight-1)*c.convert(-o.ascent/5)*(D-1);Cufon.textOptions.width=P,Cufon.textOptions.height=R*D+S,Cufon.textOptions.lines=D,Cufon.textOptions.totalLineHeight=S,f?(K.width=Q,K.height=R+"px"):(K.paddingLeft=Q,K.paddingBottom=R-1+"px");var T=Cufon.textOptions.context||I.getContext("2d"),U=N/p.height;Cufon.textOptions.fontAscent=o.ascent*U,Cufon.textOptions.boundaries=null;for(var V=Cufon.textOptions.shadowOffsets,h=g.length;h--;)V[h]=[g[h][0]*U,g[h][1]*U];T.save(),T.scale(U,U),T.translate(-w-1/U*I.width/2+(Cufon.fonts[o.family].offsetLeft||0),-m-Cufon.textOptions.height/U/2+(Cufon.fonts[o.family].offsetTop||0)),T.lineWidth=o.face["underline-thickness"],T.save();var W=Cufon.getTextDecoration(t),X="italic"===t.fontStyle;if(T.save(),s(),b)for(var h=0,x=b.length;x>h;++h){var j=b[h];T.save(),T.translate.apply(T,g[h]),u(j.color),T.restore()}return u(),T.restore(),T.restore(),T.restore(),H}}()),Cufon.registerEngine("vml",function(){function e(e,f){return n(e,/(?:em|ex|%)$/i.test(f)?"1em":f)}function n(e,n){if(/px$/i.test(n))return parseFloat(n);var f=e.style.left,o=e.runtimeStyle.left;e.runtimeStyle.left=e.currentStyle.left,e.style.left=n;var d=e.style.pixelLeft;return e.style.left=f,e.runtimeStyle.left=o,d}if(fabric.document.namespaces){var f=fabric.document.createElement("canvas");if(!(f&&f.getContext&&f.getContext.apply)){null==fabric.document.namespaces.cvml&&fabric.document.namespaces.add("cvml","urn:schemas-microsoft-com:vml");var o=fabric.document.createElement("cvml:shape");if(o.style.behavior="url(#default#VML)",o.coordsize)return o=null,fabric.document.write('<style type="text/css">.cufon-vml-canvas{text-indent:0}@media screen{cvml\\:shape,cvml\\:shadow{behavior:url(#default#VML);display:block;antialias:true;position:absolute}.cufon-vml-canvas{position:absolute;text-align:left}.cufon-vml{display:inline-block;position:relative;vertical-align:middle}.cufon-vml .cufon-alt{position:absolute;left:-10000in;font-size:1px}a .cufon-vml{cursor:pointer}}@media print{.cufon-vml *{display:none}.cufon-vml .cufon-alt{display:inline}}</style>'),function(f,o,d,i,t,l,s){var u=null===o;u&&(o=t.alt);var a=f.viewBox,p=d.computedFontSize||(d.computedFontSize=new Cufon.CSS.Size(e(l,d.get("fontSize"))+"px",f.baseSize)),c=d.computedLSpacing;void 0==c&&(c=d.get("letterSpacing"),d.computedLSpacing=c="normal"==c?0:~~p.convertFrom(n(l,c)));var y,m;if(u)y=t,m=t.firstChild;else{if(y=fabric.document.createElement("span"),y.className="cufon cufon-vml",y.alt=o,m=fabric.document.createElement("span"),m.className="cufon-vml-canvas",y.appendChild(m),i.printable){var r=fabric.document.createElement("span");r.className="cufon-alt",r.appendChild(fabric.document.createTextNode(o)),y.appendChild(r)}s||y.appendChild(fabric.document.createElement("cvml:shape"))}var v=y.style,w=m.style,b=p.convert(a.height),g=Math.ceil(b),h=g/b,x=a.minX,j=a.minY;w.height=g,w.top=Math.round(p.convert(j-f.ascent)),w.left=Math.round(p.convert(x)),v.height=p.convert(f.height)+"px";for(var k,q,z=(Cufon.getTextDecoration(i),d.get("color")),A=Cufon.CSS.textTransform(o,d).split(""),B=0,C=0,D=null,E=i.textShadow,F=0,G=0,H=A.length;H>F;++F)k=f.glyphs[A[F]]||f.missingGlyph,k&&(B+=D=~~(k.w||f.w)+c);if(null===D)return null;var I,J=-x+B+(a.width-D),K=p.convert(J*h),L=Math.round(K),M=J+","+a.height,N="r"+M+"nsnf";for(F=0;H>F;++F)if(k=f.glyphs[A[F]]||f.missingGlyph){u?(q=m.childNodes[G],q.firstChild&&q.removeChild(q.firstChild)):(q=fabric.document.createElement("cvml:shape"),m.appendChild(q)),q.stroked="f",q.coordsize=M,q.coordorigin=I=x-C+","+j,q.path=(k.d?"m"+k.d+"xe":"")+"m"+I+N,q.fillcolor=z;var O=q.style;if(O.width=L,O.height=g,E){var P,Q=E[0],R=E[1],S=Cufon.CSS.color(Q.color),T=fabric.document.createElement("cvml:shadow");T.on="t",T.color=S.color,T.offset=Q.offX+","+Q.offY,R&&(P=Cufon.CSS.color(R.color),T.type="double",T.color2=P.color,T.offset2=R.offX+","+R.offY),T.opacity=S.opacity||P&&P.opacity||1,q.appendChild(T)}C+=~~(k.w||f.w)+c,++G}return v.width=Math.max(Math.ceil(p.convert(B*h)),0),y}}}}()),Cufon.getTextDecoration=function(e){return{underline:"underline"===e.textDecoration,overline:"overline"===e.textDecoration,"line-through":"line-through"===e.textDecoration}},"undefined"!=typeof exports&&(exports.Cufon=Cufon),"object"!=typeof JSON&&(JSON={}),function(){"use strict";function f(e){return 10>e?"0"+e:e}function quote(e){return escapable.lastIndex=0,escapable.test(e)?'"'+e.replace(escapable,function(e){var n=meta[e];return"string"==typeof n?n:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+e+'"'}function str(e,n){var f,o,d,i,t,l=gap,s=n[e];switch(s&&"object"==typeof s&&"function"==typeof s.toJSON&&(s=s.toJSON(e)),"function"==typeof rep&&(s=rep.call(n,e,s)),typeof s){case"string":return quote(s);case"number":return isFinite(s)?String(s):"null";case"boolean":case"null":return String(s);case"object":if(!s)return"null";if(gap+=indent,t=[],"[object Array]"===Object.prototype.toString.apply(s)){for(i=s.length,f=0;i>f;f+=1)t[f]=str(f,s)||"null";return d=0===t.length?"[]":gap?"[\n"+gap+t.join(",\n"+gap)+"\n"+l+"]":"["+t.join(",")+"]",gap=l,d}if(rep&&"object"==typeof rep)for(i=rep.length,f=0;i>f;f+=1)"string"==typeof rep[f]&&(o=rep[f],d=str(o,s),d&&t.push(quote(o)+(gap?": ":":")+d));else for(o in s)Object.prototype.hasOwnProperty.call(s,o)&&(d=str(o,s),d&&t.push(quote(o)+(gap?": ":":")+d));return d=0===t.length?"{}":gap?"{\n"+gap+t.join(",\n"+gap)+"\n"+l+"}":"{"+t.join(",")+"}",gap=l,d}}"function"!=typeof Date.prototype.toJSON&&(Date.prototype.toJSON=function(){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+f(this.getUTCMonth()+1)+"-"+f(this.getUTCDate())+"T"+f(this.getUTCHours())+":"+f(this.getUTCMinutes())+":"+f(this.getUTCSeconds())+"Z":null},String.prototype.toJSON=Number.prototype.toJSON=Boolean.prototype.toJSON=function(){return this.valueOf()});var cx,escapable,gap,indent,meta,rep;"function"!=typeof JSON.stringify&&(escapable=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,meta={"\b":"\\b","	":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},JSON.stringify=function(e,n,f){var o;if(gap="",indent="","number"==typeof f)for(o=0;f>o;o+=1)indent+=" ";else"string"==typeof f&&(indent=f);if(rep=n,n&&"function"!=typeof n&&("object"!=typeof n||"number"!=typeof n.length))throw new Error("JSON.stringify");return str("",{"":e})}),"function"!=typeof JSON.parse&&(cx=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,JSON.parse=function(text,reviver){function walk(e,n){var f,o,d=e[n];if(d&&"object"==typeof d)for(f in d)Object.prototype.hasOwnProperty.call(d,f)&&(o=walk(d,f),void 0!==o?d[f]=o:delete d[f]);return reviver.call(e,n,d)}var j;if(text=String(text),cx.lastIndex=0,cx.test(text)&&(text=text.replace(cx,function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})),/^[\],:{}\s]*$/.test(text.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"")))return j=eval("("+text+")"),"function"==typeof reviver?walk({"":j},""):j;throw new SyntaxError("JSON.parse")})}(),function(){function e(e,n){this.__eventListeners[e]&&(n?fabric.util.removeFromArray(this.__eventListeners[e],n):this.__eventListeners[e].length=0)}function n(e,n){if(this.__eventListeners||(this.__eventListeners={}),1===arguments.length)for(var f in e)this.on(f,e[f]);else this.__eventListeners[e]||(this.__eventListeners[e]=[]),this.__eventListeners[e].push(n);return this}function f(n,f){if(this.__eventListeners){if(0===arguments.length)this.__eventListeners={};else if(1===arguments.length&&"object"==typeof arguments[0])for(var o in n)e.call(this,o,n[o]);else e.call(this,n,f);return this}}function o(e,n){if(this.__eventListeners){var f=this.__eventListeners[e];if(f){for(var o=0,d=f.length;d>o;o++)f[o].call(this,n||{});return this}}}fabric.Observable={observe:n,stopObserving:f,fire:o,on:n,off:f,trigger:o}}(),fabric.Collection={add:function(){this._objects.push.apply(this._objects,arguments);for(var e=0,n=arguments.length;n>e;e++)this._onObjectAdded(arguments[e]);return this.renderOnAddRemove&&this.renderAll(),this},insertAt:function(e,n,f){var o=this.getObjects();return f?o[n]=e:o.splice(n,0,e),this._onObjectAdded(e),this.renderOnAddRemove&&this.renderAll(),this},remove:function(){for(var e,n=this.getObjects(),f=0,o=arguments.length;o>f;f++)e=n.indexOf(arguments[f]),-1!==e&&(n.splice(e,1),this._onObjectRemoved(arguments[f]));return this.renderOnAddRemove&&this.renderAll(),this},forEachObject:function(e,n){for(var f=this.getObjects(),o=f.length;o--;)e.call(n,f[o],o,f);return this},getObjects:function(e){return"undefined"==typeof e?this._objects:this._objects.filter(function(n){return n.type===e})},item:function(e){return this.getObjects()[e]},isEmpty:function(){return 0===this.getObjects().length},size:function(){return this.getObjects().length},contains:function(e){return this.getObjects().indexOf(e)>-1},complexity:function(){return this.getObjects().reduce(function(e,n){return e+=n.complexity?n.complexity():0},0)}},function(e){var n=Math.sqrt,f=Math.atan2,o=Math.PI/180;fabric.util={removeFromArray:function(e,n){var f=e.indexOf(n);return-1!==f&&e.splice(f,1),e},getRandomInt:function(e,n){return Math.floor(Math.random()*(n-e+1))+e},degreesToRadians:function(e){return e*o},radiansToDegrees:function(e){return e/o},rotatePoint:function(e,n,f){var o=Math.sin(f),d=Math.cos(f);e.subtractEquals(n);var i=e.x*d-e.y*o,t=e.x*o+e.y*d;return new fabric.Point(i,t).addEquals(n)},transformPoint:function(e,n,f){return f?new fabric.Point(n[0]*e.x+n[1]*e.y,n[2]*e.x+n[3]*e.y):new fabric.Point(n[0]*e.x+n[1]*e.y+n[4],n[2]*e.x+n[3]*e.y+n[5])},invertTransform:function(e){var n=e.slice(),f=1/(e[0]*e[3]-e[1]*e[2]);n=[f*e[3],-f*e[1],-f*e[2],f*e[0],0,0];var o=fabric.util.transformPoint({x:e[4],y:e[5]},n);return n[4]=-o.x,n[5]=-o.y,n},toFixed:function(e,n){return parseFloat(Number(e).toFixed(n))},parseUnit:function(e){var n=/\D{0,2}$/.exec(e),f=parseFloat(e);switch(n[0]){case"mm":return f*fabric.DPI/25.4;case"cm":return f*fabric.DPI/2.54;case"in":return f*fabric.DPI;case"pt":return f*fabric.DPI/72;case"pc":return f*fabric.DPI/72*12;default:return f}},falseFunction:function(){return!1},getKlass:function(e,n){return e=fabric.util.string.camelize(e.charAt(0).toUpperCase()+e.slice(1)),fabric.util.resolveNamespace(n)[e]},resolveNamespace:function(n){if(!n)return fabric;for(var f=n.split("."),o=f.length,d=e||fabric.window,i=0;o>i;++i)d=d[f[i]];return d},loadImage:function(e,n,f,o){if(!e)return void(n&&n.call(f,e));var d=fabric.util.createImage();d.onload=function(){n&&n.call(f,d),d=d.onload=d.onerror=null},d.onerror=function(){fabric.log("Error loading "+d.src),n&&n.call(f,null,!0),d=d.onload=d.onerror=null},0!==e.indexOf("data")&&"undefined"!=typeof o&&(d.crossOrigin=o),d.src=e},enlivenObjects:function(e,n,f,o){function d(){++t===l&&n&&n(i)}e=e||[];var i=[],t=0,l=e.length;return l?void e.forEach(function(e,n){if(!e||!e.type)return void d();var t=fabric.util.getKlass(e.type,f);t.async?t.fromObject(e,function(f,t){t||(i[n]=f,o&&o(e,i[n])),d()}):(i[n]=t.fromObject(e),o&&o(e,i[n]),d())}):void(n&&n(i))},groupSVGElements:function(e,n,f){var o;return o=new fabric.PathGroup(e,n),"undefined"!=typeof f&&o.setSourcePath(f),o},populateWithProperties:function(e,n,f){if(f&&"[object Array]"===Object.prototype.toString.call(f))for(var o=0,d=f.length;d>o;o++)f[o]in e&&(n[f[o]]=e[f[o]])},drawDashedLine:function(e,o,d,i,t,l){var s=i-o,u=t-d,a=n(s*s+u*u),p=f(u,s),c=l.length,y=0,m=!0;for(e.save(),e.translate(o,d),e.moveTo(0,0),e.rotate(p),o=0;a>o;)o+=l[y++%c],o>a&&(o=a),e[m?"lineTo":"moveTo"](o,0),m=!m;e.restore()},createCanvasElement:function(e){return e||(e=fabric.document.createElement("canvas")),e.getContext||"undefined"==typeof G_vmlCanvasManager||G_vmlCanvasManager.initElement(e),e},createImage:function(){return fabric.isLikelyNode?new(_dereq_("canvas").Image):fabric.document.createElement("img")},createAccessors:function(e){for(var n=e.prototype,f=n.stateProperties.length;f--;){var o=n.stateProperties[f],d=o.charAt(0).toUpperCase()+o.slice(1),i="set"+d,t="get"+d;n[t]||(n[t]=function(e){return new Function('return this.get("'+e+'")')}(o)),n[i]||(n[i]=function(e){return new Function("value",'return this.set("'+e+'", value)')}(o))}},clipContext:function(e,n){n.save(),n.beginPath(),e.clipTo(n),n.clip()},multiplyTransformMatrices:function(e,n){for(var f=[[e[0],e[2],e[4]],[e[1],e[3],e[5]],[0,0,1]],o=[[n[0],n[2],n[4]],[n[1],n[3],n[5]],[0,0,1]],d=[],i=0;3>i;i++){d[i]=[];for(var t=0;3>t;t++){for(var l=0,s=0;3>s;s++)l+=f[i][s]*o[s][t];d[i][t]=l}}return[d[0][0],d[1][0],d[0][1],d[1][1],d[0][2],d[1][2]]},getFunctionBody:function(e){return(String(e).match(/function[^{]*\{([\s\S]*)\}/)||{})[1]},isTransparent:function(e,n,f,o){o>0&&(n>o?n-=o:n=0,f>o?f-=o:f=0);for(var d=!0,i=e.getImageData(n,f,2*o||1,2*o||1),t=3,l=i.data.length;l>t;t+=4){var s=i.data[t];if(d=0>=s,d===!1)break}return i=null,d}}}("undefined"!=typeof exports?exports:this),function(){function e(e,d,t,l,s,u,a){var p=i.call(arguments);if(o[p])return o[p];var c=Math.PI,y=a*(c/180),m=Math.sin(y),r=Math.cos(y),v=0,w=0;t=Math.abs(t),l=Math.abs(l);var b=-r*e-m*d,g=-r*d+m*e,h=t*t,x=l*l,j=g*g,k=b*b,q=4*h*x-h*j-x*k,z=0;if(0>q){var A=Math.sqrt(1-.25*q/(h*x));t*=A,l*=A}else z=(s===u?-.5:.5)*Math.sqrt(q/(h*j+x*k));var B=z*t*g/l,C=-z*l*b/t,D=r*B-m*C+e/2,E=m*B+r*C+d/2,F=f(1,0,(b-B)/t,(g-C)/l),G=f((b-B)/t,(g-C)/l,(-b-B)/t,(-g-C)/l);0===u&&G>0?G-=2*c:1===u&&0>G&&(G+=2*c);for(var H=Math.ceil(Math.abs(G/(.5*c))),I=[],J=G/H,K=8/3*Math.sin(J/4)*Math.sin(J/4)/Math.sin(J/2),L=F+J,M=0;H>M;M++)I[M]=n(F,L,r,m,t,l,D,E,K,v,w),v=I[M][4],w=I[M][5],F+=J,L+=J;return o[p]=I,I}function n(e,n,f,o,t,l,s,u,a,p,c){var y=i.call(arguments);if(d[y])return d[y];var m=Math.cos(e),r=Math.sin(e),v=Math.cos(n),w=Math.sin(n),b=f*t*v-o*l*w+s,g=o*t*v+f*l*w+u,h=p+a*(-f*t*r-o*l*m),x=c+a*(-o*t*r+f*l*m),j=b+a*(f*t*w+o*l*v),k=g+a*(o*t*w-f*l*v);return d[y]=[h,x,j,k,b,g],d[y]}function f(e,n,f,o){var d=Math.atan2(n,e),i=Math.atan2(o,f);return i>=d?i-d:2*Math.PI-(d-i)}var o={},d={},i=Array.prototype.join;fabric.util.drawArc=function(n,f,o,d){for(var i=d[0],t=d[1],l=d[2],s=d[3],u=d[4],a=d[5],p=d[6],c=[[],[],[],[]],y=e(a-f,p-o,i,t,s,u,l),m=0,r=y.length;r>m;m++)c[m][0]=y[m][0]+f,c[m][1]=y[m][1]+o,c[m][2]=y[m][2]+f,c[m][3]=y[m][3]+o,c[m][4]=y[m][4]+f,c[m][5]=y[m][5]+o,n.bezierCurveTo.apply(n,c[m])}}(),function(){function e(e,n){for(var f=d.call(arguments,2),o=[],i=0,t=e.length;t>i;i++)o[i]=f.length?e[i][n].apply(e[i],f):e[i][n].call(e[i]);return o}function n(e,n){return o(e,n,function(e,n){return e>=n})}function f(e,n){return o(e,n,function(e,n){return n>e})}function o(e,n,f){if(e&&0!==e.length){var o=e.length-1,d=n?e[o][n]:e[o];if(n)for(;o--;)f(e[o][n],d)&&(d=e[o][n]);else for(;o--;)f(e[o],d)&&(d=e[o]);return d}}var d=Array.prototype.slice;fabric.util.array={invoke:e,min:f,max:n}}(),function(){function e(e,n){for(var f in n)e[f]=n[f];return e}function n(n){return e({},n)}fabric.util.object={extend:e,clone:n}}(),function(){function e(e){return e.replace(/-+(.)?/g,function(e,n){return n?n.toUpperCase():""})}function n(e,n){return e.charAt(0).toUpperCase()+(n?e.slice(1):e.slice(1).toLowerCase())}function f(e){return e.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}fabric.util.string={camelize:e,capitalize:n,escapeXml:f}}(),function(){function e(){}function n(e){var n=this.constructor.superclass.prototype[e];return arguments.length>1?n.apply(this,o.call(arguments,1)):n.call(this)}function f(){function f(){this.initialize.apply(this,arguments)}var i=null,l=o.call(arguments,0);"function"==typeof l[0]&&(i=l.shift()),f.superclass=i,f.subclasses=[],i&&(e.prototype=i.prototype,f.prototype=new e,i.subclasses.push(f));for(var s=0,u=l.length;u>s;s++)t(f,l[s],i);return f.prototype.initialize||(f.prototype.initialize=d),f.prototype.constructor=f,f.prototype.callSuper=n,f}var o=Array.prototype.slice,d=function(){},i=function(){for(var e in{toString:1})if("toString"===e)return!1;return!0}(),t=function(e,n,f){for(var o in n)e.prototype[o]=o in e.prototype&&"function"==typeof e.prototype[o]&&(n[o]+"").indexOf("callSuper")>-1?function(e){return function(){var o=this.constructor.superclass;this.constructor.superclass=f;var d=n[e].apply(this,arguments);return this.constructor.superclass=o,"initialize"!==e?d:void 0}}(o):n[o],i&&(n.toString!==Object.prototype.toString&&(e.prototype.toString=n.toString),n.valueOf!==Object.prototype.valueOf&&(e.prototype.valueOf=n.valueOf))};fabric.util.createClass=f}(),function(){function e(e){var n,f,o=Array.prototype.slice.call(arguments,1),d=o.length;for(f=0;d>f;f++)if(n=typeof e[o[f]],!/^(?:function|object|unknown)$/.test(n))return!1;return!0}function n(e,n){return{handler:n,wrappedHandler:f(e,n)}}function f(e,n){return function(f){n.call(t(e),f||fabric.window.event)}}function o(e,n){return function(f){if(r[e]&&r[e][n])for(var o=r[e][n],d=0,i=o.length;i>d;d++)o[d].call(this,f||fabric.window.event)}}function d(e,n){e||(e=fabric.window.event);var f=e.target||(typeof e.srcElement!==s?e.srcElement:null),o=fabric.util.getScrollLeftTop(f,n);return{x:v(e)+o.left,y:w(e)+o.top}}function i(e,n,f){var o="touchend"===e.type?"changedTouches":"touches";return e[o]&&e[o][0]?e[o][0][n]-(e[o][0][n]-e[o][0][f])||e[f]:e[f]}var t,l,s="unknown",u=function(){var e=0;return function(n){return n.__uniqueID||(n.__uniqueID="uniqueID__"+e++)}}();!function(){var e={};t=function(n){return e[n]},l=function(n,f){e[n]=f}}();var a,p,c=e(fabric.document.documentElement,"addEventListener","removeEventListener")&&e(fabric.window,"addEventListener","removeEventListener"),y=e(fabric.document.documentElement,"attachEvent","detachEvent")&&e(fabric.window,"attachEvent","detachEvent"),m={},r={};c?(a=function(e,n,f){e.addEventListener(n,f,!1)},p=function(e,n,f){e.removeEventListener(n,f,!1)}):y?(a=function(e,f,o){var d=u(e);l(d,e),m[d]||(m[d]={}),m[d][f]||(m[d][f]=[]);var i=n(d,o);m[d][f].push(i),e.attachEvent("on"+f,i.wrappedHandler)},p=function(e,n,f){var o,d=u(e);if(m[d]&&m[d][n])for(var i=0,t=m[d][n].length;t>i;i++)o=m[d][n][i],o&&o.handler===f&&(e.detachEvent("on"+n,o.wrappedHandler),m[d][n][i]=null)}):(a=function(e,n,f){var d=u(e);if(r[d]||(r[d]={}),!r[d][n]){r[d][n]=[];var i=e["on"+n];i&&r[d][n].push(i),e["on"+n]=o(d,n)}r[d][n].push(f)},p=function(e,n,f){var o=u(e);if(r[o]&&r[o][n])for(var d=r[o][n],i=0,t=d.length;t>i;i++)d[i]===f&&d.splice(i,1)}),fabric.util.addListener=a,fabric.util.removeListener=p;var v=function(e){return typeof e.clientX!==s?e.clientX:0},w=function(e){return typeof e.clientY!==s?e.clientY:0};fabric.isTouchSupported&&(v=function(e){return i(e,"pageX","clientX")},w=function(e){return i(e,"pageY","clientY")}),fabric.util.getPointer=d,fabric.util.object.extend(fabric.util,fabric.Observable)}(),function(){function e(e,n){var f=e.style;if(!f)return e;if("string"==typeof n)return e.style.cssText+=";"+n,n.indexOf("opacity")>-1?i(e,n.match(/opacity:\s*(\d?\.?\d*)/)[1]):e;for(var o in n)if("opacity"===o)i(e,n[o]);else{var d="float"===o||"cssFloat"===o?"undefined"==typeof f.styleFloat?"cssFloat":"styleFloat":o;f[d]=n[o]}return e}var n=fabric.document.createElement("div"),f="string"==typeof n.style.opacity,o="string"==typeof n.style.filter,d=/alpha\s*\(\s*opacity\s*=\s*([^\)]+)\)/,i=function(e){return e};f?i=function(e,n){return e.style.opacity=n,e}:o&&(i=function(e,n){var f=e.style;return e.currentStyle&&!e.currentStyle.hasLayout&&(f.zoom=1),d.test(f.filter)?(n=n>=.9999?"":"alpha(opacity="+100*n+")",f.filter=f.filter.replace(d,n)):f.filter+=" alpha(opacity="+100*n+")",e}),fabric.util.setStyle=e}(),function(){function e(e){return"string"==typeof e?fabric.document.getElementById(e):e}function n(e,n){var f=fabric.document.createElement(e);for(var o in n)"class"===o?f.className=n[o]:"for"===o?f.htmlFor=n[o]:f.setAttribute(o,n[o]);return f}function f(e,n){e&&-1===(" "+e.className+" ").indexOf(" "+n+" ")&&(e.className+=(e.className?" ":"")+n)}function o(e,f,o){return"string"==typeof f&&(f=n(f,o)),e.parentNode&&e.parentNode.replaceChild(f,e),f.appendChild(e),f}function d(e,n){var f,o,d=0,i=0,t=fabric.document.documentElement,l=fabric.document.body||{scrollLeft:0,scrollTop:0};for(o=e;e&&e.parentNode&&!f;)e=e.parentNode,e!==fabric.document&&"fixed"===fabric.util.getElementStyle(e,"position")&&(f=e),e!==fabric.document&&o!==n&&"absolute"===fabric.util.getElementStyle(e,"position")?(d=0,i=0):e===fabric.document?(d=l.scrollLeft||t.scrollLeft||0,i=l.scrollTop||t.scrollTop||0):(d+=e.scrollLeft||0,i+=e.scrollTop||0);return{left:d,top:i}}function i(e){var n,f,o=e&&e.ownerDocument,d={left:0,top:0},i={left:0,top:0},t={borderLeftWidth:"left",borderTopWidth:"top",paddingLeft:"left",paddingTop:"top"};if(!o)return{left:0,top:0};for(var l in t)i[t[l]]+=parseInt(a(e,l),10)||0;return n=o.documentElement,"undefined"!=typeof e.getBoundingClientRect&&(d=e.getBoundingClientRect()),f=fabric.util.getScrollLeftTop(e,null),{left:d.left+f.left-(n.clientLeft||0)+i.left,top:d.top+f.top-(n.clientTop||0)+i.top}}var t,l=Array.prototype.slice,s=function(e){return l.call(e,0)};try{t=s(fabric.document.childNodes)instanceof Array}catch(u){}t||(s=function(e){for(var n=new Array(e.length),f=e.length;f--;)n[f]=e[f];return n});var a;a=fabric.document.defaultView&&fabric.document.defaultView.getComputedStyle?function(e,n){return fabric.document.defaultView.getComputedStyle(e,null)[n]}:function(e,n){var f=e.style[n];return!f&&e.currentStyle&&(f=e.currentStyle[n]),f},function(){function e(e){return"undefined"!=typeof e.onselectstart&&(e.onselectstart=fabric.util.falseFunction),o?e.style[o]="none":"string"==typeof e.unselectable&&(e.unselectable="on"),e}function n(e){return"undefined"!=typeof e.onselectstart&&(e.onselectstart=null),o?e.style[o]="":"string"==typeof e.unselectable&&(e.unselectable=""),e}var f=fabric.document.documentElement.style,o="userSelect"in f?"userSelect":"MozUserSelect"in f?"MozUserSelect":"WebkitUserSelect"in f?"WebkitUserSelect":"KhtmlUserSelect"in f?"KhtmlUserSelect":"";fabric.util.makeElementUnselectable=e,fabric.util.makeElementSelectable=n}(),function(){function e(e,n){var f=fabric.document.getElementsByTagName("head")[0],o=fabric.document.createElement("script"),d=!0;o.onload=o.onreadystatechange=function(e){if(d){if("string"==typeof this.readyState&&"loaded"!==this.readyState&&"complete"!==this.readyState)return;d=!1,n(e||fabric.window.event),o=o.onload=o.onreadystatechange=null}},o.src=e,f.appendChild(o)}fabric.util.getScript=e}(),fabric.util.getById=e,fabric.util.toArray=s,fabric.util.makeElement=n,fabric.util.addClass=f,fabric.util.wrapElement=o,fabric.util.getScrollLeftTop=d,fabric.util.getElementOffset=i,fabric.util.getElementStyle=a}(),function(){function e(e,n){return e+(/\?/.test(e)?"&":"?")+n}function n(){}function f(f,d){d||(d={});var i,t=d.method?d.method.toUpperCase():"GET",l=d.onComplete||function(){},s=o();return s.onreadystatechange=function(){4===s.readyState&&(l(s),s.onreadystatechange=n)},"GET"===t&&(i=null,"string"==typeof d.parameters&&(f=e(f,d.parameters))),s.open(t,f,!0),("POST"===t||"PUT"===t)&&s.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),s.send(i),s}var o=function(){for(var e=[function(){return new ActiveXObject("Microsoft.XMLHTTP")},function(){return new ActiveXObject("Msxml2.XMLHTTP")},function(){return new ActiveXObject("Msxml2.XMLHTTP.3.0")},function(){return new XMLHttpRequest}],n=e.length;n--;)try{var f=e[n]();if(f)return e[n]}catch(o){}}();fabric.util.request=f}(),fabric.log=function(){},fabric.warn=function(){},"undefined"!=typeof console&&["log","warn"].forEach(function(e){"undefined"!=typeof console[e]&&console[e].apply&&(fabric[e]=function(){return console[e].apply(console,arguments)})}),function(e){"use strict";function n(e){return e in j?j[e]:e}function f(e,n,f){var o,d="[object Array]"===Object.prototype.toString.call(n);return"fill"!==e&&"stroke"!==e||"none"!==n?"fillRule"===e?n="evenodd"===n?"destination-over":n:"strokeDashArray"===e?n=n.replace(/,/g," ").split(/\s+/).map(function(e){return parseInt(e)}):"transformMatrix"===e?n=f&&f.transformMatrix?x(f.transformMatrix,r.parseTransformAttribute(n)):r.parseTransformAttribute(n):"visible"===e?(n="none"===n||"hidden"===n?!1:!0,f&&f.visible===!1&&(n=!1)):"originX"===e?n="start"===n?"left":"end"===n?"right":"center":o=d?n.map(h):h(n):n="",!d&&isNaN(o)?n:o}function o(e){for(var n in k)if(e[n]&&"undefined"!=typeof e[k[n]]&&0!==e[n].indexOf("url(")){var f=new r.Color(e[n]);e[n]=f.setAlpha(g(f.getAlpha()*e[k[n]],2)).toRgba()}return e}function d(e,n){var f=e.match(/(normal|italic)?\s*(normal|small-caps)?\s*(normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900)?\s*(\d+)px(?:\/(normal|[\d\.]+))?\s+(.*)/);if(f){var o=f[1],d=f[3],i=f[4],t=f[5],l=f[6];o&&(n.fontStyle=o),d&&(n.fontWeight=isNaN(parseFloat(d))?d:parseFloat(d)),i&&(n.fontSize=parseFloat(i)),l&&(n.fontFamily=l),t&&(n.lineHeight="normal"===t?1:t)}}function i(e,o){var i,t;e.replace(/;$/,"").split(";").forEach(function(e){var l=e.split(":");i=n(l[0].trim().toLowerCase()),t=f(i,l[1].trim()),"font"===i?d(t,o):o[i]=t})}function t(e,o){var i,t;for(var l in e)"undefined"!=typeof e[l]&&(i=n(l.toLowerCase()),t=f(i,e[l]),"font"===i?d(t,o):o[i]=t)}function l(e){var n={};for(var f in r.cssRules)if(s(e,f.split(" ")))for(var o in r.cssRules[f])n[o]=r.cssRules[f][o];return n}function s(e,n){var f,o=!0;return f=a(e,n.pop()),f&&n.length&&(o=u(e,n)),f&&o&&0===n.length}function u(e,n){for(var f,o=!0;e.parentNode&&1===e.parentNode.nodeType&&n.length;)o&&(f=n.pop()),e=e.parentNode,o=a(e,f);return 0===n.length}function a(e,n){var f,o=e.nodeName,d=e.getAttribute("class"),i=e.getAttribute("id");if(f=new RegExp("^"+o,"i"),n=n.replace(f,""),i&&n.length&&(f=new RegExp("#"+i+"(?![a-zA-Z\\-]+)","i"),n=n.replace(f,"")),d&&n.length){d=d.split(" ");for(var t=d.length;t--;)f=new RegExp("\\."+d[t]+"(?![a-zA-Z\\-]+)","i"),n=n.replace(f,"")}return 0===n.length}function p(e){for(var n=e.getElementsByTagName("use");n.length;){for(var f,o=n[0],d=o.getAttribute("xlink:href").substr(1),i=o.getAttribute("x")||0,t=o.getAttribute("y")||0,l=e.getElementById(d).cloneNode(!0),s=(o.getAttribute("transform")||"")+" translate("+i+", "+t+")",u=0,a=o.attributes,p=a.length;p>u;u++){var c=a.item(u);"x"!==c.nodeName&&"y"!==c.nodeName&&"xlink:href"!==c.nodeName&&("transform"===c.nodeName?s=s+" "+c.nodeValue:l.setAttribute(c.nodeName,c.nodeValue))}l.setAttribute("transform",s),l.removeAttribute("id"),f=o.parentNode,f.replaceChild(l,o)}}function c(e,n){if(n[3]=n[0]=n[0]>n[3]?n[3]:n[0],1!==n[0]||1!==n[3]||0!==n[4]||0!==n[5]){for(var f=e.ownerDocument.createElement("g");null!=e.firstChild;)f.appendChild(e.firstChild);f.setAttribute("transform","matrix("+n[0]+" "+n[1]+" "+n[2]+" "+n[3]+" "+n[4]+" "+n[5]+")"),e.appendChild(f)}}function y(e){var n=e.objects,f=e.options;return n=n.map(function(e){return r[w(e.type)].fromObject(e)}),{objects:n,options:f}}function m(e,n,f){n[f]&&n[f].toSVG&&e.push('<pattern x="0" y="0" id="',f,'Pattern" ','width="',n[f].source.width,'" height="',n[f].source.height,'" patternUnits="userSpaceOnUse">','<image x="0" y="0" ','width="',n[f].source.width,'" height="',n[f].source.height,'" xlink:href="',n[f].source.src,'"></image></pattern>')}var r=e.fabric||(e.fabric={}),v=r.util.object.extend,w=r.util.string.capitalize,b=r.util.object.clone,g=r.util.toFixed,h=r.util.parseUnit,x=r.util.multiplyTransformMatrices,j={cx:"left",x:"left",r:"radius",cy:"top",y:"top",display:"visible",visibility:"visible",transform:"transformMatrix","fill-opacity":"fillOpacity","fill-rule":"fillRule","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","stroke-dasharray":"strokeDashArray","stroke-linecap":"strokeLineCap","stroke-linejoin":"strokeLineJoin","stroke-miterlimit":"strokeMiterLimit","stroke-opacity":"strokeOpacity","stroke-width":"strokeWidth","text-decoration":"textDecoration","text-anchor":"originX"},k={stroke:"strokeOpacity",fill:"fillOpacity"};r.parseTransformAttribute=function(){function e(e,n){var f=n[0];e[0]=Math.cos(f),e[1]=Math.sin(f),e[2]=-Math.sin(f),e[3]=Math.cos(f)}function n(e,n){var f=n[0],o=2===n.length?n[1]:n[0];e[0]=f,e[3]=o}function f(e,n){e[2]=n[0]}function o(e,n){e[1]=n[0]}function d(e,n){e[4]=n[0],2===n.length&&(e[5]=n[1])}var i=[1,0,0,1,0,0],t="(?:[-+]?(?:\\d+|\\d*\\.\\d+)(?:e[-+]?\\d+)?)",l="(?:\\s+,?\\s*|,\\s*)",s="(?:(skewX)\\s*\\(\\s*("+t+")\\s*\\))",u="(?:(skewY)\\s*\\(\\s*("+t+")\\s*\\))",a="(?:(rotate)\\s*\\(\\s*("+t+")(?:"+l+"("+t+")"+l+"("+t+"))?\\s*\\))",p="(?:(scale)\\s*\\(\\s*("+t+")(?:"+l+"("+t+"))?\\s*\\))",c="(?:(translate)\\s*\\(\\s*("+t+")(?:"+l+"("+t+"))?\\s*\\))",y="(?:(matrix)\\s*\\(\\s*("+t+")"+l+"("+t+")"+l+"("+t+")"+l+"("+t+")"+l+"("+t+")"+l+"("+t+")\\s*\\))",m="(?:"+y+"|"+c+"|"+p+"|"+a+"|"+s+"|"+u+")",v="(?:"+m+"(?:"+l+m+")*)",w="^\\s*(?:"+v+"?)\\s*$",b=new RegExp(w),g=new RegExp(m,"g");return function(t){var l=i.concat(),s=[];if(!t||t&&!b.test(t))return l;t.replace(g,function(t){var u=new RegExp(m).exec(t).filter(function(e){return""!==e&&null!=e}),a=u[1],p=u.slice(2).map(parseFloat);switch(a){case"translate":d(l,p);break;case"rotate":p[0]=r.util.degreesToRadians(p[0]),e(l,p);break;case"scale":n(l,p);break;case"skewX":f(l,p);break;case"skewY":o(l,p);break;case"matrix":l=p}s.push(l.concat()),l=i.concat()});for(var u=s[0];s.length>1;)s.shift(),u=r.util.multiplyTransformMatrices(u,s[0]);return u}}(),r.parseSVGDocument=function(){function e(e,n){for(;e&&(e=e.parentNode);)if(n.test(e.nodeName))return!0;return!1}var n=/^(path|circle|polygon|polyline|ellipse|rect|line|image|text)$/,f="(?:[-+]?(?:\\d+|\\d*\\.\\d+)(?:e[-+]?\\d+)?)",o=new RegExp("^\\s*("+f+"+)\\s*,?\\s*("+f+"+)\\s*,?\\s*("+f+"+)\\s*,?\\s*("+f+"+)\\s*$");return function(f,d,i){if(f){var t=new Date;p(f);var l,s,u=f.getAttribute("viewBox"),a=h(f.getAttribute("width")||"100%"),y=h(f.getAttribute("height")||"100%");if(u&&(u=u.match(o))){var m=parseFloat(u[1]),v=parseFloat(u[2]),w=1,g=1;l=parseFloat(u[3]),s=parseFloat(u[4]),a&&a!==l&&(w=a/l),y&&y!==s&&(g=y/s),c(f,[w,0,0,g,w*-m,g*-v])}var x=r.util.toArray(f.getElementsByTagName("*"));if(0===x.length&&r.isLikelyNode){x=f.selectNodes('//*[name(.)!="svg"]');for(var j=[],k=0,q=x.length;q>k;k++)j[k]=x[k];x=j}var z=x.filter(function(f){return n.test(f.tagName)&&!e(f,/^(?:pattern|defs)$/)});if(!z||z&&!z.length)return void(d&&d([],{}));var A={width:a?a:l,height:y?y:s,widthAttr:a,heightAttr:y};r.gradientDefs=r.getGradientDefs(f),r.cssRules=r.getCSSRules(f),r.parseElements(z,function(e){r.documentParsingTime=new Date-t,d&&d(e,A)},b(A),i)}}}();var q={has:function(e,n){n(!1)},get:function(){},set:function(){}};v(r,{getGradientDefs:function(e){var n,f,o,d,i=e.getElementsByTagName("linearGradient"),t=e.getElementsByTagName("radialGradient"),l=0,s=[],u={},a={};for(s.length=i.length+t.length,f=i.length;f--;)s[l++]=i[f];for(f=t.length;f--;)s[l++]=t[f];for(;l--;)n=s[l],d=n.getAttribute("xlink:href"),o=n.getAttribute("id"),d&&(a[o]=d.substr(1)),u[o]=n;for(o in a){var p=u[a[o]].cloneNode(!0);for(n=u[o];p.firstChild;)n.appendChild(p.firstChild)}return u},parseAttributes:function(e,d){if(e){var i,t={};e.parentNode&&/^symbol|[g|a]$/i.test(e.parentNode.nodeName)&&(t=r.parseAttributes(e.parentNode,d));var s=d.reduce(function(o,d){return i=e.getAttribute(d),i&&(d=n(d),i=f(d,i,t),o[d]=i),o},{});return s=v(s,v(l(e),r.parseStyleAttribute(e))),o(v(t,s))}},parseElements:function(e,n,f,o){new r.ElementsParser(e,n,f,o).parse()},parseStyleAttribute:function(e){var n={},f=e.getAttribute("style");return f?("string"==typeof f?i(f,n):t(f,n),n):n
},parsePointsAttribute:function(e){if(!e)return null;e=e.replace(/,/g," ").trim(),e=e.split(/\s+/);var n,f,o=[];for(n=0,f=e.length;f>n;n+=2)o.push({x:parseFloat(e[n]),y:parseFloat(e[n+1])});return o},getCSSRules:function(e){for(var o,d=e.getElementsByTagName("style"),i={},t=0,l=d.length;l>t;t++){var s=d[0].textContent;s=s.replace(/\/\*[\s\S]*?\*\//g,""),o=s.match(/[^{]*\{[\s\S]*?\}/g),o=o.map(function(e){return e.trim()}),o.forEach(function(e){for(var o=e.match(/([\s\S]*?)\s*\{([^}]*)\}/),d={},t=o[2].trim(),l=t.replace(/;$/,"").split(/\s*;\s*/),s=0,u=l.length;u>s;s++){var a=l[s].split(/\s*:\s*/),p=n(a[0]),c=f(p,a[1],a[0]);d[p]=c}e=o[1],e.split(",").forEach(function(e){i[e.trim()]=r.util.object.clone(d)})})}return i},loadSVGFromURL:function(e,n,f){function o(o){var d=o.responseXML;d&&!d.documentElement&&r.window.ActiveXObject&&o.responseText&&(d=new ActiveXObject("Microsoft.XMLDOM"),d.async="false",d.loadXML(o.responseText.replace(/<!DOCTYPE[\s\S]*?(\[[\s\S]*\])*?>/i,""))),d&&d.documentElement&&r.parseSVGDocument(d.documentElement,function(f,o){q.set(e,{objects:r.util.array.invoke(f,"toObject"),options:o}),n(f,o)},f)}e=e.replace(/^\n\s*/,"").trim(),q.has(e,function(f){f?q.get(e,function(e){var f=y(e);n(f.objects,f.options)}):new r.util.request(e,{method:"get",onComplete:o})})},loadSVGFromString:function(e,n,f){e=e.trim();var o;if("undefined"!=typeof DOMParser){var d=new DOMParser;d&&d.parseFromString&&(o=d.parseFromString(e,"text/xml"))}else r.window.ActiveXObject&&(o=new ActiveXObject("Microsoft.XMLDOM"),o.async="false",o.loadXML(e.replace(/<!DOCTYPE[\s\S]*?(\[[\s\S]*\])*?>/i,"")));r.parseSVGDocument(o.documentElement,function(e,f){n(e,f)},f)},createSVGFontFacesMarkup:function(e){for(var n="",f=0,o=e.length;o>f;f++)"text"===e[f].type&&e[f].path&&(n+=["@font-face {","font-family: ",e[f].fontFamily,"; ","src: url('",e[f].path,"')","}"].join(""));return n&&(n=['<style type="text/css">',"<![CDATA[",n,"]]>","</style>"].join("")),n},createSVGRefElementsMarkup:function(e){var n=[];return m(n,e,"backgroundColor"),m(n,e,"overlayColor"),n.join("")}})}("undefined"!=typeof exports?exports:this),fabric.ElementsParser=function(e,n,f,o){this.elements=e,this.callback=n,this.options=f,this.reviver=o},fabric.ElementsParser.prototype.parse=function(){this.instances=new Array(this.elements.length),this.numElements=this.elements.length,this.createObjects()},fabric.ElementsParser.prototype.createObjects=function(){for(var e=0,n=this.elements.length;n>e;e++)!function(e,n){setTimeout(function(){e.createObject(e.elements[n],n)},0)}(this,e)},fabric.ElementsParser.prototype.createObject=function(e,n){var f=fabric[fabric.util.string.capitalize(e.tagName)];if(f&&f.fromElement)try{this._createObject(f,e,n)}catch(o){fabric.log(o)}else this.checkIfDone()},fabric.ElementsParser.prototype._createObject=function(e,n,f){if(e.async)e.fromElement(n,this.createCallback(f,n),this.options);else{var o=e.fromElement(n,this.options);this.resolveGradient(o,"fill"),this.resolveGradient(o,"stroke"),this.reviver&&this.reviver(n,o),this.instances[f]=o,this.checkIfDone()}},fabric.ElementsParser.prototype.createCallback=function(e,n){var f=this;return function(o){f.resolveGradient(o,"fill"),f.resolveGradient(o,"stroke"),f.reviver&&f.reviver(n,o),f.instances[e]=o,f.checkIfDone()}},fabric.ElementsParser.prototype.resolveGradient=function(e,n){var f=e.get(n);if(/^url\(/.test(f)){var o=f.slice(5,f.length-1);fabric.gradientDefs[o]&&e.set(n,fabric.Gradient.fromElement(fabric.gradientDefs[o],e))}},fabric.ElementsParser.prototype.checkIfDone=function(){0===--this.numElements&&(this.instances=this.instances.filter(function(e){return null!=e}),this.callback(this.instances))},function(e){"use strict";function n(e,n){this.x=e,this.y=n}var f=e.fabric||(e.fabric={});return f.Point?void f.warn("fabric.Point is already defined"):(f.Point=n,void(n.prototype={constructor:n,add:function(e){return new n(this.x+e.x,this.y+e.y)},addEquals:function(e){return this.x+=e.x,this.y+=e.y,this},scalarAdd:function(e){return new n(this.x+e,this.y+e)},scalarAddEquals:function(e){return this.x+=e,this.y+=e,this},subtract:function(e){return new n(this.x-e.x,this.y-e.y)},subtractEquals:function(e){return this.x-=e.x,this.y-=e.y,this},scalarSubtract:function(e){return new n(this.x-e,this.y-e)},scalarSubtractEquals:function(e){return this.x-=e,this.y-=e,this},multiply:function(e){return new n(this.x*e,this.y*e)},multiplyEquals:function(e){return this.x*=e,this.y*=e,this},divide:function(e){return new n(this.x/e,this.y/e)},divideEquals:function(e){return this.x/=e,this.y/=e,this},eq:function(e){return this.x===e.x&&this.y===e.y},lt:function(e){return this.x<e.x&&this.y<e.y},lte:function(e){return this.x<=e.x&&this.y<=e.y},gt:function(e){return this.x>e.x&&this.y>e.y},gte:function(e){return this.x>=e.x&&this.y>=e.y},lerp:function(e,f){return new n(this.x+(e.x-this.x)*f,this.y+(e.y-this.y)*f)},distanceFrom:function(e){var n=this.x-e.x,f=this.y-e.y;return Math.sqrt(n*n+f*f)},midPointFrom:function(e){return new n(this.x+(e.x-this.x)/2,this.y+(e.y-this.y)/2)},min:function(e){return new n(Math.min(this.x,e.x),Math.min(this.y,e.y))},max:function(e){return new n(Math.max(this.x,e.x),Math.max(this.y,e.y))},toString:function(){return this.x+","+this.y},setXY:function(e,n){this.x=e,this.y=n},setFromPoint:function(e){this.x=e.x,this.y=e.y},swap:function(e){var n=this.x,f=this.y;this.x=e.x,this.y=e.y,e.x=n,e.y=f}}))}("undefined"!=typeof exports?exports:this),function(e){"use strict";function n(e){this.status=e,this.points=[]}var f=e.fabric||(e.fabric={});return f.Intersection?void f.warn("fabric.Intersection is already defined"):(f.Intersection=n,f.Intersection.prototype={appendPoint:function(e){this.points.push(e)},appendPoints:function(e){this.points=this.points.concat(e)}},f.Intersection.intersectLineLine=function(e,o,d,i){var t,l=(i.x-d.x)*(e.y-d.y)-(i.y-d.y)*(e.x-d.x),s=(o.x-e.x)*(e.y-d.y)-(o.y-e.y)*(e.x-d.x),u=(i.y-d.y)*(o.x-e.x)-(i.x-d.x)*(o.y-e.y);if(0!==u){var a=l/u,p=s/u;a>=0&&1>=a&&p>=0&&1>=p?(t=new n("Intersection"),t.points.push(new f.Point(e.x+a*(o.x-e.x),e.y+a*(o.y-e.y)))):t=new n}else t=new n(0===l||0===s?"Coincident":"Parallel");return t},f.Intersection.intersectLinePolygon=function(e,f,o){for(var d=new n,i=o.length,t=0;i>t;t++){var l=o[t],s=o[(t+1)%i],u=n.intersectLineLine(e,f,l,s);d.appendPoints(u.points)}return d.points.length>0&&(d.status="Intersection"),d},f.Intersection.intersectPolygonPolygon=function(e,f){for(var o=new n,d=e.length,i=0;d>i;i++){var t=e[i],l=e[(i+1)%d],s=n.intersectLinePolygon(t,l,f);o.appendPoints(s.points)}return o.points.length>0&&(o.status="Intersection"),o},void(f.Intersection.intersectPolygonRectangle=function(e,o,d){var i=o.min(d),t=o.max(d),l=new f.Point(t.x,i.y),s=new f.Point(i.x,t.y),u=n.intersectLinePolygon(i,l,e),a=n.intersectLinePolygon(l,t,e),p=n.intersectLinePolygon(t,s,e),c=n.intersectLinePolygon(s,i,e),y=new n;return y.appendPoints(u.points),y.appendPoints(a.points),y.appendPoints(p.points),y.appendPoints(c.points),y.points.length>0&&(y.status="Intersection"),y}))}("undefined"!=typeof exports?exports:this),function(e){"use strict";function n(e){e?this._tryParsingColor(e):this.setSource([0,0,0,1])}function f(e,n,f){return 0>f&&(f+=1),f>1&&(f-=1),1/6>f?e+6*(n-e)*f:.5>f?n:2/3>f?e+(n-e)*(2/3-f)*6:e}var o=e.fabric||(e.fabric={});return o.Color?void o.warn("fabric.Color is already defined."):(o.Color=n,o.Color.prototype={_tryParsingColor:function(e){var f;return e in n.colorNameMap&&(e=n.colorNameMap[e]),"transparent"===e?void this.setSource([255,255,255,0]):(f=n.sourceFromHex(e),f||(f=n.sourceFromRgb(e)),f||(f=n.sourceFromHsl(e)),void(f&&this.setSource(f)))},_rgbToHsl:function(e,n,f){e/=255,n/=255,f/=255;var d,i,t,l=o.util.array.max([e,n,f]),s=o.util.array.min([e,n,f]);if(t=(l+s)/2,l===s)d=i=0;else{var u=l-s;switch(i=t>.5?u/(2-l-s):u/(l+s),l){case e:d=(n-f)/u+(f>n?6:0);break;case n:d=(f-e)/u+2;break;case f:d=(e-n)/u+4}d/=6}return[Math.round(360*d),Math.round(100*i),Math.round(100*t)]},getSource:function(){return this._source},setSource:function(e){this._source=e},toRgb:function(){var e=this.getSource();return"rgb("+e[0]+","+e[1]+","+e[2]+")"},toRgba:function(){var e=this.getSource();return"rgba("+e[0]+","+e[1]+","+e[2]+","+e[3]+")"},toHsl:function(){var e=this.getSource(),n=this._rgbToHsl(e[0],e[1],e[2]);return"hsl("+n[0]+","+n[1]+"%,"+n[2]+"%)"},toHsla:function(){var e=this.getSource(),n=this._rgbToHsl(e[0],e[1],e[2]);return"hsla("+n[0]+","+n[1]+"%,"+n[2]+"%,"+e[3]+")"},toHex:function(){var e,n,f,o=this.getSource();return e=o[0].toString(16),e=1===e.length?"0"+e:e,n=o[1].toString(16),n=1===n.length?"0"+n:n,f=o[2].toString(16),f=1===f.length?"0"+f:f,e.toUpperCase()+n.toUpperCase()+f.toUpperCase()},getAlpha:function(){return this.getSource()[3]},setAlpha:function(e){var n=this.getSource();return n[3]=e,this.setSource(n),this},toGrayscale:function(){var e=this.getSource(),n=parseInt((.3*e[0]+.59*e[1]+.11*e[2]).toFixed(0),10),f=e[3];return this.setSource([n,n,n,f]),this},toBlackWhite:function(e){var n=this.getSource(),f=(.3*n[0]+.59*n[1]+.11*n[2]).toFixed(0),o=n[3];return e=e||127,f=Number(f)<Number(e)?0:255,this.setSource([f,f,f,o]),this},overlayWith:function(e){e instanceof n||(e=new n(e));for(var f=[],o=this.getAlpha(),d=.5,i=this.getSource(),t=e.getSource(),l=0;3>l;l++)f.push(Math.round(i[l]*(1-d)+t[l]*d));return f[3]=o,this.setSource(f),this}},o.Color.reRGBa=/^rgba?\(\s*(\d{1,3}(?:\.\d+)?\%?)\s*,\s*(\d{1,3}(?:\.\d+)?\%?)\s*,\s*(\d{1,3}(?:\.\d+)?\%?)\s*(?:\s*,\s*(\d+(?:\.\d+)?)\s*)?\)$/,o.Color.reHSLa=/^hsla?\(\s*(\d{1,3})\s*,\s*(\d{1,3}\%)\s*,\s*(\d{1,3}\%)\s*(?:\s*,\s*(\d+(?:\.\d+)?)\s*)?\)$/,o.Color.reHex=/^#?([0-9a-f]{6}|[0-9a-f]{3})$/i,o.Color.colorNameMap={aqua:"#00FFFF",black:"#000000",blue:"#0000FF",fuchsia:"#FF00FF",gray:"#808080",green:"#008000",lime:"#00FF00",maroon:"#800000",navy:"#000080",olive:"#808000",orange:"#FFA500",purple:"#800080",red:"#FF0000",silver:"#C0C0C0",teal:"#008080",white:"#FFFFFF",yellow:"#FFFF00"},o.Color.fromRgb=function(e){return n.fromSource(n.sourceFromRgb(e))},o.Color.sourceFromRgb=function(e){var f=e.match(n.reRGBa);if(f){var o=parseInt(f[1],10)/(/%$/.test(f[1])?100:1)*(/%$/.test(f[1])?255:1),d=parseInt(f[2],10)/(/%$/.test(f[2])?100:1)*(/%$/.test(f[2])?255:1),i=parseInt(f[3],10)/(/%$/.test(f[3])?100:1)*(/%$/.test(f[3])?255:1);return[parseInt(o,10),parseInt(d,10),parseInt(i,10),f[4]?parseFloat(f[4]):1]}},o.Color.fromRgba=n.fromRgb,o.Color.fromHsl=function(e){return n.fromSource(n.sourceFromHsl(e))},o.Color.sourceFromHsl=function(e){var o=e.match(n.reHSLa);if(o){var d,i,t,l=(parseFloat(o[1])%360+360)%360/360,s=parseFloat(o[2])/(/%$/.test(o[2])?100:1),u=parseFloat(o[3])/(/%$/.test(o[3])?100:1);if(0===s)d=i=t=u;else{var a=.5>=u?u*(s+1):u+s-u*s,p=2*u-a;d=f(p,a,l+1/3),i=f(p,a,l),t=f(p,a,l-1/3)}return[Math.round(255*d),Math.round(255*i),Math.round(255*t),o[4]?parseFloat(o[4]):1]}},o.Color.fromHsla=n.fromHsl,o.Color.fromHex=function(e){return n.fromSource(n.sourceFromHex(e))},o.Color.sourceFromHex=function(e){if(e.match(n.reHex)){var f=e.slice(e.indexOf("#")+1),o=3===f.length,d=o?f.charAt(0)+f.charAt(0):f.substring(0,2),i=o?f.charAt(1)+f.charAt(1):f.substring(2,4),t=o?f.charAt(2)+f.charAt(2):f.substring(4,6);return[parseInt(d,16),parseInt(i,16),parseInt(t,16),1]}},void(o.Color.fromSource=function(e){var f=new n;return f.setSource(e),f}))}("undefined"!=typeof exports?exports:this),function(){function e(e){var n,f,o,d=e.getAttribute("style"),i=e.getAttribute("offset");if(i=parseFloat(i)/(/%$/.test(i)?100:1),i=0>i?0:i>1?1:i,d){var t=d.split(/\s*;\s*/);""===t[t.length-1]&&t.pop();for(var l=t.length;l--;){var s=t[l].split(/\s*:\s*/),u=s[0].trim(),a=s[1].trim();"stop-color"===u?n=a:"stop-opacity"===u&&(o=a)}}return n||(n=e.getAttribute("stop-color")||"rgb(0,0,0)"),o||(o=e.getAttribute("stop-opacity")),n=new fabric.Color(n),f=n.getAlpha(),o=isNaN(parseFloat(o))?1:parseFloat(o),o*=f,{offset:i,color:n.toRgb(),opacity:o}}function n(e){return{x1:e.getAttribute("x1")||0,y1:e.getAttribute("y1")||0,x2:e.getAttribute("x2")||"100%",y2:e.getAttribute("y2")||0}}function f(e){return{x1:e.getAttribute("fx")||e.getAttribute("cx")||"50%",y1:e.getAttribute("fy")||e.getAttribute("cy")||"50%",r1:0,x2:e.getAttribute("cx")||"50%",y2:e.getAttribute("cy")||"50%",r2:e.getAttribute("r")||"50%"}}function o(e,n,f){var o,d=0,i=1,t="";for(var l in n)o=parseFloat(n[l],10),i="string"==typeof n[l]&&/^\d+%$/.test(n[l])?.01:1,"x1"===l||"x2"===l||"r2"===l?(i*="objectBoundingBox"===f?e.width:1,d="objectBoundingBox"===f?e.left||0:0):("y1"===l||"y2"===l)&&(i*="objectBoundingBox"===f?e.height:1,d="objectBoundingBox"===f?e.top||0:0),n[l]=o*i+d;if("ellipse"===e.type&&null!==n.r2&&"objectBoundingBox"===f&&e.rx!==e.ry){var s=e.ry/e.rx;t=" scale(1, "+s+")",n.y1&&(n.y1/=s),n.y2&&(n.y2/=s)}return t}fabric.Gradient=fabric.util.createClass({offsetX:0,offsetY:0,initialize:function(e){e||(e={});var n={};this.id=fabric.Object.__uid++,this.type=e.type||"linear",n={x1:e.coords.x1||0,y1:e.coords.y1||0,x2:e.coords.x2||0,y2:e.coords.y2||0},"radial"===this.type&&(n.r1=e.coords.r1||0,n.r2=e.coords.r2||0),this.coords=n,this.colorStops=e.colorStops.slice(),e.gradientTransform&&(this.gradientTransform=e.gradientTransform),this.offsetX=e.offsetX||this.offsetX,this.offsetY=e.offsetY||this.offsetY},addColorStop:function(e){for(var n in e){var f=new fabric.Color(e[n]);this.colorStops.push({offset:n,color:f.toRgb(),opacity:f.getAlpha()})}return this},toObject:function(){return{type:this.type,coords:this.coords,colorStops:this.colorStops,offsetX:this.offsetX,offsetY:this.offsetY}},toSVG:function(e){var n,f,o=fabric.util.object.clone(this.coords);if(this.colorStops.sort(function(e,n){return e.offset-n.offset}),!e.group||"path-group"!==e.group.type)for(var d in o)"x1"===d||"x2"===d||"r2"===d?o[d]+=this.offsetX-e.width/2:("y1"===d||"y2"===d)&&(o[d]+=this.offsetY-e.height/2);f='id="SVGID_'+this.id+'" gradientUnits="userSpaceOnUse"',this.gradientTransform&&(f+=' gradientTransform="matrix('+this.gradientTransform.join(" ")+')" '),"linear"===this.type?n=["<linearGradient ",f,' x1="',o.x1,'" y1="',o.y1,'" x2="',o.x2,'" y2="',o.y2,'">\n']:"radial"===this.type&&(n=["<radialGradient ",f,' cx="',o.x2,'" cy="',o.y2,'" r="',o.r2,'" fx="',o.x1,'" fy="',o.y1,'">\n']);for(var i=0;i<this.colorStops.length;i++)n.push("<stop ",'offset="',100*this.colorStops[i].offset+"%",'" style="stop-color:',this.colorStops[i].color,null!=this.colorStops[i].opacity?";stop-opacity: "+this.colorStops[i].opacity:";",'"/>\n');return n.push("linear"===this.type?"</linearGradient>\n":"</radialGradient>\n"),n.join("")},toLive:function(e){var n;if(this.type){"linear"===this.type?n=e.createLinearGradient(this.coords.x1,this.coords.y1,this.coords.x2,this.coords.y2):"radial"===this.type&&(n=e.createRadialGradient(this.coords.x1,this.coords.y1,this.coords.r1,this.coords.x2,this.coords.y2,this.coords.r2));for(var f=0,o=this.colorStops.length;o>f;f++){var d=this.colorStops[f].color,i=this.colorStops[f].opacity,t=this.colorStops[f].offset;"undefined"!=typeof i&&(d=new fabric.Color(d).setAlpha(i).toRgba()),n.addColorStop(parseFloat(t),d)}return n}}}),fabric.util.object.extend(fabric.Gradient,{fromElement:function(d,i){var t,l=d.getElementsByTagName("stop"),s="linearGradient"===d.nodeName?"linear":"radial",u=d.getAttribute("gradientUnits")||"objectBoundingBox",a=d.getAttribute("gradientTransform"),p=[],c={};"linear"===s?c=n(d):"radial"===s&&(c=f(d));for(var y=l.length;y--;)p.push(e(l[y]));t=o(i,c,u);var m=new fabric.Gradient({type:s,coords:c,colorStops:p,offsetX:-i.left,offsetY:-i.top});return(a||""!==t)&&(m.gradientTransform=fabric.parseTransformAttribute((a||"")+t)),m},forObject:function(e,n){return n||(n={}),o(e,n.coords,"userSpaceOnUse"),new fabric.Gradient(n)}})}(),fabric.Pattern=fabric.util.createClass({repeat:"repeat",offsetX:0,offsetY:0,initialize:function(e){if(e||(e={}),this.id=fabric.Object.__uid++,e.source)if("string"==typeof e.source)if("undefined"!=typeof fabric.util.getFunctionBody(e.source))this.source=new Function(fabric.util.getFunctionBody(e.source));else{var n=this;this.source=fabric.util.createImage(),fabric.util.loadImage(e.source,function(e){n.source=e})}else this.source=e.source;e.repeat&&(this.repeat=e.repeat),e.offsetX&&(this.offsetX=e.offsetX),e.offsetY&&(this.offsetY=e.offsetY)},toObject:function(){var e;return"function"==typeof this.source?e=String(this.source):"string"==typeof this.source.src&&(e=this.source.src),{source:e,repeat:this.repeat,offsetX:this.offsetX,offsetY:this.offsetY}},toSVG:function(e){var n="function"==typeof this.source?this.source():this.source,f=n.width/e.getWidth(),o=n.height/e.getHeight(),d="";return n.src?d=n.src:n.toDataURL&&(d=n.toDataURL()),'<pattern id="SVGID_'+this.id+'" x="'+this.offsetX+'" y="'+this.offsetY+'" width="'+f+'" height="'+o+'"><image x="0" y="0" width="'+n.width+'" height="'+n.height+'" xlink:href="'+d+'"></image></pattern>'},toLive:function(e){var n="function"==typeof this.source?this.source():this.source;if(!n)return"";if("undefined"!=typeof n.src){if(!n.complete)return"";if(0===n.naturalWidth||0===n.naturalHeight)return""}return e.createPattern(n,this.repeat)}}),function(e){"use strict";var n=e.fabric||(e.fabric={});return n.Shadow?void n.warn("fabric.Shadow is already defined."):(n.Shadow=n.util.createClass({color:"rgb(0,0,0)",blur:0,offsetX:0,offsetY:0,affectStroke:!1,includeDefaultValues:!0,initialize:function(e){"string"==typeof e&&(e=this._parseShadow(e));for(var f in e)this[f]=e[f];this.id=n.Object.__uid++},_parseShadow:function(e){var f=e.trim(),o=n.Shadow.reOffsetsAndBlur.exec(f)||[],d=f.replace(n.Shadow.reOffsetsAndBlur,"")||"rgb(0,0,0)";return{color:d.trim(),offsetX:parseInt(o[1],10)||0,offsetY:parseInt(o[2],10)||0,blur:parseInt(o[3],10)||0}},toString:function(){return[this.offsetX,this.offsetY,this.blur,this.color].join("px ")},toSVG:function(e){var n="SourceAlpha";return!e||e.fill!==this.color&&e.stroke!==this.color||(n="SourceGraphic"),'<filter id="SVGID_'+this.id+'" y="-40%" height="180%"><feGaussianBlur in="'+n+'" stdDeviation="'+(this.blur?this.blur/3:0)+'"></feGaussianBlur><feOffset dx="'+this.offsetX+'" dy="'+this.offsetY+'"></feOffset><feMerge><feMergeNode></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter>'},toObject:function(){if(this.includeDefaultValues)return{color:this.color,blur:this.blur,offsetX:this.offsetX,offsetY:this.offsetY};var e={},f=n.Shadow.prototype;return this.color!==f.color&&(e.color=this.color),this.blur!==f.blur&&(e.blur=this.blur),this.offsetX!==f.offsetX&&(e.offsetX=this.offsetX),this.offsetY!==f.offsetY&&(e.offsetY=this.offsetY),e}}),void(n.Shadow.reOffsetsAndBlur=/(?:\s|^)(-?\d+(?:px)?(?:\s?|$))?(-?\d+(?:px)?(?:\s?|$))?(\d+(?:px)?)?(?:\s?|$)(?:$|\s)/))}("undefined"!=typeof exports?exports:this),function(){"use strict";if(fabric.StaticCanvas)return void fabric.warn("fabric.StaticCanvas is already defined.");var e=fabric.util.object.extend,n=fabric.util.getElementOffset,f=fabric.util.removeFromArray,o=new Error("Could not initialize `canvas` element");fabric.StaticCanvas=fabric.util.createClass({initialize:function(e,n){n||(n={}),this._initStatic(e,n),fabric.StaticCanvas.activeInstance=this},backgroundColor:"",backgroundImage:null,overlayColor:"",overlayImage:null,includeDefaultValues:!0,stateful:!0,renderOnAddRemove:!0,clipTo:null,controlsAboveOverlay:!1,allowTouchScrolling:!1,imageSmoothingEnabled:!0,viewportTransform:[1,0,0,1,0,0],onBeforeScaleRotate:function(){},_initStatic:function(e,n){this._objects=[],this._createLowerCanvas(e),this._initOptions(n),this._setImageSmoothing(),n.overlayImage&&this.setOverlayImage(n.overlayImage,this.renderAll.bind(this)),n.backgroundImage&&this.setBackgroundImage(n.backgroundImage,this.renderAll.bind(this)),n.backgroundColor&&this.setBackgroundColor(n.backgroundColor,this.renderAll.bind(this)),n.overlayColor&&this.setOverlayColor(n.overlayColor,this.renderAll.bind(this)),this.calcOffset()},calcOffset:function(){return this._offset=n(this.lowerCanvasEl),this},setOverlayImage:function(e,n,f){return this.__setBgOverlayImage("overlayImage",e,n,f)},setBackgroundImage:function(e,n,f){return this.__setBgOverlayImage("backgroundImage",e,n,f)},setOverlayColor:function(e,n){return this.__setBgOverlayColor("overlayColor",e,n)},setBackgroundColor:function(e,n){return this.__setBgOverlayColor("backgroundColor",e,n)},_setImageSmoothing:function(){var e=this.getContext();e.imageSmoothingEnabled=this.imageSmoothingEnabled,e.webkitImageSmoothingEnabled=this.imageSmoothingEnabled,e.mozImageSmoothingEnabled=this.imageSmoothingEnabled,e.msImageSmoothingEnabled=this.imageSmoothingEnabled,e.oImageSmoothingEnabled=this.imageSmoothingEnabled},__setBgOverlayImage:function(e,n,f,o){return"string"==typeof n?fabric.util.loadImage(n,function(n){this[e]=new fabric.Image(n,o),f&&f()},this):(this[e]=n,f&&f()),this},__setBgOverlayColor:function(e,n,f){if(n&&n.source){var o=this;fabric.util.loadImage(n.source,function(d){o[e]=new fabric.Pattern({source:d,repeat:n.repeat,offsetX:n.offsetX,offsetY:n.offsetY}),f&&f()})}else this[e]=n,f&&f();return this},_createCanvasElement:function(){var e=fabric.document.createElement("canvas");if(e.style||(e.style={}),!e)throw o;return this._initCanvasElement(e),e},_initCanvasElement:function(e){if(fabric.util.createCanvasElement(e),"undefined"==typeof e.getContext)throw o},_initOptions:function(e){for(var n in e)this[n]=e[n];this.width=this.width||parseInt(this.lowerCanvasEl.width,10)||0,this.height=this.height||parseInt(this.lowerCanvasEl.height,10)||0,this.lowerCanvasEl.style&&(this.lowerCanvasEl.width=this.width,this.lowerCanvasEl.height=this.height,this.lowerCanvasEl.style.width=this.width+"px",this.lowerCanvasEl.style.height=this.height+"px",this.viewportTransform=this.viewportTransform.slice())},_createLowerCanvas:function(e){this.lowerCanvasEl=fabric.util.getById(e)||this._createCanvasElement(),this._initCanvasElement(this.lowerCanvasEl),fabric.util.addClass(this.lowerCanvasEl,"lower-canvas"),this.interactive&&this._applyCanvasStyle(this.lowerCanvasEl),this.contextContainer=this.lowerCanvasEl.getContext("2d")},getWidth:function(){return this.width},getHeight:function(){return this.height},setWidth:function(e,n){return this.setDimensions({width:e},n)},setHeight:function(e,n){return this.setDimensions({height:e},n)},setDimensions:function(e,n){var f;n=n||{};for(var o in e)f=e[o],n.cssOnly||(this._setBackstoreDimension(o,e[o]),f+="px"),n.backstoreOnly||this._setCssDimension(o,f);return n.cssOnly||this.renderAll(),this.calcOffset(),this},_setBackstoreDimension:function(e,n){return this.lowerCanvasEl[e]=n,this.upperCanvasEl&&(this.upperCanvasEl[e]=n),this.cacheCanvasEl&&(this.cacheCanvasEl[e]=n),this[e]=n,this},_setCssDimension:function(e,n){return this.lowerCanvasEl.style[e]=n,this.upperCanvasEl&&(this.upperCanvasEl.style[e]=n),this.wrapperEl&&(this.wrapperEl.style[e]=n),this},getZoom:function(){return Math.sqrt(this.viewportTransform[0]*this.viewportTransform[3])},setViewportTransform:function(e){this.viewportTransform=e,this.renderAll();for(var n=0,f=this._objects.length;f>n;n++)this._objects[n].setCoords();return this},zoomToPoint:function(e,n){var f=e;e=fabric.util.transformPoint(e,fabric.util.invertTransform(this.viewportTransform)),this.viewportTransform[0]=n,this.viewportTransform[3]=n;var o=fabric.util.transformPoint(e,this.viewportTransform);this.viewportTransform[4]+=f.x-o.x,this.viewportTransform[5]+=f.y-o.y,this.renderAll();for(var d=0,i=this._objects.length;i>d;d++)this._objects[d].setCoords();return this},setZoom:function(e){return this.zoomToPoint(new fabric.Point(0,0),e),this},absolutePan:function(e){this.viewportTransform[4]=-e.x,this.viewportTransform[5]=-e.y,this.renderAll();for(var n=0,f=this._objects.length;f>n;n++)this._objects[n].setCoords();return this},relativePan:function(e){return this.absolutePan(new fabric.Point(-e.x-this.viewportTransform[4],-e.y-this.viewportTransform[5]))},getElement:function(){return this.lowerCanvasEl},getActiveObject:function(){return null},getActiveGroup:function(){return null},_draw:function(e,n){if(n){e.save();var f=this.viewportTransform;e.transform(f[0],f[1],f[2],f[3],f[4],f[5]),n.render(e),e.restore(),this.controlsAboveOverlay||n._renderControls(e)}},_onObjectAdded:function(e){this.stateful&&e.setupState(),e.canvas=this,e.setCoords(),this.fire("object:added",{target:e}),e.fire("added")},_onObjectRemoved:function(e){this.getActiveObject()===e&&(this.fire("before:selection:cleared",{target:e}),this._discardActiveObject(),this.fire("selection:cleared")),this.fire("object:removed",{target:e}),e.fire("removed")},clearContext:function(e){return e.clearRect(0,0,this.width,this.height),this},getContext:function(){return this.contextContainer},clear:function(){return this._objects.length=0,this.discardActiveGroup&&this.discardActiveGroup(),this.discardActiveObject&&this.discardActiveObject(),this.clearContext(this.contextContainer),this.contextTop&&this.clearContext(this.contextTop),this.fire("canvas:cleared"),this.renderAll(),this},renderAll:function(e){var n=this[e===!0&&this.interactive?"contextTop":"contextContainer"],f=this.getActiveGroup();return this.contextTop&&this.selection&&!this._groupSelector&&this.clearContext(this.contextTop),e||this.clearContext(n),this.fire("before:render"),this.clipTo&&fabric.util.clipContext(this,n),this._renderBackground(n),this._renderObjects(n,f),this._renderActiveGroup(n,f),this.clipTo&&n.restore(),this._renderOverlay(n),this.controlsAboveOverlay&&this.interactive&&this.drawControls(n),this.fire("after:render"),this},_renderObjects:function(e,n){var f,o;if(n)for(f=0,o=this._objects.length;o>f;++f)this._objects[f]&&!n.contains(this._objects[f])&&this._draw(e,this._objects[f]);else for(f=0,o=this._objects.length;o>f;++f)this._draw(e,this._objects[f])},_renderActiveGroup:function(e,n){if(n){var f=[];this.forEachObject(function(e){n.contains(e)&&f.push(e)}),n._set("objects",f),this._draw(e,n)}},_renderBackground:function(e){this.backgroundColor&&(e.fillStyle=this.backgroundColor.toLive?this.backgroundColor.toLive(e):this.backgroundColor,e.fillRect(this.backgroundColor.offsetX||0,this.backgroundColor.offsetY||0,this.width,this.height)),this.backgroundImage&&this._draw(e,this.backgroundImage)},_renderOverlay:function(e){this.overlayColor&&(e.fillStyle=this.overlayColor.toLive?this.overlayColor.toLive(e):this.overlayColor,e.fillRect(this.overlayColor.offsetX||0,this.overlayColor.offsetY||0,this.width,this.height)),this.overlayImage&&this._draw(e,this.overlayImage)},renderTop:function(){var e=this.contextTop||this.contextContainer;this.clearContext(e),this.selection&&this._groupSelector&&this._drawSelection();var n=this.getActiveGroup();return n&&n.render(e),this._renderOverlay(e),this.fire("after:render"),this},getCenter:function(){return{top:this.getHeight()/2,left:this.getWidth()/2}},centerObjectH:function(e){return this._centerObject(e,new fabric.Point(this.getCenter().left,e.getCenterPoint().y)),this.renderAll(),this},centerObjectV:function(e){return this._centerObject(e,new fabric.Point(e.getCenterPoint().x,this.getCenter().top)),this.renderAll(),this},centerObject:function(e){var n=this.getCenter();return this._centerObject(e,new fabric.Point(n.left,n.top)),this.renderAll(),this},_centerObject:function(e,n){return e.setPositionByOrigin(n,"center","center"),this},toDatalessJSON:function(e){return this.toDatalessObject(e)},toObject:function(e){return this._toObjectMethod("toObject",e)},toDatalessObject:function(e){return this._toObjectMethod("toDatalessObject",e)},_toObjectMethod:function(n,f){var o=this.getActiveGroup();o&&this.discardActiveGroup();var d={objects:this._toObjects(n,f)};return e(d,this.__serializeBgOverlay()),fabric.util.populateWithProperties(this,d,f),o&&(this.setActiveGroup(new fabric.Group(o.getObjects(),{originX:"center",originY:"center"})),o.forEachObject(function(e){e.set("active",!0)}),this._currentTransform&&(this._currentTransform.target=this.getActiveGroup())),d},_toObjects:function(e,n){return this.getObjects().map(function(f){return this._toObject(f,e,n)},this)},_toObject:function(e,n,f){var o;this.includeDefaultValues||(o=e.includeDefaultValues,e.includeDefaultValues=!1);var d=e[n](f);return this.includeDefaultValues||(e.includeDefaultValues=o),d},__serializeBgOverlay:function(){var e={background:this.backgroundColor&&this.backgroundColor.toObject?this.backgroundColor.toObject():this.backgroundColor};return this.overlayColor&&(e.overlay=this.overlayColor.toObject?this.overlayColor.toObject():this.overlayColor),this.backgroundImage&&(e.backgroundImage=this.backgroundImage.toObject()),this.overlayImage&&(e.overlayImage=this.overlayImage.toObject()),e},svgViewportTransformation:!0,toSVG:function(e,n){e||(e={});var f=[];return this._setSVGPreamble(f,e),this._setSVGHeader(f,e),this._setSVGBgOverlayColor(f,"backgroundColor"),this._setSVGBgOverlayImage(f,"backgroundImage"),this._setSVGObjects(f,n),this._setSVGBgOverlayColor(f,"overlayColor"),this._setSVGBgOverlayImage(f,"overlayImage"),f.push("</svg>"),f.join("")},_setSVGPreamble:function(e,n){n.suppressPreamble||e.push('<?xml version="1.0" encoding="',n.encoding||"UTF-8",'" standalone="no" ?>','<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" ','"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">\n')},_setSVGHeader:function(e,n){var f,o,d;n.viewBox?(f=n.viewBox.width,o=n.viewBox.height):(f=this.width,o=this.height,this.svgViewportTransformation||(d=this.viewportTransform,f/=d[0],o/=d[3])),e.push("<svg ",'xmlns="http://www.w3.org/2000/svg" ','xmlns:xlink="http://www.w3.org/1999/xlink" ','version="1.1" ','width="',f,'" ','height="',o,'" ',this.backgroundColor&&!this.backgroundColor.toLive?'style="background-color: '+this.backgroundColor+'" ':null,n.viewBox?'viewBox="'+n.viewBox.x+" "+n.viewBox.y+" "+n.viewBox.width+" "+n.viewBox.height+'" ':null,'xml:space="preserve">',"<desc>Created with Fabric.js ",fabric.version,"</desc>","<defs>",fabric.createSVGFontFacesMarkup(this.getObjects()),fabric.createSVGRefElementsMarkup(this),"</defs>")},_setSVGObjects:function(e,n){var f=this.getActiveGroup();f&&this.discardActiveGroup();for(var o=0,d=this.getObjects(),i=d.length;i>o;o++)e.push(d[o].toSVG(n));f&&(this.setActiveGroup(new fabric.Group(f.getObjects())),f.forEachObject(function(e){e.set("active",!0)}))},_setSVGBgOverlayImage:function(e,n){this[n]&&this[n].toSVG&&e.push(this[n].toSVG())},_setSVGBgOverlayColor:function(e,n){this[n]&&this[n].source?e.push('<rect x="',this[n].offsetX,'" y="',this[n].offsetY,'" ','width="',"repeat-y"===this[n].repeat||"no-repeat"===this[n].repeat?this[n].source.width:this.width,'" height="',"repeat-x"===this[n].repeat||"no-repeat"===this[n].repeat?this[n].source.height:this.height,'" fill="url(#'+n+'Pattern)"',"></rect>"):this[n]&&"overlayColor"===n&&e.push('<rect x="0" y="0" ','width="',this.width,'" height="',this.height,'" fill="',this[n],'"',"></rect>")},sendToBack:function(e){return f(this._objects,e),this._objects.unshift(e),this.renderAll&&this.renderAll()},bringToFront:function(e){return f(this._objects,e),this._objects.push(e),this.renderAll&&this.renderAll()},sendBackwards:function(e,n){var o=this._objects.indexOf(e);if(0!==o){var d=this._findNewLowerIndex(e,o,n);f(this._objects,e),this._objects.splice(d,0,e),this.renderAll&&this.renderAll()}return this},_findNewLowerIndex:function(e,n,f){var o;if(f){o=n;for(var d=n-1;d>=0;--d){var i=e.intersectsWithObject(this._objects[d])||e.isContainedWithinObject(this._objects[d])||this._objects[d].isContainedWithinObject(e);if(i){o=d;break}}}else o=n-1;return o},bringForward:function(e,n){var o=this._objects.indexOf(e);if(o!==this._objects.length-1){var d=this._findNewUpperIndex(e,o,n);f(this._objects,e),this._objects.splice(d,0,e),this.renderAll&&this.renderAll()}return this},_findNewUpperIndex:function(e,n,f){var o;if(f){o=n;for(var d=n+1;d<this._objects.length;++d){var i=e.intersectsWithObject(this._objects[d])||e.isContainedWithinObject(this._objects[d])||this._objects[d].isContainedWithinObject(e);if(i){o=d;
break}}}else o=n+1;return o},moveTo:function(e,n){return f(this._objects,e),this._objects.splice(n,0,e),this.renderAll&&this.renderAll()},dispose:function(){return this.clear(),this.interactive&&this.removeListeners(),this},toString:function(){return"#<fabric.Canvas ("+this.complexity()+"): { objects: "+this.getObjects().length+" }>"}}),e(fabric.StaticCanvas.prototype,fabric.Observable),e(fabric.StaticCanvas.prototype,fabric.Collection),e(fabric.StaticCanvas.prototype,fabric.DataURLExporter),e(fabric.StaticCanvas,{EMPTY_JSON:'{"objects": [], "background": "white"}',supports:function(e){var n=fabric.util.createCanvasElement();if(!n||!n.getContext)return null;var f=n.getContext("2d");if(!f)return null;switch(e){case"getImageData":return"undefined"!=typeof f.getImageData;case"setLineDash":return"undefined"!=typeof f.setLineDash;case"toDataURL":return"undefined"!=typeof n.toDataURL;case"toDataURLWithQuality":try{return n.toDataURL("image/jpeg",0),!0}catch(o){}return!1;default:return null}}}),fabric.StaticCanvas.prototype.toJSON=fabric.StaticCanvas.prototype.toObject}(),fabric.BaseBrush=fabric.util.createClass({color:"rgb(0, 0, 0)",width:1,shadow:null,strokeLineCap:"round",strokeLineJoin:"round",setShadow:function(e){return this.shadow=new fabric.Shadow(e),this},_setBrushStyles:function(){var e=this.canvas.contextTop;e.strokeStyle=this.color,e.lineWidth=this.width,e.lineCap=this.strokeLineCap,e.lineJoin=this.strokeLineJoin},_setShadow:function(){if(this.shadow){var e=this.canvas.contextTop;e.shadowColor=this.shadow.color,e.shadowBlur=this.shadow.blur,e.shadowOffsetX=this.shadow.offsetX,e.shadowOffsetY=this.shadow.offsetY}},_resetShadow:function(){var e=this.canvas.contextTop;e.shadowColor="",e.shadowBlur=e.shadowOffsetX=e.shadowOffsetY=0}}),function(){var e=fabric.util.array.min,n=fabric.util.array.max;fabric.PencilBrush=fabric.util.createClass(fabric.BaseBrush,{initialize:function(e){this.canvas=e,this._points=[]},onMouseDown:function(e){this._prepareForDrawing(e),this._captureDrawingPath(e),this._render()},onMouseMove:function(e){this._captureDrawingPath(e),this.canvas.clearContext(this.canvas.contextTop),this._render()},onMouseUp:function(){this._finalizeAndAddPath()},_prepareForDrawing:function(e){var n=new fabric.Point(e.x,e.y);this._reset(),this._addPoint(n),this.canvas.contextTop.moveTo(n.x,n.y)},_addPoint:function(e){this._points.push(e)},_reset:function(){this._points.length=0,this._setBrushStyles(),this._setShadow()},_captureDrawingPath:function(e){var n=new fabric.Point(e.x,e.y);this._addPoint(n)},_render:function(){var e=this.canvas.contextTop,n=this.canvas.viewportTransform,f=this._points[0],o=this._points[1];e.save(),e.transform(n[0],n[1],n[2],n[3],n[4],n[5]),e.beginPath(),2===this._points.length&&f.x===o.x&&f.y===o.y&&(f.x-=.5,o.x+=.5),e.moveTo(f.x,f.y);for(var d=1,i=this._points.length;i>d;d++){var t=f.midPointFrom(o);e.quadraticCurveTo(f.x,f.y,t.x,t.y),f=this._points[d],o=this._points[d+1]}e.lineTo(f.x,f.y),e.stroke(),e.restore()},_getSVGPathData:function(){return this.box=this.getPathBoundingBox(this._points),this.convertPointsToSVGPath(this._points,this.box.minX,this.box.minY)},getPathBoundingBox:function(f){for(var o=[],d=[],i=f[0],t=f[1],l=i,s=1,u=f.length;u>s;s++){var a=i.midPointFrom(t);o.push(l.x),o.push(a.x),d.push(l.y),d.push(a.y),i=f[s],t=f[s+1],l=a}return o.push(i.x),d.push(i.y),{minX:e(o),minY:e(d),maxX:n(o),maxY:n(d)}},convertPointsToSVGPath:function(e,n,f){var o=[],d=new fabric.Point(e[0].x-n,e[0].y-f),i=new fabric.Point(e[1].x-n,e[1].y-f);o.push("M ",e[0].x-n," ",e[0].y-f," ");for(var t=1,l=e.length;l>t;t++){var s=d.midPointFrom(i);o.push("Q ",d.x," ",d.y," ",s.x," ",s.y," "),d=new fabric.Point(e[t].x-n,e[t].y-f),t+1<e.length&&(i=new fabric.Point(e[t+1].x-n,e[t+1].y-f))}return o.push("L ",d.x," ",d.y," "),o},createPath:function(e){var n=new fabric.Path(e);return n.fill=null,n.stroke=this.color,n.strokeWidth=this.width,n.strokeLineCap=this.strokeLineCap,n.strokeLineJoin=this.strokeLineJoin,this.shadow&&(this.shadow.affectStroke=!0,n.setShadow(this.shadow)),n},_finalizeAndAddPath:function(){var e=this.canvas.contextTop;e.closePath();var n=this._getSVGPathData().join("");if("M 0 0 Q 0 0 0 0 L 0 0"===n)return void this.canvas.renderAll();var f=this.box.minX+(this.box.maxX-this.box.minX)/2,o=this.box.minY+(this.box.maxY-this.box.minY)/2;this.canvas.contextTop.arc(f,o,3,0,2*Math.PI,!1);var d=this.createPath(n);d.set({left:f,top:o,originX:"center",originY:"center"}),this.canvas.add(d),d.setCoords(),this.canvas.clearContext(this.canvas.contextTop),this._resetShadow(),this.canvas.renderAll(),this.canvas.fire("path:created",{path:d})}})}(),fabric.CircleBrush=fabric.util.createClass(fabric.BaseBrush,{width:10,initialize:function(e){this.canvas=e,this.points=[]},drawDot:function(e){var n=this.addPoint(e),f=this.canvas.contextTop,o=this.canvas.viewportTransform;f.save(),f.transform(o[0],o[1],o[2],o[3],o[4],o[5]),f.fillStyle=n.fill,f.beginPath(),f.arc(n.x,n.y,n.radius,0,2*Math.PI,!1),f.closePath(),f.fill(),f.restore()},onMouseDown:function(e){this.points.length=0,this.canvas.clearContext(this.canvas.contextTop),this._setShadow(),this.drawDot(e)},onMouseMove:function(e){this.drawDot(e)},onMouseUp:function(){var e=this.canvas.renderOnAddRemove;this.canvas.renderOnAddRemove=!1;for(var n=[],f=0,o=this.points.length;o>f;f++){var d=this.points[f],i=new fabric.Circle({radius:d.radius,left:d.x,top:d.y,originX:"center",originY:"center",fill:d.fill});this.shadow&&i.setShadow(this.shadow),n.push(i)}var t=new fabric.Group(n,{originX:"center",originY:"center"});t.canvas=this.canvas,this.canvas.add(t),this.canvas.fire("path:created",{path:t}),this.canvas.clearContext(this.canvas.contextTop),this._resetShadow(),this.canvas.renderOnAddRemove=e,this.canvas.renderAll()},addPoint:function(e){var n=new fabric.Point(e.x,e.y),f=fabric.util.getRandomInt(Math.max(0,this.width-20),this.width+20)/2,o=new fabric.Color(this.color).setAlpha(fabric.util.getRandomInt(0,100)/100).toRgba();return n.radius=f,n.fill=o,this.points.push(n),n}}),fabric.SprayBrush=fabric.util.createClass(fabric.BaseBrush,{width:10,density:20,dotWidth:1,dotWidthVariance:1,randomOpacity:!1,optimizeOverlapping:!0,initialize:function(e){this.canvas=e,this.sprayChunks=[]},onMouseDown:function(e){this.sprayChunks.length=0,this.canvas.clearContext(this.canvas.contextTop),this._setShadow(),this.addSprayChunk(e),this.render()},onMouseMove:function(e){this.addSprayChunk(e),this.render()},onMouseUp:function(){var e=this.canvas.renderOnAddRemove;this.canvas.renderOnAddRemove=!1;for(var n=[],f=0,o=this.sprayChunks.length;o>f;f++)for(var d=this.sprayChunks[f],i=0,t=d.length;t>i;i++){var l=new fabric.Rect({width:d[i].width,height:d[i].width,left:d[i].x+1,top:d[i].y+1,originX:"center",originY:"center",fill:this.color});this.shadow&&l.setShadow(this.shadow),n.push(l)}this.optimizeOverlapping&&(n=this._getOptimizedRects(n));var s=new fabric.Group(n,{originX:"center",originY:"center"});s.canvas=this.canvas,this.canvas.add(s),this.canvas.fire("path:created",{path:s}),this.canvas.clearContext(this.canvas.contextTop),this._resetShadow(),this.canvas.renderOnAddRemove=e,this.canvas.renderAll()},_getOptimizedRects:function(e){for(var n,f={},o=0,d=e.length;d>o;o++)n=e[o].left+""+e[o].top,f[n]||(f[n]=e[o]);var i=[];for(n in f)i.push(f[n]);return i},render:function(){var e=this.canvas.contextTop;e.fillStyle=this.color;var n=this.canvas.viewportTransform;e.save(),e.transform(n[0],n[1],n[2],n[3],n[4],n[5]);for(var f=0,o=this.sprayChunkPoints.length;o>f;f++){var d=this.sprayChunkPoints[f];"undefined"!=typeof d.opacity&&(e.globalAlpha=d.opacity),e.fillRect(d.x,d.y,d.width,d.width)}e.restore()},addSprayChunk:function(e){this.sprayChunkPoints=[];for(var n,f,o,d=this.width/2,i=0;i<this.density;i++){n=fabric.util.getRandomInt(e.x-d,e.x+d),f=fabric.util.getRandomInt(e.y-d,e.y+d),o=this.dotWidthVariance?fabric.util.getRandomInt(Math.max(1,this.dotWidth-this.dotWidthVariance),this.dotWidth+this.dotWidthVariance):this.dotWidth;var t=new fabric.Point(n,f);t.width=o,this.randomOpacity&&(t.opacity=fabric.util.getRandomInt(0,100)/100),this.sprayChunkPoints.push(t)}this.sprayChunks.push(this.sprayChunkPoints)}}),fabric.PatternBrush=fabric.util.createClass(fabric.PencilBrush,{getPatternSrc:function(){var e=20,n=5,f=fabric.document.createElement("canvas"),o=f.getContext("2d");return f.width=f.height=e+n,o.fillStyle=this.color,o.beginPath(),o.arc(e/2,e/2,e/2,0,2*Math.PI,!1),o.closePath(),o.fill(),f},getPatternSrcFunction:function(){return String(this.getPatternSrc).replace("this.color",'"'+this.color+'"')},getPattern:function(){return this.canvas.contextTop.createPattern(this.source||this.getPatternSrc(),"repeat")},_setBrushStyles:function(){this.callSuper("_setBrushStyles"),this.canvas.contextTop.strokeStyle=this.getPattern()},createPath:function(e){var n=this.callSuper("createPath",e);return n.stroke=new fabric.Pattern({source:this.source||this.getPatternSrcFunction()}),n}}),fabric.util.object.extend(fabric.StaticCanvas.prototype,{toDataURL:function(e){e||(e={});var n=e.format||"png",f=e.quality||1,o=e.multiplier||1,d={left:e.left,top:e.top,width:e.width,height:e.height};return 1!==o?this.__toDataURLWithMultiplier(n,f,d,o):this.__toDataURL(n,f,d)},__toDataURL:function(e,n,f){this.renderAll(!0);var o=this.upperCanvasEl||this.lowerCanvasEl,d=this.__getCroppedCanvas(o,f);"jpg"===e&&(e="jpeg");var i=fabric.StaticCanvas.supports("toDataURLWithQuality")?(d||o).toDataURL("image/"+e,n):(d||o).toDataURL("image/"+e);return this.contextTop&&this.clearContext(this.contextTop),this.renderAll(),d&&(d=null),i},__getCroppedCanvas:function(e,n){var f,o,d="left"in n||"top"in n||"width"in n||"height"in n;return d&&(f=fabric.util.createCanvasElement(),o=f.getContext("2d"),f.width=n.width||this.width,f.height=n.height||this.height,o.drawImage(e,-n.left||0,-n.top||0)),f},__toDataURLWithMultiplier:function(e,n,f,o){var d=this.getWidth(),i=this.getHeight(),t=d*o,l=i*o,s=this.getActiveObject(),u=this.getActiveGroup(),a=this.contextTop||this.contextContainer;o>1&&this.setWidth(t).setHeight(l),a.scale(o,o),f.left&&(f.left*=o),f.top&&(f.top*=o),f.width?f.width*=o:1>o&&(f.width=t),f.height?f.height*=o:1>o&&(f.height=l),u?this._tempRemoveBordersControlsFromGroup(u):s&&this.deactivateAll&&this.deactivateAll(),this.renderAll(!0);var p=this.__toDataURL(e,n,f);return this.width=d,this.height=i,a.scale(1/o,1/o),this.setWidth(d).setHeight(i),u?this._restoreBordersControlsOnGroup(u):s&&this.setActiveObject&&this.setActiveObject(s),this.contextTop&&this.clearContext(this.contextTop),this.renderAll(),p},toDataURLWithMultiplier:function(e,n,f){return this.toDataURL({format:e,multiplier:n,quality:f})},_tempRemoveBordersControlsFromGroup:function(e){e.origHasControls=e.hasControls,e.origBorderColor=e.borderColor,e.hasControls=!0,e.borderColor="rgba(0,0,0,0)",e.forEachObject(function(e){e.origBorderColor=e.borderColor,e.borderColor="rgba(0,0,0,0)"})},_restoreBordersControlsOnGroup:function(e){e.hideControls=e.origHideControls,e.borderColor=e.origBorderColor,e.forEachObject(function(e){e.borderColor=e.origBorderColor,delete e.origBorderColor})}}),fabric.util.object.extend(fabric.StaticCanvas.prototype,{loadFromDatalessJSON:function(e,n,f){return this.loadFromJSON(e,n,f)},loadFromJSON:function(e,n,f){if(e){var o="string"==typeof e?JSON.parse(e):e;this.clear();var d=this;return this._enlivenObjects(o.objects,function(){d._setBgOverlay(o,n)},f),this}},_setBgOverlay:function(e,n){var f=this,o={backgroundColor:!1,overlayColor:!1,backgroundImage:!1,overlayImage:!1};if(!(e.backgroundImage||e.overlayImage||e.background||e.overlay))return void(n&&n());var d=function(){o.backgroundImage&&o.overlayImage&&o.backgroundColor&&o.overlayColor&&(f.renderAll(),n&&n())};this.__setBgOverlay("backgroundImage",e.backgroundImage,o,d),this.__setBgOverlay("overlayImage",e.overlayImage,o,d),this.__setBgOverlay("backgroundColor",e.background,o,d),this.__setBgOverlay("overlayColor",e.overlay,o,d),d()},__setBgOverlay:function(e,n,f,o){var d=this;return n?void("backgroundImage"===e||"overlayImage"===e?fabric.Image.fromObject(n,function(n){d[e]=n,f[e]=!0,o&&o()}):this["set"+fabric.util.string.capitalize(e,!0)](n,function(){f[e]=!0,o&&o()})):void(f[e]=!0)},_enlivenObjects:function(e,n,f){var o=this;if(!e||0===e.length)return void(n&&n());var d=this.renderOnAddRemove;this.renderOnAddRemove=!1,fabric.util.enlivenObjects(e,function(e){e.forEach(function(e,n){o.insertAt(e,n,!0)}),o.renderOnAddRemove=d,n&&n()},null,f)},_toDataURL:function(e,n){this.clone(function(f){n(f.toDataURL(e))})},_toDataURLWithMultiplier:function(e,n,f){this.clone(function(o){f(o.toDataURLWithMultiplier(e,n))})},clone:function(e,n){var f=JSON.stringify(this.toJSON(n));this.cloneWithoutData(function(n){n.loadFromJSON(f,function(){e&&e(n)})})},cloneWithoutData:function(e){var n=fabric.document.createElement("canvas");n.width=this.getWidth(),n.height=this.getHeight();var f=new fabric.Canvas(n);f.clipTo=this.clipTo,this.backgroundImage?(f.setBackgroundImage(this.backgroundImage.src,function(){f.renderAll(),e&&e(f)}),f.backgroundImageOpacity=this.backgroundImageOpacity,f.backgroundImageStretch=this.backgroundImageStretch):e&&e(f)}}),function(e){"use strict";var n=e.fabric||(e.fabric={}),f=n.util.object.extend,o=n.util.toFixed,d=n.util.string.capitalize,i=n.util.degreesToRadians,t=n.StaticCanvas.supports("setLineDash");n.Object||(n.Object=n.util.createClass({type:"object",originX:"left",originY:"top",top:0,left:0,width:0,height:0,scaleX:1,scaleY:1,flipX:!1,flipY:!1,opacity:1,angle:0,cornerSize:12,transparentCorners:!0,hoverCursor:null,padding:0,borderColor:"rgba(102,153,255,0.75)",cornerColor:"rgba(102,153,255,0.5)",centeredScaling:!1,centeredRotation:!0,fill:"rgb(0,0,0)",fillRule:"source-over",backgroundColor:"",stroke:null,strokeWidth:1,strokeDashArray:null,strokeLineCap:"butt",strokeLineJoin:"miter",strokeMiterLimit:10,shadow:null,borderOpacityWhenMoving:.4,borderScaleFactor:1,transformMatrix:null,minScaleLimit:.01,selectable:!0,evented:!0,visible:!0,hasControls:!0,hasBorders:!0,hasRotatingPoint:!0,rotatingPointOffset:40,perPixelTargetFind:!1,includeDefaultValues:!0,clipTo:null,lockMovementX:!1,lockMovementY:!1,lockRotation:!1,lockScalingX:!1,lockScalingY:!1,lockUniScaling:!1,lockScalingFlip:!1,stateProperties:"top left width height scaleX scaleY flipX flipY originX originY transformMatrix stroke strokeWidth strokeDashArray strokeLineCap strokeLineJoin strokeMiterLimit angle opacity fill fillRule shadow clipTo visible backgroundColor".split(" "),initialize:function(e){e&&this.setOptions(e)},_initGradient:function(e){!e.fill||!e.fill.colorStops||e.fill instanceof n.Gradient||this.set("fill",new n.Gradient(e.fill))},_initPattern:function(e){!e.fill||!e.fill.source||e.fill instanceof n.Pattern||this.set("fill",new n.Pattern(e.fill)),!e.stroke||!e.stroke.source||e.stroke instanceof n.Pattern||this.set("stroke",new n.Pattern(e.stroke))},_initClipping:function(e){if(e.clipTo&&"string"==typeof e.clipTo){var f=n.util.getFunctionBody(e.clipTo);"undefined"!=typeof f&&(this.clipTo=new Function("ctx",f))}},setOptions:function(e){for(var n in e)this.set(n,e[n]);this._initGradient(e),this._initPattern(e),this._initClipping(e)},transform:function(e,n){this.group&&this.group.transform(e,n),e.globalAlpha=this.opacity;var f=n?this._getLeftTopCoords():this.getCenterPoint();e.translate(f.x,f.y),e.rotate(i(this.angle)),e.scale(this.scaleX*(this.flipX?-1:1),this.scaleY*(this.flipY?-1:1))},toObject:function(e){var f=n.Object.NUM_FRACTION_DIGITS,d={type:this.type,originX:this.originX,originY:this.originY,left:o(this.left,f),top:o(this.top,f),width:o(this.width,f),height:o(this.height,f),fill:this.fill&&this.fill.toObject?this.fill.toObject():this.fill,stroke:this.stroke&&this.stroke.toObject?this.stroke.toObject():this.stroke,strokeWidth:o(this.strokeWidth,f),strokeDashArray:this.strokeDashArray,strokeLineCap:this.strokeLineCap,strokeLineJoin:this.strokeLineJoin,strokeMiterLimit:o(this.strokeMiterLimit,f),scaleX:o(this.scaleX,f),scaleY:o(this.scaleY,f),angle:o(this.getAngle(),f),flipX:this.flipX,flipY:this.flipY,opacity:o(this.opacity,f),shadow:this.shadow&&this.shadow.toObject?this.shadow.toObject():this.shadow,visible:this.visible,clipTo:this.clipTo&&String(this.clipTo),backgroundColor:this.backgroundColor};return this.includeDefaultValues||(d=this._removeDefaultValues(d)),n.util.populateWithProperties(this,d,e),d},toDatalessObject:function(e){return this.toObject(e)},_removeDefaultValues:function(e){var f=n.util.getKlass(e.type).prototype,o=f.stateProperties;return o.forEach(function(n){e[n]===f[n]&&delete e[n]}),e},toString:function(){return"#<fabric."+d(this.type)+">"},get:function(e){return this[e]},_setObject:function(e){for(var n in e)this._set(n,e[n])},set:function(e,n){return"object"==typeof e?this._setObject(e):"function"==typeof n&&"clipTo"!==e?this._set(e,n(this.get(e))):this._set(e,n),this},_set:function(e,f){var d="scaleX"===e||"scaleY"===e;return d&&(f=this._constrainScale(f)),"scaleX"===e&&0>f?(this.flipX=!this.flipX,f*=-1):"scaleY"===e&&0>f?(this.flipY=!this.flipY,f*=-1):"width"===e||"height"===e?this.minScaleLimit=o(Math.min(.1,1/Math.max(this.width,this.height)),2):"shadow"!==e||!f||f instanceof n.Shadow||(f=new n.Shadow(f)),this[e]=f,this},toggle:function(e){var n=this.get(e);return"boolean"==typeof n&&this.set(e,!n),this},setSourcePath:function(e){return this.sourcePath=e,this},getViewportTransform:function(){return this.canvas&&this.canvas.viewportTransform?this.canvas.viewportTransform:[1,0,0,1,0,0]},render:function(e,f){if(0!==this.width&&0!==this.height&&this.visible){if(e.save(),this._setupFillRule(e),this._transform(e,f),this._setStrokeStyles(e),this._setFillStyles(e),this.group&&"path-group"===this.group.type){e.translate(-this.group.width/2,-this.group.height/2);var o=this.transformMatrix;o&&e.transform.apply(e,o)}e.globalAlpha=this.group?e.globalAlpha*this.opacity:this.opacity,this._setShadow(e),this.clipTo&&n.util.clipContext(this,e),this._render(e,f),this.clipTo&&e.restore(),this._removeShadow(e),this._restoreFillRule(e),e.restore()}},_transform:function(e,n){var f=this.transformMatrix;f&&!this.group&&e.setTransform.apply(e,f),n||this.transform(e)},_setStrokeStyles:function(e){this.stroke&&(e.lineWidth=this.strokeWidth,e.lineCap=this.strokeLineCap,e.lineJoin=this.strokeLineJoin,e.miterLimit=this.strokeMiterLimit,e.strokeStyle=this.stroke.toLive?this.stroke.toLive(e):this.stroke)},_setFillStyles:function(e){this.fill&&(e.fillStyle=this.fill.toLive?this.fill.toLive(e):this.fill)},_renderControls:function(e,f){var o=this.getViewportTransform();if(e.save(),this.active&&!f){var d;this.group&&(d=n.util.transformPoint(this.group.getCenterPoint(),o),e.translate(d.x,d.y),e.rotate(i(this.group.angle))),d=n.util.transformPoint(this.getCenterPoint(),o,null!=this.group),this.group&&(d.x*=this.group.scaleX,d.y*=this.group.scaleY),e.translate(d.x,d.y),e.rotate(i(this.angle)),this.drawBorders(e),this.drawControls(e)}e.restore()},_setShadow:function(e){this.shadow&&(e.shadowColor=this.shadow.color,e.shadowBlur=this.shadow.blur,e.shadowOffsetX=this.shadow.offsetX,e.shadowOffsetY=this.shadow.offsetY)},_removeShadow:function(e){this.shadow&&(e.shadowColor="",e.shadowBlur=e.shadowOffsetX=e.shadowOffsetY=0)},_renderFill:function(e){if(this.fill){if(e.save(),this.fill.toLive&&e.translate(-this.width/2+this.fill.offsetX||0,-this.height/2+this.fill.offsetY||0),this.fill.gradientTransform){var n=this.fill.gradientTransform;e.transform.apply(e,n)}"destination-over"===this.fillRule?e.fill("evenodd"):e.fill(),e.restore(),this.shadow&&!this.shadow.affectStroke&&this._removeShadow(e)}},_renderStroke:function(e){if(this.stroke&&0!==this.strokeWidth){if(e.save(),this.strokeDashArray)1&this.strokeDashArray.length&&this.strokeDashArray.push.apply(this.strokeDashArray,this.strokeDashArray),t?(e.setLineDash(this.strokeDashArray),this._stroke&&this._stroke(e)):this._renderDashedStroke&&this._renderDashedStroke(e),e.stroke();else{if(this.stroke.gradientTransform){var n=this.stroke.gradientTransform;e.transform.apply(e,n)}this._stroke?this._stroke(e):e.stroke()}this._removeShadow(e),e.restore()}},clone:function(e,f){return this.constructor.fromObject?this.constructor.fromObject(this.toObject(f),e):new n.Object(this.toObject(f))},cloneAsImage:function(e){var f=this.toDataURL();return n.util.loadImage(f,function(f){e&&e(new n.Image(f))}),this},toDataURL:function(e){e||(e={});var f=n.util.createCanvasElement(),o=this.getBoundingRect();f.width=o.width,f.height=o.height,n.util.wrapElement(f,"div");var d=new n.Canvas(f);"jpg"===e.format&&(e.format="jpeg"),"jpeg"===e.format&&(d.backgroundColor="#fff");var i={active:this.get("active"),left:this.getLeft(),top:this.getTop()};this.set("active",!1),this.setPositionByOrigin(new n.Point(f.width/2,f.height/2),"center","center");var t=this.canvas;d.add(this);var l=d.toDataURL(e);return this.set(i).setCoords(),this.canvas=t,d.dispose(),d=null,l},isType:function(e){return this.type===e},complexity:function(){return 0},toJSON:function(e){return this.toObject(e)},setGradient:function(e,f){f||(f={});var o={colorStops:[]};o.type=f.type||(f.r1||f.r2?"radial":"linear"),o.coords={x1:f.x1,y1:f.y1,x2:f.x2,y2:f.y2},(f.r1||f.r2)&&(o.coords.r1=f.r1,o.coords.r2=f.r2);for(var d in f.colorStops){var i=new n.Color(f.colorStops[d]);o.colorStops.push({offset:d,color:i.toRgb(),opacity:i.getAlpha()})}return this.set(e,n.Gradient.forObject(this,o))},setPatternFill:function(e){return this.set("fill",new n.Pattern(e))},setShadow:function(e){return this.set("shadow",e?new n.Shadow(e):null)},setColor:function(e){return this.set("fill",e),this},setAngle:function(e){var n=("center"!==this.originX||"center"!==this.originY)&&this.centeredRotation;return n&&this._setOriginToCenter(),this.set("angle",e),n&&this._resetOrigin(),this},centerH:function(){return this.canvas.centerObjectH(this),this},centerV:function(){return this.canvas.centerObjectV(this),this},center:function(){return this.canvas.centerObject(this),this},remove:function(){return this.canvas.remove(this),this},getLocalPointer:function(e,n){n=n||this.canvas.getPointer(e);var f=this.translateToOriginPoint(this.getCenterPoint(),"left","top");return{x:n.x-f.x,y:n.y-f.y}},_setupFillRule:function(e){this.fillRule&&(this._prevFillRule=e.globalCompositeOperation,e.globalCompositeOperation=this.fillRule)},_restoreFillRule:function(e){this.fillRule&&this._prevFillRule&&(e.globalCompositeOperation=this._prevFillRule)}}),n.util.createAccessors(n.Object),n.Object.prototype.rotate=n.Object.prototype.setAngle,f(n.Object.prototype,n.Observable),n.Object.NUM_FRACTION_DIGITS=2,n.Object.__uid=0)}("undefined"!=typeof exports?exports:this),function(){var e=fabric.util.degreesToRadians;fabric.util.object.extend(fabric.Object.prototype,{translateToCenterPoint:function(n,f,o){var d=n.x,i=n.y,t=this.stroke?this.strokeWidth:0;return"left"===f?d=n.x+(this.getWidth()+t*this.scaleX)/2:"right"===f&&(d=n.x-(this.getWidth()+t*this.scaleX)/2),"top"===o?i=n.y+(this.getHeight()+t*this.scaleY)/2:"bottom"===o&&(i=n.y-(this.getHeight()+t*this.scaleY)/2),fabric.util.rotatePoint(new fabric.Point(d,i),n,e(this.angle))},translateToOriginPoint:function(n,f,o){var d=n.x,i=n.y,t=this.stroke?this.strokeWidth:0;return"left"===f?d=n.x-(this.getWidth()+t*this.scaleX)/2:"right"===f&&(d=n.x+(this.getWidth()+t*this.scaleX)/2),"top"===o?i=n.y-(this.getHeight()+t*this.scaleY)/2:"bottom"===o&&(i=n.y+(this.getHeight()+t*this.scaleY)/2),fabric.util.rotatePoint(new fabric.Point(d,i),n,e(this.angle))},getCenterPoint:function(){var e=new fabric.Point(this.left,this.top);return this.translateToCenterPoint(e,this.originX,this.originY)},getPointByOrigin:function(e,n){var f=this.getCenterPoint();return this.translateToOriginPoint(f,e,n)},toLocalPoint:function(n,f,o){var d,i,t=this.getCenterPoint(),l=this.stroke?this.strokeWidth:0;return f&&o?(d="left"===f?t.x-(this.getWidth()+l*this.scaleX)/2:"right"===f?t.x+(this.getWidth()+l*this.scaleX)/2:t.x,i="top"===o?t.y-(this.getHeight()+l*this.scaleY)/2:"bottom"===o?t.y+(this.getHeight()+l*this.scaleY)/2:t.y):(d=this.left,i=this.top),fabric.util.rotatePoint(new fabric.Point(n.x,n.y),t,-e(this.angle)).subtractEquals(new fabric.Point(d,i))},setPositionByOrigin:function(e,n,f){var o=this.translateToCenterPoint(e,n,f),d=this.translateToOriginPoint(o,this.originX,this.originY);this.set("left",d.x),this.set("top",d.y)},adjustPosition:function(n){var f=e(this.angle),o=this.getWidth()/2,d=Math.cos(f)*o,i=Math.sin(f)*o,t=this.getWidth(),l=Math.cos(f)*t,s=Math.sin(f)*t;"center"===this.originX&&"left"===n||"right"===this.originX&&"center"===n?(this.left-=d,this.top-=i):"left"===this.originX&&"center"===n||"center"===this.originX&&"right"===n?(this.left+=d,this.top+=i):"left"===this.originX&&"right"===n?(this.left+=l,this.top+=s):"right"===this.originX&&"left"===n&&(this.left-=l,this.top-=s),this.setCoords(),this.originX=n},_setOriginToCenter:function(){this._originalOriginX=this.originX,this._originalOriginY=this.originY;var e=this.getCenterPoint();this.originX="center",this.originY="center",this.left=e.x,this.top=e.y},_resetOrigin:function(){var e=this.translateToOriginPoint(this.getCenterPoint(),this._originalOriginX,this._originalOriginY);this.originX=this._originalOriginX,this.originY=this._originalOriginY,this.left=e.x,this.top=e.y,this._originalOriginX=null,this._originalOriginY=null},_getLeftTopCoords:function(){return this.translateToOriginPoint(this.getCenterPoint(),"left","center")}})}(),function(){var e=fabric.util.degreesToRadians;fabric.util.object.extend(fabric.Object.prototype,{oCoords:null,intersectsWithRect:function(e,n){var f=this.oCoords,o=new fabric.Point(f.tl.x,f.tl.y),d=new fabric.Point(f.tr.x,f.tr.y),i=new fabric.Point(f.bl.x,f.bl.y),t=new fabric.Point(f.br.x,f.br.y),l=fabric.Intersection.intersectPolygonRectangle([o,d,t,i],e,n);return"Intersection"===l.status},intersectsWithObject:function(e){function n(e){return{tl:new fabric.Point(e.tl.x,e.tl.y),tr:new fabric.Point(e.tr.x,e.tr.y),bl:new fabric.Point(e.bl.x,e.bl.y),br:new fabric.Point(e.br.x,e.br.y)}}var f=n(this.oCoords),o=n(e.oCoords),d=fabric.Intersection.intersectPolygonPolygon([f.tl,f.tr,f.br,f.bl],[o.tl,o.tr,o.br,o.bl]);return"Intersection"===d.status},isContainedWithinObject:function(e){var n=e.getBoundingRect(),f=new fabric.Point(n.left,n.top),o=new fabric.Point(n.left+n.width,n.top+n.height);return this.isContainedWithinRect(f,o)},isContainedWithinRect:function(e,n){var f=this.getBoundingRect();return f.left>=e.x&&f.left+f.width<=n.x&&f.top>=e.y&&f.top+f.height<=n.y},containsPoint:function(e){var n=this._getImageLines(this.oCoords),f=this._findCrossPoints(e,n);return 0!==f&&f%2===1},_getImageLines:function(e){return{topline:{o:e.tl,d:e.tr},rightline:{o:e.tr,d:e.br},bottomline:{o:e.br,d:e.bl},leftline:{o:e.bl,d:e.tl}}},_findCrossPoints:function(e,n){var f,o,d,i,t,l,s,u=0;for(var a in n)if(s=n[a],!(s.o.y<e.y&&s.d.y<e.y||s.o.y>=e.y&&s.d.y>=e.y||(s.o.x===s.d.x&&s.o.x>=e.x?(t=s.o.x,l=e.y):(f=0,o=(s.d.y-s.o.y)/(s.d.x-s.o.x),d=e.y-f*e.x,i=s.o.y-o*s.o.x,t=-(d-i)/(f-o),l=d+f*t),t>=e.x&&(u+=1),2!==u)))break;return u},getBoundingRectWidth:function(){return this.getBoundingRect().width},getBoundingRectHeight:function(){return this.getBoundingRect().height},getBoundingRect:function(){this.oCoords||this.setCoords();var e=[this.oCoords.tl.x,this.oCoords.tr.x,this.oCoords.br.x,this.oCoords.bl.x],n=fabric.util.array.min(e),f=fabric.util.array.max(e),o=Math.abs(n-f),d=[this.oCoords.tl.y,this.oCoords.tr.y,this.oCoords.br.y,this.oCoords.bl.y],i=fabric.util.array.min(d),t=fabric.util.array.max(d),l=Math.abs(i-t);return{left:n,top:i,width:o,height:l}},getWidth:function(){return this.width*this.scaleX},getHeight:function(){return this.height*this.scaleY},_constrainScale:function(e){return Math.abs(e)<this.minScaleLimit?0>e?-this.minScaleLimit:this.minScaleLimit:e},scale:function(e){return e=this._constrainScale(e),0>e&&(this.flipX=!this.flipX,this.flipY=!this.flipY,e*=-1),this.scaleX=e,this.scaleY=e,this.setCoords(),this},scaleToWidth:function(e){var n=this.getBoundingRectWidth()/this.getWidth();return this.scale(e/this.width/n)},scaleToHeight:function(e){var n=this.getBoundingRectHeight()/this.getHeight();return this.scale(e/this.height/n)},setCoords:function(){var n=this.strokeWidth>1?this.strokeWidth:0,f=e(this.angle),o=this.getViewportTransform(),d=function(e){return fabric.util.transformPoint(e,o)},i=this.width,t=this.height,l="round"===this.strokeLineCap||"square"===this.strokeLineCap,s="line"===this.type&&1===this.width,u="line"===this.type&&1===this.height,a=l&&u||"line"!==this.type,p=l&&s||"line"!==this.type;s?i=n:u&&(t=n),a&&(i+=n),p&&(t+=n),this.currentWidth=i*this.scaleX,this.currentHeight=t*this.scaleY,this.currentWidth<0&&(this.currentWidth=Math.abs(this.currentWidth));var c=Math.sqrt(Math.pow(this.currentWidth/2,2)+Math.pow(this.currentHeight/2,2)),y=Math.atan(isFinite(this.currentHeight/this.currentWidth)?this.currentHeight/this.currentWidth:0),m=Math.cos(y+f)*c,r=Math.sin(y+f)*c,v=Math.sin(f),w=Math.cos(f),b=this.getCenterPoint(),g=new fabric.Point(this.currentWidth,this.currentHeight),h=new fabric.Point(b.x-m,b.y-r),x=new fabric.Point(h.x+g.x*w,h.y+g.x*v),j=new fabric.Point(h.x-g.y*v,h.y+g.y*w),k=new fabric.Point(h.x+g.x/2*w,h.y+g.x/2*v),q=d(h),z=d(x),A=d(new fabric.Point(x.x-g.y*v,x.y+g.y*w)),B=d(j),C=d(new fabric.Point(h.x-g.y/2*v,h.y+g.y/2*w)),D=d(k),E=d(new fabric.Point(x.x-g.y/2*v,x.y+g.y/2*w)),F=d(new fabric.Point(j.x+g.x/2*w,j.y+g.x/2*v)),G=d(new fabric.Point(k.x,k.y)),H=Math.cos(y+f)*this.padding*Math.sqrt(2),I=Math.sin(y+f)*this.padding*Math.sqrt(2);return q=q.add(new fabric.Point(-H,-I)),z=z.add(new fabric.Point(I,-H)),A=A.add(new fabric.Point(H,I)),B=B.add(new fabric.Point(-I,H)),C=C.add(new fabric.Point((-H-I)/2,(-I+H)/2)),D=D.add(new fabric.Point((I-H)/2,-(I+H)/2)),E=E.add(new fabric.Point((I+H)/2,(I-H)/2)),F=F.add(new fabric.Point((H-I)/2,(H+I)/2)),G=G.add(new fabric.Point((I-H)/2,-(I+H)/2)),this.oCoords={tl:q,tr:z,br:A,bl:B,ml:C,mt:D,mr:E,mb:F,mtr:G},this._setCornerCoords&&this._setCornerCoords(),this}})}(),fabric.util.object.extend(fabric.Object.prototype,{sendToBack:function(){return this.group?fabric.StaticCanvas.prototype.sendToBack.call(this.group,this):this.canvas.sendToBack(this),this},bringToFront:function(){return this.group?fabric.StaticCanvas.prototype.bringToFront.call(this.group,this):this.canvas.bringToFront(this),this},sendBackwards:function(e){return this.group?fabric.StaticCanvas.prototype.sendBackwards.call(this.group,this,e):this.canvas.sendBackwards(this,e),this},bringForward:function(e){return this.group?fabric.StaticCanvas.prototype.bringForward.call(this.group,this,e):this.canvas.bringForward(this,e),this},moveTo:function(e){return this.group?fabric.StaticCanvas.prototype.moveTo.call(this.group,this,e):this.canvas.moveTo(this,e),this}}),fabric.util.object.extend(fabric.Object.prototype,{getSvgStyles:function(){var e=this.fill?this.fill.toLive?"url(#SVGID_"+this.fill.id+")":this.fill:"none",n="destination-over"===this.fillRule?"evenodd":this.fillRule,f=this.stroke?this.stroke.toLive?"url(#SVGID_"+this.stroke.id+")":this.stroke:"none",o=this.strokeWidth?this.strokeWidth:"0",d=this.strokeDashArray?this.strokeDashArray.join(" "):"",i=this.strokeLineCap?this.strokeLineCap:"butt",t=this.strokeLineJoin?this.strokeLineJoin:"miter",l=this.strokeMiterLimit?this.strokeMiterLimit:"4",s="undefined"!=typeof this.opacity?this.opacity:"1",u=this.visible?"":" visibility: hidden;",a=this.shadow&&"text"!==this.type?"filter: url(#SVGID_"+this.shadow.id+");":"";return["stroke: ",f,"; ","stroke-width: ",o,"; ","stroke-dasharray: ",d,"; ","stroke-linecap: ",i,"; ","stroke-linejoin: ",t,"; ","stroke-miterlimit: ",l,"; ","fill: ",e,"; ","fill-rule: ",n,"; ","opacity: ",s,";",a,u].join("")},getSvgTransform:function(){if(this.group)return"";var e=fabric.util.toFixed,n=this.getAngle(),f=!this.canvas||this.canvas.svgViewportTransformation?this.getViewportTransform():[1,0,0,1,0,0],o=fabric.util.transformPoint(this.getCenterPoint(),f),d=fabric.Object.NUM_FRACTION_DIGITS,i="path-group"===this.type?"":"translate("+e(o.x,d)+" "+e(o.y,d)+")",t=0!==n?" rotate("+e(n,d)+")":"",l=1===this.scaleX&&1===this.scaleY&&1===f[0]&&1===f[3]?"":" scale("+e(this.scaleX*f[0],d)+" "+e(this.scaleY*f[3],d)+")",s="path-group"===this.type?this.width*f[0]:0,u=this.flipX?" matrix(-1 0 0 1 "+s+" 0) ":"",a="path-group"===this.type?this.height*f[3]:0,p=this.flipY?" matrix(1 0 0 -1 0 "+a+")":"";
return[i,t,l,u,p].join("")},getSvgTransformMatrix:function(){return this.transformMatrix?" matrix("+this.transformMatrix.join(" ")+")":""},_createBaseSVGMarkup:function(){var e=[];return this.fill&&this.fill.toLive&&e.push(this.fill.toSVG(this,!1)),this.stroke&&this.stroke.toLive&&e.push(this.stroke.toSVG(this,!1)),this.shadow&&e.push(this.shadow.toSVG(this)),e}}),fabric.util.object.extend(fabric.Object.prototype,{hasStateChanged:function(){return this.stateProperties.some(function(e){return this.get(e)!==this.originalState[e]},this)},saveState:function(e){return this.stateProperties.forEach(function(e){this.originalState[e]=this.get(e)},this),e&&e.stateProperties&&e.stateProperties.forEach(function(e){this.originalState[e]=this.get(e)},this),this},setupState:function(){return this.originalState={},this.saveState(),this}}),function(e){"use strict";function n(e,n){var f=e.origin,o=e.axis1,d=e.axis2,i=e.dimension,t=n.nearest,l=n.center,s=n.farthest;return function(){switch(this.get(f)){case t:return Math.min(this.get(o),this.get(d));case l:return Math.min(this.get(o),this.get(d))+.5*this.get(i);case s:return Math.max(this.get(o),this.get(d))}}}var f=e.fabric||(e.fabric={}),o=f.util.object.extend,d={x1:1,x2:1,y1:1,y2:1},i=f.StaticCanvas.supports("setLineDash");return f.Line?void f.warn("fabric.Line is already defined"):(f.Line=f.util.createClass(f.Object,{type:"line",x1:0,y1:0,x2:0,y2:0,initialize:function(e,n){n=n||{},e||(e=[0,0,0,0]),this.callSuper("initialize",n),this.set("x1",e[0]),this.set("y1",e[1]),this.set("x2",e[2]),this.set("y2",e[3]),this._setWidthHeight(n)},_setWidthHeight:function(e){e||(e={}),this.width=Math.abs(this.x2-this.x1)||1,this.height=Math.abs(this.y2-this.y1)||1,this.left="left"in e?e.left:this._getLeftToOriginX(),this.top="top"in e?e.top:this._getTopToOriginY()},_set:function(e,n){return this[e]=n,"undefined"!=typeof d[e]&&this._setWidthHeight(),this},_getLeftToOriginX:n({origin:"originX",axis1:"x1",axis2:"x2",dimension:"width"},{nearest:"left",center:"center",farthest:"right"}),_getTopToOriginY:n({origin:"originY",axis1:"y1",axis2:"y2",dimension:"height"},{nearest:"top",center:"center",farthest:"bottom"}),_render:function(e,n){if(e.beginPath(),n){var f=this.getCenterPoint();e.translate(f.x,f.y)}if(!this.strokeDashArray||this.strokeDashArray&&i){var o=this.x1<=this.x2?-1:1,d=this.y1<=this.y2?-1:1;e.moveTo(1===this.width?0:o*this.width/2,1===this.height?0:d*this.height/2),e.lineTo(1===this.width?0:-1*o*this.width/2,1===this.height?0:-1*d*this.height/2)}e.lineWidth=this.strokeWidth;var t=e.strokeStyle;e.strokeStyle=this.stroke||e.fillStyle,this.stroke&&this._renderStroke(e),e.strokeStyle=t},_renderDashedStroke:function(e){var n=this.x1<=this.x2?-1:1,o=this.y1<=this.y2?-1:1,d=1===this.width?0:n*this.width/2,i=1===this.height?0:o*this.height/2;e.beginPath(),f.util.drawDashedLine(e,d,i,-d,-i,this.strokeDashArray),e.closePath()},toObject:function(e){return o(this.callSuper("toObject",e),{x1:this.get("x1"),y1:this.get("y1"),x2:this.get("x2"),y2:this.get("y2")})},toSVG:function(e){var n=this._createBaseSVGMarkup(),f="";if(!this.group){var o=-this.width/2-(this.x1>this.x2?this.x2:this.x1),d=-this.height/2-(this.y1>this.y2?this.y2:this.y1);f="translate("+o+", "+d+") "}return n.push("<line ",'x1="',this.x1,'" y1="',this.y1,'" x2="',this.x2,'" y2="',this.y2,'" style="',this.getSvgStyles(),'" transform="',this.getSvgTransform(),f,this.getSvgTransformMatrix(),'"/>\n'),e?e(n.join("")):n.join("")},complexity:function(){return 1}}),f.Line.ATTRIBUTE_NAMES=f.SHARED_ATTRIBUTES.concat("x1 y1 x2 y2".split(" ")),f.Line.fromElement=function(e,n){var d=f.parseAttributes(e,f.Line.ATTRIBUTE_NAMES),i=[d.x1||0,d.y1||0,d.x2||0,d.y2||0];return new f.Line(i,o(d,n))},void(f.Line.fromObject=function(e){var n=[e.x1,e.y1,e.x2,e.y2];return new f.Line(n,e)}))}("undefined"!=typeof exports?exports:this),function(e){"use strict";function n(e){return"radius"in e&&e.radius>0}var f=e.fabric||(e.fabric={}),o=2*Math.PI,d=f.util.object.extend;return f.Circle?void f.warn("fabric.Circle is already defined."):(f.Circle=f.util.createClass(f.Object,{type:"circle",radius:0,initialize:function(e){e=e||{},this.callSuper("initialize",e),this.set("radius",e.radius||0)},_set:function(e,n){return this.callSuper("_set",e,n),"radius"===e&&this.setRadius(n),this},toObject:function(e){return d(this.callSuper("toObject",e),{radius:this.get("radius")})},toSVG:function(e){var n=this._createBaseSVGMarkup(),f=0,o=0;return this.group&&(f=this.left+this.radius,o=this.top+this.radius),n.push("<circle ",'cx="'+f+'" cy="'+o+'" ','r="',this.radius,'" style="',this.getSvgStyles(),'" transform="',this.getSvgTransform()," ",this.getSvgTransformMatrix(),'"/>\n'),e?e(n.join("")):n.join("")},_render:function(e,n){e.beginPath(),e.arc(n?this.left+this.radius:0,n?this.top+this.radius:0,this.radius,0,o,!1),this._renderFill(e),this._renderStroke(e)},getRadiusX:function(){return this.get("radius")*this.get("scaleX")},getRadiusY:function(){return this.get("radius")*this.get("scaleY")},setRadius:function(e){this.radius=e,this.set("width",2*e).set("height",2*e)},complexity:function(){return 1}}),f.Circle.ATTRIBUTE_NAMES=f.SHARED_ATTRIBUTES.concat("cx cy r".split(" ")),f.Circle.fromElement=function(e,o){o||(o={});var i=f.parseAttributes(e,f.Circle.ATTRIBUTE_NAMES);if(!n(i))throw new Error("value of `r` attribute is required and can not be negative");i.left=i.left||0,i.top=i.top||0;var t=new f.Circle(d(i,o));return t.left-=t.radius,t.top-=t.radius,t},void(f.Circle.fromObject=function(e){return new f.Circle(e)}))}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={});return n.Triangle?void n.warn("fabric.Triangle is already defined"):(n.Triangle=n.util.createClass(n.Object,{type:"triangle",initialize:function(e){e=e||{},this.callSuper("initialize",e),this.set("width",e.width||100).set("height",e.height||100)},_render:function(e){var n=this.width/2,f=this.height/2;e.beginPath(),e.moveTo(-n,f),e.lineTo(0,-f),e.lineTo(n,f),e.closePath(),this._renderFill(e),this._renderStroke(e)},_renderDashedStroke:function(e){var f=this.width/2,o=this.height/2;e.beginPath(),n.util.drawDashedLine(e,-f,o,0,-o,this.strokeDashArray),n.util.drawDashedLine(e,0,-o,f,o,this.strokeDashArray),n.util.drawDashedLine(e,f,o,-f,o,this.strokeDashArray),e.closePath()},toSVG:function(e){var n=this._createBaseSVGMarkup(),f=this.width/2,o=this.height/2,d=[-f+" "+o,"0 "+-o,f+" "+o].join(",");return n.push("<polygon ",'points="',d,'" style="',this.getSvgStyles(),'" transform="',this.getSvgTransform(),'"/>'),e?e(n.join("")):n.join("")},complexity:function(){return 1}}),void(n.Triangle.fromObject=function(e){return new n.Triangle(e)}))}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={}),f=2*Math.PI,o=n.util.object.extend;return n.Ellipse?void n.warn("fabric.Ellipse is already defined."):(n.Ellipse=n.util.createClass(n.Object,{type:"ellipse",rx:0,ry:0,initialize:function(e){e=e||{},this.callSuper("initialize",e),this.set("rx",e.rx||0),this.set("ry",e.ry||0),this.set("width",2*this.get("rx")),this.set("height",2*this.get("ry"))},toObject:function(e){return o(this.callSuper("toObject",e),{rx:this.get("rx"),ry:this.get("ry")})},toSVG:function(e){var n=this._createBaseSVGMarkup(),f=0,o=0;return this.group&&(f=this.left+this.rx,o=this.top+this.ry),n.push("<ellipse ",'cx="',f,'" cy="',o,'" ','rx="',this.rx,'" ry="',this.ry,'" style="',this.getSvgStyles(),'" transform="',this.getSvgTransform(),this.getSvgTransformMatrix(),'"/>\n'),e?e(n.join("")):n.join("")},_render:function(e,n){e.beginPath(),e.save(),e.transform(1,0,0,this.ry/this.rx,0,0),e.arc(n?this.left+this.rx:0,n?(this.top+this.ry)*this.rx/this.ry:0,this.rx,0,f,!1),e.restore(),this._renderFill(e),this._renderStroke(e)},complexity:function(){return 1}}),n.Ellipse.ATTRIBUTE_NAMES=n.SHARED_ATTRIBUTES.concat("cx cy rx ry".split(" ")),n.Ellipse.fromElement=function(e,f){f||(f={});var d=n.parseAttributes(e,n.Ellipse.ATTRIBUTE_NAMES);d.left=d.left||0,d.top=d.top||0;var i=new n.Ellipse(o(d,f));return i.top-=i.ry,i.left-=i.rx,i},void(n.Ellipse.fromObject=function(e){return new n.Ellipse(e)}))}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={}),f=n.util.object.extend;if(n.Rect)return void console.warn("fabric.Rect is already defined");var o=n.Object.prototype.stateProperties.concat();o.push("rx","ry","x","y"),n.Rect=n.util.createClass(n.Object,{stateProperties:o,type:"rect",rx:0,ry:0,strokeDashArray:null,initialize:function(e){e=e||{},this.callSuper("initialize",e),this._initRxRy()},_initRxRy:function(){this.rx&&!this.ry?this.ry=this.rx:this.ry&&!this.rx&&(this.rx=this.ry)},_render:function(e,n){if(1===this.width&&1===this.height)return void e.fillRect(0,0,1,1);var f=this.rx?Math.min(this.rx,this.width/2):0,o=this.ry?Math.min(this.ry,this.height/2):0,d=this.width,i=this.height,t=n?this.left:-this.width/2,l=n?this.top:-this.height/2,s=0!==f||0!==o,u=.4477152502;e.beginPath(),e.moveTo(t+f,l),e.lineTo(t+d-f,l),s&&e.bezierCurveTo(t+d-u*f,l,t+d,l+u*o,t+d,l+o),e.lineTo(t+d,l+i-o),s&&e.bezierCurveTo(t+d,l+i-u*o,t+d-u*f,l+i,t+d-f,l+i),e.lineTo(t+f,l+i),s&&e.bezierCurveTo(t+u*f,l+i,t,l+i-u*o,t,l+i-o),e.lineTo(t,l+o),s&&e.bezierCurveTo(t,l+u*o,t+u*f,l,t+f,l),e.closePath(),this._renderFill(e),this._renderStroke(e)},_renderDashedStroke:function(e){var f=-this.width/2,o=-this.height/2,d=this.width,i=this.height;e.beginPath(),n.util.drawDashedLine(e,f,o,f+d,o,this.strokeDashArray),n.util.drawDashedLine(e,f+d,o,f+d,o+i,this.strokeDashArray),n.util.drawDashedLine(e,f+d,o+i,f,o+i,this.strokeDashArray),n.util.drawDashedLine(e,f,o+i,f,o,this.strokeDashArray),e.closePath()},toObject:function(e){var n=f(this.callSuper("toObject",e),{rx:this.get("rx")||0,ry:this.get("ry")||0});return this.includeDefaultValues||this._removeDefaultValues(n),n},toSVG:function(e){var n=this._createBaseSVGMarkup(),f=this.left,o=this.top;return this.group||(f=-this.width/2,o=-this.height/2),n.push("<rect ",'x="',f,'" y="',o,'" rx="',this.get("rx"),'" ry="',this.get("ry"),'" width="',this.width,'" height="',this.height,'" style="',this.getSvgStyles(),'" transform="',this.getSvgTransform(),this.getSvgTransformMatrix(),'"/>\n'),e?e(n.join("")):n.join("")},complexity:function(){return 1}}),n.Rect.ATTRIBUTE_NAMES=n.SHARED_ATTRIBUTES.concat("x y rx ry width height".split(" ")),n.Rect.fromElement=function(e,o){if(!e)return null;o=o||{};var d=n.parseAttributes(e,n.Rect.ATTRIBUTE_NAMES);return d.left=d.left||0,d.top=d.top||0,new n.Rect(f(o?n.util.object.clone(o):{},d))},n.Rect.fromObject=function(e){return new n.Rect(e)}}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={}),f=n.util.toFixed;return n.Polyline?void n.warn("fabric.Polyline is already defined"):(n.Polyline=n.util.createClass(n.Object,{type:"polyline",points:null,initialize:function(e,n){n=n||{},this.set("points",e),this.callSuper("initialize",n),this._calcDimensions()},_calcDimensions:function(){return n.Polygon.prototype._calcDimensions.call(this)},_applyPointOffset:function(){return n.Polygon.prototype._applyPointOffset.call(this)},toObject:function(e){return n.Polygon.prototype.toObject.call(this,e)},toSVG:function(e){for(var n=[],o=this._createBaseSVGMarkup(),d=0,i=this.points.length;i>d;d++)n.push(f(this.points[d].x,2),",",f(this.points[d].y,2)," ");return o.push("<polyline ",'points="',n.join(""),'" style="',this.getSvgStyles(),'" transform="',this.getSvgTransform()," ",this.getSvgTransformMatrix(),'"/>\n'),e?e(o.join("")):o.join("")},_render:function(e){var n;e.beginPath(),this._applyPointOffset&&(this.group&&"path-group"===this.group.type||this._applyPointOffset(),this._applyPointOffset=null),e.moveTo(this.points[0].x,this.points[0].y);for(var f=0,o=this.points.length;o>f;f++)n=this.points[f],e.lineTo(n.x,n.y);this._renderFill(e),this._renderStroke(e)},_renderDashedStroke:function(e){var f,o;e.beginPath();for(var d=0,i=this.points.length;i>d;d++)f=this.points[d],o=this.points[d+1]||f,n.util.drawDashedLine(e,f.x,f.y,o.x,o.y,this.strokeDashArray)},complexity:function(){return this.get("points").length}}),n.Polyline.ATTRIBUTE_NAMES=n.SHARED_ATTRIBUTES.concat(),n.Polyline.fromElement=function(e,f){if(!e)return null;f||(f={});var o=n.parsePointsAttribute(e.getAttribute("points")),d=n.parseAttributes(e,n.Polyline.ATTRIBUTE_NAMES);return null===o?null:new n.Polyline(o,n.util.object.extend(d,f))},void(n.Polyline.fromObject=function(e){var f=e.points;return new n.Polyline(f,e,!0)}))}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={}),f=n.util.object.extend,o=n.util.array.min,d=n.util.array.max,i=n.util.toFixed;return n.Polygon?void n.warn("fabric.Polygon is already defined"):(n.Polygon=n.util.createClass(n.Object,{type:"polygon",points:null,initialize:function(e,n){n=n||{},this.points=e,this.callSuper("initialize",n),this._calcDimensions()},_calcDimensions:function(){var e=this.points,n=o(e,"x"),f=o(e,"y"),i=d(e,"x"),t=d(e,"y");this.width=i-n||1,this.height=t-f||1,this.left=n,this.top=f},_applyPointOffset:function(){this.points.forEach(function(e){e.x-=this.left+this.width/2,e.y-=this.top+this.height/2},this)},toObject:function(e){return f(this.callSuper("toObject",e),{points:this.points.concat()})},toSVG:function(e){for(var n=[],f=this._createBaseSVGMarkup(),o=0,d=this.points.length;d>o;o++)n.push(i(this.points[o].x,2),",",i(this.points[o].y,2)," ");return f.push("<polygon ",'points="',n.join(""),'" style="',this.getSvgStyles(),'" transform="',this.getSvgTransform()," ",this.getSvgTransformMatrix(),'"/>\n'),e?e(f.join("")):f.join("")},_render:function(e){var n;e.beginPath(),this._applyPointOffset&&(this.group&&"path-group"===this.group.type||this._applyPointOffset(),this._applyPointOffset=null),e.moveTo(this.points[0].x,this.points[0].y);for(var f=0,o=this.points.length;o>f;f++)n=this.points[f],e.lineTo(n.x,n.y);this._renderFill(e),(this.stroke||this.strokeDashArray)&&(e.closePath(),this._renderStroke(e))},_renderDashedStroke:function(e){var f,o;e.beginPath();for(var d=0,i=this.points.length;i>d;d++)f=this.points[d],o=this.points[d+1]||this.points[0],n.util.drawDashedLine(e,f.x,f.y,o.x,o.y,this.strokeDashArray);e.closePath()},complexity:function(){return this.points.length}}),n.Polygon.ATTRIBUTE_NAMES=n.SHARED_ATTRIBUTES.concat(),n.Polygon.fromElement=function(e,o){if(!e)return null;o||(o={});var d=n.parsePointsAttribute(e.getAttribute("points")),i=n.parseAttributes(e,n.Polygon.ATTRIBUTE_NAMES);return null===d?null:new n.Polygon(d,f(i,o))},void(n.Polygon.fromObject=function(e){return new n.Polygon(e.points,e,!0)}))}("undefined"!=typeof exports?exports:this),function(e){"use strict";function n(e){return"H"===e[0]?e[1]:e[e.length-2]}function f(e){return"V"===e[0]?e[1]:e[e.length-1]}var o=e.fabric||(e.fabric={}),d=o.util.array.min,i=o.util.array.max,t=o.util.object.extend,l=Object.prototype.toString,s=o.util.drawArc,u={m:2,l:2,h:1,v:1,c:6,s:4,q:4,t:2,a:7},a={m:"l",M:"L"};return o.Path?void o.warn("fabric.Path is already defined"):(o.Path=o.util.createClass(o.Object,{type:"path",path:null,initialize:function(e,n){if(n=n||{},this.setOptions(n),!e)throw new Error("`path` argument is required");var f="[object Array]"===l.call(e);this.path=f?e:e.match&&e.match(/[mzlhvcsqta][^mzlhvcsqta]*/gi),this.path&&(f||(this.path=this._parsePath()),this._initializePath(n),n.sourcePath&&this.setSourcePath(n.sourcePath))},_initializePath:function(e){var n="width"in e&&null!=e.width,f="height"in e&&null!=e.width,o="left"in e,d="top"in e,i=o?this.left:0,l=d?this.top:0;n&&f?(d||(this.top=this.height/2),o||(this.left=this.width/2)):(t(this,this._parseDimensions()),n&&(this.width=e.width),f&&(this.height=e.height)),this.pathOffset=this.pathOffset||this._calculatePathOffset(i,l)},_calculatePathOffset:function(e,n){return{x:this.left-e-this.width/2,y:this.top-n-this.height/2}},_render:function(e,n){var f,o,d,i,t,l=null,u=0,a=0,p=0,c=0,y=0,m=0,r=-(this.width/2+this.pathOffset.x),v=-(this.height/2+this.pathOffset.y);n&&(r+=this.width/2,v+=this.height/2);for(var w=0,b=this.path.length;b>w;++w){switch(f=this.path[w],f[0]){case"l":p+=f[1],c+=f[2],e.lineTo(p+r,c+v);break;case"L":p=f[1],c=f[2],e.lineTo(p+r,c+v);break;case"h":p+=f[1],e.lineTo(p+r,c+v);break;case"H":p=f[1],e.lineTo(p+r,c+v);break;case"v":c+=f[1],e.lineTo(p+r,c+v);break;case"V":c=f[1],e.lineTo(p+r,c+v);break;case"m":p+=f[1],c+=f[2],u=p,a=c,e.moveTo(p+r,c+v);break;case"M":p=f[1],c=f[2],u=p,a=c,e.moveTo(p+r,c+v);break;case"c":o=p+f[5],d=c+f[6],y=p+f[3],m=c+f[4],e.bezierCurveTo(p+f[1]+r,c+f[2]+v,y+r,m+v,o+r,d+v),p=o,c=d;break;case"C":p=f[5],c=f[6],y=f[3],m=f[4],e.bezierCurveTo(f[1]+r,f[2]+v,y+r,m+v,p+r,c+v);break;case"s":o=p+f[3],d=c+f[4],y=y?2*p-y:p,m=m?2*c-m:c,e.bezierCurveTo(y+r,m+v,p+f[1]+r,c+f[2]+v,o+r,d+v),y=p+f[1],m=c+f[2],p=o,c=d;break;case"S":o=f[3],d=f[4],y=2*p-y,m=2*c-m,e.bezierCurveTo(y+r,m+v,f[1]+r,f[2]+v,o+r,d+v),p=o,c=d,y=f[1],m=f[2];break;case"q":o=p+f[3],d=c+f[4],y=p+f[1],m=c+f[2],e.quadraticCurveTo(y+r,m+v,o+r,d+v),p=o,c=d;break;case"Q":o=f[3],d=f[4],e.quadraticCurveTo(f[1]+r,f[2]+v,o+r,d+v),p=o,c=d,y=f[1],m=f[2];break;case"t":o=p+f[1],d=c+f[2],null===l[0].match(/[QqTt]/)?(y=p,m=c):"t"===l[0]?(y=2*p-i,m=2*c-t):"q"===l[0]&&(y=2*p-y,m=2*c-m),i=y,t=m,e.quadraticCurveTo(y+r,m+v,o+r,d+v),p=o,c=d,y=p+f[1],m=c+f[2];break;case"T":o=f[1],d=f[2],y=2*p-y,m=2*c-m,e.quadraticCurveTo(y+r,m+v,o+r,d+v),p=o,c=d;break;case"a":s(e,p+r,c+v,[f[1],f[2],f[3],f[4],f[5],f[6]+p+r,f[7]+c+v]),p+=f[6],c+=f[7];break;case"A":s(e,p+r,c+v,[f[1],f[2],f[3],f[4],f[5],f[6]+r,f[7]+v]),p=f[6],c=f[7];break;case"z":case"Z":p=u,c=a,e.closePath()}l=f}},render:function(e,n){if(this.visible){e.save(),n&&e.translate(-this.width/2,-this.height/2);var f=this.transformMatrix;f&&e.transform(f[0],f[1],f[2],f[3],f[4],f[5]),n||this.transform(e),this._setStrokeStyles(e),this._setFillStyles(e),this._setShadow(e),this.clipTo&&o.util.clipContext(this,e),e.beginPath(),e.globalAlpha=this.group?e.globalAlpha*this.opacity:this.opacity,this._render(e,n),this._renderFill(e),this._renderStroke(e),this.clipTo&&e.restore(),this._removeShadow(e),e.restore()}},toString:function(){return"#<fabric.Path ("+this.complexity()+'): { "top": '+this.top+', "left": '+this.left+" }>"},toObject:function(e){var n=t(this.callSuper("toObject",e),{path:this.path.map(function(e){return e.slice()}),pathOffset:this.pathOffset});return this.sourcePath&&(n.sourcePath=this.sourcePath),this.transformMatrix&&(n.transformMatrix=this.transformMatrix),n},toDatalessObject:function(e){var n=this.toObject(e);return this.sourcePath&&(n.path=this.sourcePath),delete n.sourcePath,n},toSVG:function(e){for(var n=[],f=this._createBaseSVGMarkup(),o=0,d=this.path.length;d>o;o++)n.push(this.path[o].join(" "));var i=n.join(" ");return f.push("<path ",'d="',i,'" style="',this.getSvgStyles(),'" transform="',this.getSvgTransform(),this.getSvgTransformMatrix(),'" stroke-linecap="round" ',"/>\n"),e?e(f.join("")):f.join("")},complexity:function(){return this.path.length},_parsePath:function(){for(var e,n,f,o,d,i=[],t=[],l=/([-+]?((\d+\.\d+)|((\d+)|(\.\d+)))(?:e[-+]?\d+)?)/gi,s=0,p=this.path.length;p>s;s++){for(e=this.path[s],o=e.slice(1).trim(),t.length=0;f=l.exec(o);)t.push(f[0]);d=[e.charAt(0)];for(var c=0,y=t.length;y>c;c++)n=parseFloat(t[c]),isNaN(n)||d.push(n);var m=d[0],r=u[m.toLowerCase()],v=a[m]||m;if(d.length-1>r)for(var w=1,b=d.length;b>w;w+=r)i.push([m].concat(d.slice(w,w+r))),m=v;else i.push(d)}return i},_parseDimensions:function(){var e=[],n=[],f={};this.path.forEach(function(o,d){this._getCoordsFromCommand(o,d,e,n,f)},this);var o=d(e),t=d(n),l=i(e),s=i(n),u=l-o,a=s-t,p={left:this.left+(o+u/2),top:this.top+(t+a/2),width:u,height:a};return p},_getCoordsFromCommand:function(e,o,d,i,t){var l=!1;"H"!==e[0]&&(t.x=n(0===o?e:this.path[o-1])),"V"!==e[0]&&(t.y=f(0===o?e:this.path[o-1])),e[0]===e[0].toLowerCase()&&(l=!0);var s,u=this._getXY(e,l,t);s=parseInt(u.x,10),isNaN(s)||d.push(s),s=parseInt(u.y,10),isNaN(s)||i.push(s)},_getXY:function(e,o,d){var i=o?d.x+n(e):"V"===e[0]?d.x:n(e),t=o?d.y+f(e):"H"===e[0]?d.y:f(e);return{x:i,y:t}}}),o.Path.fromObject=function(e,n){"string"==typeof e.path?o.loadSVGFromURL(e.path,function(f){var d=f[0],i=e.path;delete e.path,o.util.object.extend(d,e),d.setSourcePath(i),n(d)}):n(new o.Path(e.path,e))},o.Path.ATTRIBUTE_NAMES=o.SHARED_ATTRIBUTES.concat(["d"]),o.Path.fromElement=function(e,n,f){var d=o.parseAttributes(e,o.Path.ATTRIBUTE_NAMES);n&&n(new o.Path(d.d,t(d,f)))},void(o.Path.async=!0))}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={}),f=n.util.object.extend,o=n.util.array.invoke,d=n.Object.prototype.toObject;return n.PathGroup?void n.warn("fabric.PathGroup is already defined"):(n.PathGroup=n.util.createClass(n.Path,{type:"path-group",fill:"",initialize:function(e,n){n=n||{},this.paths=e||[];for(var f=this.paths.length;f--;)this.paths[f].group=this;this.setOptions(n),n.widthAttr&&(this.scaleX=n.widthAttr/n.width),n.heightAttr&&(this.scaleY=n.heightAttr/n.height),this.setCoords(),n.sourcePath&&this.setSourcePath(n.sourcePath)},render:function(e){if(this.visible){e.save();var f=this.transformMatrix;f&&e.transform(f[0],f[1],f[2],f[3],f[4],f[5]),this.transform(e),this._setShadow(e),this.clipTo&&n.util.clipContext(this,e);for(var o=0,d=this.paths.length;d>o;++o)this.paths[o].render(e,!0);this.clipTo&&e.restore(),this._removeShadow(e),e.restore()}},_set:function(e,n){if("fill"===e&&n&&this.isSameColor())for(var f=this.paths.length;f--;)this.paths[f]._set(e,n);return this.callSuper("_set",e,n)},toObject:function(e){var n=f(d.call(this,e),{paths:o(this.getObjects(),"toObject",e)});return this.sourcePath&&(n.sourcePath=this.sourcePath),n},toDatalessObject:function(e){var n=this.toObject(e);return this.sourcePath&&(n.paths=this.sourcePath),n},toSVG:function(e){for(var n=this.getObjects(),f="translate("+this.left+" "+this.top+")",o=["<g ",'style="',this.getSvgStyles(),'" ','transform="',f,this.getSvgTransform(),'" ',">\n"],d=0,i=n.length;i>d;d++)o.push(n[d].toSVG(e));return o.push("</g>\n"),e?e(o.join("")):o.join("")},toString:function(){return"#<fabric.PathGroup ("+this.complexity()+"): { top: "+this.top+", left: "+this.left+" }>"},isSameColor:function(){var e=(this.getObjects()[0].get("fill")||"").toLowerCase();return this.getObjects().every(function(n){return(n.get("fill")||"").toLowerCase()===e})},complexity:function(){return this.paths.reduce(function(e,n){return e+(n&&n.complexity?n.complexity():0)},0)},getObjects:function(){return this.paths}}),n.PathGroup.fromObject=function(e,f){"string"==typeof e.paths?n.loadSVGFromURL(e.paths,function(o){var d=e.paths;delete e.paths;var i=n.util.groupSVGElements(o,e,d);f(i)}):n.util.enlivenObjects(e.paths,function(o){delete e.paths,f(new n.PathGroup(o,e))})},void(n.PathGroup.async=!0))}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={}),f=n.util.object.extend,o=n.util.array.min,d=n.util.array.max,i=n.util.array.invoke;if(!n.Group){var t={lockMovementX:!0,lockMovementY:!0,lockRotation:!0,lockScalingX:!0,lockScalingY:!0,lockUniScaling:!0};n.Group=n.util.createClass(n.Object,n.Collection,{type:"group",initialize:function(e,n){n=n||{},this._objects=e||[];for(var o=this._objects.length;o--;)this._objects[o].group=this;this.originalState={},this.callSuper("initialize"),this._calcBounds(),this._updateObjectsCoords(),n&&f(this,n),this._setOpacityIfSame(),this.setCoords(),this.saveCoords()},_updateObjectsCoords:function(){this.forEachObject(this._updateObjectCoords,this)},_updateObjectCoords:function(e){var n=e.getLeft(),f=e.getTop();e.set({originalLeft:n,originalTop:f,left:n-this.left,top:f-this.top}),e.setCoords(),e.__origHasControls=e.hasControls,e.hasControls=!1},toString:function(){return"#<fabric.Group: ("+this.complexity()+")>"},addWithUpdate:function(e){return this._restoreObjectsState(),e&&(this._objects.push(e),e.group=this),this.forEachObject(this._setObjectActive,this),this._calcBounds(),this._updateObjectsCoords(),this},_setObjectActive:function(e){e.set("active",!0),e.group=this},removeWithUpdate:function(e){return this._moveFlippedObject(e),this._restoreObjectsState(),this.forEachObject(this._setObjectActive,this),this.remove(e),this._calcBounds(),this._updateObjectsCoords(),this},_onObjectAdded:function(e){e.group=this},_onObjectRemoved:function(e){delete e.group,e.set("active",!1)},delegatedProperties:{fill:!0,opacity:!0,fontFamily:!0,fontWeight:!0,fontSize:!0,fontStyle:!0,lineHeight:!0,textDecoration:!0,textAlign:!0,backgroundColor:!0},_set:function(e,n){if(e in this.delegatedProperties){var f=this._objects.length;for(this[e]=n;f--;)this._objects[f].set(e,n)}else this[e]=n},toObject:function(e){return f(this.callSuper("toObject",e),{objects:i(this._objects,"toObject",e)})},render:function(e){if(this.visible){e.save(),this.clipTo&&n.util.clipContext(this,e);for(var f=0,o=this._objects.length;o>f;f++)this._renderObject(this._objects[f],e);this.clipTo&&e.restore(),e.restore()}},_renderControls:function(e,n){this.callSuper("_renderControls",e,n);for(var f=0,o=this._objects.length;o>f;f++)this._objects[f]._renderControls(e)},_renderObject:function(e,n){var f=e.hasRotatingPoint;e.visible&&(e.hasRotatingPoint=!1,e.render(n),e.hasRotatingPoint=f)},_restoreObjectsState:function(){return this._objects.forEach(this._restoreObjectState,this),this},_moveFlippedObject:function(e){var n=e.get("originX"),f=e.get("originY"),o=e.getCenterPoint();e.set({originX:"center",originY:"center",left:o.x,top:o.y}),this._toggleFlipping(e);var d=e.getPointByOrigin(n,f);return e.set({originX:n,originY:f,left:d.x,top:d.y}),this},_toggleFlipping:function(e){this.flipX&&(e.toggle("flipX"),e.set("left",-e.get("left")),e.setAngle(-e.getAngle())),this.flipY&&(e.toggle("flipY"),e.set("top",-e.get("top")),e.setAngle(-e.getAngle()))},_restoreObjectState:function(e){return this._setObjectPosition(e),e.setCoords(),e.hasControls=e.__origHasControls,delete e.__origHasControls,e.set("active",!1),e.setCoords(),delete e.group,this},_setObjectPosition:function(e){var n=this.getLeft(),f=this.getTop(),o=this._getRotatedLeftTop(e);e.set({angle:e.getAngle()+this.getAngle(),left:n+o.left,top:f+o.top,scaleX:e.get("scaleX")*this.get("scaleX"),scaleY:e.get("scaleY")*this.get("scaleY")})},_getRotatedLeftTop:function(e){var n=this.getAngle()*(Math.PI/180);return{left:-Math.sin(n)*e.getTop()*this.get("scaleY")+Math.cos(n)*e.getLeft()*this.get("scaleX"),top:Math.cos(n)*e.getTop()*this.get("scaleY")+Math.sin(n)*e.getLeft()*this.get("scaleX")}},destroy:function(){return this._objects.forEach(this._moveFlippedObject,this),this._restoreObjectsState()},saveCoords:function(){return this._originalLeft=this.get("left"),this._originalTop=this.get("top"),this},hasMoved:function(){return this._originalLeft!==this.get("left")||this._originalTop!==this.get("top")},setObjectsCoords:function(){return this.forEachObject(function(e){e.setCoords()}),this},_setOpacityIfSame:function(){var e=this.getObjects(),n=e[0]?e[0].get("opacity"):1,f=e.every(function(e){return e.get("opacity")===n});f&&(this.opacity=n)},_calcBounds:function(e){for(var n,f=[],o=[],d=0,i=this._objects.length;i>d;++d){n=this._objects[d],n.setCoords();for(var t in n.oCoords)f.push(n.oCoords[t].x),o.push(n.oCoords[t].y)}this.set(this._getBounds(f,o,e))},_getBounds:function(e,f,i){var t=n.util.invertTransform(this.getViewportTransform()),l=n.util.transformPoint(new n.Point(o(e),o(f)),t),s=n.util.transformPoint(new n.Point(d(e),d(f)),t),u={width:s.x-l.x||0,height:s.y-l.y||0};return i||(u.left=(l.x+s.x)/2||0,u.top=(l.y+s.y)/2||0),u},toSVG:function(e){for(var n=["<g ",'transform="',this.getSvgTransform(),'">\n'],f=0,o=this._objects.length;o>f;f++)n.push(this._objects[f].toSVG(e));return n.push("</g>\n"),e?e(n.join("")):n.join("")},get:function(e){if(e in t){if(this[e])return this[e];for(var n=0,f=this._objects.length;f>n;n++)if(this._objects[n][e])return!0;return!1}return e in this.delegatedProperties?this._objects[0]&&this._objects[0].get(e):this[e]}}),n.Group.fromObject=function(e,f){n.util.enlivenObjects(e.objects,function(o){delete e.objects,f&&f(new n.Group(o,e))})},n.Group.async=!0}}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=fabric.util.object.extend;return e.fabric||(e.fabric={}),e.fabric.Image?void fabric.warn("fabric.Image is already defined."):(fabric.Image=fabric.util.createClass(fabric.Object,{type:"image",crossOrigin:"",initialize:function(e,n){n||(n={}),this.filters=[],this.callSuper("initialize",n),this._initElement(e,n),this._initConfig(n),n.filters&&(this.filters=n.filters,this.applyFilters())},getElement:function(){return this._element},setElement:function(e,n){return this._element=e,this._originalElement=e,this._initConfig(),0!==this.filters.length&&this.applyFilters(n),this},setCrossOrigin:function(e){return this.crossOrigin=e,this._element.crossOrigin=e,this},getOriginalSize:function(){var e=this.getElement();return{width:e.width,height:e.height}},_stroke:function(e){e.save(),this._setStrokeStyles(e),e.beginPath(),e.strokeRect(-this.width/2,-this.height/2,this.width,this.height),e.closePath(),e.restore()},_renderDashedStroke:function(e){var n=-this.width/2,f=-this.height/2,o=this.width,d=this.height;e.save(),this._setStrokeStyles(e),e.beginPath(),fabric.util.drawDashedLine(e,n,f,n+o,f,this.strokeDashArray),fabric.util.drawDashedLine(e,n+o,f,n+o,f+d,this.strokeDashArray),fabric.util.drawDashedLine(e,n+o,f+d,n,f+d,this.strokeDashArray),fabric.util.drawDashedLine(e,n,f+d,n,f,this.strokeDashArray),e.closePath(),e.restore()},toObject:function(e){return n(this.callSuper("toObject",e),{src:this._originalElement.src||this._originalElement._src,filters:this.filters.map(function(e){return e&&e.toObject()}),crossOrigin:this.crossOrigin})},toSVG:function(e){var n=[],f=-this.width/2,o=-this.height/2;if(this.group&&(f=this.left,o=this.top),n.push('<g transform="',this.getSvgTransform(),this.getSvgTransformMatrix(),'">\n','<image xlink:href="',this.getSvgSrc(),'" x="',f,'" y="',o,'" style="',this.getSvgStyles(),'" width="',this.width,'" height="',this.height,'" preserveAspectRatio="none"',"></image>\n"),this.stroke||this.strokeDashArray){var d=this.fill;this.fill=null,n.push("<rect ",'x="',f,'" y="',o,'" width="',this.width,'" height="',this.height,'" style="',this.getSvgStyles(),'"/>\n'),this.fill=d}return n.push("</g>\n"),e?e(n.join("")):n.join("")},getSrc:function(){return this.getElement()?this.getElement().src||this.getElement()._src:void 0},toString:function(){return'#<fabric.Image: { src: "'+this.getSrc()+'" }>'},clone:function(e,n){this.constructor.fromObject(this.toObject(n),e)},applyFilters:function(e){if(this._originalElement){if(0===this.filters.length)return this._element=this._originalElement,void(e&&e());var n=this._originalElement,f=fabric.util.createCanvasElement(),o=fabric.util.createImage(),d=this;return f.width=n.width,f.height=n.height,f.getContext("2d").drawImage(n,0,0,n.width,n.height),this.filters.forEach(function(e){e&&e.applyTo(f)}),o.width=n.width,o.height=n.height,fabric.isLikelyNode?(o.src=f.toBuffer(void 0,fabric.Image.pngCompression),d._element=o,e&&e()):(o.onload=function(){d._element=o,e&&e(),o.onload=f=n=null},o.src=f.toDataURL("image/png")),this}},_render:function(e,n){this._element&&e.drawImage(this._element,n?this.left:-this.width/2,n?this.top:-this.height/2,this.width,this.height),this._renderStroke(e)},_resetWidthHeight:function(){var e=this.getElement();this.set("width",e.width),this.set("height",e.height)},_initElement:function(e){this.setElement(fabric.util.getById(e)),fabric.util.addClass(this.getElement(),fabric.Image.CSS_CANVAS)},_initConfig:function(e){e||(e={}),this.setOptions(e),this._setWidthHeight(e),this._element&&this.crossOrigin&&(this._element.crossOrigin=this.crossOrigin)},_initFilters:function(e,n){e.filters&&e.filters.length?fabric.util.enlivenObjects(e.filters,function(e){n&&n(e)},"fabric.Image.filters"):n&&n()},_setWidthHeight:function(e){this.width="width"in e?e.width:this.getElement()?this.getElement().width||0:0,this.height="height"in e?e.height:this.getElement()?this.getElement().height||0:0
},complexity:function(){return 1}}),fabric.Image.CSS_CANVAS="canvas-img",fabric.Image.prototype.getSvgSrc=fabric.Image.prototype.getSrc,fabric.Image.fromObject=function(e,n){fabric.util.loadImage(e.src,function(f){fabric.Image.prototype._initFilters.call(e,e,function(o){e.filters=o||[];var d=new fabric.Image(f,e);n&&n(d)})},null,e.crossOrigin)},fabric.Image.fromURL=function(e,n,f){fabric.util.loadImage(e,function(e){n(new fabric.Image(e,f))},null,f&&f.crossOrigin)},fabric.Image.ATTRIBUTE_NAMES=fabric.SHARED_ATTRIBUTES.concat("x y width height xlink:href".split(" ")),fabric.Image.fromElement=function(e,f,o){var d=fabric.parseAttributes(e,fabric.Image.ATTRIBUTE_NAMES);fabric.Image.fromURL(d["xlink:href"],f,n(o?fabric.util.object.clone(o):{},d))},fabric.Image.async=!0,void(fabric.Image.pngCompression=1))}("undefined"!=typeof exports?exports:this),fabric.Image.filters=fabric.Image.filters||{},fabric.Image.filters.BaseFilter=fabric.util.createClass({type:"BaseFilter",toObject:function(){return{type:this.type}},toJSON:function(){return this.toObject()}}),function(e){"use strict";var n=e.fabric||(e.fabric={}),f=n.util.object.extend;n.Image.filters.Brightness=n.util.createClass(n.Image.filters.BaseFilter,{type:"Brightness",initialize:function(e){e=e||{},this.brightness=e.brightness||0},applyTo:function(e){for(var n=e.getContext("2d"),f=n.getImageData(0,0,e.width,e.height),o=f.data,d=this.brightness,i=0,t=o.length;t>i;i+=4)o[i]+=d,o[i+1]+=d,o[i+2]+=d;n.putImageData(f,0,0)},toObject:function(){return f(this.callSuper("toObject"),{brightness:this.brightness})}}),n.Image.filters.Brightness.fromObject=function(e){return new n.Image.filters.Brightness(e)}}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={}),f=n.util.object.extend;n.Image.filters.Convolute=n.util.createClass(n.Image.filters.BaseFilter,{type:"Convolute",initialize:function(e){e=e||{},this.opaque=e.opaque,this.matrix=e.matrix||[0,0,0,0,1,0,0,0,0];var f=n.util.createCanvasElement();this.tmpCtx=f.getContext("2d")},_createImageData:function(e,n){return this.tmpCtx.createImageData(e,n)},applyTo:function(e){for(var n=this.matrix,f=e.getContext("2d"),o=f.getImageData(0,0,e.width,e.height),d=Math.round(Math.sqrt(n.length)),i=Math.floor(d/2),t=o.data,l=o.width,s=o.height,u=l,a=s,p=this._createImageData(u,a),c=p.data,y=this.opaque?1:0,m=0;a>m;m++)for(var r=0;u>r;r++){for(var v=m,w=r,b=4*(m*u+r),g=0,h=0,x=0,j=0,k=0;d>k;k++)for(var q=0;d>q;q++){var z=v+k-i,A=w+q-i;if(!(0>z||z>s||0>A||A>l)){var B=4*(z*l+A),C=n[k*d+q];g+=t[B]*C,h+=t[B+1]*C,x+=t[B+2]*C,j+=t[B+3]*C}}c[b]=g,c[b+1]=h,c[b+2]=x,c[b+3]=j+y*(255-j)}f.putImageData(p,0,0)},toObject:function(){return f(this.callSuper("toObject"),{opaque:this.opaque,matrix:this.matrix})}}),n.Image.filters.Convolute.fromObject=function(e){return new n.Image.filters.Convolute(e)}}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={}),f=n.util.object.extend;n.Image.filters.GradientTransparency=n.util.createClass(n.Image.filters.BaseFilter,{type:"GradientTransparency",initialize:function(e){e=e||{},this.threshold=e.threshold||100},applyTo:function(e){for(var n=e.getContext("2d"),f=n.getImageData(0,0,e.width,e.height),o=f.data,d=this.threshold,i=o.length,t=0,l=o.length;l>t;t+=4)o[t+3]=d+255*(i-t)/i;n.putImageData(f,0,0)},toObject:function(){return f(this.callSuper("toObject"),{threshold:this.threshold})}}),n.Image.filters.GradientTransparency.fromObject=function(e){return new n.Image.filters.GradientTransparency(e)}}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={});n.Image.filters.Grayscale=n.util.createClass(n.Image.filters.BaseFilter,{type:"Grayscale",applyTo:function(e){for(var n,f=e.getContext("2d"),o=f.getImageData(0,0,e.width,e.height),d=o.data,i=o.width*o.height*4,t=0;i>t;)n=(d[t]+d[t+1]+d[t+2])/3,d[t]=n,d[t+1]=n,d[t+2]=n,t+=4;f.putImageData(o,0,0)}}),n.Image.filters.Grayscale.fromObject=function(){return new n.Image.filters.Grayscale}}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={});n.Image.filters.Invert=n.util.createClass(n.Image.filters.BaseFilter,{type:"Invert",applyTo:function(e){var n,f=e.getContext("2d"),o=f.getImageData(0,0,e.width,e.height),d=o.data,i=d.length;for(n=0;i>n;n+=4)d[n]=255-d[n],d[n+1]=255-d[n+1],d[n+2]=255-d[n+2];f.putImageData(o,0,0)}}),n.Image.filters.Invert.fromObject=function(){return new n.Image.filters.Invert}}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={}),f=n.util.object.extend;n.Image.filters.Mask=n.util.createClass(n.Image.filters.BaseFilter,{type:"Mask",initialize:function(e){e=e||{},this.mask=e.mask,this.channel=[0,1,2,3].indexOf(e.channel)>-1?e.channel:0},applyTo:function(e){if(this.mask){var f,o=e.getContext("2d"),d=o.getImageData(0,0,e.width,e.height),i=d.data,t=this.mask.getElement(),l=n.util.createCanvasElement(),s=this.channel,u=d.width*d.height*4;l.width=t.width,l.height=t.height,l.getContext("2d").drawImage(t,0,0,t.width,t.height);var a=l.getContext("2d").getImageData(0,0,t.width,t.height),p=a.data;for(f=0;u>f;f+=4)i[f+3]=p[f+s];o.putImageData(d,0,0)}},toObject:function(){return f(this.callSuper("toObject"),{mask:this.mask.toObject(),channel:this.channel})}}),n.Image.filters.Mask.fromObject=function(e,f){n.util.loadImage(e.mask.src,function(o){e.mask=new n.Image(o,e.mask),f&&f(new n.Image.filters.Mask(e))})},n.Image.filters.Mask.async=!0}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={}),f=n.util.object.extend;n.Image.filters.Noise=n.util.createClass(n.Image.filters.BaseFilter,{type:"Noise",initialize:function(e){e=e||{},this.noise=e.noise||0},applyTo:function(e){for(var n,f=e.getContext("2d"),o=f.getImageData(0,0,e.width,e.height),d=o.data,i=this.noise,t=0,l=d.length;l>t;t+=4)n=(.5-Math.random())*i,d[t]+=n,d[t+1]+=n,d[t+2]+=n;f.putImageData(o,0,0)},toObject:function(){return f(this.callSuper("toObject"),{noise:this.noise})}}),n.Image.filters.Noise.fromObject=function(e){return new n.Image.filters.Noise(e)}}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={}),f=n.util.object.extend;n.Image.filters.Pixelate=n.util.createClass(n.Image.filters.BaseFilter,{type:"Pixelate",initialize:function(e){e=e||{},this.blocksize=e.blocksize||4},applyTo:function(e){var n,f,o,d,i,t,l,s=e.getContext("2d"),u=s.getImageData(0,0,e.width,e.height),a=u.data,p=u.height,c=u.width;for(f=0;p>f;f+=this.blocksize)for(o=0;c>o;o+=this.blocksize){n=4*f*c+4*o,d=a[n],i=a[n+1],t=a[n+2],l=a[n+3];for(var y=f,m=f+this.blocksize;m>y;y++)for(var r=o,v=o+this.blocksize;v>r;r++)n=4*y*c+4*r,a[n]=d,a[n+1]=i,a[n+2]=t,a[n+3]=l}s.putImageData(u,0,0)},toObject:function(){return f(this.callSuper("toObject"),{blocksize:this.blocksize})}}),n.Image.filters.Pixelate.fromObject=function(e){return new n.Image.filters.Pixelate(e)}}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={}),f=n.util.object.extend;n.Image.filters.RemoveWhite=n.util.createClass(n.Image.filters.BaseFilter,{type:"RemoveWhite",initialize:function(e){e=e||{},this.threshold=e.threshold||30,this.distance=e.distance||20},applyTo:function(e){for(var n,f,o,d=e.getContext("2d"),i=d.getImageData(0,0,e.width,e.height),t=i.data,l=this.threshold,s=this.distance,u=255-l,a=Math.abs,p=0,c=t.length;c>p;p+=4)n=t[p],f=t[p+1],o=t[p+2],n>u&&f>u&&o>u&&a(n-f)<s&&a(n-o)<s&&a(f-o)<s&&(t[p+3]=1);d.putImageData(i,0,0)},toObject:function(){return f(this.callSuper("toObject"),{threshold:this.threshold,distance:this.distance})}}),n.Image.filters.RemoveWhite.fromObject=function(e){return new n.Image.filters.RemoveWhite(e)}}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={});n.Image.filters.Sepia=n.util.createClass(n.Image.filters.BaseFilter,{type:"Sepia",applyTo:function(e){var n,f,o=e.getContext("2d"),d=o.getImageData(0,0,e.width,e.height),i=d.data,t=i.length;for(n=0;t>n;n+=4)f=.3*i[n]+.59*i[n+1]+.11*i[n+2],i[n]=f+100,i[n+1]=f+50,i[n+2]=f+255;o.putImageData(d,0,0)}}),n.Image.filters.Sepia.fromObject=function(){return new n.Image.filters.Sepia}}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={});n.Image.filters.Sepia2=n.util.createClass(n.Image.filters.BaseFilter,{type:"Sepia2",applyTo:function(e){var n,f,o,d,i=e.getContext("2d"),t=i.getImageData(0,0,e.width,e.height),l=t.data,s=l.length;for(n=0;s>n;n+=4)f=l[n],o=l[n+1],d=l[n+2],l[n]=(.393*f+.769*o+.189*d)/1.351,l[n+1]=(.349*f+.686*o+.168*d)/1.203,l[n+2]=(.272*f+.534*o+.131*d)/2.14;i.putImageData(t,0,0)}}),n.Image.filters.Sepia2.fromObject=function(){return new n.Image.filters.Sepia2}}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={}),f=n.util.object.extend;n.Image.filters.Tint=n.util.createClass(n.Image.filters.BaseFilter,{type:"Tint",initialize:function(e){e=e||{},this.color=e.color||"#000000",this.opacity="undefined"!=typeof e.opacity?e.opacity:new n.Color(this.color).getAlpha()},applyTo:function(e){var f,o,d,i,t,l,s,u,a,p=e.getContext("2d"),c=p.getImageData(0,0,e.width,e.height),y=c.data,m=y.length;for(a=new n.Color(this.color).getSource(),o=a[0]*this.opacity,d=a[1]*this.opacity,i=a[2]*this.opacity,u=1-this.opacity,f=0;m>f;f+=4)t=y[f],l=y[f+1],s=y[f+2],y[f]=o+t*u,y[f+1]=d+l*u,y[f+2]=i+s*u;p.putImageData(c,0,0)},toObject:function(){return f(this.callSuper("toObject"),{color:this.color,opacity:this.opacity})}}),n.Image.filters.Tint.fromObject=function(e){return new n.Image.filters.Tint(e)}}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={}),f=n.util.object.extend;n.Image.filters.Multiply=n.util.createClass(n.Image.filters.BaseFilter,{type:"Multiply",initialize:function(e){e=e||{},this.color=e.color||"#000000"},applyTo:function(e){var f,o,d=e.getContext("2d"),i=d.getImageData(0,0,e.width,e.height),t=i.data,l=t.length;for(o=new n.Color(this.color).getSource(),f=0;l>f;f+=4)t[f]*=o[0]/255,t[f+1]*=o[1]/255,t[f+2]*=o[2]/255;d.putImageData(i,0,0)},toObject:function(){return f(this.callSuper("toObject"),{color:this.color})}}),n.Image.filters.Multiply.fromObject=function(e){return new n.Image.filters.Multiply(e)}}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric;n.Image.filters.Blend=n.util.createClass({type:"Blend",initialize:function(e){e=e||{},this.color=e.color||"#000",this.image=e.image||!1,this.mode=e.mode||"multiply",this.alpha=e.alpha||1},applyTo:function(e){var f,o,d,i,t,l,s,u=e.getContext("2d"),a=u.getImageData(0,0,e.width,e.height),p=a.data,c=!1;if(this.image){c=!0;var y=n.util.createCanvasElement();y.width=this.image.width,y.height=this.image.height;var m=new n.StaticCanvas(y);m.add(this.image);var r=m.getContext("2d");s=r.getImageData(0,0,m.width,m.height).data}else s=new n.Color(this.color).getSource(),f=s[0]*this.alpha,o=s[1]*this.alpha,d=s[2]*this.alpha;for(var v=0,w=p.length;w>v;v+=4)switch(i=p[v],t=p[v+1],l=p[v+2],c&&(f=s[v]*this.alpha,o=s[v+1]*this.alpha,d=s[v+2]*this.alpha),this.mode){case"multiply":p[v]=i*f/255,p[v+1]=t*o/255,p[v+2]=l*d/255;break;case"screen":p[v]=1-(1-i)*(1-f),p[v+1]=1-(1-t)*(1-o),p[v+2]=1-(1-l)*(1-d);break;case"add":p[v]=Math.min(255,i+f),p[v+1]=Math.min(255,t+o),p[v+2]=Math.min(255,l+d);break;case"diff":case"difference":p[v]=Math.abs(i-f),p[v+1]=Math.abs(t-o),p[v+2]=Math.abs(l-d);break;case"subtract":var b=i-f,g=t-o,h=l-d;p[v]=0>b?0:b,p[v+1]=0>g?0:g,p[v+2]=0>h?0:h;break;case"darken":p[v]=Math.min(i,f),p[v+1]=Math.min(t,o),p[v+2]=Math.min(l,d);break;case"lighten":p[v]=Math.max(i,f),p[v+1]=Math.max(t,o),p[v+2]=Math.max(l,d)}u.putImageData(a,0,0)}}),n.Image.filters.Blend.fromObject=function(e){return new n.Image.filters.Blend(e)}}("undefined"!=typeof exports?exports:this),function(e){"use strict";var n=e.fabric||(e.fabric={}),f=n.util.object.extend,o=n.util.object.clone,d=n.util.toFixed,i=n.StaticCanvas.supports("setLineDash");if(n.Text)return void n.warn("fabric.Text is already defined");var t=n.Object.prototype.stateProperties.concat();t.push("fontFamily","fontWeight","fontSize","text","textDecoration","textAlign","fontStyle","lineHeight","textBackgroundColor","useNative","path"),n.Text=n.util.createClass(n.Object,{_dimensionAffectingProps:{fontSize:!0,fontWeight:!0,fontFamily:!0,textDecoration:!0,fontStyle:!0,lineHeight:!0,stroke:!0,strokeWidth:!0,text:!0},_reNewline:/\r?\n/,type:"text",fontSize:40,fontWeight:"normal",fontFamily:"Times New Roman",textDecoration:"",textAlign:"left",fontStyle:"",lineHeight:1.3,textBackgroundColor:"",path:null,useNative:!0,stateProperties:t,stroke:null,shadow:null,initialize:function(e,n){n=n||{},this.text=e,this.__skipDimension=!0,this.setOptions(n),this.__skipDimension=!1,this._initDimensions()},_initDimensions:function(){if(!this.__skipDimension){var e=n.util.createCanvasElement();this._render(e.getContext("2d"))}},toString:function(){return"#<fabric.Text ("+this.complexity()+'): { "text": "'+this.text+'", "fontFamily": "'+this.fontFamily+'" }>'},_render:function(e){"undefined"==typeof Cufon||this.useNative===!0?this._renderViaNative(e):this._renderViaCufon(e)},_renderViaNative:function(e){var f=this.text.split(this._reNewline);this._setTextStyles(e),this.width=this._getTextWidth(e,f),this.height=this._getTextHeight(e,f),this.clipTo&&n.util.clipContext(this,e),this._renderTextBackground(e,f),this._translateForTextAlign(e),this._renderText(e,f),"left"!==this.textAlign&&"justify"!==this.textAlign&&e.restore(),this._renderTextDecoration(e,f),this.clipTo&&e.restore(),this._setBoundaries(e,f),this._totalLineHeight=0},_renderText:function(e,n){e.save(),this._setShadow(e),this._setupFillRule(e),this._renderTextFill(e,n),this._renderTextStroke(e,n),this._restoreFillRule(e),this._removeShadow(e),e.restore()},_translateForTextAlign:function(e){"left"!==this.textAlign&&"justify"!==this.textAlign&&(e.save(),e.translate("center"===this.textAlign?this.width/2:this.width,0))},_setBoundaries:function(e,n){this._boundaries=[];for(var f=0,o=n.length;o>f;f++){var d=this._getLineWidth(e,n[f]),i=this._getLineLeftOffset(d);this._boundaries.push({height:this.fontSize*this.lineHeight,width:d,left:i})}},_setTextStyles:function(e){this._setFillStyles(e),this._setStrokeStyles(e),e.textBaseline="alphabetic",this.skipTextAlign||(e.textAlign=this.textAlign),e.font=this._getFontDeclaration()},_getTextHeight:function(e,n){return this.fontSize*n.length*this.lineHeight},_getTextWidth:function(e,n){for(var f=e.measureText(n[0]||"|").width,o=1,d=n.length;d>o;o++){var i=e.measureText(n[o]).width;i>f&&(f=i)}return f},_renderChars:function(e,n,f,o,d){n[e](f,o,d)},_renderTextLine:function(e,n,f,o,d,i){if(d-=this.fontSize/4,"justify"!==this.textAlign)return void this._renderChars(e,n,f,o,d,i);var t=n.measureText(f).width,l=this.width;if(l>t)for(var s=f.split(/\s+/),u=n.measureText(f.replace(/\s+/g,"")).width,a=l-u,p=s.length-1,c=a/p,y=0,m=0,r=s.length;r>m;m++)this._renderChars(e,n,s[m],o+y,d,i),y+=n.measureText(s[m]).width+c;else this._renderChars(e,n,f,o,d,i)},_getLeftOffset:function(){return n.isLikelyNode?0:-this.width/2},_getTopOffset:function(){return-this.height/2},_renderTextFill:function(e,n){if(this.fill||this._skipFillStrokeCheck){this._boundaries=[];for(var f=0,o=0,d=n.length;d>o;o++){var i=this._getHeightOfLine(e,o,n);f+=i,this._renderTextLine("fillText",e,n[o],this._getLeftOffset(),this._getTopOffset()+f,o)}}},_renderTextStroke:function(e,n){if(this.stroke&&0!==this.strokeWidth||this._skipFillStrokeCheck){var f=0;e.save(),this.strokeDashArray&&(1&this.strokeDashArray.length&&this.strokeDashArray.push.apply(this.strokeDashArray,this.strokeDashArray),i&&e.setLineDash(this.strokeDashArray)),e.beginPath();for(var o=0,d=n.length;d>o;o++){var t=this._getHeightOfLine(e,o,n);f+=t,this._renderTextLine("strokeText",e,n[o],this._getLeftOffset(),this._getTopOffset()+f,o)}e.closePath(),e.restore()}},_getHeightOfLine:function(){return this.fontSize*this.lineHeight},_renderTextBackground:function(e,n){this._renderTextBoxBackground(e),this._renderTextLinesBackground(e,n)},_renderTextBoxBackground:function(e){this.backgroundColor&&(e.save(),e.fillStyle=this.backgroundColor,e.fillRect(this._getLeftOffset(),this._getTopOffset(),this.width,this.height),e.restore())},_renderTextLinesBackground:function(e,n){if(this.textBackgroundColor){e.save(),e.fillStyle=this.textBackgroundColor;for(var f=0,o=n.length;o>f;f++)if(""!==n[f]){var d=this._getLineWidth(e,n[f]),i=this._getLineLeftOffset(d);e.fillRect(this._getLeftOffset()+i,this._getTopOffset()+f*this.fontSize*this.lineHeight,d,this.fontSize*this.lineHeight)}e.restore()}},_getLineLeftOffset:function(e){return"center"===this.textAlign?(this.width-e)/2:"right"===this.textAlign?this.width-e:0},_getLineWidth:function(e,n){return"justify"===this.textAlign?this.width:e.measureText(n).width},_renderTextDecoration:function(e,n){function f(f){for(var i=0,t=n.length;t>i;i++){var l=d._getLineWidth(e,n[i]),s=d._getLineLeftOffset(l);e.fillRect(d._getLeftOffset()+s,~~(f+i*d._getHeightOfLine(e,i,n)-o),l,1)}}if(this.textDecoration){var o=this._getTextHeight(e,n)/2,d=this;this.textDecoration.indexOf("underline")>-1&&f(this.fontSize*this.lineHeight),this.textDecoration.indexOf("line-through")>-1&&f(this.fontSize*this.lineHeight-this.fontSize/2),this.textDecoration.indexOf("overline")>-1&&f(this.fontSize*this.lineHeight-this.fontSize)}},_getFontDeclaration:function(){return[n.isLikelyNode?this.fontWeight:this.fontStyle,n.isLikelyNode?this.fontStyle:this.fontWeight,this.fontSize+"px",n.isLikelyNode?'"'+this.fontFamily+'"':this.fontFamily].join(" ")},render:function(e,n){if(this.visible){e.save(),this._transform(e,n);var f=this.transformMatrix,o=this.group&&"path-group"===this.group.type;o&&e.translate(-this.group.width/2,-this.group.height/2),f&&e.transform(f[0],f[1],f[2],f[3],f[4],f[5]),o&&e.translate(this.left,this.top),this._render(e),e.restore()}},toObject:function(e){var n=f(this.callSuper("toObject",e),{text:this.text,fontSize:this.fontSize,fontWeight:this.fontWeight,fontFamily:this.fontFamily,fontStyle:this.fontStyle,lineHeight:this.lineHeight,textDecoration:this.textDecoration,textAlign:this.textAlign,path:this.path,textBackgroundColor:this.textBackgroundColor,useNative:this.useNative});return this.includeDefaultValues||this._removeDefaultValues(n),n},toSVG:function(e){var n=[],f=this.text.split(this._reNewline),o=this._getSVGLeftTopOffsets(f),d=this._getSVGTextAndBg(o.lineTop,o.textLeft,f),i=this._getSVGShadows(o.lineTop,f);return o.textTop+=this._fontAscent?this._fontAscent/5*this.lineHeight:0,this._wrapSVGTextAndBg(n,d,i,o),e?e(n.join("")):n.join("")},_getSVGLeftTopOffsets:function(e){var n=this.useNative?this.fontSize*this.lineHeight:-this._fontAscent-this._fontAscent/5*this.lineHeight,f=-(this.width/2),o=this.useNative?this.fontSize-1:this.height/2-e.length*this.fontSize-this._totalLineHeight;return{textLeft:f+(this.group&&"path-group"===this.group.type?this.left:0),textTop:o+(this.group&&"path-group"===this.group.type?this.top:0),lineTop:n}},_wrapSVGTextAndBg:function(e,n,f,o){e.push('<g transform="',this.getSvgTransform(),this.getSvgTransformMatrix(),'">\n',n.textBgRects.join(""),"<text ",this.fontFamily?'font-family="'+this.fontFamily.replace(/"/g,"'")+'" ':"",this.fontSize?'font-size="'+this.fontSize+'" ':"",this.fontStyle?'font-style="'+this.fontStyle+'" ':"",this.fontWeight?'font-weight="'+this.fontWeight+'" ':"",this.textDecoration?'text-decoration="'+this.textDecoration+'" ':"",'style="',this.getSvgStyles(),'" ','transform="translate(',d(o.textLeft,2)," ",d(o.textTop,2),')">',f.join(""),n.textSpans.join(""),"</text>\n","</g>\n")},_getSVGShadows:function(e,f){var o,i,t=[],l=1;if(!this.shadow||!this._boundaries)return t;for(o=0,i=f.length;i>o;o++)if(""!==f[o]){var s=this._boundaries&&this._boundaries[o]?this._boundaries[o].left:0;t.push('<tspan x="',d(s+l+this.shadow.offsetX,2),0===o||this.useNative?'" y':'" dy','="',d(this.useNative?e*o-this.height/2+this.shadow.offsetY:e+(0===o?this.shadow.offsetY:0),2),'" ',this._getFillAttributes(this.shadow.color),">",n.util.string.escapeXml(f[o]),"</tspan>"),l=1}else l++;return t},_getSVGTextAndBg:function(e,n,f){var o=[],d=[],i=1;this._setSVGBg(d);for(var t=0,l=f.length;l>t;t++)""!==f[t]?(this._setSVGTextLineText(f[t],t,o,e,i,d),i=1):i++,this.textBackgroundColor&&this._boundaries&&this._setSVGTextLineBg(d,t,n,e);return{textSpans:o,textBgRects:d}},_setSVGTextLineText:function(e,f,o,i,t){var l=this._boundaries&&this._boundaries[f]?d(this._boundaries[f].left,2):0;o.push('<tspan x="',l,'" ',0===f||this.useNative?"y":"dy",'="',d(this.useNative?i*f-this.height/2:i*t,2),'" ',this._getFillAttributes(this.fill),">",n.util.string.escapeXml(e),"</tspan>")},_setSVGTextLineBg:function(e,n,f,o){e.push("<rect ",this._getFillAttributes(this.textBackgroundColor),' x="',d(f+this._boundaries[n].left,2),'" y="',d(o*n-this.height/2,2),'" width="',d(this._boundaries[n].width,2),'" height="',d(this._boundaries[n].height,2),'"></rect>\n')},_setSVGBg:function(e){this.backgroundColor&&this._boundaries&&e.push("<rect ",this._getFillAttributes(this.backgroundColor),' x="',d(-this.width/2,2),'" y="',d(-this.height/2,2),'" width="',d(this.width,2),'" height="',d(this.height,2),'"></rect>')},_getFillAttributes:function(e){var f=e&&"string"==typeof e?new n.Color(e):"";return f&&f.getSource()&&1!==f.getAlpha()?'opacity="'+f.getAlpha()+'" fill="'+f.setAlpha(1).toRgb()+'"':'fill="'+e+'"'},_set:function(e,n){"fontFamily"===e&&this.path&&(this.path=this.path.replace(/(.*?)([^\/]*)(\.font\.js)/,"$1"+n+"$3")),this.callSuper("_set",e,n),e in this._dimensionAffectingProps&&(this._initDimensions(),this.setCoords())},complexity:function(){return 1}}),n.Text.ATTRIBUTE_NAMES=n.SHARED_ATTRIBUTES.concat("x y dx dy font-family font-style font-weight font-size text-decoration text-anchor".split(" ")),n.Text.DEFAULT_SVG_FONT_SIZE=16,n.Text.fromElement=function(e,f){if(!e)return null;var o=n.parseAttributes(e,n.Text.ATTRIBUTE_NAMES);f=n.util.object.extend(f?n.util.object.clone(f):{},o),"dx"in o&&(f.left+=o.dx),"dy"in o&&(f.top+=o.dy),"fontSize"in f||(f.fontSize=n.Text.DEFAULT_SVG_FONT_SIZE),f.originX||(f.originX="left");var d=new n.Text(e.textContent,f),i=0;return"left"===d.originX&&(i=d.getWidth()/2),"right"===d.originX&&(i=-d.getWidth()/2),d.set({left:d.getLeft()+i,top:d.getTop()-d.getHeight()/2}),d},n.Text.fromObject=function(e){return new n.Text(e.text,o(e))},n.util.createAccessors(n.Text)}("undefined"!=typeof exports?exports:this)}).call(this,_dereq_("buffer").Buffer)},{buffer:2,canvas:1,jsdom:1}]},{},[6])(6)});