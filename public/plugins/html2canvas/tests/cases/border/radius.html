<!DOCTYPE html>
<html>
  <head>
    <title>Borders tests</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <script type="text/javascript" src="../../test.js"></script>
    <style type="text/css">
      div {
        width: 200px;
        height: 200px;
        display: inline-block;
        margin: 10px;
        background:#6F428C;
        border-style: solid;
        border-radius: 50px;
      }

      .box1 {
        border-width: 1px;
        border-left-color: #00b5e2;
        border-top-color: red;
        border-right-color: green;
      }

      .box2 {
        border-width: 3px;
        border-left-color: #00b5e2;
        border-top-color: red;
        border-right-color: green;
      }

      .box3 {
        border-width: 10px;
        border-left-color: transparent;
        border-top-color: red;
        border-right-color: green;
      }

      .box4 {
        border-width: 50px;
        border-left-color: transparent;
        border-top-color: red;
        border-top-width: 10px;
        border-right-color: green;
        border-bottom-right-radius: 190px;
        background-clip: padding-box;
      }

      .box5 {
          margin: 100px;
          border-width: 50px;
          border-left-color: transparent;
          border-top-color: red;
          border-top-width: 10px;
          border-right-color: green;
          border-radius: 25px;
          background-clip: padding-box;
      }

      .box6 {
        height: 200px;
        width: 200px;
        border-radius: 200px;
      }

      html {
        background: #3a84c3;
      }
    </style>
  </head>
  <body>

    <div class="box1">&nbsp;</div>
    <div class="box2">&nbsp;</div>
    <div class="box3">&nbsp;</div>
    <div class="box4">&nbsp;</div>
    <div class="box5">&nbsp;</div>
    <div class="box6">&nbsp;</div>
  </body>
</html>
