<!DOCTYPE html>
<html>
  <head>
    <title>Borders tests</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <script type="text/javascript" src="../../test.js"></script>
    <style type="text/css">
      div {
        width: 200px;
        height: 200px;
        display: inline-block;
        margin: 10px;
        background:#6F428C;
        border-style: solid;
      }

      .box1 {
        border-width: 1px;
        border-color: #00b5e2;
      }

      .box2 {
        border-width: 3px;
        border-color: red;
      }

      .box3 {
        border-width: 10px;
      }

      .box4 {
        border-width: 50px;
        border-color: green;
      }

      html {
        background: #3a84c3;
      }
    </style>
  </head>
  <body>
    <div class="box1">&nbsp;</div>
    <div class="box2">&nbsp;</div>
    <div class="box3">&nbsp;</div>
    <div class="box4">&nbsp;</div>
  </body>
</html>
