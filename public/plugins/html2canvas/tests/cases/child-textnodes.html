<!DOCTYPE html>
<html>
    <head>
        <title>Inline text in the top element</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <script type="text/javascript" src="../test.js"></script>
        <style>
        span {
            color:blue;
        }
        p {
            background-color: green;
        }
        </style>

    </head>
    <body>
        Some inline text <span> followed by text in span </span> followed by more inline text.
        <p>Then a block level element.</p>
        Then more inline text.
    </body>
</html>
