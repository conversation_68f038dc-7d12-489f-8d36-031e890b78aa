<!DOCTYPE html>
<html>
<head>
    <title>Inline text in the top element</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <script type="text/javascript" src="../test.js"></script>
    <style>
        span {
            color:blue;
        }
        p {
            background-color: green;
        }
        div {
            background: red;
            border: 5px solid blue;
        }
    </style>

</head>
<body>
<div style="clip: rect(0px, 400px, 50px, 200px); ">Some inline text <span> followed by text in span </span> followed by more inline text.
    <p>Then a block level element.</p>
    Then more inline text.</div>

<div style="clip: rect(0px, 400px, 50px, 200px); position: relative; ">Some inline text <span> followed by text in span </span> followed by more inline text.
    <p>Then a block level element.</p>
    Then more inline text.</div>

<div style="clip: rect(0px, 400px, 50px, 200px); position: fixed; ">Some inline text <span> followed by text in span </span> followed by more inline text.
    <p>Then a block level element.</p>
    Then more inline text.</div>

<div style="clip: rect(0px, 400px, 50px, 200px); position: absolute; top: 250px; left: 500px;">Some inline text <span> followed by text in span </span> followed by more inline text.
    <p>Then a block level element.</p>
    Then more inline text.</div>
</body>

<div style="clip: rect(0px, 400px, 50px, 200px); position: absolute; top: 500px;">Some inline text <span> followed by text in span </span> followed by more inline text.
    <p>Then a block level element.</p>
    Then more inline text.</div>
</body>
</html>
