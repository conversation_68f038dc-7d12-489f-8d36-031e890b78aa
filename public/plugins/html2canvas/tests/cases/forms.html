<!DOCTYPE html>
<html>
    <head>
        <title>Form tests</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <script type="text/javascript" src="../test.js"></script>
        <style>
            input[type="radio"], input[type="checkbox"] {
                margin: 10px;
                display: inline-block;
            }
        </style>

    </head>
    <body>
        <input type="hidden" value="THIS SHOULD NOT BE VISIBLE!" />
        <input type="text" value="textbox" />
        <input type="password" value="textbox" />
        <input type="text" value="textbox" style="border:5px solid navy;" />
        <input type="text" value="textbox" style="border:5px solid navy;height:40px;" />

        <input type="text" value="textbox" style="border:5px solid navy;height:40px;padding:10px;" />

        <input type="text" value="textbox" style="padding:10px;" />
        <input type="text" value="textbox" style="padding:10px;text-align:right;" />
        <hr />
        <select>
            <option value="1">Value 1</option>
            <option value="2">Value 2</option>
            <option value="3">Value 3</option>
        </select>
        <select>

        </select>
        <select>
            <option value="">2</option>
        </select>


        <select>
            <option value="1">Value 1</option>
            <option value="2" selected>Value 2 with something else</option>
            <option value="3">Value 3</option>
        </select>
        <hr />
        <input type="submit" value="Submit" />
        <input type="Button" value="Button" />
        <input type="Reset" value="Reset" />


        <input type="submit" value="Submit" style="width:200px;" />
        <input type="Button" value="Button" style="width:200px;height:50px;"  />
        <input type="Reset" value="Reset" style="width:200px;height:50px;text-align:left;"  />

        <hr />

        <textarea>  </textarea>
        <textarea style="border-width:10px;"></textarea>

                <textarea> text </textarea>
        <textarea style="border-width:10px;">text</textarea>
        <hr />
        <input type="radio" value="radio1" />
        <input type="radio" value="radio2" style="transform:scale(3)" />
        <input type="RADIO" value="radio3" checked />
        <input type="radio" value="radio4" style="transform:scale(3)" checked />
        <input type="radio" value="radio5" style="transform:scale(3); background: red;" />
        <input type="radio" value="radio6" style="width: 200px;" />
        <input type="radio" value="radio6" style="width: 200px; height: 200px;" />
        <input type="radio" value="radio6" style="width: 200px; height: 200px;" checked />
        <input type="checkbox" value="checkbox1" />
        <input type="chECKbox" value="checkbox2" style="transform:scale(3)" />
        <input type="checkbox" value="checkbox3" checked />
        <input type="checkbox" value="checkbox4" style="transform:scale(3)" checked />
        <input type="checkbox" value="checkbox5" style="transform:scale(3); background: red;" />
        <input type="checkbox" value="checkbox6" style="width: 200px;" />
        <input type="checkbox" value="checkbox6" style="width: 200px; height: 200px;" />
        <input type="checkbox" value="checkbox6" style="width: 200px; height: 200px;" checked />
        <div style="position: absolute; width: 10px; height: 10px; border:1px solid rgb(165,165,165); border-radius: 3px; background: rgb(222,222,222)"></div>
    </body>
</html>
