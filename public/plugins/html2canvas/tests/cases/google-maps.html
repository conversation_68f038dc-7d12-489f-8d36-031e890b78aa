<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title>google maps</title>
    <script>
        var dontRun = true;
    </script>
    <script type="text/javascript" src="../test.js"></script>
    <script src="https://maps.googleapis.com/maps/api/js?sensor=false"></script>
    <style>
        #map {
            width: 100%;
            height: 500px;
            position: absolute;
            top: 0;
        }
        body, html {
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
<div>
    <h2>Google maps</h2>
    <div id="map"></div>
    <script>
        var mapOptions = { zoom: 13, center: new google.maps.LatLng(60.1656623, 24.9477731) };
        var map = new google.maps.Map(document.querySelector('#map'), mapOptions);
        google.maps.event.addListenerOnce(map, 'idle', function(){
            if (typeof(window.run) === "function") {
                window.run();
            } else {
                dontRun = undefined;
            }
        });
    </script>
</div>
</body>
</html>
