<!DOCTYPE html>
<html>
<head>
    <title>Nested transform tests</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <script type="text/javascript" src="../../test.js"></script>
    <style>
        #first {
            background: indianred;
            margin-top: 100px;
        }
        #second {
            border: 15px solid red;
            background: darkseagreen;
            -webkit-transform: rotate(7.5deg);  /* Chrome, Safari 3.1+ */
            -moz-transform: rotate(7.5deg);  /* Firefox 3.5-15 */
            -ms-transform: rotate(7.5deg);  /* IE 9 */
            -o-transform: rotate(7.5deg);  /* Opera 10.50-12.00 */
            transform: rotate(7.5deg);
        }
        #third {
            background: cadetblue;
            -webkit-transform: rotate(-70.5deg);  /* Chrome, Safari 3.1+ */
            -moz-transform: rotate(-70.5deg);  /* Firefox 3.5-15 */
            -ms-transform: rotate(-70.5deg);  /* IE 9 */
            -o-transform: rotate(-70.5deg);  /* Opera 10.50-12.00 */
            transform: rotate(-70.5deg);  /* Firefox 16+, IE 10+, Opera 12.10+ */

        }
        #fourth {
            background: #bc8f8f;
        }

        div {
            display: inline-block;

        }
    </style>

</head>
<body>
<div id="first">First level content <div id="second">with second level content <div id="third">and third level content</div>, ending second</div>, ending first</div>
<div id="fourth">something else</div>
</body>
</html>
