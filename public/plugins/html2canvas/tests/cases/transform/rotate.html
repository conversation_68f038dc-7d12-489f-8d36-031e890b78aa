<!DOCTYPE html>
<html>
<head>
    <title>Rotation transform tests</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <script type="text/javascript" src="../../test.js"></script>
    <style>
        .container {
            position: relative;
        }
        .image1 {
            position: absolute;
            left: 100px;
            -webkit-transform: rotate(-45deg);  /* Chrome, Safari 3.1+ */
            -moz-transform: rotate(-45deg);  /* Firefox 3.5-15 */
            -ms-transform: rotate(-45deg);   /* IE 9 */
            -o-transform: rotate(-45deg);   /* Opera 10.50-12.00 */
            transform:rotate(-45deg);
        }
        .image2 {
            position: absolute;
            left: 634px;
            -webkit-transform: rotate(90deg);  /* Chrome, Safari 3.1+ */
            -moz-transform: rotate(90deg);  /* Firefox 3.5-15 */
            -ms-transform: rotate(90deg);   /* IE 9 */
            -o-transform: rotate(90deg);   /* Opera 10.50-12.00 */
            transform:rotate(90deg);
        }
        .image3 {
            position: absolute;
            top: 250px;
            left: 100px;
            -webkit-transform: rotate(45deg);  /* Chrome, Safari 3.1+ */
            -moz-transform: rotate(45deg);  /* Firefox 3.5-15 */
            -ms-transform: rotate(45deg);   /* IE 9 */
            -o-transform: rotate(45deg);   /* Opera 10.50-12.00 */
            transform:rotate(45deg);
        }
    </style
</head>
<body>
<div class="container">
    <div class="image1">
        <img src="../../assets/image.jpg" style="width: 200px; height: 200px;">
    </div>
    <div class="image2">
        <img src="../../assets/image2.jpg" style="width: 50px; height: 200px;">
    </div>
    <div class="image3">
        <img src="../../assets/image.jpg" style="width: 100px; height: 200px;">
    </div>
</div>
</body>
</html>
