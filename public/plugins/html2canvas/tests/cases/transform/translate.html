<!DOCTYPE html>
<html>
<head>
    <title>Nested transform tests</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <script type="text/javascript" src="../../test.js"></script>
    <style>
        #first {
            background: indianred;
            margin-top: 100px;
        }
        #second {
            border: 10px solid red;
            background: darkseagreen;
            -webkit-transform: translate(125px);  /* Chrome, Safari 3.1+ */
            -moz-transform: translate(125px);  /* Firefox 3.5-15 */
            -ms-transform: translate(125px);  /* IE 9 */
            -o-transform: translate(125px);  /* Opera 10.50-12.00 */
            transform: translate(125px);
        }
        #third {
            background: cadetblue;
            -webkit-transform: translate(-100px, -25px);  /* Chrome, Safari 3.1+ */
            -moz-transform: translate(100px, -25px);  /* Firefox 3.5-15 */
            -ms-transform: translate(100px, -25px);  /* IE 9 */
            -o-transform: translate(100px, -25px);  /* Opera 10.50-12.00 */
            transform: translate(100px, -25px);
            -webkit-transform-origin: 100px 50px;
            -moz-transform-origin: 100px 50px;
            -ms-transform-origin: 100px 50px;
            -o-transform-origin: 100px 50px;
            transform-origin: 100px 50px;

        }
        div {
            display: inline-block;
            padding: 10px;
        }
    </style>

</head>
<body>
<div id="first">First level content <div id="second">with second level content <div id="third">and third level content</div>, ending second</div>, ending first</div>
</body>
</html>
