<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" 
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>Stacking and float</title>
<!-- https://developer.mozilla.org/en-US/docs/Web/Guide/CSS/Understanding_z_index/Stacking_and_float?redirectlocale=en-US&redirectslug=CSS%2FUnderstanding_z-index%2FStacking_and_float -->
<script type="text/javascript" src="../../test.js"></script>
<style type="text/css">

div {
   font: 12px Arial;
}

span.bold { font-weight: bold; }

#absdiv1 {
   opacity: 0.7;
   position: absolute;
   width: 150px;
   height: 200px;
   top: 10px;
   right: 140px;
   border: 1px solid #990000;
   background-color: #ffdddd;
   text-align: center;
}

#normdiv {
   /*opacity: 0.7;*/
   height: 100px;
   border: 1px solid #999966;
   background-color: #ffffcc;
   margin: 0px 10px 0px 10px;
   text-align: left;
}

#flodiv1 {
   opacity: 0.7;
   margin: 0px 10px 0px 20px;
   float: left;
   width: 150px;
   height: 200px;
   border: 1px solid #009900;
   background-color: #ccffcc;
   text-align: center;
}

#flodiv2 {
   opacity: 0.7;
   margin: 0px 20px 0px 10px;
   float: right;
   width: 150px;
   height: 200px;
   border: 1px solid #009900;
   background-color: #ccffcc;
   text-align: center;
}

#absdiv2 {
   opacity: 0.7;
   position: absolute;
   width: 150px;
   height: 100px;
   top: 130px;
   left: 100px;
   border: 1px solid #990000;
   background-color: #ffdddd;
   text-align: center;
}

</style></head>

<body>

<br /><br />

<div id="absdiv1">
<br /><span class="bold">DIV #1</span>
<br />position: absolute;
</div>

<div id="flodiv1">
<br /><span class="bold">DIV #2</span>
<br />float: left;
</div>

<div id="flodiv2">
<br /><span class="bold">DIV #3</span>
<br />float: right;
</div>

<br />

<div id="normdiv">
<br /><span class="bold">DIV #4</span>
<br />no positioning
</div>

<div id="absdiv2">
<br /><span class="bold">DIV #5</span>
<br />position: absolute;
</div>

</body></html>