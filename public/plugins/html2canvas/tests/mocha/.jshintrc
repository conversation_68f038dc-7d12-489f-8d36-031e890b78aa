{"curly": true, "eqeqeq": true, "immed": true, "latedef": false, "newcap": true, "noarg": true, "sub": true, "undef": true, "boss": true, "eqnull": true, "browser": true, "globals": {"jQuery": true}, "predef": ["deepEqual", "module", "test", "$", "QUnit", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NodeContainer", "StackingContext", "TextContainer", "ImageLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Support", "bind", "Promise", "ImageContainer", "ProxyImageContainer", "DummyImageContainer", "Font", "FontMetrics", "GradientContainer", "LinearGradientContainer", "WebkitGradientContainer", "log", "smallImage", "parseBackgrounds"]}