<html>
<head>
    <meta charset="utf-8">
    <title>Mocha Tests</title>
    <link rel="stylesheet" href="lib/mocha.css" />
    <script src="../../node_modules/bluebird/js/browser/bluebird.js"></script>
    <script src="../../dist/html2canvas.js"></script>
    <script src="../assets/jquery-1.6.2.js"></script>
    <script src="lib/expect.js"></script>
    <script src="lib/mocha.js"></script>
    <style>
        #block {
            width: 200px;
            height: 200px;
        }
        #green-block {
            width: 200px;
            height: 200px;
            background: green;
        }
        #background-block {
            width: 200px;
            height: 200px;
            background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNgaGD4DwAChAGAJVtEDQAAAABJRU5ErkJggg==) red;
        }
        #gradient-block {
            width: 200px;
            height: 200px;
            background-image: -webkit-linear-gradient(top, #008000, #008000);
            background-image:    -moz-linear-gradient(to bottom, #008000, #008000);
            background-image:         linear-gradient(to bottom, #008000, #008000);
        }
    </style>
</head>
<body>
<div id="mocha"></div>
<script>mocha.setup('bdd')</script>
<div id="block"></div>
<div id="green-block"></div>
<div id="background-block"></div>
<div id="gradient-block"></div>
<script>
    describe("options.background", function() {
        it("with hexcolor", function(done) {
            html2canvas(document.querySelector("#block"), {background: '#008000'}).then(function(canvas) {
                expect(canvas.width).to.equal(200);
                expect(canvas.height).to.equal(200);
                validCanvasPixels(canvas);
                done();
            }).catch(function(error) {
                done(error);
            });
        });

        it("with named color", function(done) {
            html2canvas(document.querySelector("#block"), {background: 'green'}).then(function(canvas) {
                expect(canvas.width).to.equal(200);
                expect(canvas.height).to.equal(200);
                validCanvasPixels(canvas);
                done();
            }).catch(function(error) {
                done(error);
            });
        });

        it("with element background", function(done) {
            html2canvas(document.querySelector("#green-block"), {background: 'red'}).then(function(canvas) {
                expect(canvas.width).to.equal(200);
                expect(canvas.height).to.equal(200);
                validCanvasPixels(canvas);
                done();
            }).catch(function(error) {
                done(error);
            });
        });
    });

    describe('element background', function() {
        it('with background-color', function(done) {
            html2canvas(document.querySelector("#green-block")).then(function(canvas) {
                expect(canvas.width).to.equal(200);
                expect(canvas.height).to.equal(200);
                validCanvasPixels(canvas);
                done();
            }).catch(function(error) {
                done(error);
            });
        });

        it('with background-image', function(done) {
            html2canvas(document.querySelector("#background-block")).then(function(canvas) {
                expect(canvas.width).to.equal(200);
                expect(canvas.height).to.equal(200);
                validCanvasPixels(canvas);
                done();
            }).catch(function(error) {
                done(error);
            });
        });

        it('with gradient background-image', function(done) {
            html2canvas(document.querySelector("#gradient-block")).then(function(canvas) {
                expect(canvas.width).to.equal(200);
                expect(canvas.height).to.equal(200);
                validCanvasPixels(canvas);
                done();
            }).catch(function(error) {
                done(error);
            });
        });
    });

    function validCanvasPixels(canvas) {
        var ctx = canvas.getContext("2d");
        var data = ctx.getImageData(0, 0, canvas.width, canvas.height).data;
        for (var i = 0, len = data.length; i < len; i+=4) {
            if (data[i] !== 0 || data[i+1] !== 128 || data[i+2] !== 0 || data[i+3] !== 255) {
                expect().fail("Invalid canvas data");
            }
        }
    }

    mocha.checkLeaks();
    mocha.globals(['jQuery']);
    if (window.mochaPhantomJS) {
        mochaPhantomJS.run();
    }
    else {
        mocha.run();
    }
    mocha.suite.afterAll(function() {
        document.body.setAttribute('data-complete', 'true');
    });
</script>
</body>
</html>
