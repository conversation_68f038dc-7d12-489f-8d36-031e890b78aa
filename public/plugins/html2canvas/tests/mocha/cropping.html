<html>
<head>
    <meta charset="utf-8">
    <title>Mocha Tests</title>
    <link rel="stylesheet" href="lib/mocha.css" />
    <script src="../../node_modules/bluebird/js/browser/bluebird.js"></script>
    <script src="../../dist/html2canvas.js"></script>
    <script src="../assets/jquery-1.6.2.js"></script>
    <script src="lib/expect.js"></script>
    <script src="lib/mocha.js"></script>
</head>
<body>
<div id="mocha"></div>
<script>mocha.setup('bdd')</script>
<div style="background: green; width: 40px; height:40px;" id="block"></div>
<div style="visibility: hidden">
    <iframe src="iframe1.htm" style="height: 350px; width: 450px; border: 0;" scrolling="no" id="frame1"></iframe>
    <iframe src="iframe2.htm" style="height: 350px; width: 450px; border: 0;" scrolling="yes" id="frame2"></iframe>
</div>
<script>
    describe("Cropping", function() {
        it("window view with body", function(done) {
            html2canvas(document.body, {type: 'view'}).then(function(canvas) {
                expect(canvas.width).to.equal(window.innerWidth);
                expect(canvas.height).to.equal(window.innerHeight);
                done();
            }).catch(function(error) {
                done(error);
            });
        });

        it("window view with documentElement", function(done) {
            html2canvas(document.documentElement, {type: 'view'}).then(function(canvas) {
                expect(canvas.width).to.equal(window.innerWidth);
                expect(canvas.height).to.equal(window.innerHeight);
                done();
            }).catch(function(error) {
                done(error);
            });
        });

        it("iframe with body", function(done) {
            html2canvas(document.querySelector("#frame1").contentWindow.document.body, {type: 'view'}).then(function(canvas) {
                expect(canvas.width).to.equal(450);
                expect(canvas.height).to.equal(350);
                validCanvasPixels(canvas);
                done();
            }).catch(function(error) {
                done(error);
            });
        });

        it("iframe with document element", function(done) {
            html2canvas(document.querySelector("#frame1").contentWindow.document.documentElement, {type: 'view'}).then(function(canvas) {
                expect(canvas.width).to.equal(450);
                expect(canvas.height).to.equal(350);
                validCanvasPixels(canvas);
                done();
            }).catch(function(error) {
                done(error);
            });
        });

        it("with node", function(done) {
            html2canvas(document.querySelector("#block")).then(function(canvas) {
                expect(canvas.width).to.equal(40);
                expect(canvas.height).to.equal(40);
                validCanvasPixels(canvas);
                done();
            }).catch(function(error) {
                done(error);
            });
        });

        it("with node and size", function(done) {
            html2canvas(document.querySelector("#block"), {width: 20, height: 20}).then(function(canvas) {
                expect(canvas.width).to.equal(20);
                expect(canvas.height).to.equal(20);
                validCanvasPixels(canvas);
                done();
            }).catch(function(error) {
                done(error);
            });
        });

        document.querySelector("#frame2").addEventListener("load", function() {

            document.querySelector("#frame2").contentWindow.scrollTo(0, 350);
            describe("with scrolled content", function() {
                it("iframe with body", function(done) {
                    html2canvas(document.querySelector("#frame2").contentWindow.document.body, {type: 'view'}).then(function(canvas) {
                        // phantomjs issue https://github.com/ariya/phantomjs/issues/10581
                        if (canvas.height !== 1200) {
                            expect(canvas.width).to.equal(450);
                            expect(canvas.height).to.equal(350);
                            validCanvasPixels(canvas);
                        }
                        done();
                    }).catch(function(error) {
                        done(error);
                    });
                });

                it("iframe with document element", function(done) {
                    html2canvas(document.querySelector("#frame2").contentWindow.document.documentElement, {type: 'view'}).then(function(canvas) {
                        // phantomjs issue https://github.com/ariya/phantomjs/issues/10581
                        if (canvas.height !== 1200) {
                            expect(canvas.width).to.equal(450);
                            expect(canvas.height).to.equal(350);
                            validCanvasPixels(canvas);
                        }
                        done();
                    }).catch(function(error) {
                        done(error);
                    });
                });
            });
        }, false);
    });


    function validCanvasPixels(canvas) {
        var ctx = canvas.getContext("2d");
        var data = ctx.getImageData(0, 0, canvas.width, canvas.height).data;
        for (var i = 0, len = data.length; i < len; i+=4) {
            if (data[i] !== 0 || data[i+1] !== 128 || data[i+2] !== 0 || data[i+3] !== 255) {
                expect().fail("Invalid canvas data");
            }
        }
    }

    mocha.checkLeaks();
    mocha.globals(['jQuery']);
    if (window.mochaPhantomJS) {
        mochaPhantomJS.run();
    }
    else {
        mocha.run();
    }
    mocha.suite.afterAll(function() {
        document.body.setAttribute('data-complete', 'true');
    });
</script>
</body>
</html>
