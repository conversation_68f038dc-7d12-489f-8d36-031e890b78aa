<html>
<head>
    <meta charset="utf-8">
    <title>Mocha Tests</title>
    <link rel="stylesheet" href="lib/mocha.css" />
    <script src="../../node_modules/bluebird/js/browser/bluebird.js"></script>
    <script src="../../dist/html2canvas.js"></script>
    <script src="../assets/jquery-1.6.2.js"></script>
    <script src="lib/expect.js"></script>
    <script src="lib/mocha.js"></script>
    <style>
        .block {
            width: 200px;
            height: 200px;
        }
    </style>
</head>
<body>
<div id="mocha"></div>
<script>mocha.setup('bdd')</script>
<div id="block1" class="block">
    <input type="text" value="text" />
</div>
<div id="block2" class="block">
    <input type="password" value="password" />
</div>
<div id="block3" class="block">
    <input type="text" value="text" />
</div>

<div id="block4" class="block">
    <textarea>text</textarea>
</div>

<div id="block5" class="block">
    <select>
        <option value="1">1</option>
        <option value="2" selected>2</option>
        <option value="3">3</option>
    </select>
</div>

<div id="green-block"></div>
<script>
    var CanvasRenderer = html2canvas.CanvasRenderer;

    describe("Rendering input values", function() {
        it("uses default value for input[type='text']", function(done) {
            CanvasRenderer.prototype.text = function(text) {
                expect(text).to.equal('text');
            };
            html2canvas(document.querySelector("#block1"), {renderer: CanvasRenderer, strict: true}).then(function(canvas) {
                expect(canvas.width).to.equal(200);
                expect(canvas.height).to.equal(200);
                done();
            }).catch(function(error) {
                done(error);
            });
        });

        it("uses transformed value for input[type='password']", function(done) {
            var count = 0;
            CanvasRenderer.prototype.text = function(text) {
                expect(text).to.equal('•');
                count++;
            };
            html2canvas(document.querySelector("#block2"), {renderer: CanvasRenderer, strict: true}).then(function(canvas) {
                expect(canvas.width).to.equal(200);
                expect(canvas.height).to.equal(200);
                expect(count).to.equal("password".length);
                done();
            }).catch(function(error) {
                done(error);
            });
        });

        it("used property and not attribute for rendering", function(done) {
            document.querySelector("#block3 input").value = 'updated';

            CanvasRenderer.prototype.text = function(text) {
                expect(text).to.equal('updated');
            };
            html2canvas(document.querySelector("#block3"), {renderer: CanvasRenderer, strict: true}).then(function(canvas) {
                expect(canvas.width).to.equal(200);
                expect(canvas.height).to.equal(200);
                done();
            }).catch(function(error) {
                done(error);
            });
        });

        describe("Rendering textarea values", function() {
            it("uses default value correctly", function(done) {
                CanvasRenderer.prototype.text = function(text) {
                    expect(text).to.equal('text');
                };
                html2canvas(document.querySelector("#block4"), {renderer: CanvasRenderer, strict: true}).then(function(canvas) {
                    expect(canvas.width).to.equal(200);
                    expect(canvas.height).to.equal(200);
                    done();
                }).catch(function(error) {
                    done(error);
                });
            });

            it("used property and not attribute for rendering", function(done) {
                document.querySelector("#block4 textarea").value = 'updated';

                CanvasRenderer.prototype.text = function(text) {
                    expect(text).to.equal('updated');
                };
                html2canvas(document.querySelector("#block4"), {renderer: CanvasRenderer, strict: true}).then(function(canvas) {
                    expect(canvas.width).to.equal(200);
                    expect(canvas.height).to.equal(200);
                    done();
                }).catch(function(error) {
                    done(error);
                });
            });
        });

        describe("Select values", function() {
            it("uses default value correctly", function(done) {
                CanvasRenderer.prototype.text = function(text) {
                    expect(text).to.equal('2');
                };
                html2canvas(document.querySelector("#block5"), {renderer: CanvasRenderer, strict: true}).then(function(canvas) {
                    expect(canvas.width).to.equal(200);
                    expect(canvas.height).to.equal(200);
                    done();
                }).catch(function(error) {
                    done(error);
                });
            });

            it("used property and not attribute for rendering", function(done) {
                document.querySelector("#block5 select").value = '3';

                CanvasRenderer.prototype.text = function(text) {
                    expect(text).to.equal('3');
                };
                html2canvas(document.querySelector("#block5"), {renderer: CanvasRenderer, strict: true}).then(function(canvas) {
                    expect(canvas.width).to.equal(200);
                    expect(canvas.height).to.equal(200);
                    done();
                }).catch(function(error) {
                    done(error);
                });
            });
        });
    });

    mocha.checkLeaks();
    mocha.globals(['jQuery']);
    if (window.mochaPhantomJS) {
        mochaPhantomJS.run();
    }
    else {
        mocha.run();
    }
    mocha.suite.afterAll(function() {
        document.body.setAttribute('data-complete', 'true');
    });
</script>
</body>
</html>
