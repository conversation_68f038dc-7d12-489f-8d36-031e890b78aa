<html>
<head>
    <meta charset="utf-8">
    <title>Mocha Tests</title>
    <link rel="stylesheet" href="lib/mocha.css" />
    <script src="../../node_modules/bluebird/js/browser/bluebird.js"></script>
    <script src="../../dist/html2canvas.js"></script>
    <script src="../assets/jquery-1.6.2.js"></script>
    <script src="lib/expect.js"></script>
    <script src="lib/mocha.js"></script>
    <style>
        #block {
            width: 200px;
            height: 200px;
        }
        #green-block {
            width: 200px;
            height: 200px;
            background: green;
        }
    </style>
</head>
<body>
<div id="mocha"></div>
<div id="block"></div>
<div id="green-block"></div>
<script>mocha.setup('bdd')</script>
<script>
    describe("Multiple renders", function() {
        it("render correctly", function(done) {
            this.timeout(10000);
            var d = 0;
            var count = 3;
            for (var i = 0; i < count; i++) {
                html2canvas(document.querySelector('#green-block')).then(function (canvas) {
                    expect(canvas.width).to.equal(200);
                    expect(canvas.height).to.equal(200);
                    validCanvasPixels(canvas);
                    canvas.width = canvas.height = 10;
                    d++;
                    if (d === count) {
                        done();
                    }
                });
            }
        });

        it("render correctly when non sequential", function(done) {
            this.timeout(10000);
            var d = 0;
            var count = 3;
            for (var i = 0; i < count; i++) {
                html2canvas(document.querySelector('#block'), {onclone: function(document) {
                    return new Promise(function(resolve) {
                        document.querySelector('#block').style.backgroundColor = 'green';
                        setTimeout(function() {
                            resolve();
                        }, 100);
                    });
                }}).then(function (canvas) {
                    expect(canvas.width).to.equal(200);
                    expect(canvas.height).to.equal(200);
                    validCanvasPixels(canvas);
                    canvas.width = canvas.height = 10;
                    d++;
                    if (d === count) {
                        done();
                    }
                });
            }
        });
    });

    function validCanvasPixels(canvas) {
        var ctx = canvas.getContext("2d");
        var data = ctx.getImageData(0, 0, canvas.width, canvas.height).data;
        for (var i = 0, len = 200*199*4; i < len; i+=4) {
            if (data[i] !== 0 || data[i+1] !== 128 || data[i+2] !== 0 || data[i+3] !== 255) {
                console.log(i, data[i], data[i+1], data[i+2], data[i+3]);
                expect().fail("Invalid canvas data");
            }
        }
    }

    mocha.suite.afterAll(function() {
        document.body.setAttribute('data-complete', 'true');
    });

    mocha.checkLeaks();
    mocha.globals(['jQuery']);
    if (window.mochaPhantomJS) {
        mochaPhantomJS.run();
    }
    else {
        mocha.run();
    }
</script>
</body>
</html>
