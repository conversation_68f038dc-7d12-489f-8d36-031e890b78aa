<html>
<head>
    <meta charset="utf-8">
    <title>Mocha Tests</title>
    <link rel="stylesheet" href="lib/mocha.css" />
    <script src="../../node_modules/bluebird/js/browser/bluebird.js"></script>
    <script src="../../dist/html2canvas.js"></script>
    <script src="../assets/jquery-1.6.2.js"></script>
    <script src="lib/expect.js"></script>
    <script src="lib/mocha.js"></script>
    <style>
        #block2 {
            background: red;
        }

        .my-css-class #block2 {
            background: green;
        }
    </style>
</head>
<body>
<div id="mocha"></div>
<script>mocha.setup('bdd')</script>
<div style="background: red; width: 200px; height:200px;" id="block"></div>
<div style="width: 200px; height:200px;" id="block2"></div>
<script>
    describe("options.onclone", function() {
        it("with a function", function(done) {
            html2canvas(document.querySelector("#block"), {onclone: function(document) {
                document.querySelector("#block").style.background = "green";
            }}).then(function(canvas) {
                expect(canvas.width).to.equal(200);
                expect(canvas.height).to.equal(200);
                expect(document.querySelector("#block").style.backgroundColor).to.equal("red");
                validCanvasPixels(canvas);
                done();
            }).catch(function(error) {
                done(error);
            });
        });

        it("with a promise", function(done) {
            html2canvas(document.querySelector("#block"), {onclone: function(document) {
                return new Promise(function(resolve) {
                    setTimeout(function() {
                        document.querySelector("#block").style.background = "green";
                        resolve();
                    }, 500);
                });
            }}).then(function(canvas) {
                expect(canvas.width).to.equal(200);
                expect(canvas.height).to.equal(200);
                expect(document.querySelector("#block").style.backgroundColor).to.equal("red");
                validCanvasPixels(canvas);
                done();
            }).catch(function(error) {
                done(error);
            });
        });

        it("add class to node", function(done) {
            html2canvas(document.querySelector("#block2"), {onclone: function(document) {
                document.documentElement.className = "my-css-class";
            }}).then(function(canvas) {
                expect(canvas.width).to.equal(200);
                expect(canvas.height).to.equal(200);
                validCanvasPixels(canvas);
                done();
            }).catch(function(error) {
                done(error);
            });
        });
    });

    function validCanvasPixels(canvas) {
        var ctx = canvas.getContext("2d");
        var data = ctx.getImageData(0, 0, canvas.width, canvas.height).data;
        for (var i = 0, len = data.length; i < len; i+=4) {
            if (data[i] !== 0 || data[i+1] !== 128 || data[i+2] !== 0 || data[i+3] !== 255) {
                expect().fail("Invalid canvas data");
            }
        }
    }

    mocha.checkLeaks();
    mocha.globals(['jQuery']);
    if (window.mochaPhantomJS) {
        mochaPhantomJS.run();
    }
    else {
        mocha.run();
    }
    mocha.suite.afterAll(function() {
        document.body.setAttribute('data-complete', 'true');
    });
</script>
</body>
</html>
