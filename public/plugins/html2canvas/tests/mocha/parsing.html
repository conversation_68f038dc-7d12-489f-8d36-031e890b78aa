<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Mocha Tests</title>
    <link rel="stylesheet" href="lib/mocha.css" />
    <script src="../../dist/html2canvas.js"></script>
    <script src="../assets/jquery-1.6.2.js"></script>
    <script src="lib/expect.js"></script>
    <script src="lib/mocha.js"></script>
</head>
<body>
<div id="mocha"></div>
<script>mocha.setup('bdd')</script>
<div style="visibility: hidden">
    <div id="borders">Yep, here's some more.
        <div style="border-width: 1px 0;">Div 1</div>
        <div style="border-width: 1em 0;">Div 2</div>
        <div style="border-width: thin medium thick;">Some more divs</div>
        <div style="border-width: 5% 6px 12%;"></div> <!-- percentages aren't valid -->
        <div style="border-width: 5em 5ex 5in 5cm;"></div>
        <div style="border-width: 500em 500ex 500in 500cm;"></div>
        <div style="border-width: 5mm 5pt 5pc 5px;"></div>
        <div style="border-width: auto inherit;"></div>
        <div style="border-width: 500mm 500pt 500pc 500px;"></div>
    </div>

    <div id="padding">
        <div style="padding: 1px 0;"></div>
        <div style="padding: 1em 0;"></div>
        <div style="padding: thin medium thick;"></div>
        <div style="padding: 5em 5ex 5in 5cm;"></div>
        <div style="padding: 500em 500ex 500in 500cm;"></div>
        <div style="padding: 5mm 5pt 5pc 5px;"></div>
        <div style="padding: 500mm 500pt 500pc 500px;"></div>
        <div style="padding: 1px 5%;"></div>
        <div style="padding: 15% 0 3%;"></div>
    </div>

    <div id="textShadows">
        <div style=""></div>
        <div style="text-shadow: 1px 1px 1px"></div>
        <div style="text-shadow: 2px 2px rgb(2, 2, 2)"></div>
        <div style="text-shadow: 3px 3px rgba(2, 2, 2, .2)"></div>
        <div style="text-shadow: rgb(2, 2, 2) 4px 4px"></div>
        <div style="text-shadow: rgba(2, 2, 2, .2) 5px 5px"></div>
        <div style="text-shadow: rgb(2, 2, 2) 6px 6px, #222222 2px 2px"></div>
        <div style="text-shadow: 7px 7px rgba(2, 2, 2, .2), #222222 2px 2px"></div>
    </div>

    <div id="backgroundPosition">
        <div style="background-position: 1px 0;"></div>
        <div style="background-position: 1em 0;"></div>
        <div style="background-position: thin medium;"></div>
        <div style="background-position: 5em 5ex;"></div>
        <div style="background-position: 5in 5cm;"></div>
        <div style="background-position: 500in 500cm;"></div>
        <div style="background-position: 500em 500ex;"></div>
        <div style="background-position: 5pc 5px;"></div>
        <div style="background-position: 500pc 500px;"></div>
        <div style="background-position: 5mm 5pt;"></div>
        <div style="background-position: 500mm 500pt;"></div>
    </div>

    <div id="backgroundPositionPercentage">
        <div style="background-position: 5% 6px;"></div>
        <div style="background-position: center center;"></div>
        <div style="background-position: left bottom;"></div>
    </div>

    <div id="backgroundGradients">
        <style scoped>
        .linearGradientSimple {
            /* FF 3.6+ */
            background: -moz-linear-gradient(left, #ff0000, #ffff00, #00ff00);
            /* Chrome,Safari4+ */
            background: -webkit-gradient(linear, left center, right center, color-stop(#ff0000), color-stop(#ffff00), color-stop(#00ff00));
            /* Chrome 10+, Safari 5.1+ */
            background: -webkit-linear-gradient(left, #ff0000, #ffff00, #00ff00);
            /* Opera 11.10+ */
            background: -o-linear-gradient(left, #ff0000, #ffff00, #00ff00);
            /* IE 10+ */
            background: -ms-linear-gradient(left, #ff0000, #ffff00, #00ff00);
            /* W3C */
            background: linear-gradient(left, #ff0000, #ffff00, #00ff00);
        }
        .linearGradientSimple2 {
            /* FF 3.6+ */
            background: -moz-linear-gradient(left, red, #ff0, #0f0);
            /* Chrome,Safari4+ */
            background: -webkit-gradient(linear, left center, right center, color-stop(red), color-stop(#ff0), color-stop(#0f0));
            /* Chrome 10+, Safari 5.1+ */
            background: -webkit-linear-gradient(left, red, #ff0, #0f0);
            /* Opera 11.10+ */
            background: -o-linear-gradient(left, red, #ff0, #0f0);
            /* IE 10+ */
            background: -ms-linear-gradient(left, red, #ff0, #0f0);
            /* W3C */
            background: linear-gradient(left, red, #ff0, #0f0);
        }
        .linearGradientWithStops {
            /* FF 3.6+ */
            background: -moz-linear-gradient(left, #cedbe9 0%, #aac5de 17%, #6199c7 50%, #3a84c3 51%, #419ad6 59%, #4bb8f0 71%, #3a8bc2 84%, #26558b 100%);
            /* Chrome, Safari 4+ */
            background: -webkit-gradient(linear, left center, right center, color-stop(0%, #cedbe9), color-stop(17%, #aac5de), color-stop(50%, #6199c7), color-stop(51%, #3a84c3), color-stop(59%, #419ad6), color-stop(71%, #4bb8f0), color-stop(84%, #3a8bc2), color-stop(100%, #26558b));
            /* Chrome 10+, Safari 5.1+ */
            background: -webkit-linear-gradient(left, #cedbe9 0%, #aac5de 17%, #6199c7 50%, #3a84c3 51%, #419ad6 59%, #4bb8f0 71%, #3a8bc2 84%, #26558b 100%);
            /* Opera 11.10+ */
            background: -o-linear-gradient(left, #cedbe9 0%, #aac5de 17%, #6199c7 50%, #3a84c3 51%, #419ad6 59%, #4bb8f0 71%, #3a8bc2 84%, #26558b 100%);
            /* IE 10+ */
            background: -ms-linear-gradient(left, #cedbe9 0%, #aac5de 17%, #6199c7 50%, #3a84c3 51%, #419ad6 59%, #4bb8f0 71%, #3a8bc2 84%, #26558b 100%);
            /* W3C */
            background: linear-gradient(left, #cedbe9 0%, #aac5de 17%, #6199c7 50%, #3a84c3 51%, #419ad6 59%, #4bb8f0 71%, #3a8bc2 84%, #26558b 100%);
        }
        .linearGradientWithPixelLengthStops {
            width: 100px;
            height: 100px;
            /* FF 3.6+ */
            background: -moz-linear-gradient(left, #cedbe9 0%, #aac5de 17px, #6199c7 50%, #3a84c3 51px, #419ad6 59%, #4bb8f0 71px, #3a8bc2 84%, #26558b 100px);
            /* Chrome, Safari 4+ */
            background: -webkit-gradient(linear, left center, right center, color-stop(0%, #cedbe9), color-stop(17px, #aac5de), color-stop(50%, #6199c7), color-stop(51%, #3a84c3), color-stop(59%, #419ad6), color-stop(71px, #4bb8f0), color-stop(84%, #3a8bc2), color-stop(100px, #26558b));
            /* Chrome 10+, Safari 5.1+ */
            background: -webkit-linear-gradient(left, #cedbe9 0%, #aac5de 17px, #6199c7 50%, #3a84c3 51px, #419ad6 59%, #4bb8f0 71px, #3a8bc2 84%, #26558b 100px);
            /* Opera 11.10+ */
            background: -o-linear-gradient(left, #cedbe9 0%, #aac5de 17px, #6199c7 50%, #3a84c3 51px, #419ad6 59%, #4bb8f0 71px, #3a8bc2 84%, #26558b 100px);
            /* IE 10+ */
            background: -ms-linear-gradient(left, #cedbe9 0%, #aac5de 17px, #6199c7 50%, #3a84c3 51px, #419ad6 59%, #4bb8f0 71px, #3a8bc2 84%, #26558b 100px);
            /* W3C */
            background: linear-gradient(left, #cedbe9 0%, #aac5de 17px, #6199c7 50%, #3a84c3 51px, #419ad6 59%, #4bb8f0 71px, #3a8bc2 84%, #26558b 100px);
        }
        .linearGradient5 {
            /* FF 3.6+ */
            background: -moz-linear-gradient(top, #f0b7a1 0%, #8c3310 50%, #752201 51%, #bf6e4e 100%);
            /* Chrome, Safari 4+ */
            background: -webkit-gradient(linear, center top, center bottom, color-stop(0%, #f0b7a1), color-stop(50%, #8c3310), color-stop(51%, #752201), color-stop(100%, #bf6e4e));
            /* Opera 11.10+ */
            background: -o-linear-gradient(top, #f0b7a1 0%, #8c3310 50%, #752201 51%, #bf6e4e 100%);
            /* IE 10+ */
            background: -ms-linear-gradient(top, #f0b7a1 0%, #8c3310 50%, #752201 51%, #bf6e4e 100%);
            /* W3C */
            background: linear-gradient(top, #f0b7a1 0%, #8c3310 50%, #752201 51%, #bf6e4e 100%);
        }
        .linearGradient6 {
            /* FF 3.6+ */
            background: -moz-linear-gradient(top, #F00 0, green 31.4159%, #0000fF 51%, rgba(0, 0, 0, 0.0) 100%);
            /* Chrome, Safari 4+ */
            background: -webkit-gradient(linear, center top, center bottom, color-stop(0, #F00), color-stop(31.4159%, green), color-stop(51%, #0000fF), color-stop(100%, rgba(0, 0, 0, 0.0)));
            /* Opera 11.10+ */
            background: -o-linear-gradient(top, #F00 0, green 31.4159%, #0000fF 51%, rgba(0, 0, 0, 0.0) 100%);
            /* IE 10+ */
            background: -ms-linear-gradient(top, #F00 0, green 31.4159%, #0000fF 51%, rgba(0, 0, 0, 0.0) 100%);
            /* W3C */
            background: linear-gradient(top, #F00 0, green 31.4159%, #0000fF 51%, rgba(0, 0, 0, 0.0) 100%);
        }
        .linearGradient7 {
            background: -webkit-linear-gradient(0deg, #ddd, #ddd 50%, transparent 50%);
            background: linear-gradient(0deg, #ddd, #ddd 50%, transparent 50%);
        }

        .radialGradient {
            background: -moz-radial-gradient(75% 19%, ellipse closest-side, #ababab, #0000ff 33%,#991f1f 100%);
            background: -webkit-radial-gradient(75% 19%, ellipse closest-side, #ababab, #0000ff 33%,#991f1f 100%);
            background: -o-radial-gradient(75% 19%, ellipse closest-side, #ababab, #0000ff 33%,#991f1f 100%);
            background: -ms-radial-gradient(75% 19%, ellipse closest-side, #ababab, #0000ff 33%,#991f1f 100%);
            background: radial-gradient(75% 19%, ellipse closest-side, #ababab, #0000ff 33%,#991f1f 100%);
        }

        .radialGradient2 {
            background: -moz-radial-gradient(75% 19%, ellipse closest-corner, #ababab, #0000ff 33%,#991f1f 100%);
            background: -webkit-radial-gradient(75% 19%, ellipse closest-corner, #ababab, #0000ff 33%,#991f1f 100%);
            background: -o-radial-gradient(75% 19%, ellipse closest-corner, #ababab, #0000ff 33%,#991f1f 100%);
            background: -ms-radial-gradient(75% 19%, ellipse closest-corner, #ababab, #0000ff 33%,#991f1f 100%);
            background: radial-gradient(75% 19%, ellipse closest-corner, #ababab, #0000ff 33%,#991f1f 100%);
        }
        .radialGradient3 {
            background: -moz-radial-gradient(75% 19%, ellipse farthest-side, #ababab, #0000ff 33%,#991f1f 100%);
            background: -webkit-radial-gradient(75% 19%, ellipse farthest-side, #ababab, #0000ff 33%,#991f1f 100%);
            background: -o-radial-gradient(75% 19%, ellipse farthest-side, #ababab, #0000ff 33%,#991f1f 100%);
            background: -ms-radial-gradient(75% 19%, ellipse farthest-side, #ababab, #0000ff 33%,#991f1f 100%);
            background: radial-gradient(75% 19%, ellipse farthest-side, #ababab, #0000ff 33%,#991f1f 100%)
        }
        .radialGradient4 {
            background: -moz-radial-gradient(75% 19%, ellipse farthest-corner, #ababab, #0000ff 33%,#991f1f 100%);
            background: -webkit-radial-gradient(75% 19%, ellipse farthest-corner, #ababab, #0000ff 33%,#991f1f 100%);
            background: -o-radial-gradient(75% 19%, ellipse farthest-corner, #ababab, #0000ff 33%,#991f1f 100%);
            background: -ms-radial-gradient(75% 19%, ellipse farthest-corner, #ababab, #0000ff 33%,#991f1f 100%);
            background: radial-gradient(75% 19%, ellipse farthest-corner, #ababab, #0000ff 33%,#991f1f 100%);
        }
        .radialGradient5 {
            background: -moz-radial-gradient(75% 19%, ellipse contain, #ababab, #0000ff 33%,#991f1f 100%);
            background: -webkit-radial-gradient(75% 19%, ellipse contain, #ababab, #0000ff 33%,#991f1f 100%);
            background: -o-radial-gradient(75% 19%, ellipse contain, #ababab, #0000ff 33%,#991f1f 100%);
            background: -ms-radial-gradient(75% 19%, ellipse contain, #ababab, #0000ff 33%,#991f1f 100%);
            background: radial-gradient(75% 19%, ellipse contain, #ababab, #0000ff 33%,#991f1f 100%);
        }
        .radialGradient6 {
            background: -moz-radial-gradient(75% 19%, ellipse cover, #ababab, #0000ff 33%,#991f1f 100%);
            background: -webkit-radial-gradient(75% 19%, ellipse cover, #ababab, #0000ff 33%,#991f1f 100%);
            background: -o-radial-gradient(75% 19%, ellipse cover, #ababab, #0000ff 33%,#991f1f 100%);
            background: -ms-radial-gradient(75% 19%, ellipse cover, #ababab, #0000ff 33%,#991f1f 100%);
            background: radial-gradient(75% 19%, ellipse cover, #ababab, #0000ff 33%,#991f1f 100%);
        }
        </style>
        <div class="linearGradientSimple"></div>
        <div class="linearGradientSimple2"></div>
        <div class="linearGradientWithStops"></div>
        <div class="linearGradientWithPixelLengthStops"></div>
        <div class="linearGradient5"></div>
        <div class="linearGradient6"></div>
        <div class="linearGradient7"></div>
        <div class="radialGradient"></div>
        <div class="radialGradient2"></div>
        <div class="radialGradient3"></div>
        <div class="radialGradient4"></div>
        <div class="radialGradient5"></div>
        <div class="radialGradient6"></div>
    </div>
</div>
<script src="css.js"></script>
<script src="gradients.js"></script>
<script>
    mocha.checkLeaks();
    mocha.globals(['jQuery']);
    if (window.mochaPhantomJS) {
        mochaPhantomJS.run();
    }
    else {
        mocha.run();
    }
    mocha.suite.afterAll(function() {
        document.body.setAttribute('data-complete', 'true');
    });
</script>
</body>
</html>
