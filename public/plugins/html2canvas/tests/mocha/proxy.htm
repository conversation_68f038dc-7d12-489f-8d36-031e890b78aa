<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title>Proxy tests</title>
    <link rel="stylesheet" href="lib/mocha.css" />
    <script src="../../dist/html2canvas.js"></script>
    <script src="../assets/jquery-1.6.2.js"></script>
    <script src="lib/expect.js"></script>
    <script src="lib/mocha.js"></script>
    <style>
        #content {
            width: 200px;
            height: 200px;
        }
    </style>
</head>
<body>
<div id="mocha"></div>
<script>mocha.setup('bdd')</script>
<div id="content" style="display: inline-block;background: red;">
    <iframe src="http://localhost:8083/tests/mocha/iframe3.htm" style="border: 0; width: 200px; height: 200px;" scrolling="no"></iframe>
</div>
<script>
    describe("Proxy", function() {
        it("with iframe through proxy", function (done) {
            this.timeout(10000);
            html2canvas(document.querySelector("#content"), {proxy: 'http://localhost:8082'}).then(function (canvas) {
                validCanvasPixels(canvas);
                done();
            }).catch(function (error) {
                done(error);
            });
        });

        it("with iframe without proxy", function (done) {
            html2canvas(document.querySelector("#content")).then(function (canvas) {
                expect(canvas.width).to.equal(200);
                expect(canvas.height).to.equal(200);
                done();
            }).catch(function (error) {
                done(error);
            });
        });

        it("with url using proxy", function (done) {
            html2canvas("http://localhost:8083/tests/mocha/iframe3.htm", {proxy: 'http://localhost:8082', width: 200, height: 200, type: 'view'}).then(function (canvas) {
                expect(canvas.width).to.equal(200);
                expect(canvas.height).to.equal(200);
                done();
            }).catch(function (error) {
                done(error);
            });
        });

        it("with url without proxy", function (done) {
            html2canvas("http://localhost:8083/tests/mocha/iframe3.htm").then(function () {
                done("Should throw error");
            }).catch(function (error) {
                expect(error).to.equal("Proxy must be used when rendering url");
                done();
            });
        });
    });

    function validCanvasPixels(canvas) {
        var ctx = canvas.getContext("2d");
        var data = ctx.getImageData(0, 0, canvas.width, canvas.height).data;
        for (var i = 0, len = data.length; i < len; i+=4) {
            if (data[i] !== 0 || data[i+1] !== 128 || data[i+2] !== 0 || data[i+3] !== 255) {
                expect().fail("Invalid canvas data");
            }
        }
    }

    mocha.checkLeaks();
    mocha.globals(['jQuery']);
    if (window.mochaPhantomJS) {
        mochaPhantomJS.run();
    }
    else {
        mocha.run();
    }
    mocha.suite.afterAll(function() {
        document.body.setAttribute('data-complete', 'true');
    });
</script>
</body>
</html>
