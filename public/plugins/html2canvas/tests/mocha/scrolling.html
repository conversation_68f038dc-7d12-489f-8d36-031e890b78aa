<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title>Scrolling tests</title>
    <link rel="stylesheet" href="lib/mocha.css" />
    <script src="../../node_modules/bluebird/js/browser/bluebird.js"></script>
    <script src="../../dist/html2canvas.js"></script>
    <script src="../assets/jquery-1.6.2.js"></script>
    <script src="lib/expect.js"></script>
    <script src="lib/mocha.js"></script>
</head>
<body>
<div id="mocha"></div>
<script>mocha.setup('bdd')</script>
<div id="scroll-render" style="height: 200px; width: 200px;">
    <div style="height: 500px; width: 400px;overflow: scroll;" id="scrollable">
        <div style="height: 500px;background: red;"></div>
        <div style="height: 650px; background: green"></div>
    </div>
</div>
<div style="height: 2200px"></div>
<div style="height: 500px;background: green;" id="bottom-content"><a name="content">&nbsp;</a></div>
<script>
    describe("Scrolling", function() {
        it("with random scroll", function (done) {
            $(window).scrollTop(123);
            setTimeout(function() {
                html2canvas(document.body, {type: 'view'}).then(function () {
                    expect($(window).scrollTop()).to.equal(123);
                    done();
                }).catch(function (error) {
                    done(error);
                });
            }, 0);
        });

        it("with url anchor", function (done) {
            window.location.hash = "#content";
            setTimeout(function() {
                var top = $(window).scrollTop();
                html2canvas(document.body, {type: 'view'}).then(function () {
                    var currentTop = $(window).scrollTop();
                    window.location.hash = "";
                    expect(currentTop).to.be.greaterThan(1500);
                    if ((currentTop - top) !== 200) { // phantomjs issue
                        expect(currentTop).to.equal(top);
                    }
                    done();
                }).catch(function (error) {
                    done(error);
                });
            }, 0);
        });

        it("with content scroll", function (done) {
            $("#scrollable").scrollTop(500);
            setTimeout(function() {
                html2canvas(document.querySelector("#scroll-render")).then(function (canvas) {
                    expect($("#scrollable").scrollTop()).to.equal(500);
                    expect(canvas.width).to.equal(200);
                    expect(canvas.height).to.equal(200);
                    validCanvasPixels(canvas);
                    done();
                }).catch(function (error) {
                    done(error);
                });
            }, 0);
        });


        it("with window scroll", function (done) {
            $(window).scrollTop(500);
            setTimeout(function() {
                console.log(document.querySelector("#bottom-content").getBoundingClientRect().top);
                html2canvas(document.querySelector("#bottom-content")).then(function (canvas) {
                    expect($(window).scrollTop()).to.equal(500);
                    validCanvasPixels(canvas);
                    done();
                }).catch(function (error) {
                    done(error);
                });
            }, 0);
        });
    });

    function validCanvasPixels(canvas) {
        var ctx = canvas.getContext("2d");
        var data = ctx.getImageData(0, 0, canvas.width, canvas.height).data;
        for (var i = 0, len = 200*199*4; i < len; i+=4) {
            if (data[i] !== 0 || data[i+1] !== 128 || data[i+2] !== 0 || data[i+3] !== 255) {
                console.log(i, data[i], data[i+1], data[i+2], data[i+3]);
                expect().fail("Invalid canvas data");
            }
        }
    }

    after(function() {
        if (history) {
            history.pushState("", document.title, window.location.pathname + window.location.search);
        }
    });

    mocha.checkLeaks();
    mocha.globals(['jQuery']);
    if (window.mochaPhantomJS) {
        mochaPhantomJS.run();
    }
    else {
        mocha.run();
    }
    mocha.suite.afterAll(function() {
        document.body.setAttribute('data-complete', 'true');
    });
</script>
</body>
</html>
