<!DOCTYPE HTML>
<html lang="en-US">
<head>
	<meta charset="UTF-8">
	<title></title>
	<script src="js/jquery-1.3.2.min.js"></script>
	<script src="js/JsBarcode.all.js"></script>

</head>
<body>
<div id="barcode-submit">
	<div>
		纸张高：<input type="text" class="sheetHeight" value="30">mm<br/>
		条码高：<input type="text" class="codeHeight" value="10">mm<br/>
	</div>
	<input type="button" id="subtest" value="生成条码">
	<input type="button" id="btnPrint" value="打印"/>
</div>
<iframe id="crop-content" src='iframe-demo.html' frameborder='0' height='100%' width='200' style='height: 555px; border:1px solid #ccc;'></iframe>
<div class="barcode-box">
</div>
Please fill in the code : <input type="text" class="barcodeValue" value="92345670wws"><br/>
<input type="text" class="barcodeValue" value="12345670ww"><br/>
<script>
	$(function(){
		//生成条形码
		$('#subtest').click(function(event) {
			/* Act on the event */
			var sheetHeight = $('.sheetHeight').val();
			var codeHeight = $('.codeHeight').val();
			var dataLst = [];
			$('.barcodeValue').each(function(index, el) {
				var $this = $(el);
				dataLst.push($this.val());

			});
			var options = {
				data : dataLst,
				height : codeHeight,
				sheetHeigth : sheetHeight,
				//element: $('.barcode-box')
				element: $('#crop-content').contents().find('#barcodeTarget')
			};

			generateBarCode(options);

		});

		$("#btnPrint").click(function(){
			//ie打印
			//window.frames['crop-content'].focus();
			//window.print();

			//$('#generator').hide();
			$('#crop-content')[0].contentWindow.iframePrint();
			//window.print();

		});
	});


</script>
</body>
</html>
