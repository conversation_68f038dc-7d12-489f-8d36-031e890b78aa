/*!
 * jquery-confirm v2.0.0 (http://craftpip.github.io/jquery-confirm/)
 * Author: boniface pereira
 * Website: www.craftpip.com
 * Contact: <EMAIL>
 *
 * Copyright 2013-2015 jquery-confirm
 * Licensed under MIT (https://github.com/craftpip/jquery-confirm/blob/master/LICENSE)
 */
body.jconfirm-noscroll {
  overflow: hidden !important;
}
@-webkit-keyframes jconfirm-rotate {
  from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
@keyframes jconfirm-rotate {
  from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
.jconfirm {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 8888;
  font-family: inherit;
  overflow: hidden;
}
.jconfirm .jconfirm-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;  filter:alpha(opacity=0);
  -webkit-transition: all .4s;
          transition: all .4s;
}
.jconfirm .jconfirm-bg.seen {
  opacity: 0.8;filter:alpha(opacity=80);
}
.jconfirm .jconfirm-scrollpane {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
}
.jconfirm .jconfirm-box {
  background: white;
  border-radius: 4px;
  position: relative;
  outline: none;
}
.jconfirm .jconfirm-box div.closeIcon {
  height: 20px;
  width: 20px;
  position: absolute;
  top: 12px;
  right: 12px;
  cursor: pointer;
  opacity: .6;filter:alpha(opacity=60);
  text-align: center;
  display: none;
}
.jconfirm .jconfirm-box div.closeIcon:hover {
  opacity: 1;filter:alpha(opacity=100);
}
.jconfirm .jconfirm-box div.title {
  font-size: 24px;
  font-weight: bold;
  font-family: inherit;
  padding: 10px 15px 5px;
}
.jconfirm .jconfirm-box div.content {
  /*padding-top: 10px;
  padding: 10px 15px 10px;*/
  margin: 0 auto;
}
.jconfirm .jconfirm-box div.content:empty {
  height: 40px;
  position: relative;
  opacity: 0.6;filter:alpha(opacity=60);
}
.jconfirm .jconfirm-box div.content:empty:before {
  content: '';
  height: 20px;
  width: 20px;
  border: solid 2px #aaa;
  position: absolute;
  left: 50%;
  margin-left: -45px;
  border-radius: 20%;
  -webkit-animation: jconfirm-rotate 1s infinite;
          animation: jconfirm-rotate 1s infinite;
}
.jconfirm .jconfirm-box div.content:empty:after {
  content: 'loading..';
  position: absolute;
  left: 50%;
  margin-left: -15px;
}
.jconfirm .jconfirm-box .buttons {
  padding: 10px 15px;
}
.jconfirm .jconfirm-box .buttons button + button {
  margin-left: 5px;
}
.jconfirm .jquery-clear {
  clear: both;
}
.jconfirm.rtl {
  direction: rtl;
}
.jconfirm.rtl div.closeIcon {
  left: 12px;
  right: auto;
}
.jconfirm.white .jconfirm-bg {
  background-color: #cccccc;
}
.jconfirm.white .jconfirm-box {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
}
.jconfirm.white .jconfirm-box .buttons {
  float: right;
}
.jconfirm.white .jconfirm-box .buttons button {
  border: none;
  background-image: none;
  text-transform: uppercase;
  font-size: 14px;
  font-weight: bold;
  text-shadow: none;
  -webkit-transition: background .1s;
          transition: background .1s;
  color: white;
}
.jconfirm.white .jconfirm-box .buttons button.btn-default {
  box-shadow: none;
  color: #333;
}
.jconfirm.white .jconfirm-box .buttons button.btn-default:hover {
  background: #ddd;
}
.jconfirm.black .jconfirm-bg {
  background-color: rgba(0, 0, 0, 0.5);
}
.jconfirm.black .jconfirm-box {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  background: #444;
  border-radius: 5px;
  color: white;
}
.jconfirm.black .jconfirm-box .buttons {
  float: right;
}
.jconfirm.black .jconfirm-box .buttons button {
  border: none;
  background-image: none;
  text-transform: uppercase;
  font-size: 14px;
  font-weight: bold;
  text-shadow: none;
  -webkit-transition: background .1s;
          transition: background .1s;
  color: white;
}
.jconfirm.black .jconfirm-box .buttons button.btn-default {
  box-shadow: none;
  color: #fff;
  background: none;
}
.jconfirm.black .jconfirm-box .buttons button.btn-default:hover {
  background: #666;
}
.jconfirm.hololight .jconfirm-bg {
  background-color: rgba(0, 0, 0, 0.5);
}
.jconfirm.hololight .jconfirm-box {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  border-radius: 2px;
  overflow: hidden;
}
.jconfirm.hololight .jconfirm-box div.title {
  font-weight: inherit;
  border-bottom: solid 2px #76CFDF;
  color: #76CFDF;
}
.jconfirm.hololight .jconfirm-box .buttons {
  border-top: solid 2px #E7E7E7;
  width: 100%;
  float: none;
  padding: 0;
}
.jconfirm.hololight .jconfirm-box .buttons button {
  margin: 0;
  border: none;
  background: #fff;
  border-radius: 0px;
  width: 50%;
  padding: 13px;
  font-size: 16px;
  font-weight: bold;
  color: #666;
}
.jconfirm.hololight .jconfirm-box .buttons button + button {
  border-left: solid 2px #E7E7E7;
}
.jconfirm.holodark .jconfirm-bg {
  background-color: rgba(0, 0, 0, 0.5);
}
.jconfirm.holodark .jconfirm-box {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  border-radius: 2px;
  background: #333;
  overflow: hidden;
}
.jconfirm.holodark .jconfirm-box div.closeIcon {
  color: white;
}
.jconfirm.holodark .jconfirm-box div.title {
  font-weight: inherit;
  border-bottom: solid 2px #76CFDF;
  color: #76CFDF;
}
.jconfirm.holodark .jconfirm-box div.content {
  color: white;
}
.jconfirm.holodark .jconfirm-box .buttons {
  border-top: solid 2px rgba(255, 255, 255, 0.2);
  width: 100%;
  float: none;
  padding: 0;
}
.jconfirm.holodark .jconfirm-box .buttons button {
  margin: 0;
  border: none;
  background: #333;
  border-radius: 0px;
  width: 50%;
  padding: 13px;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  text-shadow: none;
}
.jconfirm.holodark .jconfirm-box .buttons button + button {
  border-left: solid 2px rgba(255, 255, 255, 0.2);
}
.jconfirm .jconfirm-box.hilight {
  -webkit-animation: hilight 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
          animation: hilight 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
}
@-webkit-keyframes hilight {
  10%,
  90% {
    -webkit-transform: translate3d(-2px, 0, 0);
            transform: translate3d(-2px, 0, 0);
  }
  20%,
  80% {
    -webkit-transform: translate3d(4px, 0, 0);
            transform: translate3d(4px, 0, 0);
  }
  30%,
  50%,
  70% {
    -webkit-transform: translate3d(-8px, 0, 0);
            transform: translate3d(-8px, 0, 0);
  }
  40%,
  60% {
    -webkit-transform: translate3d(8px, 0, 0);
            transform: translate3d(8px, 0, 0);
  }
}
@keyframes hilight {
  10%,
  90% {
    -webkit-transform: translate3d(-2px, 0, 0);
            transform: translate3d(-2px, 0, 0);
  }
  20%,
  80% {
    -webkit-transform: translate3d(4px, 0, 0);
            transform: translate3d(4px, 0, 0);
  }
  30%,
  50%,
  70% {
    -webkit-transform: translate3d(-8px, 0, 0);
            transform: translate3d(-8px, 0, 0);
  }
  40%,
  60% {
    -webkit-transform: translate3d(8px, 0, 0);
            transform: translate3d(8px, 0, 0);
  }
}
/*Transition rules*/
.jconfirm {
  -webkit-perspective: 400px;
          perspective: 400px;
}
.jconfirm .jconfirm-box {
  opacity: 1;filter:alpha(opacity=100);
  -webkit-transition-property: -webkit-transform, opacity, box-shadow;
          transition-property: transform, opacity, box-shadow;
}
.jconfirm .jconfirm-box.anim-top,
.jconfirm .jconfirm-box.anim-left,
.jconfirm .jconfirm-box.anim-right,
.jconfirm .jconfirm-box.anim-bottom,
.jconfirm .jconfirm-box.anim-opacity,
.jconfirm .jconfirm-box.anim-zoom,
.jconfirm .jconfirm-box.anim-scale,
.jconfirm .jconfirm-box.anim-none,
.jconfirm .jconfirm-box.anim-rotate,
.jconfirm .jconfirm-box.anim-rotatex,
.jconfirm .jconfirm-box.anim-rotatey,
.jconfirm .jconfirm-box.anim-scaley,
.jconfirm .jconfirm-box.anim-scalex {
  opacity: 0;filter:alpha(opacity=0)
}
.jconfirm .jconfirm-box.anim-rotate {
  -webkit-transform: rotate(90deg);
      -ms-transform: rotate(90deg);
          transform: rotate(90deg);
}
.jconfirm .jconfirm-box.anim-rotatex {
  -webkit-transform: rotateX(90deg);
          transform: rotateX(90deg);
  -webkit-transform-origin: center;
      -ms-transform-origin: center;
          transform-origin: center;
}
.jconfirm .jconfirm-box.anim-rotatey {
  -webkit-transform: rotatey(90deg);
      -ms-transform: rotatey(90deg);
          transform: rotatey(90deg);
  -webkit-transform-origin: center;
      -ms-transform-origin: center;
          transform-origin: center;
}
.jconfirm .jconfirm-box.anim-scaley {
  -webkit-transform: scaley(1.5);
      -ms-transform: scaley(1.5);
          transform: scaley(1.5);
  -webkit-transform-origin: center;
      -ms-transform-origin: center;
          transform-origin: center;
}
.jconfirm .jconfirm-box.anim-scalex {
  -webkit-transform: scalex(1.5);
      -ms-transform: scalex(1.5);
          transform: scalex(1.5);
  -webkit-transform-origin: center;
      -ms-transform-origin: center;
          transform-origin: center;
}
.jconfirm .jconfirm-box.anim-top {
  -webkit-transform: translate(0px, -100px);
      -ms-transform: translate(0px, -100px);
          transform: translate(0px, -100px);
}
.jconfirm .jconfirm-box.anim-left {
  -webkit-transform: translate(-100px, 0px);
      -ms-transform: translate(-100px, 0px);
          transform: translate(-100px, 0px);
}
.jconfirm .jconfirm-box.anim-right {
  -webkit-transform: translate(100px, 0px);
      -ms-transform: translate(100px, 0px);
          transform: translate(100px, 0px);
}
.jconfirm .jconfirm-box.anim-bottom {
  -webkit-transform: translate(0px, 100px);
      -ms-transform: translate(0px, 100px);
          transform: translate(0px, 100px);
}
.jconfirm .jconfirm-box.anim-zoom {
  -webkit-transform: scale(1.2);
      -ms-transform: scale(1.2);
          transform: scale(1.2);
}
.jconfirm .jconfirm-box.anim-scale {
  -webkit-transform: scale(0.5);
      -ms-transform: scale(0.5);
          transform: scale(0.5);
}
.jconfirm .jconfirm-box.anim-none {
  display: none;
}
.jconfirm.supervan .jconfirm-bg {
  background-color: rgba(54, 70, 93, 0.95);
}
.jconfirm.supervan .jconfirm-box {
  background-color: transparent;
}
.jconfirm.supervan .jconfirm-box div.closeIcon {
  color: white;
}
.jconfirm.supervan .jconfirm-box div.title {
  text-align: center;
  color: white;
  font-size: 28px;
  font-weight: normal;
}
.jconfirm.supervan .jconfirm-box div.content {
  text-align: center;
  color: white;
}
.jconfirm.supervan .jconfirm-box .buttons {
  text-align: center;
}
.jconfirm.supervan .jconfirm-box .buttons button {
  font-size: 16px;
  border-radius: 2px;
  background: #303f53;
  text-shadow: none;
  border: none;
  color: white;
  width: 25%;
  padding: 10px;
}
/*ie8 css author:<EMAIL> begin*/
.ie8 .col-md-2{width: 20.66666667%;}
.ie8 .col-md-3{width: 30.66666667%;}
.ie8 .col-md-4{width: 33.33333333%;}
.ie8 .col-md-5{width: 41.66666667%;}
.ie8 .col-md-6{width: 50%;}
.ie8 .col-md-7{width: 58.33333333%}
.ie8 .col-md-8{width: 66.66666667%;}
.ie8 .col-md-9 {width: 75%;}
.ie8 .col-md-10 {width: 83.33333333%;}
.ie8 .col-md-11 {width: 91.66666667%;}
.ie8 .col-md-12{width: 1138px;margin: 0 auto;}

.ie8 .col-md-offset-1{margin-left: 8.33333333%;}
.ie8 .col-md-offset-2{margin-left: 16.66666667%;}
.ie8 .col-md-offset-3{margin-left: 25%;}
.ie8 .col-md-offset-4{margin-left: 33.33333333%;}
.ie8 .col-md-offset-5{margin-left: 41.66666667%;}
.ie8 .col-md-offset-6{margin-left: 50%;}
.ie8 .col-md-offset-7{margin-left: 58.33333333%;}
.ie8 .col-md-offset-8{margin-left: 66.66666667%;}
.ie8 .col-md-offset-9{margin-left: 75%;}
.ie8 .col-md-offset-10{margin-left: 83.33333333%;}
.ie8 .col-md-offset-11{margin-left: 91.66666667%;}
.ie8 .col-md-offset-12{margin-left: 100%;}
/*ie8 css author:<EMAIL> end*/

/*jconfirm-width-unit css author:<EMAIL> begin*/
.jconfirm-width-unit .col-md-2{width: 236px;}
.jconfirm-width-unit .col-md-3{width: 283px;}
.jconfirm-width-unit .col-md-4{width: 293px;}
.jconfirm-width-unit .col-md-5{width: 487px;}
.jconfirm-width-unit .col-md-6{width: 585px;}
.jconfirm-width-unit .col-md-7{width: 682px}
.jconfirm-width-unit .col-md-8{width: 780px;}
.jconfirm-width-unit .col-md-9 {width: 878px;}
.jconfirm-width-unit .col-md-10 {width: 975px;}
.jconfirm-width-unit .col-md-11 {width: 1072px;}
.jconfirm-width-unit .col-md-12{width: 1138px;margin: 0 auto;}

.jconfirm-width-unit .col-md-offset-1{margin-left: 100px;}
.jconfirm-width-unit .col-md-offset-2{margin-left: 197px;}
.jconfirm-width-unit .col-md-offset-3{margin-left: 295px;}
.jconfirm-width-unit .col-md-offset-4{margin-left: 340px;}
.jconfirm-width-unit .col-md-offset-5{margin-left: 413px;}
.jconfirm-width-unit .col-md-offset-6{margin-left: 444px;}
.jconfirm-width-unit .col-md-offset-7{margin-left: 468px;}
.ie8.jconfirm-width-unit .col-md-offset-7{margin-left: 568px;}


/*jconfirm-width-unit css author:<EMAIL> end*/
/*2015-12-24 add <EMAIL>*/
.jconfirm-box .content{width: 100%;}
.jconfirm .jconfirm-box div.title{height: 34px; width: auto; position: static; padding: 6px 15px 5px 10px;  font-size: 16px; line-height: 18px;font-weight: 500; color: #fff;background: #545454;}
.jconfirm .jconfirm-box div.closeIcon{ top: 5px;color: #fff;opacity: 1;}
.jconfirm .jconfirm-box div.closeIcon:hover{ opacity: .6;}
.jconfirm .jconfirm-box .buttons{width: 100%;height: 63px;background-color: #ffffff;
    border-top: 0;box-shadow: 0 1px 0 #ffffff inset;text-align: center;}

.jconfirm.white .jconfirm-box .buttons .btn-box-primary{border:2px solid #6a7271; background:#fff; color:#6a7271;  border-color: #6a7271;
    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff',endColorstr='#ffffff',GradientType=0);text-shadow:none;}
.jconfirm.white .jconfirm-box .buttons button.btn-box-primary:hover{
    color: #fff; background-color: #8a908f; border-color: #8a908f;
    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ee8311',endColorstr='#ee8311',GradientType=0); border-radius:5px;
    overflow:hidden;}

/*2016-3-16 add <EMAIL>*/
.jconfirm.white .jconfirm-box .buttons .btn-box{height: 35px;}
.jconfirm.white .jconfirm-box{ box-shadow: 0 1px 6px rgba(0, 0, 0,0.7);border: 2px solid #545454; border-radius: 2px;}
.jconfirm-box .prompt-box{width:100%; height: 70px;border: 1px solid #a5da61;border-radius: 10px;background: #f6fced;}
.jconfirm-box .prompt-box .p-text{background: url(../../../images/ti_bg.png) no-repeat 7% 50%;
    height: 70px;
    line-height: 70px;
    text-align: center;
    font-size: 14px;
    display: inline-block;
    margin: 0 auto;
    width: 100%;
    padding-left: 45px;}
.jconfirm .succeed-box .jconfirm-box{border-radius: 10px;border: 0;box-shadow:none; background: none;}
/*.jconfirm .succeed-box{ width: 236px;margin-left: 468px;}*/
.jconfirm .succeed-box .prompt{width: 100%;}
