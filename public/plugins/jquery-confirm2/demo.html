<!doctype html>
<html>
<head>
<meta charset="utf-8">
<title>窗体</title>
<link href="../bootstrap/css/bootstrap.css" rel="stylesheet" type="text/css" />
<link href="../bootstrap/css/bootstrap-theme.css" rel="stylesheet" type="text/css" />
<link href="../umeditor-dev/themes/default/_css/umeditor.css" rel="stylesheet" type="text/css" />
<link href="css/jquery-confirm.css" rel="stylesheet" type="text/css" />
<link href="../summernote/dist/font-awesome.css" rel="stylesheet" type="text/css" />

<link href="../../stylesheets/common.css" rel="stylesheet" type="text/css" />
<link href="../../stylesheets/shopm.css" rel="stylesheet" type="text/css" />
<link href="../summernote/dist/summernote.css" rel="stylesheet" type="text/css" />
<style type="text/css">
	.pop-container{padding: 15px 15px;}
</style>

</head>
<body style="overflow:auto;">
<h2>交互窗</h2>
<!-- 系统信息-提示1 begin -->
<div class="pop-container ml30">
	<!-- 按钮触发模态框 -->
	<button class="btn btn-primary btn-lg dome1">
	   默认提示
	</button>

</div>
<!-- 系统信息-提示1 end -->

<!-- 系统信息-提示2 begin -->
<div class="pop-container ml30">
	<!-- 按钮触发模态框 -->
	<button class="btn btn-primary btn-lg dome2">
	   动态内容
	</button>
</div>
<!-- 系统信息-提示2 end -->

<!-- 系统信息-提示3 begin -->
<div class="pop-container ml30">
	<!-- 按钮触发模态框 -->
	<button class="btn btn-primary btn-lg dome3">
		载入HTML
	</button>
	<!-- 模态框（Modal）dome3 -->
	<div id="myModal3" style="display:none;">
		content: $('#myModal3').html()
		<!--系统信息-提示9 begin -->
		<div class="pop-container ml30">
			<!-- 按钮触发模态框 -->
			<button class="btn btn-primary btn-lg dome9">
				关闭（载入HTML）窗体
			</button>
		</div>
		<!-- 系统信息-提示9 end -->
	</div>
</div>
<!-- 系统信息-提示3 end -->

<!-- 系统信息-提示4 begin -->
<div class="pop-container ml30">
	<!-- 按钮触发模态框 -->
	<button class="btn btn-primary btn-lg dome4-1">
		url:remote.html 1
	</button>
	<button class="btn btn-primary btn-lg dome4-2">
		url:remote.html 2
	</button>
	<button class="btn btn-primary btn-lg dome4-3">
		load from bower.json
	</button>
	<button class="btn btn-primary btn-lg dome4-4">
		remote-um.html
	</button>
</div>
<!-- 系统信息-提示4 end -->

<!--系统信息-提示5 begin -->
<div class="pop-container ml30">
	<!-- 按钮触发模态框 -->
	<button class="btn btn-primary btn-lg dome5">
		带回调函数
	</button>
</div>
<!-- 系统信息-提示5 end -->

<!--系统信息-提示6 begin -->
<div class="pop-container ml30">
	<!-- 按钮触发模态框 -->
	<button class="btn btn-primary btn-lg dome6">
		alert
	</button>
</div>
<!-- 系统信息-提示6 end -->

<!--系统信息-提示7 begin -->
<div class="pop-container ml30">
	<!-- 按钮触发模态框 -->
	<button class="btn btn-primary btn-lg dome7">
		警告1
	</button>
</div>
<!-- 系统信息-提示7 end -->

<!--系统信息-提示8 begin -->
<div class="pop-container ml30">
	<!-- 按钮触发模态框 -->
	<button class="btn btn-primary btn-lg dome8">
		警告2
	</button>
</div>
<!-- 系统信息-提示8 end -->




<script type="text/javascript" src="../jquery/jquery-1.11.3.js"></script>
<script type="text/javascript" src="../bootstrap/js/bootstrap.js"></script>
<script type="text/javascript" src="../summernote/dist/summernote.js"></script>
<script type="text/javascript" src="../summernote/lang/summernote-zh-CN.js"></script>
<script type="text/javascript" src="js/pretty.js"></script>
<script type="text/javascript" src="js/jquery-confirm.js"></script>
<script type="text/javascript">
    $(document).ready(function(){

		$('.dome1').click(function(event) {
			$.confirm({
			    animation: 'top',
			    closeAnimation: 'scale'
			});
		});

		$('.dome2').click(function(event) {
			$.confirm({
			    animation: 'top',
			    closeAnimation: 'scale',
			    title: '自定义title',
			    content: '<div>我是js动态写入的！包括title</div>'
			});
		});

		var objDome3 = ''
		$('.dome3').click(function(event) {
			objDome3 = $.confirm({
			    animation: 'top',
			    closeAnimation: 'scale',
			    title: '动态html',
			    content: $('#myModal3').html()
			});
		});

		$('.dome4-1').click(function(event) {
			$.confirm({
			    animation: 'top',
			    closeAnimation: 'scale',
			    title: 'Ajax load(url)',
			    content: 'url:remote.html'
			});
		});

		$('.dome4-2').click(function(event) {
			var editWindow = $.confirm({
				confirmButtonPlural:'保存并发布',
			    animation: 'top',
			    closeAnimation: 'scale',
			    columnClass: 'col-md-12',
			    title: 'Ajax load(url)',
			    content: 'url:https://www.baidu.com/',
			    confirm: function () {
				editWindow.$body.find('button.btn-primary').hide();
				    //editWindow.$body.find('button[data-type="plural"]').hide();
					return false;
			    },
			    confirmPlural: function(){
			    	return true;
			    }

			});

		});

		$('.dome4-3').click(function(event) {
			$.confirm({
			    animation: 'top',
			    closeAnimation: 'scale',
			    columnClass: 'col-md-12',
			    title: 'Ajax load json',
			    content: function ($obj) {
                    return $.ajax({
                        url: 'bower.json',
                        dataType: 'json',
                        success: function (data) {
                            $obj.setContent('Plugin description: '+data.description);
                            // ohh success!
                        },
                        error: function () {
                            $obj.contentDiv.html("Something went wrong, please try again later.");
                            // Handle it your way
                        }
                    });
                }
			});
		});

		$('.dome4-4').click(function(event) {
			var dateTime = new Date().getTime();
			$.confirm({
			    animation: 'top',
			    closeAnimation: 'scale',
			    title: '编辑器',
			    columnClass: 'col-md-8 col-md-offset-2',
			    //content: 'url:../summernote/index.html?'+dateTime,
			    confirm:function(){
			    	//var sHTML = $('.summernote').code();
			    	//alert(sHTML);
			    }
			    
			});
		});


		$('.dome5').click(function(event) {
			$.confirm({
				title: '回调函数',
			    content: '1、点击确认回调;<br/>2、点击取消回调;<br/>3、点击遮罩层关闭窗口回调和取消是同一个',
			    animation: 'top',
			    animationSpeed: 100,
        		animationBounce: 1.2,
			    closeAnimation: 'scale',
			    keyboardEnabled: true, //点击遮罩层关闭窗口回调和取消是同一个	    
			    confirm: function () {
					alert(2)
                },
                
		        onAction: function () {
		        	alert(1)
		        }
			});
		});

		$('.dome6').click(function(event) {
			$.alert({
			    animation: 'top',
			    closeAnimation: 'scale',
			    title: 'Hello alert',
			    content: '<div>My alert</div>'
			});
		});

		$('.dome7').click(function(event) {
			$.confirm({
                title: false,
                content: '隐藏 title buttom',
                cancelButton: false,
                confirmButton: false
            });
		});

		$('.dome8').click(function(event) {
			alertSucceed('密码修改成功！','col-md-2 col-md-offset-7', 3000);
		});

		/**
		 * 保存成功后关闭
		 * @param str 提示语
		 * @param columnClass 窗口的宽、位移
		 * @param timeout  关闭时间
		 */
		function alertSucceed(str, columnClass, timeout, callback){
			var htmlTemp = [], _timeout = 0;
			if(timeout){
				_timeout = timeout;
			}else{
				_timeout = 2000;
			}
			htmlTemp.push('<div class="prompt">');
			htmlTemp.push('	<em class="prompt-img"></em>');
			htmlTemp.push('	<p>'+str+'</p>');
			htmlTemp.push('</div>');

			var $confirm = $.confirm({
				title: false,
				confirmTop:'40px',
				animation: 'opacity',
				animationSpeed: 1000,
				content: htmlTemp.join(' '),
				columnClass: ''+columnClass+' succeed-box',
				cancelButton: false,
				confirmButton: false,
				backgroundOpacity:0,
				closeIcon: false
			});

//			setTimeout(function(){
//				$confirm.close();
//				if(typeof callback == 'function'){
//					callback();
//				}
//			}, _timeout);

		};

		$('body').delegate('.dome9', 'click', function(event) {
			if(objDome3 == null) return;
			objDome3.close();
		});
       
    });
</script>
</body>
</html>
