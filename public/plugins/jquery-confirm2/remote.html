<link href="../bootstrap-datetimepicker/css/bootstrap-datetimepicker-bs3.css" rel="stylesheet" type="text/css" />
	<script type="text/javascript" src="../bootstrap-datetimepicker/js/moment.js"></script>
	<script type="text/javascript" src="../bootstrap-datetimepicker/js/bootstrap-datetimepicker.js"></script>
	my remote.html
	<!-- dome3 begin -->
	<div class="pl25 pt20">
		<small>可选年费月份：</small>
		<div id="reportrange3" class="btn calendar-default">
		 	<span></span><i class="glyphicon glyphicon-calendar fa fa-calendar"></i>
		</div>
	</div>
	<!-- dome3 end -->
	<script type="text/javascript">
	$(function(){
		//dome3 begin
	    $('#reportrange3 span').html(moment().startOf('hour').format('YYYY/MM/DD'));
	    $('#reportrange3').daterangepicker({
	    	startDate: moment().startOf('hour'),
			endDate: moment().startOf('hour'),
			timePicker: false, // 小时分钟true为可选
			timePicker12Hour:false,
			showDropdowns: true
	    },function(start, end, label) {
	            //console.log(start.toISOString(), end.toISOString(), label);
	            $('#reportrange3 span').html(start.format('YYYY/MM/DD'));
	    });
	    //dome3 end
	});
	</script>
