<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Owl Carousel - Content Manipulations</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="jQuery Responsive Carousel - Owl Carusel">
    <meta name="author" content="<PERSON><PERSON><PERSON>">
    
    <link href='http://fonts.googleapis.com/css?family=Open+Sans:400italic,400,300,600,700' rel='stylesheet' type='text/css'>
    <link href="../assets/css/bootstrapTheme.css" rel="stylesheet">
    <link href="../assets/css/custom.css" rel="stylesheet">

    <!-- Owl Carousel Assets -->
    <link href="../m-slider/m.slider.css" rel="stylesheet">

    <!-- Prettify -->
    <link href="../assets/js/google-code-prettify/prettify.css" rel="stylesheet">
    

    <!-- Le fav and touch icons -->
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="../assets/ico/apple-touch-icon-144-precomposed.png">
    <link rel="apple-touch-icon-precomposed" sizes="114x114" href="../assets/ico/apple-touch-icon-114-precomposed.png">
      <link rel="apple-touch-icon-precomposed" sizes="72x72" href="../assets/ico/apple-touch-icon-72-precomposed.png">
                    <link rel="apple-touch-icon-precomposed" href="../assets/ico/apple-touch-icon-57-precomposed.png">
                                   <link rel="shortcut icon" href="../assets/ico/favicon.png">
  </head>
  <body>

      <div id="top-nav" class="navbar navbar-fixed-top">
        <div class="navbar-inner">
          <div class="container">
            <button type="button" class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse">
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
            <div class="nav-collapse collapse">
            <ul class="nav pull-right">
              <li><a href="../index.html"><i class="icon-chevron-left"></i> Back to Frontpage</a></li>
              <li><a href="../owl.carousel.zip" class="download download-on" data-spy="affix" data-offset-top="450">Download</a></li>
            </ul>
            <ul class="nav pull-left">
              
            </ul>
            </div>
          </div>
        </div>
      </div>
   
    <div id="title">
      <div class="container">
        <div class="row">
          <div class="span12">
            <h1>Content manipulations.</h1>
          </div>
        </div>
      </div>
    </div>

      <div id="demo">
        <div class="container">
          <div class="row">

            <div class="span10">
              <div id="owl-demo" class="m-slider">
                <div class="item dodgerBlue"><h1>Start</h1></div>
              </div>
            </div>

            <div class="span2">
              
              <div class="left">
                <a href="" class="btn add">Add Item</a>
              </div>

              <div class="left">
                <a href="" class="btn remove">Remove Item</a>
              </div>

              <div class="left">
              <a class="btn reinit" href="">Reinit + singlItem</a>
              </div>

              <div class="left">
              <a class="btn destroy" href="">Destroy</a>
              </div>

              <div class="left">
              <a class="btn rebuild" href="">Rebuild</a>              
              </div>

            </div>

          </div>
        </div>

    </div>

    <div id="example-info">
      <div class="container">
        <div class="row">
          <div class="span12">
            <h1>Setup</h1>
            <p>See buttons on top right? Great! Now have a play with them and you get the idea. Thats all. If you have any questions let me know on <a href="https://github.com/OwlFonk/OwlCarousel">github</a> </p>
            <ul class="nav nav-tabs" id="myTab">
              <li class="active"><a href="#javascript">Javascript</a></li>
              <li><a href="#HTML">HTML</a></li>
              <li><a href="#CSS">CSS</a></li>
            </ul>
             
            <div class="tab-content">

              <div class="tab-pane active" id="javascript">
<pre class="pre-show prettyprint linenums">
$(document).ready(function() {

  var owl = $("#owl-demo"),
      i = 0,
      textholder,
      booleanValue = false;

  //init carousel
  owl.mSlider();

  /*
  addItem() method add new slides on given position

  Syntax:
  owldata.addItem(htmlString, targetPosition)

  First parameter(mandatory) "htmlString" accept string like this:
  var newItem = "&lt;div&gt;new Item&lt;/div&gt;"
  
  Second parameter "targetPosition" is optional and accept number values. 
  To add item at the end of carousel you could use -1 value. Last item is default value.
  */
  
  $('.add').on("click", function(e){
    e.preventDefault();
    i += 1;
    var content = "&lt;div class=\"item dodgerBlue\"&gt;&lt;h1&gt;"+i+"&lt;/h1&gt;&lt;/div&gt;";
    owl.data('owlCarousel').addItem(content);
  });

  /*
  Next new owl method is .removeItem()

  Syntax:
  owldata.removeItem(targetPosition)

  Where parameter "targetPosition" is target item you will remove. 
  targetPosition is optional. Default value is last item (-1);
  */

  $('.remove').on("click", function(e){
    e.preventDefault();
    i -= 1;
    if(i&lt;=0)i=0;
    $("#owl-demo").data('owlCarousel').removeItem();
  });

  /*
  destroy() method unwrap whole plugin and leave original pre carousel structure
  
  Syntax:
  owldata.destroy();
  */

  $('.destroy').click(function(e){
    e.preventDefault()
    $("#owl-demo").data('owlCarousel').destroy();
  });

  /*
  reinit() method reinitialize plugin 

  Syntax:
  owldata.reinit(newOptions)

  Yes! you can reinit plugin with new options. Old options
  will be overwritten if exist or added if new.

  You can easly add new content by ajax or change old options with reinit method.
  */

  $('.reinit').click(function(e){
    e.preventDefault()
    if(booleanValue === true){
      booleanValue = false;
    } else if(booleanValue === false){
      booleanValue = true;
    }

    owl.data('owlCarousel').reinit({
        singleItem : booleanValue
      });
  })

  /*
  Well, if you destroyed plugin why not resurect it?
  */

  $('.rebuild').click(function(e){
    e.preventDefault()
    owl.mSlider();
  });

});
</pre>  

              </div>

              <div class="tab-pane" id="HTML">
<pre class="pre-show prettyprint linenums">
&lt;div id="owl-demo" class="m-slider"&gt;
  &lt;div class="item dodgerBlue"&gt;&lt;h1&gt;Start&lt;/h1&gt;&lt;/div&gt;
&lt;/div&gt;
</pre>
              </div>

              <div class="tab-pane" id="CSS">
<pre class="pre-show prettyprint linenums">
#owl-demo .item{
  display: block;
  padding: 54px 0px;
  margin: 5px;
  color: #FFF;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  text-align: center;
}
.left{
  text-align: left;
  margin-bottom: 10px;
}
.left .btn {
  display: block;
}
</pre>
              </div>
            </div><!--End Tab Content-->

          </div>
        </div>
      </div>
    </div>

    <div id="more">
      <div class="container">

        <div class="row">
          <div class="span12">
            <h1>More Demos</h1>
          </div>
        </div>

        <div class="row demos-row">
          <div class="span3">
            <a href="images.html" class="demo-box">
              <div class="demo-wrapper demo-images clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Images</h3>
            </a>
          </div>

          <div class="span3">
            <a href="custom.html" class="demo-box">
              <div class="demo-wrapper demo-custom clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Custom</h3>
            </a>
          </div>

          <div class="span3">
            <a href="itemsCustom.html" class="demo-box">
              <div class="demo-wrapper demo-full clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Custom 2</h3>
            </a>
          </div>

          <div class="span3">
            <a href="one.html" class="demo-box">
              <div class="demo-wrapper demo-one clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>One Slide</h3>
            </a>
          </div>

        </div>
        <div class="row demos-row">
          <div class="span3">
            <a href="json.html" class="demo-box">
              <div class="demo-wrapper demo-Json clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>JSON</h3>
            </a>
          </div>

          <div class="span3">
            <a href="customJson.html" class="demo-box">
              <div class="demo-wrapper demo-Json-custom clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>JSON Custom</h3>
            </a>
          </div>

          <div class="span3">
            <a href="lazyLoad.html" class="demo-box">
              <div class="demo-wrapper demo-lazy clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Lazy Load</h3>
            </a>
          </div>

          <div class="span3">
            <a href="autoHeight.html" class="demo-box">
              <div class="demo-wrapper demo-height clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Auto Height</h3>
            </a>
          </div>

        </div>
      </div>
    </div>
 
    <div id="footer">
      <div class="container">
        <div class="row">
          <div class="span12">
            <h5>Bartosz Wojciechowski 2013 / @OwlFonk / 
            <a href="mailto:<EMAIL>?subject=Hey Owl!">email</a> / 
            <a href="../changelog.html">changelog</a> /
            <a href="https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=EFSGXZS7V2U9N">donate</a> / 
            <a href="https://twitter.com/share" class="twitter-share-button" data-url="http://owlgraphic.com/owlcarousel/" data-text="Awesome jQuery Owl Carousel Responsive Plugin" data-via="OwlFonk" data-count="none" data-hashtags="owlcarousel"></a>
            <script>
            var owldomain = window.location.hostname.indexOf("owlgraphic");
            if(owldomain !== -1){
              !function(d,s,id){var js,fjs=d.getElementsByTagName(s)[0],p=/^http:/.test(d.location)?'http':'https';if(!d.getElementById(id)){js=d.createElement(s);js.id=id;js.src=p+'://platform.twitter.com/widgets.js';fjs.parentNode.insertBefore(js,fjs);}}(document, 'script', 'twitter-wjs');
            }
            </script>
            </h5>
          </div>
        </div>
      </div>
    </div>

    <script src="../assets/js/jquery-1.9.1.min.js"></script> 
    <script src="../m-slider/m.slider.js"></script>


    <!-- Demo -->

    <style>
      #owl-demo .item{
        display: block;
        padding: 54px 0px;
        margin: 5px;
        color: #FFF;
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;
        text-align: center;
      }
      .left{
        text-align: left;
        margin-bottom: 10px;
      }
      .left .btn {
        display: block;
      }
    </style>


    <script>
    $(document).ready(function() {

      var owl = $("#owl-demo"),
          i = 0,
          textholder,
          booleanValue = false;

      //init carousel
      owl.mSlider();

      /*
      addItem() method add new slides on given position

      Syntax:
      owldata.addItem(htmlString, targetPosition)

      First parameter(mandatory) "htmlString" accept string like this:
      var newItem = "<div>new Item</div>"
      
      Second parameter "targetPosition" is optional and accept number values. 
      To add item at the end of carousel you could use -1 value. Last item is default value.
      */
      
      $('.add').on("click", function(e){
        e.preventDefault();
        i += 1;
        var content = "<div class=\"item dodgerBlue\"><h1>"+i+"</h1></div>";
        owl.data('owlCarousel').addItem(content);
      });

      /*
      Next new owl method is .removeItem()

      Syntax:
      owldata.removeItem(targetPosition)

      Where parameter "targetPosition" is target item you will remove. 
      targetPosition is optional. Default value is last item (-1);
      */

      $('.remove').on("click", function(e){
        e.preventDefault();
        i -= 1;
        if(i<=0)i=0;
        $("#owl-demo").data('owlCarousel').removeItem();
      });

      /*
      destroy() method unwrap whole plugin and leave original pre carousel structure
      
      Syntax:
      owldata.destroy();
      */

      $('.destroy').click(function(e){
        e.preventDefault()
        $("#owl-demo").data('owlCarousel').destroy();
      });

      /*
      reinit() method reinitialize plugin 

      Syntax:
      owldata.reinit(newOptions)

      Yes! you can reinit plugin with new options. Old options
      will be overwritten if exist or added if new.

      You can easly add new content by ajax or change old options with reinit method.
      */

      $('.reinit').click(function(e){
        e.preventDefault()
        if(booleanValue === true){
          booleanValue = false;
        } else if(booleanValue === false){
          booleanValue = true;
        }

        owl.data('owlCarousel').reinit({
            singleItem : booleanValue
          });
      })

      /*
      Well, if you destroyed plugin why not resurect it?
      */

      $('.rebuild').click(function(e){
        e.preventDefault()
        owl.mSlider();
      });

    });
    </script>


    <script src="../assets/js/bootstrap-collapse.js"></script>
    <script src="../assets/js/bootstrap-transition.js"></script>
    <script src="../assets/js/bootstrap-tab.js"></script>

    <script src="../assets/js/google-code-prettify/prettify.js"></script>
    <script src="../assets/js/application.js"></script>

  </body>
</html>
