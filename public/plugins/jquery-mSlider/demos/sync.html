<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Owl Carousel - Synced Owls</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="jQuery Responsive Carousel - Owl Carusel">
    <meta name="author" content="<PERSON><PERSON><PERSON>">

    <link href='http://fonts.googleapis.com/css?family=Open+Sans:400italic,400,300,600,700' rel='stylesheet' type='text/css'>
    <link href="../assets/css/bootstrapTheme.css" rel="stylesheet">
    <link href="../assets/css/custom.css" rel="stylesheet">

    <!-- Owl Carousel Assets -->
    <link href="../m-slider/m.slider.css" rel="stylesheet">

    <link href="../assets/js/google-code-prettify/prettify.css" rel="stylesheet">

    <!-- Le fav and touch icons -->
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="../assets/ico/apple-touch-icon-144-precomposed.png">
    <link rel="apple-touch-icon-precomposed" sizes="114x114" href="../assets/ico/apple-touch-icon-114-precomposed.png">
      <link rel="apple-touch-icon-precomposed" sizes="72x72" href="../assets/ico/apple-touch-icon-72-precomposed.png">
                    <link rel="apple-touch-icon-precomposed" href="../assets/ico/apple-touch-icon-57-precomposed.png">
                                   <link rel="shortcut icon" href="../assets/ico/favicon.png">
  </head>
  <body>

      <div id="top-nav" class="navbar navbar-fixed-top">
        <div class="navbar-inner">
          <div class="container">
            <button type="button" class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse">
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
            <div class="nav-collapse collapse">
            <ul class="nav pull-right">
              <li><a href="../index.html"><i class="icon-chevron-left"></i> Back to Frontpage</a></li>
              <li><a href="../owl.carousel.zip" class="download download-on" data-spy="affix" data-offset-top="450">Download</a></li>
            </ul>
            <ul class="nav pull-left">
              
            </ul>
            </div>
          </div>
        </div>
      </div>
   
    <div id="title">
      <div class="container">
        <div class="row">
          <div class="span12">
            <h1>Synced Owls</h1>
          </div>
        </div>
      </div>
    </div>

      <div id="demo">
        <div class="container">
          <div class="row">
            <div class="span12">

              <div id="sync1" class="m-slider">
                <div class="item"><h1>1</h1></div>
                <div class="item"><h1>2</h1></div>
                <div class="item"><h1>3</h1></div>
                <div class="item"><h1>4</h1></div>
                <div class="item"><h1>5</h1></div>
                <div class="item"><h1>6</h1></div>
                <div class="item"><h1>7</h1></div>
                <div class="item"><h1>8</h1></div>
                <div class="item"><h1>9</h1></div>
                <div class="item"><h1>10</h1></div>
                <div class="item"><h1>11</h1></div>
                <div class="item"><h1>12</h1></div>
                <div class="item"><h1>13</h1></div>
                <div class="item"><h1>14</h1></div>
                <div class="item"><h1>15</h1></div>
                <div class="item"><h1>16</h1></div>
                <div class="item"><h1>17</h1></div>
                <div class="item"><h1>18</h1></div>
                <div class="item"><h1>19</h1></div>
                <div class="item"><h1>20</h1></div>
                <div class="item"><h1>21</h1></div>
                <div class="item"><h1>22</h1></div>
                <div class="item"><h1>23</h1></div>
              </div>

              <div id="sync2" class="m-slider">
                <div class="item" ><h1>1</h1></div>
                <div class="item" ><h1>2</h1></div>
                <div class="item" ><h1>3</h1></div>
                <div class="item" ><h1>4</h1></div>
                <div class="item" ><h1>5</h1></div>
                <div class="item" ><h1>6</h1></div>
                <div class="item" ><h1>7</h1></div>
                <div class="item" ><h1>8</h1></div>
                <div class="item" ><h1>9</h1></div>
                <div class="item"><h1>10</h1></div>
                <div class="item"><h1>11</h1></div>
                <div class="item"><h1>12</h1></div>
                <div class="item"><h1>13</h1></div>
                <div class="item"><h1>14</h1></div>
                <div class="item"><h1>15</h1></div>
                <div class="item"><h1>16</h1></div>
                <div class="item"><h1>17</h1></div>
                <div class="item"><h1>18</h1></div>
                <div class="item"><h1>19</h1></div>
                <div class="item"><h1>20</h1></div>
                <div class="item"><h1>21</h1></div>
                <div class="item"><h1>22</h1></div>
                <div class="item"><h1>23</h1></div>
              </div>


            </div>
          </div>
        </div>

    </div>

    <div id="example-info">
      <div class="container">
        <div class="row">
          <div class="span12">
            <h1>Setup</h1>
            <p>This is an example of using two synced Owl Carousel. This is not out of the box function, rather a demo or even "how to".
            </p>
            <ul class="nav nav-tabs" id="myTab">
              <li class="active"><a href="#javascript">Javascript</a></li>
              <li><a href="#HTML">HTML</a></li>
              <li><a href="#CSS">CSS</a></li>
            </ul>
             
            <div class="tab-content">

              <div class="tab-pane active" id="javascript">
<pre class="pre-show prettyprint linenums">
$(document).ready(function() {

  var sync1 = $("#sync1");
  var sync2 = $("#sync2");

  sync1.mSlider({
    singleItem : true,
    slideSpeed : 1000,
    navigation: true,
    pagination:false,
    afterAction : syncPosition,
    responsiveRefreshRate : 200,
  });

  sync2.mSlider({
    items : 15,
    itemsDesktop      : [1199,10],
    itemsDesktopSmall     : [979,10],
    itemsTablet       : [768,8],
    itemsMobile       : [479,4],
    pagination:false,
    responsiveRefreshRate : 100,
    afterInit : function(el){
      el.find(".owl-item").eq(0).addClass("synced");
    }
  });

  function syncPosition(el){
    var current = this.currentItem;
    $("#sync2")
      .find(".owl-item")
      .removeClass("synced")
      .eq(current)
      .addClass("synced")
    if($("#sync2").data("owlCarousel") !== undefined){
      center(current)
    }
  }

  $("#sync2").on("click", ".owl-item", function(e){
    e.preventDefault();
    var number = $(this).data("owlItem");
    sync1.trigger("owl.goTo",number);
  });

  function center(number){
    var sync2visible = sync2.data("owlCarousel").owl.visibleItems;
    var num = number;
    var found = false;
    for(var i in sync2visible){
      if(num === sync2visible[i]){
        var found = true;
      }
    }

    if(found===false){
      if(num>sync2visible[sync2visible.length-1]){
        sync2.trigger("owl.goTo", num - sync2visible.length+2)
      }else{
        if(num - 1 === -1){
          num = 0;
        }
        sync2.trigger("owl.goTo", num);
      }
    } else if(num === sync2visible[sync2visible.length-1]){
      sync2.trigger("owl.goTo", sync2visible[1])
    } else if(num === sync2visible[0]){
      sync2.trigger("owl.goTo", num-1)
    }
    
  }

});
</pre>

              </div>

              <div class="tab-pane" id="HTML">
<pre class="pre-show prettyprint linenums">
&lt;div id="sync1" class="m-slider"&gt;
  &lt;div class="item"&gt;&lt;h1&gt;1&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;2&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;3&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;4&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;5&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;6&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;7&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;8&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;9&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;10&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;11&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;12&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;13&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;14&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;15&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;16&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;17&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;18&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;19&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;20&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;21&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;22&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;23&lt;/h1&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;div id="sync2" class="m-slider"&gt;
  &lt;div class="item"&gt;&lt;h1&gt;1&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;2&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;3&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;4&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;5&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;6&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;7&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;8&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;9&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;10&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;11&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;12&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;13&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;14&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;15&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;16&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;17&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;18&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;19&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;20&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;21&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;22&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;23&lt;/h1&gt;&lt;/div&gt;
&lt;/div&gt;
</pre>
              </div>

              <div class="tab-pane" id="CSS">
<pre class="pre-show prettyprint linenums">
#sync1 .item{
    background: #0c83e7;
    padding: 80px 0px;
    margin: 5px;
    color: #FFF;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    text-align: center;
}
#sync2 .item{
    background: #C9C9C9;
    padding: 10px 0px;
    margin: 5px;
    color: #FFF;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    text-align: center;
    cursor: pointer;
}
#sync2 .item h1{
  font-size: 18px;
}
#sync2 .synced .item{
  background: #0c83e7;
}

</pre>
              </div>
            </div><!--End Tab Content-->

          </div>
        </div>
      </div>
    </div>

    <div id="more">
      <div class="container">

        <div class="row">
          <div class="span12">
            <h1>More Demos</h1>
          </div>
        </div>

        <div class="row demos-row">
          <div class="span3">
            <a href="images.html" class="demo-box">
              <div class="demo-wrapper demo-images clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Images</h3>
            </a>
          </div>

          <div class="span3">
            <a href="custom.html" class="demo-box">
              <div class="demo-wrapper demo-custom clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Custom</h3>
            </a>
          </div>

          <div class="span3">
            <a href="itemsCustom.html" class="demo-box">
              <div class="demo-wrapper demo-full clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Custom 2</h3>
            </a>
          </div>

          <div class="span3">
            <a href="one.html" class="demo-box">
              <div class="demo-wrapper demo-one clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>One Slide</h3>
            </a>
          </div>

        </div>
        <div class="row demos-row">
          <div class="span3">
            <a href="json.html" class="demo-box">
              <div class="demo-wrapper demo-Json clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>JSON</h3>
            </a>
          </div>

          <div class="span3">
            <a href="customJson.html" class="demo-box">
              <div class="demo-wrapper demo-Json-custom clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>JSON Custom</h3>
            </a>
          </div>

          <div class="span3">
            <a href="lazyLoad.html" class="demo-box">
              <div class="demo-wrapper demo-lazy clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Lazy Load</h3>
            </a>
          </div>

          <div class="span3">
            <a href="autoHeight.html" class="demo-box">
              <div class="demo-wrapper demo-height clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Auto Height</h3>
            </a>
          </div>

        </div>
      </div>
    </div>


 
    <div id="footer">
      <div class="container">
        <div class="row">
          <div class="span12">
            <h5>Bartosz Wojciechowski 2013 / @OwlFonk / 
            <a href="mailto:<EMAIL>?subject=Hey Owl!">email</a> / 
            <a href="../changelog.html">changelog</a> /
            <a href="https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=EFSGXZS7V2U9N">donate</a> / 
            <a href="https://twitter.com/share" class="twitter-share-button" data-url="http://owlgraphic.com/owlcarousel/" data-text="Awesome jQuery Owl Carousel Responsive Plugin" data-via="OwlFonk" data-count="none" data-hashtags="owlcarousel"></a>
            <script>
            var owldomain = window.location.hostname.indexOf("owlgraphic");
            if(owldomain !== -1){
              !function(d,s,id){var js,fjs=d.getElementsByTagName(s)[0],p=/^http:/.test(d.location)?'http':'https';if(!d.getElementById(id)){js=d.createElement(s);js.id=id;js.src=p+'://platform.twitter.com/widgets.js';fjs.parentNode.insertBefore(js,fjs);}}(document, 'script', 'twitter-wjs');
            }
            </script>
            </h5>
          </div>
        </div>
      </div>
    </div>


    <script src="../assets/js/jquery-1.9.1.min.js"></script> 
    <script src="../m-slider/m.slider.js"></script>


    <!-- Demo -->

    <style>
    #sync1 .item{
        background: #0c83e7;
        padding: 80px 0px;
        margin: 5px;
        color: #FFF;
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;
        text-align: center;
    }
    #sync2 .item{
        background: #C9C9C9;
        padding: 10px 0px;
        margin: 5px;
        color: #FFF;
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;
        text-align: center;
        cursor: pointer;
    }
    #sync2 .item h1{
      font-size: 18px;
    }
    #sync2 .synced .item{
      background: #0c83e7;
    }
    </style>


    <script>
    $(document).ready(function() {

      var sync1 = $("#sync1");
      var sync2 = $("#sync2");

      sync1.mSlider({
        singleItem : true,
        slideSpeed : 1000,
        navigation: true,
        pagination:false,
        afterAction : syncPosition,
        responsiveRefreshRate : 200,
      });

      sync2.mSlider({
        items : 15,
        itemsDesktop      : [1199,10],
        itemsDesktopSmall     : [979,10],
        itemsTablet       : [768,8],
        itemsMobile       : [479,4],
        pagination:false,
        responsiveRefreshRate : 100,
        afterInit : function(el){
          el.find(".owl-item").eq(0).addClass("synced");
        }
      });

      function syncPosition(el){
        var current = this.currentItem;
        $("#sync2")
          .find(".owl-item")
          .removeClass("synced")
          .eq(current)
          .addClass("synced")
        if($("#sync2").data("owlCarousel") !== undefined){
          center(current)
        }

      }

      $("#sync2").on("click", ".owl-item", function(e){
        e.preventDefault();
        var number = $(this).data("owlItem");
        sync1.trigger("owl.goTo",number);
      });

      function center(number){
        var sync2visible = sync2.data("owlCarousel").owl.visibleItems;

        var num = number;
        var found = false;
        for(var i in sync2visible){
          if(num === sync2visible[i]){
            var found = true;
          }
        }

        if(found===false){
          if(num>sync2visible[sync2visible.length-1]){
            sync2.trigger("owl.goTo", num - sync2visible.length+2)
          }else{
            if(num - 1 === -1){
              num = 0;
            }
            sync2.trigger("owl.goTo", num);
          }
        } else if(num === sync2visible[sync2visible.length-1]){
          sync2.trigger("owl.goTo", sync2visible[1])
        } else if(num === sync2visible[0]){
          sync2.trigger("owl.goTo", num-1)
        }
      }

    });
    </script>


    <script src="../assets/js/bootstrap-collapse.js"></script>
    <script src="../assets/js/bootstrap-transition.js"></script>
    <script src="../assets/js/bootstrap-tab.js"></script>

    <script src="../assets/js/google-code-prettify/prettify.js"></script>
	  <script src="../assets/js/application.js"></script>

  </body>
</html>
