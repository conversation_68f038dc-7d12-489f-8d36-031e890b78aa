/* 
 * 	Core Owl Carousel CSS File
 *	v1.3.3
 */

img {
	/* Responsive images (ensure images don't scale beyond their parents) */

	max-width: 100%;
	/* Part 1: Set a maxium relative to the parent */

	width: auto\9;
	/* IE7-8 need help adjusting responsive images */

	height: auto;
	/* Part 2: Scale the height according to the width, otherwise you get stretching */

	vertical-align: middle;
	border: 0;
	-ms-interpolation-mode: bicubic;
}
/* clearfix */
.m-slider .m-wrapper:after {
	content: ".";
	display: block;
	clear: both;
	visibility: hidden;
	line-height: 0;
	height: 0;
}
/* display none until init */
.m-slider{
	display: none;
	position: relative;
	width: 100%;
	-ms-touch-action: pan-y;
}
.m-slider .m-wrapper{
	display: none;
	position: relative;
	-webkit-transform: translate3d(0px, 0px, 0px);
}
.m-slider .m-wrapper-outer{
	overflow: hidden;
	position: relative;
	width: 100%;
}
.m-slider .m-wrapper-outer.autoHeight{
	-webkit-transition: height 500ms ease-in-out;
	-moz-transition: height 500ms ease-in-out;
	-ms-transition: height 500ms ease-in-out;
	-o-transition: height 500ms ease-in-out;
	transition: height 500ms ease-in-out;
}
	
.m-slider .m-item{
	float: left;
}
.m-controls .m-page,
.m-controls .m-buttons div{
	cursor: pointer;
}
.m-controls {
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/* mouse grab icon */
.grabbing { 
    cursor:url(grabbing.png) 8 8, move;
}

/* fix */
.m-slider  .m-wrapper,
.m-slider  .m-item{
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility:    hidden;
	-ms-backface-visibility:     hidden;
  -webkit-transform: translate3d(0,0,0);
  -moz-transform: translate3d(0,0,0);
  -ms-transform: translate3d(0,0,0);
}

/*
* 	Owl Carousel Owl Demo Theme
*	v1.3.3
*/

.m-theme .m-controls{
	margin-top: 10px;
	text-align: center;
}

/* Styling Next and Prev buttons */

.m-theme .m-controls .m-buttons div{
	color: #FFF;
	display: inline-block;
	zoom: 1;
	*display: inline;/*IE7 life-saver */
	margin: 5px;
	padding: 3px 10px;
	font-size: 12px;
	-webkit-border-radius: 30px;
	-moz-border-radius: 30px;
	border-radius: 30px;
	background: #869791;
	filter: Alpha(Opacity=50);/*IE7 fix*/
	opacity: 0.5;
}
/* Clickable class fix problem with hover on touch devices */
/* Use it for non-touch hover action */
.m-theme .m-controls.clickable .m-buttons div:hover{
	filter: Alpha(Opacity=100);/*IE7 fix*/
	opacity: 1;
	text-decoration: none;
}

/* Styling Pagination*/

.m-theme .m-controls .m-page{
	display: inline-block;
	zoom: 1;
	*display: inline;/*IE7 life-saver */
}
.m-theme .m-controls .m-page span{
	display: block;
	width: 12px;
	height: 12px;
	margin: 5px 7px;
	filter: Alpha(Opacity=50);/*IE7 fix*/
	opacity: 0.5;
	-webkit-border-radius: 20px;
	-moz-border-radius: 20px;
	border-radius: 20px;
	background: #869791;
}

.m-theme .m-controls .m-page.active span,
.m-theme .m-controls.clickable .m-page:hover span{
	filter: Alpha(Opacity=90);/*IE7 fix*/
	opacity: 0.9;
}

/* If PaginationNumbers is true */

.m-theme .m-controls .m-page span.m-numbers{
	height: auto;
	width: auto;
	color: #FFF;
	padding: 2px 10px;
	font-size: 12px;
	-webkit-border-radius: 30px;
	-moz-border-radius: 30px;
	border-radius: 30px;
}

/* preloading images */
.m-item.loading{
	min-height: 150px;
	background: url(AjaxLoader.gif) no-repeat center center
}

/*
 *  Owl Carousel CSS3 Transitions
 *  v1.3.2
 */

.m-origin {
	-webkit-perspective: 1200px;
	-webkit-perspective-origin-x : 50%;
	-webkit-perspective-origin-y : 50%;
	-moz-perspective : 1200px;
	-moz-perspective-origin-x : 50%;
	-moz-perspective-origin-y : 50%;
	perspective : 1200px;
}
/* fade */
.m-fade-out {
	z-index: 10;
	-webkit-animation: fadeOut .7s both ease;
	-moz-animation: fadeOut .7s both ease;
	animation: fadeOut .7s both ease;
}
.m-fade-in {
	-webkit-animation: fadeIn .7s both ease;
	-moz-animation: fadeIn .7s both ease;
	animation: fadeIn .7s both ease;
}
/* backSlide */
.m-backSlide-out {
	-webkit-animation: backSlideOut 1s both ease;
	-moz-animation: backSlideOut 1s both ease;
	animation: backSlideOut 1s both ease;
}
.m-backSlide-in {
	-webkit-animation: backSlideIn 1s both ease;
	-moz-animation: backSlideIn 1s both ease;
	animation: backSlideIn 1s both ease;
}
/* goDown */
.m-goDown-out {
	-webkit-animation: scaleToFade .7s ease both;
	-moz-animation: scaleToFade .7s ease both;
	animation: scaleToFade .7s ease both;
}
.m-goDown-in {
	-webkit-animation: goDown .6s ease both;
	-moz-animation: goDown .6s ease both;
	animation: goDown .6s ease both;
}
/* scaleUp */
.m-fadeUp-in {
	-webkit-animation: scaleUpFrom .5s ease both;
	-moz-animation: scaleUpFrom .5s ease both;
	animation: scaleUpFrom .5s ease both;
}

.m-fadeUp-out {
	-webkit-animation: scaleUpTo .5s ease both;
	-moz-animation: scaleUpTo .5s ease both;
	animation: scaleUpTo .5s ease both;
}
/* Keyframes */
/*empty*/
@-webkit-keyframes empty {
	0% {opacity: 1}
}
@-moz-keyframes empty {
	0% {opacity: 1}
}
@keyframes empty {
	0% {opacity: 1}
}
@-webkit-keyframes fadeIn {
	0% { opacity:0; }
	100% { opacity:1; }
}
@-moz-keyframes fadeIn {
	0% { opacity:0; }
	100% { opacity:1; }
}
@keyframes fadeIn {
	0% { opacity:0; }
	100% { opacity:1; }
}
@-webkit-keyframes fadeOut {
	0% { opacity:1; }
	100% { opacity:0; }
}
@-moz-keyframes fadeOut {
	0% { opacity:1; }
	100% { opacity:0; }
}
@keyframes fadeOut {
	0% { opacity:1; }
	100% { opacity:0; }
}
@-webkit-keyframes backSlideOut {
	25% { opacity: .5; -webkit-transform: translateZ(-500px); }
	75% { opacity: .5; -webkit-transform: translateZ(-500px) translateX(-200%); }
	100% { opacity: .5; -webkit-transform: translateZ(-500px) translateX(-200%); }
}
@-moz-keyframes backSlideOut {
	25% { opacity: .5; -moz-transform: translateZ(-500px); }
	75% { opacity: .5; -moz-transform: translateZ(-500px) translateX(-200%); }
	100% { opacity: .5; -moz-transform: translateZ(-500px) translateX(-200%); }
}
@keyframes backSlideOut {
	25% { opacity: .5; transform: translateZ(-500px); }
	75% { opacity: .5; transform: translateZ(-500px) translateX(-200%); }
	100% { opacity: .5; transform: translateZ(-500px) translateX(-200%); }
}
@-webkit-keyframes backSlideIn {
	0%, 25% { opacity: .5; -webkit-transform: translateZ(-500px) translateX(200%); }
	75% { opacity: .5; -webkit-transform: translateZ(-500px); }
	100% { opacity: 1; -webkit-transform: translateZ(0) translateX(0); }
}
@-moz-keyframes backSlideIn {
	0%, 25% { opacity: .5; -moz-transform: translateZ(-500px) translateX(200%); }
	75% { opacity: .5; -moz-transform: translateZ(-500px); }
	100% { opacity: 1; -moz-transform: translateZ(0) translateX(0); }
}
@keyframes backSlideIn {
	0%, 25% { opacity: .5; transform: translateZ(-500px) translateX(200%); }
	75% { opacity: .5; transform: translateZ(-500px); }
	100% { opacity: 1; transform: translateZ(0) translateX(0); }
}
@-webkit-keyframes scaleToFade {
	to { opacity: 0; -webkit-transform: scale(.8); }
}
@-moz-keyframes scaleToFade {
	to { opacity: 0; -moz-transform: scale(.8); }
}
@keyframes scaleToFade {
	to { opacity: 0; transform: scale(.8); }
}
@-webkit-keyframes goDown {
	from { -webkit-transform: translateY(-100%); }
}
@-moz-keyframes goDown {
	from { -moz-transform: translateY(-100%); }
}
@keyframes goDown {
	from { transform: translateY(-100%); }
}

@-webkit-keyframes scaleUpFrom {
	from { opacity: 0; -webkit-transform: scale(1.5); }
}
@-moz-keyframes scaleUpFrom {
	from { opacity: 0; -moz-transform: scale(1.5); }
}
@keyframes scaleUpFrom {
	from { opacity: 0; transform: scale(1.5); }
}

@-webkit-keyframes scaleUpTo {
	to { opacity: 0; -webkit-transform: scale(1.5); }
}
@-moz-keyframes scaleUpTo {
	to { opacity: 0; -moz-transform: scale(1.5); }
}
@keyframes scaleUpTo {
	to { opacity: 0; transform: scale(1.5); }
}

.uec-banner-send{position: relative; background-size: cover; background-repeat: no-repeat; background-position: center;}
.uec-banner2-img{position: absolute; top: 50%; left: 50%; margin-left: -294px; margin-top: -30px;}

