<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Owl Carousel - Changelog</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="jQuery Responsive Carousel - Owl Carusel">
    <meta name="author" content="<PERSON><PERSON><PERSON>">

    <link href='http://fonts.googleapis.com/css?family=Open+Sans:400italic,400,300,600,700' rel='stylesheet' type='text/css'>
    <link href="assets/css/bootstrapTheme.css" rel="stylesheet">
    <link href="assets/css/custom.css" rel="stylesheet">

    <!-- Owl Carousel Assets -->
    <link href="owl-carousel/owl.carousel.css" rel="stylesheet">
    <link href="owl-carousel/owl.theme.css" rel="stylesheet">

    <!-- Prettify -->
    <link href="assets/js/google-code-prettify/prettify.css" rel="stylesheet">
	

    <!-- Le fav and touch icons -->
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="assets/ico/apple-touch-icon-144-precomposed.png">
    <link rel="apple-touch-icon-precomposed" sizes="114x114" href="assets/ico/apple-touch-icon-114-precomposed.png">
      <link rel="apple-touch-icon-precomposed" sizes="72x72" href="assets/ico/apple-touch-icon-72-precomposed.png">
                    <link rel="apple-touch-icon-precomposed" href="assets/ico/apple-touch-icon-57-precomposed.png">
                                   <link rel="shortcut icon" href="assets/ico/favicon.png">
  </head>
  <body>

      <div id="top-nav" class="navbar navbar-fixed-top">
        <div class="navbar-inner">
          <div class="container">
            <button type="button" class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse">
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
            <div class="nav-collapse collapse">
            <ul class="nav pull-right">
              <li><a href="index.html"><i class="icon-chevron-left"></i> Back to Frontpage</a></li>
              <li><a href="owl.carousel.zip" class="download download-on" data-spy="affix" data-offset-top="450">Download</a></li>
            </ul>
            <ul class="nav pull-left">
              
            </ul>
            </div>
          </div>
        </div>
      </div>

    <div id="changelog" class="container">
      <div class="row">
        <div class="span12">
          <h1>Changelog</h1>
          
          <div class="span12 log">
          <span class="label label-success">v1.3.3</span> <span class="label">12.05.2014</span>
            <ul>
              <li>Fixed $.data issue. ( This resolved jQuery1.7.1 issues )</li>
              <li>Added minified version by Google Closure compiler.</li>
            </ul>
          </div>

          <div class="span12 log">
          <span class="label label-success">v1.3.2</span> <span class="label">13.01.2014</span>
            <ul>
              <li>Changed semantic versioning v1.32 > v1.3.2</li>
              <li>Clean-up the code</li>
              <li>Move out transitons styls to owl.transitions.css file</li>             

            </ul>
          </div>

          <div class="span12 log">
          <span class="label label-success">v1.31</span> <span class="label">21.11.2013</span>
            <ul>
              <li>Fixed startDrag event</li>
              <li>Included awesome <code>itemsCustom</code> function. <a href="demos/itemsCustom.html">See demo</a>. Big thanks to <a href="https://github.com/nchankov">Nik Chankov</a> for code, demo and description.</li>
              <li>Fixed sync <a href="demos/sync.html">demo</a></li>
            </ul>
          </div>

          <div class="span12 log">
          <span class="label label-success">v1.3</span> <span class="label">18.11.2013</span>
            <ul>
              <li>Fixed startDragging callback - now it pass correct "this" value</li>
              <li>Updated owl status method with dragDirection information</li>
              <li>Fixed updating prevItem on owl status object</li>
              <li>Included support for lazy load background images</li>
              <li>Added afterLazyLoad callback(thx JaySala)</li>
              <li>Removed "opacity=0" style while using destroy method.</li>
              <li>Fixed afterMove callback. Now its fire only when you successfull slide.</li>
              <li>Disable drag gestures if there is no items outside viewport.</li>
              <li><code>scrollByPage</code> works with darg gestures now. </li>
              <li>Removed slide animation on changing browser width.</li>
              <li>Added <a href="demos/owlStatus.html">Owl Status</a> and <a href="demos/sync.html">Sync</a> demos.</li>
            </ul>
          </div>

          <div class="span12 log">
          <span class="label label-success">v1.29</span> <span class="label">11.11.2013</span>
            <ul>
              <li>Disabled right click event</li>
              <li>Prevented ghostclicking on Android Chrome browser</li>
              <li>Added visibleItems loop for future functions.</li>
            </ul>
          </div>

          <div class="span12 log">
          <span class="label label-success">v1.28</span> <span class="label">30.10.2013</span>
            <ul>
              <li>Included <code>itemsScaleUp:false</code> method</li>
              <li>Included <a href="demos/scaleup.html">scaleUp demo</a></li>
              <li>Allowing to set options via data attributes ( THX zerghiomario)</li>
            </ul>
          </div>

          <div class="span12 log">
          <span class="label label-success">v1.27</span> <span class="label">17.10.2013</span>
            <ul>
              <li>Fixed <code>destroy</code> method</li>
            </ul>
          </div>

          <div class="span12 log">
          <span class="label label-success">v1.26</span> <span class="label">11.10.2013</span>
            <ul>
              <li>Fixed autoPlay on CSS3 Transitions (Thanks Matuus)</li>
              <li>Added <code>lazyEffect : "fade"</code></li>
            </ul>
          </div>

          <div class="span12 log">
          <span class="label label-success">v1.25</span> <span class="label">10.10.2013</span>
            <ul>
              <li>Fixed lazyLoad ( THX to Aaron Layton )</li>
            </ul>
          </div>


          <div class="span12 log">
          <span class="label label-success">v1.24</span> <span class="label">09.10.2013</span>
            <ul>
              <li>Updated lazyLoad</li>
              <li>Included <code>beforeUpdate</code> and <code>afterUpdate</code> callbacks</li>
              <li>Included <code>dragBeforeAnimFinish:true</code> option to ignore whether a transition is done or not. Thanks to Euxneks</li>
              <li>Included <code>destroy</code>, <code>reinit</code>, <code>addItem</code> and <code>removeItem</code> methods</li>
              <li>Added Manipulations demo</li>
              <li>Fix to force 3d in browsers that can handle it.</li>
              <li>Fixed disableTextSelect event</li>
              <li>Fixed watchVisibility function</li>
              <li>Fixed click and drag event bug on win7 Chrome(thanks to Anydog)</li>
              <li>Fixed click events on IE8</li>
              <li>Thanks to all contributors and bug finders :)</li>
            </ul>
          </div>

          <div class="span12 log">
          <span class="label label-success">v1.23</span> <span class="label">28.09.2013</span>
            <ul>
              <li>Included CSS3 Transitions. <a href="demos/transitions.html">See demo</a></li>
              <li>Fix typo on main demo</li>
              <li>Fixed opacity bug while carousel was hidden (thx seanjacob)</li>
              <li>General cleanup</li>
              <li>Removed <code>goToFirst</code> from options list.</li>
              <li>Changed name <code>goToFirstSpeed</code> to <code>rewindSpeed</code></li>
              <li>Changed name <code>goToFirstNav</code> to <code>rewindNav</code></li>
              <li>Included <code>responsiveBaseWidth</code> options. Use it if you need responsivnes based on your own element width instead of the window width.</li>
              <li>Included <code>"owl.goTo"</code> custom event</li>
              <li>Included <code>"owl.jumpTo"</code> custom event</li>
              <li>Included new width <code>itemsTabletSmall : false</code></li>
            </ul>
          </div>

          <div class="span12 log">
          <span class="label label-success">v1.22</span> <span class="label">15.09.2013</span>
            <ul>
              <li>Better detection of touch events</li>
              <li>Add <code>active</code> class if <code>singleItem</code> is true - <b>It is not working now.</b> Please include <code>addClassActive:true</code> option to get "active" classes on visible items. Works with any numbers of items on screen.</li>
              <li>Changed option name from <code>mouseDraggable</code> to <code>mouseDrag</code></li>
              <li>Also added <code>touchDrag</code> to disable touch events</li>
              <li>Fixed issues on Chrome working on windows 8 touch-enabled PC. Now touch and mouse drag events work together.(Thanks for help to Monsieur Moustache!)</li>
              <li>Included base element in parameter for callbacks <code>afterAction</code>, <code>beforeMove</code> and <code>afterMove</code></li>
              <li>IE8 responsive fix</li>
              <li>Improved autoHeight function</li>
              <li>Added touchcancel event. That fixed Android Chrome scrolling issues.</li>
              <li>Included Progress Bar demo.</li>
              <li>Fixed some spellings</li>
              <li>Slightly updated prettify style</li>
            </ul>
          </div>

          <div class="span12 log">
          <span class="label label-success">v1.21</span> <span class="label">07.09.2013</span>
            <ul>
              <li>Check if base.owlControlls are present before calling methods on them</li>
              <li>Correct the spelling of 'controls'</li>
              <li>Rename 'autplaySpeed' to 'autoPlaySpeed'</li>
              <li>IE7 owl-theme css demo fix</li>
              <li><code>disable</code> class on navigation only if goToFirstNav set to false</li>
              <li>Cleaner code (thanks to Stravid)</li>
              <li>Added carousel selector element parameter to 'afterInit' callback</li>
              <li>Changed scrollPerPageNav to scrollPerPage</li>
              <li>Add <code>active</code> class if <code>singleItem</code> is true</li>
            </ul>
          </div>
          <div class="span12 log">
          <span class="label label-success">v1.2</span> <span class="label">05.09.2013</span>
            <ul>
              <li>Fixed capture click event</li>
              <li>Navigation text accepts html now</li>
              <li>Added <code>goToFirstNav</code> option and is set to true.</li>
              <li>Added <code>scrollPerPage</code> option. For navigation only.</li>
              <li>Added <code>responsiveRefreshRate</code> option.</li>
              <li>Included five callbacks: <code>beforeInit</code>, <code>afterInit</code>, <code>beforeMove</code>, <code>afterMove</code>, <code>afterAction</code></li>
              <li>Included <code>lazyLoad</code> function with <code>lazyFollow</code> sub-option</li>
              <li>Included <code>autoHeight</code> option.</li>
              <li>Included <code>jsonPath</code> function with <code>jsonSuccess</code> callback</li>
              <li>Included <code>mouseDraggable</code> option</li>
              <li>Four new demos on landing page</li>
              <li>Added Changelog and faq</li>
              <li>Change Custom demo page</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div id="footer">
      <div class="container">
        <div class="row">
          <div class="span12">
            <h5>Bartosz Wojciechowski 2013 / @OwlFonk / 
            <a href="mailto:<EMAIL>?subject=Hey Owl!">email</a> / 
            <a href="changelog.html">changelog</a> /
            <a href="https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=EFSGXZS7V2U9N">donate</a> / 
            </h5>
          </div>
        </div>
      </div>
    </div>


    <script src="assets/js/jquery-1.9.1.min.js"></script> 
    <script src="owl-carousel/owl.carousel.js"></script>

    <!-- Frontpage Demo -->
    <script>
    jQuery(document).ready(function($) {
      $("#owl-example").owlCarousel();
    });

    $("body").data("page", "frontpage");

    </script>
    <script src="assets/js/bootstrap-collapse.js"></script>
    <script src="assets/js/bootstrap-transition.js"></script>

    <script src="assets/js/google-code-prettify/prettify.js"></script>
	  <script src="assets/js/application.js"></script>

    <script>
    var owldomain = window.location.hostname.indexOf("owlgraphic");
    if(owldomain !== -1){
      (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
      (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
      m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
      })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

      ga('create', 'UA-41541058-1', 'owlgraphic.com');
      ga('send', 'pageview');
    }

    </script>

  </body>
</html>
