<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Owl Carousel - itemsCustom</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="jQuery Responsive Carousel - Owl Carusel">
    <meta name="author" content="<PERSON><PERSON><PERSON>">

    <link href='http://fonts.googleapis.com/css?family=Open+Sans:400italic,400,300,600,700' rel='stylesheet' type='text/css'>
    <link href="../assets/css/bootstrapTheme.css" rel="stylesheet">
    <link href="../assets/css/custom.css" rel="stylesheet">

    <!-- Owl Carousel Assets -->
    <link href="../owl-carousel/owl.carousel.css" rel="stylesheet">
    <link href="../owl-carousel/owl.theme.css" rel="stylesheet">

    <link href="../assets/js/google-code-prettify/prettify.css" rel="stylesheet">

    <!-- Le fav and touch icons -->
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="../assets/ico/apple-touch-icon-144-precomposed.png">
    <link rel="apple-touch-icon-precomposed" sizes="114x114" href="../assets/ico/apple-touch-icon-114-precomposed.png">
      <link rel="apple-touch-icon-precomposed" sizes="72x72" href="../assets/ico/apple-touch-icon-72-precomposed.png">
                    <link rel="apple-touch-icon-precomposed" href="../assets/ico/apple-touch-icon-57-precomposed.png">
                                   <link rel="shortcut icon" href="../assets/ico/favicon.png">
  </head>
  <body>

      <div id="top-nav" class="navbar navbar-fixed-top">
        <div class="navbar-inner">
          <div class="container">
            <button type="button" class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse">
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
            <div class="nav-collapse collapse">
            <ul class="nav pull-right">
              <li><a href="../index.html"><i class="icon-chevron-left"></i> Back to Frontpage</a></li>
              <li><a href="../owl.carousel.zip" class="download download-on" data-spy="affix" data-offset-top="450">Download</a></li>
            </ul>
            <ul class="nav pull-left">
              
            </ul>
            </div>
          </div>
        </div>
      </div>
   
    <div id="title">
      <div class="container">
        <div class="row">
          <div class="span12">
            <h1>Define custom and unlimited items.</h1>
          </div>
        </div>
      </div>
    </div>

      <div id="demo">
        <div id="owl-demo" class="owl-carousel">
          
          <div class="item"><h1>1</h1></div>
          <div class="item"><h1>2</h1></div>
          <div class="item"><h1>3</h1></div>
          <div class="item"><h1>4</h1></div>
          <div class="item"><h1>5</h1></div>
          <div class="item"><h1>6</h1></div>
          <div class="item"><h1>7</h1></div>
          <div class="item"><h1>8</h1></div>
          <div class="item"><h1>9</h1></div>
          <div class="item"><h1>10</h1></div>
          <div class="item"><h1>11</h1></div>
          <div class="item"><h1>12</h1></div>
          <div class="item"><h1>13</h1></div>
          <div class="item"><h1>14</h1></div>
          <div class="item"><h1>15</h1></div>
          <div class="item"><h1>16</h1></div>
          <div class="item"><h1>17</h1></div>
          <div class="item"><h1>18</h1></div>
          <div class="item"><h1>19</h1></div>
          <div class="item"><h1>20</h1></div>

        </div>
    </div>

    <div id="example-info">
      <div class="container">
        <div class="row">
          <div class="span12">
            <h1>Setup</h1>
            <p>Define custom and unlimited items depending from the width. If this option is set, itemsDeskop, itemsDesktopSmall, itemsTablet, itemsMobile etc. are disabled. For better preview, order the arrays by screen size, but it's not mandatory. Don't forget to include the lowest available screen size, otherwise it will take the default one for screens lower than lowest available. In the example there is dimension with 0 with which cover screens between 0 and 450px.
            </p>
            <ul class="nav nav-tabs" id="myTab">
              <li class="active"><a href="#javascript">Javascript</a></li>
              <li><a href="#HTML">HTML</a></li>
              <li><a href="#CSS">CSS</a></li>
            </ul>
             
            <div class="tab-content">

              <div class="tab-pane active" id="javascript">
<pre class="pre-show prettyprint linenums">
$(document).ready(function() {

  var owl = &#36;("#owl-demo")&#59;

  owl.owlCarousel({
     
      itemsCustom : [
        [0, 2],
        [450, 4],
        [600, 7],
        [700, 9],
        [1000, 10],
        [1200, 12],
        [1400, 13],
        [1600, 15]
      ],
      navigation : true

  })&#59;

})&#59;
</pre>

              </div>

              <div class="tab-pane" id="HTML">
<pre class="pre-show prettyprint linenums">
&lt;div id="owl-demo" class="owl-carousel owl-theme"&gt;
  &lt;div class="item"&gt;&lt;h1&gt;1&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;2&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;3&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;4&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;5&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;6&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;7&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;8&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;9&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;10&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;11&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;12&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;13&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;14&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;15&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;16&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;17&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;18&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;19&lt;/h1&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;h1&gt;20&lt;/h1&gt;&lt;/div&gt;
&lt;/div&gt;

</pre>
              </div>

              <div class="tab-pane" id="CSS">
<pre class="pre-show prettyprint linenums">
#owl-demo .item{
    background: #42bdc2;
    padding: 30px 0px;
    margin: 5px;
    color: #FFF;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    text-align: center;
}
</pre>
              </div>
            </div><!--End Tab Content-->

          </div>
        </div>
      </div>
    </div>

    <div id="more">
      <div class="container">

        <div class="row">
          <div class="span12">
            <h1>More Demos</h1>
          </div>
        </div>

        <div class="row demos-row">
          <div class="span3">
            <a href="images.html" class="demo-box">
              <div class="demo-wrapper demo-images clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Images</h3>
            </a>
          </div>

          <div class="span3">
            <a href="custom.html" class="demo-box">
              <div class="demo-wrapper demo-custom clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Custom</h3>
            </a>
          </div>

          <div class="span3">
            <a href="itemsCustom.html" class="demo-box">
              <div class="demo-wrapper demo-full clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Custom 2</h3>
            </a>
          </div>

          <div class="span3">
            <a href="one.html" class="demo-box">
              <div class="demo-wrapper demo-one clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>One Slide</h3>
            </a>
          </div>

        </div>
        <div class="row demos-row">
          <div class="span3">
            <a href="json.html" class="demo-box">
              <div class="demo-wrapper demo-Json clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>JSON</h3>
            </a>
          </div>

          <div class="span3">
            <a href="customJson.html" class="demo-box">
              <div class="demo-wrapper demo-Json-custom clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>JSON Custom</h3>
            </a>
          </div>

          <div class="span3">
            <a href="lazyLoad.html" class="demo-box">
              <div class="demo-wrapper demo-lazy clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Lazy Load</h3>
            </a>
          </div>

          <div class="span3">
            <a href="autoHeight.html" class="demo-box">
              <div class="demo-wrapper demo-height clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Auto Height</h3>
            </a>
          </div>

        </div>
      </div>
    </div>


 
    <div id="footer">
      <div class="container">
        <div class="row">
          <div class="span12">
            <h5>Bartosz Wojciechowski 2013 / @OwlFonk / 
            <a href="mailto:<EMAIL>?subject=Hey Owl!">email</a> / 
            <a href="../changelog.html">changelog</a> /
            <a href="https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=EFSGXZS7V2U9N">donate</a> / 
            <a href="https://twitter.com/share" class="twitter-share-button" data-url="http://owlgraphic.com/owlcarousel/" data-text="Awesome jQuery Owl Carousel Responsive Plugin" data-via="OwlFonk" data-count="none" data-hashtags="owlcarousel"></a>
            <script>
            var owldomain = window.location.hostname.indexOf("owlgraphic");
            if(owldomain !== -1){
              !function(d,s,id){var js,fjs=d.getElementsByTagName(s)[0],p=/^http:/.test(d.location)?'http':'https';if(!d.getElementById(id)){js=d.createElement(s);js.id=id;js.src=p+'://platform.twitter.com/widgets.js';fjs.parentNode.insertBefore(js,fjs);}}(document, 'script', 'twitter-wjs');
            }
            </script>
            </h5>
          </div>
        </div>
      </div>
    </div>


    <script src="../assets/js/jquery-1.9.1.min.js"></script> 
    <script src="../owl-carousel/owl.carousel.js"></script>


    <!-- Demo -->

    <style>
    #owl-demo .item{
        background: #42bdc2;
        padding: 30px 0px;
        margin: 5px;
        color: #FFF;
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;
        text-align: center;
    }
    </style>


    <script>
    $(document).ready(function() {

      var owl = $("#owl-demo");

      owl.owlCarousel({

        // Define custom and unlimited items depending from the width
        // If this option is set, itemsDeskop, itemsDesktopSmall, itemsTablet, itemsMobile etc. are disabled
        // For better preview, order the arrays by screen size, but it's not mandatory
        // Don't forget to include the lowest available screen size, otherwise it will take the default one for screens lower than lowest available.
        // In the example there is dimension with 0 with which cover screens between 0 and 450px
        
        itemsCustom : [
          [0, 2],
          [450, 4],
          [600, 7],
          [700, 9],
          [1000, 10],
          [1200, 12],
          [1400, 13],
          [1600, 15]
        ],
        navigation : true

      });



    });
    </script>

    <script src="../assets/js/bootstrap-collapse.js"></script>
    <script src="../assets/js/bootstrap-transition.js"></script>
    <script src="../assets/js/bootstrap-tab.js"></script>

    <script src="../assets/js/google-code-prettify/prettify.js"></script>
	  <script src="../assets/js/application.js"></script>

  </body>
</html>
