<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Owl Carousel - Progress Bar</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="jQuery Responsive Carousel - Owl Carusel">
    <meta name="author" content="<PERSON><PERSON><PERSON>">

    <link href='http://fonts.googleapis.com/css?family=Open+Sans:400italic,400,300,600,700' rel='stylesheet' type='text/css'>
    <link href="../assets/css/bootstrapTheme.css" rel="stylesheet">
    <link href="../assets/css/custom.css" rel="stylesheet">

    <!-- Owl Carousel Assets -->
    <link href="../owl-carousel/owl.carousel.css" rel="stylesheet">
    <link href="../owl-carousel/owl.theme.css" rel="stylesheet">

    <link href="../assets/js/google-code-prettify/prettify.css" rel="stylesheet">
  
    <!-- Le fav and touch icons -->
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="../assets/ico/apple-touch-icon-144-precomposed.png">
    <link rel="apple-touch-icon-precomposed" sizes="114x114" href="../assets/ico/apple-touch-icon-114-precomposed.png">
      <link rel="apple-touch-icon-precomposed" sizes="72x72" href="../assets/ico/apple-touch-icon-72-precomposed.png">
                    <link rel="apple-touch-icon-precomposed" href="../assets/ico/apple-touch-icon-57-precomposed.png">
                                   <link rel="shortcut icon" href="../assets/ico/favicon.png">
  </head>
  <body>

      <div id="top-nav" class="navbar navbar-fixed-top">
        <div class="navbar-inner">
          <div class="container">
            <button type="button" class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse">
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
            <div class="nav-collapse collapse">
            <ul class="nav pull-right">
              <li><a href="../index.html"><i class="icon-chevron-left"></i> Back to Frontpage</a></li>
              <li><a href="../owl.carousel.zip" class="download download-on" data-spy="affix" data-offset-top="450">Download</a></li>
            </ul>
            <ul class="nav pull-left">
              
            </ul>
            </div>
          </div>
        </div>
      </div>
   
    <div id="title">
      <div class="container">
        <div class="row">
          <div class="span12">
            <h1>Progress Bar</h1>
          </div>
        </div>
      </div>
    </div>

      <div id="demo">
        <div class="container">
          <div class="row">
            <div class="span12">
              <div id="owl-demo" class="owl-carousel">

                <div class="item"><img src="assets/fullimage1.jpg" alt="The Last of us"></div>
                <div class="item"><img src="assets/fullimage2.jpg" alt="GTA V"></div>
                <div class="item"><img src="assets/fullimage3.jpg" alt="Mirror Edge"></div>

              </div>
            </div>
          </div>
        </div>
    </div>

    <div id="example-info">
      <div class="container">
        <div class="row">
          <div class="span12">
            <h1>Setup</h1>
            <p>Progress Bar made by three callbacks: <code>afterInit</code>, <code>afterMove</code> and <code>startDragging</code>.</p>
            <ul class="nav nav-tabs" id="myTab">
              <li class="active"><a href="#javascript">Javascript</a></li>
              <li><a href="#HTML">HTML</a></li>
              <li><a href="#CSS">CSS</a></li>
            </ul>
             
            <div class="tab-content">

              <div class="tab-pane active" id="javascript">
<pre class="pre-show prettyprint linenums">
$(document).ready(function() {

  var time = 7; // time in seconds

  var $progressBar,
      $bar, 
      $elem, 
      isPause, 
      tick,
      percentTime;

    //Init the carousel
    $("#owl-demo").owlCarousel({
      slideSpeed : 500,
      paginationSpeed : 500,
      singleItem : true,
      afterInit : progressBar,
      afterMove : moved,
      startDragging : pauseOnDragging
    });

    //Init progressBar where elem is $("#owl-demo")
    function progressBar(elem){
      $elem = elem;
      //build progress bar elements
      buildProgressBar();
      //start counting
      start();
    }

    //create div#progressBar and div#bar then prepend to $("#owl-demo")
    function buildProgressBar(){
      $progressBar = $("&lt;div&gt;",{
        id:"progressBar"
      });
      $bar = $("&lt;div&gt;",{
        id:"bar"
      });
      $progressBar.append($bar).prependTo($elem);
    }

    function start() {
      //reset timer
      percentTime = 0;
      isPause = false;
      //run interval every 0.01 second
      tick = setInterval(interval, 10);
    };

    function interval() {
      if(isPause === false){
        percentTime += 1 / time;
        $bar.css({
           width: percentTime+"%"
         });
        //if percentTime is equal or greater than 100
        if(percentTime >= 100){
          //slide to next item 
          $elem.trigger('owl.next')
        }
      }
    }

    //pause while dragging 
    function pauseOnDragging(){
      isPause = true;
    }

    //moved callback
    function moved(){
      //clear interval
      clearTimeout(tick);
      //start again
      start();
    }

    //uncomment this to make pause on mouseover 
    // $elem.on('mouseover',function(){
    //   isPause = true;
    // })
    // $elem.on('mouseout',function(){
    //   isPause = false;
    // })

});
</pre>  

              </div>

              <div class="tab-pane" id="HTML">
<pre class="pre-show prettyprint linenums">
&lt;div id="owl-demo" class="owl-carousel owl-theme"&gt;

  &lt;div class="item"&gt;&lt;img src="assets/fullimage1.jpg" alt="The Last of us"&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;img src="assets/fullimage2.jpg" alt="GTA V"&gt;&lt;/div&gt;
  &lt;div class="item"&gt;&lt;img src="assets/fullimage3.jpg" alt="Mirror Edge"&gt;&lt;/div&gt;

&lt;/div&gt;
</pre>
              </div>

              <div class="tab-pane" id="CSS">
<pre class="pre-show prettyprint linenums">
#owl-demo .item img{
  display: block;
  width: 100%;
  height: auto;
}
#bar{
  width: 0%;
  max-width: 100%;
  height: 4px;
  background: #7fc242;
}
#progressBar{
  width: 100%;
  background: #EDEDED;
}
</pre>
              </div>
            </div><!--End Tab Content-->

          </div>
        </div>
      </div>
    </div>

    <div id="more">
      <div class="container">

        <div class="row">
          <div class="span12">
            <h1>More Demos</h1>
          </div>
        </div>

        <div class="row demos-row">
          <div class="span3">
            <a href="images.html" class="demo-box">
              <div class="demo-wrapper demo-images clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Images</h3>
            </a>
          </div>

          <div class="span3">
            <a href="custom.html" class="demo-box">
              <div class="demo-wrapper demo-custom clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Custom</h3>
            </a>
          </div>

          <div class="span3">
            <a href="itemsCustom.html" class="demo-box">
              <div class="demo-wrapper demo-full clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Custom 2</h3>
            </a>
          </div>

          <div class="span3">
            <a href="one.html" class="demo-box">
              <div class="demo-wrapper demo-one clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>One Slide</h3>
            </a>
          </div>

        </div>
        <div class="row demos-row">
          <div class="span3">
            <a href="json.html" class="demo-box">
              <div class="demo-wrapper demo-Json clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>JSON</h3>
            </a>
          </div>

          <div class="span3">
            <a href="customJson.html" class="demo-box">
              <div class="demo-wrapper demo-Json-custom clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>JSON Custom</h3>
            </a>
          </div>

          <div class="span3">
            <a href="lazyLoad.html" class="demo-box">
              <div class="demo-wrapper demo-lazy clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Lazy Load</h3>
            </a>
          </div>

          <div class="span3">
            <a href="autoHeight.html" class="demo-box">
              <div class="demo-wrapper demo-height clearfix">
                <div class="demo-slide"><div class="bg"></div></div>
              </div>
              <h3>Auto Height</h3>
            </a>
          </div>

        </div>
      </div>
    </div>

    <div id="footer">
      <div class="container">
        <div class="row">
          <div class="span12">
            <h5>Bartosz Wojciechowski 2013 / @OwlFonk / 
            <a href="mailto:<EMAIL>?subject=Hey Owl!">email</a> / 
            <a href="../changelog.html">changelog</a> /
            <a href="https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=EFSGXZS7V2U9N">donate</a> / 
            <a href="https://twitter.com/share" class="twitter-share-button" data-url="http://owlgraphic.com/owlcarousel/" data-text="Awesome jQuery Owl Carousel Responsive Plugin" data-via="OwlFonk" data-count="none" data-hashtags="owlcarousel"></a>
            <script>
            var owldomain = window.location.hostname.indexOf("owlgraphic");
            if(owldomain !== -1){
              !function(d,s,id){var js,fjs=d.getElementsByTagName(s)[0],p=/^http:/.test(d.location)?'http':'https';if(!d.getElementById(id)){js=d.createElement(s);js.id=id;js.src=p+'://platform.twitter.com/widgets.js';fjs.parentNode.insertBefore(js,fjs);}}(document, 'script', 'twitter-wjs');
            }
            </script>
            </h5>
          </div>
        </div>
      </div>
    </div>


    <script src="../assets/js/jquery-1.9.1.min.js"></script> 
    <script src="../owl-carousel/owl.carousel.js"></script>


    <!-- Demo -->

    <style>
    #owl-demo .item img{
      display: block;
      width: 100%;
      height: auto;
    }
    #bar{
      width: 0%;
      max-width: 100%;
      height: 4px;
      background: #7fc242;
    }
    #progressBar{
      width: 100%;
      background: #EDEDED;
    }
    </style>


    <script>
    $(document).ready(function() {

      var time = 7; // time in seconds

      var $progressBar,
          $bar, 
          $elem, 
          isPause, 
          tick,
          percentTime;

        //Init the carousel
        $("#owl-demo").owlCarousel({
          slideSpeed : 500,
          paginationSpeed : 500,
          singleItem : true,
          afterInit : progressBar,
          afterMove : moved,
          startDragging : pauseOnDragging
        });

        //Init progressBar where elem is $("#owl-demo")
        function progressBar(elem){
          $elem = elem;
          //build progress bar elements
          buildProgressBar();
          //start counting
          start();
        }

        //create div#progressBar and div#bar then prepend to $("#owl-demo")
        function buildProgressBar(){
          $progressBar = $("<div>",{
            id:"progressBar"
          });
          $bar = $("<div>",{
            id:"bar"
          });
          $progressBar.append($bar).prependTo($elem);
        }

        function start() {
          //reset timer
          percentTime = 0;
          isPause = false;
          //run interval every 0.01 second
          tick = setInterval(interval, 10);
        };

        function interval() {
          if(isPause === false){
            percentTime += 1 / time;
            $bar.css({
               width: percentTime+"%"
             });
            //if percentTime is equal or greater than 100
            if(percentTime >= 100){
              //slide to next item 
              $elem.trigger('owl.next')
            }
          }
        }

        //pause while dragging 
        function pauseOnDragging(){
          isPause = true;
        }

        //moved callback
        function moved(){
          //clear interval
          clearTimeout(tick);
          //start again
          start();
        }

        //uncomment this to make pause on mouseover 
        // $elem.on('mouseover',function(){
        //   isPause = true;
        // })
        // $elem.on('mouseout',function(){
        //   isPause = false;
        // })
    });
    </script>

    <script src="../assets/js/bootstrap-collapse.js"></script>
    <script src="../assets/js/bootstrap-transition.js"></script>
    <script src="../assets/js/bootstrap-tab.js"></script>

    <script src="../assets/js/google-code-prettify/prettify.js"></script>
    <script src="../assets/js/application.js"></script>

  </body>
</html>
