<!DOCTYPE html>
<html>
<head>
<title>Demo page</title>
<style>
	.qrcode-yard{ margin: 30px auto;}
</style>
</head>
<body>
<p>
	TODO make a nice looking pure client qrcode generator
	even allow download of the image
</p>

<div id="output"></div>

<script src="http://dn-nice.qbox.me/libs/jquery/1.x/jquery.min.js"></script>
<script type="text/javascript" src="../src/qrcode.js"></script>
<script type="text/javascript" src="../src/jquery.qrcode.js"></script>
<script>
jQuery(function(){
	var ary = ['http://www.baidu.com','http://www.baidu.com','http://www.baidu.com']
	var setWidth = 30;
	setQRCodeInit(ary,setWidth);
});

function setQRCodeInit(ary,width){
	$.each(ary, function(index, item){
		var options = {
			text: item,
			width : width,
			height : width
		}
		setQRcode(index, options);
	})
};

function setQRcode(num, settings){
	var defaults = {
		render : 'table',
		text:'table',
		width : 156,
		height : 156,
		element : $('body')
	};
	var options = $.extend(defaults,settings);
	var codeHtml = '<div class="qrcode-yard qrcode-box'+num+'"></div>';
	options.element.show().append($(codeHtml).qrcode(options).width(options.width));
};
</script>

</body>
</html>
