<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title></title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="date" content="" />
</head>
<body>
<div id="jquery.qrcode.js"
><h1
  >jquery.qrcode.js</h1
  ><p
  ><a href='http://jeromeetienne.github.com/jquery-qrcode'>jquery.qrcode.js</a> is <em
    >jquery plugin for a pure browser qrcode generation</em
    >. It allow you to easily add qrcode to your webpages. It is standalone, less than 4k after minify+gzip, no image download. It doesnt rely on external services which go on and off, or add latency while loading. It is based on a <a href='http://www.d-project.com/qrcode/index.html'>library</a> which build qrcode in various language. <a href='http://jeromeetienne.github.com/jquery-qrcode'>jquery.qrcode.js</a> wraps it to make it easy to include in your own code.</p
  ><p
  >Show, dont tell, here is a <a href='examples/basic.html'>example</a></p
  ><div id="how-to-use-it"
  ><h2
    >How to Use It</h2
    ><p
    >Let me walk you thru it. First include it in your webpage with the usual script tag</p
    ><pre
    ><code
      >&lt;script type=&quot;text/javascript&quot; src=&quot;jquery.qrcode.min.js&quot;&gt;&lt;/script&gt;
</code
      ></pre
    ><p
    >Then create a DOM element which gonna contains the generated qrcode image. Lets say a div</p
    ><pre
    ><code
      >&lt;div id=&quot;qrcode&quot;&gt;&lt;/div&gt;
</code
      ></pre
    ><p
    >Then you add the <em
      >qrcode</em
      > in this container by</p
    ><pre
    ><code
      >jquery('#qrcode').qrcode(&quot;this plugin is great&quot;);
</code
      ></pre
    ><p
    >This is it. see it <a href='examples/basic.html'>live</a>.</p
    ></div
  ><div id="conclusion"
  ><h2
    >Conclusion</h2
    ><p
    ><a href='http://jeromeetienne.github.com/jquery-qrcode'>jquery.qrcode.js</a> is available on github <a href='https://github.com/jeromeetienne/jquery-qrcode'>here</a> under <a href='https://github.com/jeromeetienne/jquery-qrcode/blob/master/MIT-LICENSE.txt'>MIT license</a>. If you hit bugs, fill issues on github. Feel free to fork, modify and have fun with it :)</p
    ></div
  ></div
>
<style>
body {
    margin: auto;
    padding-right: 1em;
    padding-left: 1em;
    max-width: 44em; 
    border-left: 1px solid #eee;
    border-right: 1px solid #eee;
    font-family		: Verdana, sans-serif;
    font-size		: 100%;
    line-height		: 140%;
    color		: #eee; 
    background-color	: #22252a;
}
pre {
    border		: 1px dotted gray;
    background-color	: #444a50;
    color		: #1111111;
    padding		: 0.5em;
}
code {
	font-family	: monospace;
}
a { 
	text-decoration	: none;
	color		: lightSteelBlue; 
}
a:hover {
	text-decoration	: underline;
}
h1, h2, h3, h4, h5 { font-family: verdana;
                     font-weight: bold;
                     border-bottom: 1px dotted #eee;
                     color: lightSteelBlue; }
h1 {
	color		: #cd5;
        font-size	: 130%;
}
h1:nth-child(1) {
	font-size	: 300%;
	text-align	: center;
	height		: 1em;
}
h2 {
        font-size	: 110%;
}

h3 {
        font-size	: 95%;
}

h4 {
        font-size	: 90%;
        font-style	: italic;
}

h5 {
        font-size	: 90%;
        font-style	: italic;
}

h1.title {
        font-size	: 200%;
        font-weight	: bold;
        padding-top	: 0.2em;
        padding-bottom	: 0.2em;
        text-align	: left;
        border		: none;
}

dt code {
        font-weight	: bold;
}
dd p {
        margin-top	: 0;
}
img {
/*	border-color	: white;
	border-style	: solid;
*/}

#footer {
        padding-top	: 1em;
        font-size	: 70%;
        color		: gray;
        text-align	: center;
}

</style>
<!-- follow me on twitter -->
<a href="http://www.twitter.com/jerome_etienne">
	<img style="position: absolute; bottom: 10px; right: 10px; border: 0;" src="https://twitter-badges.s3.amazonaws.com/follow_me-c.png" alt="Follow jerome_etienne on Twitter"/>
</a>
<!-- github ribbon -->
<a href="https://github.com/jeromeetienne/jquery-qrcode"><img style="position: absolute; top: 0; left: 0; border: 0;" src="https://assets0.github.com/img/ce742187c818c67d98af16f96ed21c00160c234a?repo=&url=http%3A%2F%2Fs3.amazonaws.com%2Fgithub%2Fribbons%2Fforkme_left_gray_6d6d6d.png&path=" alt="Fork me on GitHub"></a>
<!-- twitter share + facebook like -->
<div style="position: absolute; top: 10px; right: 10px; border: 0;">
	
	<a href="http://twitter.com/share" class="twitter-share-button" data-count="none" data-via="jerome_etienne">Tweet</a><script type="text/javascript" src="https://platform.twitter.com/widgets.js"></script>
</div>
</body>
</html>

