<!doctype html>
<html>
<head>
  <meta charset="utf-8">
  <title>control-on 编号规则</title>
  <link href="../bootstrap/css/bootstrap.css" rel="stylesheet" type="text/css" />
  <link href="../bootstrap/css/bootstrap-theme.css" rel="stylesheet" type="text/css" />
  <link href="../jquery-confirm2/css/jquery-confirm.css" rel="stylesheet" type="text/css" />
  <link href="../../stylesheets/common.css" rel="stylesheet" type="text/css" />
  <link href="css/jquery.control.no.css" rel="stylesheet" type="text/css" />
  <script type="text/javascript" src="../jquery/jquery-1.11.3.js"></script>
  <script type="text/javascript" src="../jquery-ui/jquery-ui-1.11.4.js"></script>
  <script type="text/javascript" src="../bootstrap/js/bootstrap.js"></script>
  <script type="text/javascript" src="../jquery-confirm2/js/jquery-confirm.js"></script>
  <script type="text/javascript" src="js/jquery.control.no.js"></script>
</head>
<style type="text/css">
  /*.main-column{position: absolute; top:15px; bottom: 0;left: 0;right: 0;}*/
</style>
<script type="text/javascript">

</script>
<body>
<div class="control-on redinner" data-item-id="id">
  <div class="box clear">
    <div class="left float-l">
      <span class="left-inner">组件</span>
      <div class="column-left-item column clear">
        <span class="c-on-btn" data-type="text">文本</span>
        <span class="c-on-btn" data-type="dateTime">日期</span>
        <span class="c-on-btn" data-type="variable">变量</span>
        <span class="c-on-btn" data-type="serial" data-val-type="0">流水号</span>
        <span class="c-on-btn" data-type="underLine" data-val="_">下划线 _</span>
        <span class="c-on-btn" data-type="separator" data-val="-">连接符 -</span>
      </div>
    </div>
    <div class="rigth float-l">
      <div class="rigth-top clear">
        <div class="column-right-item column">
          <em class="rigth-tis">请将左侧组件拖到此处，可以创建编号规则</em>
        </div>
        <p class="show-format">格式：<label class="lb-format"></label></p>
      </div>
      <div class="rigth-bottom on-content"></div>
    </div>
  </div>
  <div class="modal-footer redinner-footer">
    <button class="btn btnw btn-box btn-redinner-save">保存</button>
    <button class="btn btn-box btnw btn-box-primary">关闭</button>
  </div>
</div>
</body>
</html>
