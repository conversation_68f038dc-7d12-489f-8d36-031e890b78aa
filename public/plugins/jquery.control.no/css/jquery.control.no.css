.control-on.redinner{max-width: 886px;}
.control-on.redinner{ background:#fff; box-shadow: 0 0px 7px rgba(105, 105, 105, 0.3); border:1px solid #d0d7db; border-radius:5px; overflow:hidden; margin:10px auto;}
.control-on .left{ width:120px; border-right:1px solid #d0d7db;}
.control-on .left span,
.control-on .rigth-top span{ display:block; width:88px; height:42px; line-height:42px; background:#f5f6f7; border-radius:5px; border:1px solid #d6d6d6; text-align:center;cursor:pointer; color:#6c6c6c;font-size:14px;}
.control-on .left span:hover,
.control-on .left span.active,
.control-on .rigth-top span:hover,
.control-on .rigth-top span.active{ background:#ffd4a1; border:1px solid #f6c062;}
.control-on .left .left-inner,
.control-on .left .left-inner:hover{ margin:0;background:#00aec4; border:1px solid #2a9fae; display:block; width:120px; height:36px; line-height:36px; text-align:center;  color:#fff; cursor:auto; border-radius:0;}
.control-on .rigth{ width:764px;}
.control-on .rigth-top{ margin:10px 0 0 15px; height:97px;}
.control-on .rigth-notop{height:63px;}
.control-on .rigth-top span{ display:inline-block;}
.control-on .rigth-top i{ display:inline-block; height:7px; width:21px; border-top:1px solid #afafaf; *margin-top:-14px;}
.control-on .rigth-top p{ height:20px; line-height:20px; text-align:left; color:#6c6c6c; margin:15px 0 5px 5px; font-size:14px;}
.control-on .rigth-bottom{ border-top:1px solid #d0d7db; background:#fafafa; height:216px;*height:232px;}
.control-on .rigth-bottom span{ display:block; margin-left:25px; height:30px; line-height:30px; font-size:14px;}
.control-on .time{ margin:25px 0 10px 25px; font-size:16px;}
.control-on .redinner-footer{ text-align:center; padding:10px; overflow:hidden; margin:0;border-top: 1px solid #d0d7db; *display:inline;*zoom:1; *width:900px}
.control-on .rigth-top .rigth-tis{width: 245px;font-size: 12px;display: inline-block;height: 20px;line-height: 20px;background: #f2f2f2;text-align: center;border-radius: 5px;margin-left: 240px;margin-top: 29px;color: #aaa8a8;}
.control-on .content-conter{ min-height:210px;}
.control-on .content-conter .input-tis{margin: 10px 10px 18px 20px;}
.control-on .nav-bigbox{height: 50px; width:850px;overflow: hidden; position: relative; z-index: 14;}
.control-on .ypxq-tab{position: relative;top: -9px;z-index: 11;}
.control-on .leftrightbtn{ width:50px;position: relative; top:-43px;z-index: 15;  overflow:hidden;}
.control-on .leftrightbtn em{width:25px; height:25px; text-align:center; display:block; float:left; cursor:pointer;}
.control-on .leftrightbtn em:hover{ color:#41c2d3;}
.control-on .xuanqshez{ width:870px;}
.control-on em.radioem{ margin-right:10px; display:inline-block; background:url(../images/radio.png) center no-repeat; width:22px; height:22px; cursor:pointer; vertical-align:middle;}
.control-on em.radioem:hover,
.control-on em.radioemon{background:url(../images/radioon.png) center no-repeat;}
.control-on .show-format{display: none;}
.control-on .column-left-item{height: 282px;}
.control-on .column-right-item{height: 52px;}
.control-on .time span{display: inline-block;*display: inline;*zoom:1;}
.control-on .w90{width: 90px; text-align:right;display: inline-block;*display: inline;*zoom:1;}
.control-on .inline-block { display: inline-block;  *display: inline;*zoom: 1;}
.control-on .column-left-item{text-align: center;}
.control-on .column-left-item .ui-sortable-handle{ margin:5px auto; }
.ck-radio,.ck-radio-type{cursor: pointer;}
.control-on .tooltip-inner{padding: 6px 10px;}
.control-on .rigth-bottom .w280{width:280px;display: inline-block;  *display: inline;*zoom: 1;}