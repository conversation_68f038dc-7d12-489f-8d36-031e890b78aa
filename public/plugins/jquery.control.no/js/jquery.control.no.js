/**
 * Created by <PERSON><PERSON><PERSON> on 2016/1/20.
 */
$(function(){

	$('.column').sortable({
		connectWith: '.column',
		cursor:'move',
		revert: true,
		revertDuration: 300,
		placeholder: 'portlet-placeholder ui-corner-all portlet',
		receive: function(event, ui) {
			var that = $(event.target);
			var uiIndex = ui.item.index(),
				thatUi = ui.item,
				dataType = thatUi.attr('data-type'),
				uiId = dataType + '-' + uiIndex;

			if(that.hasClass('column-right-item')){
				var tHtml = controlOnTemplate(dataType,uiId),
					$thatOnContent = that.parent().siblings('.on-content');

				thatUi.attr('data-uid',uiId);
				$thatOnContent.append(tHtml);

				that.parents('.box').find('div[data-uid='+uiId+']').siblings().hide();
				that.find(thatUi).addClass('active').siblings('span').removeClass('active');

				$('[data-toggle="tooltip"]').tooltip();
				if(dataType == 'variable'){
					var vHtml = getShortLst();
					that.parents('.box').find('div[data-uid='+uiId+']').empty().html(vHtml);
				}
				//拖拽后格式刷新
				getNoFormat(that);

			}else{
				var id = ui.item.attr('data-uid');

				that.parents('.box').find('div[data-uid='+id+']').remove();
				that.parents('.box').find('lable.'+id+'').remove();
				ui.item.removeClass('active').removeAttr('data-uid');
			}
		},
		over: function(event, ui){
			var that = $(event.target);
			that.find('.rigth-tis').remove();
			that.siblings('.show-format').show();

		},
		stop: function(event, ui){    //当排序动作结束时触发
			var that = $(event.target),  // 得到排序之前的容器
				len = that.find('.c-on-btn').length,
				thatUi = ui.item,
				thatLen = that.find(thatUi).length,
				dataType = thatUi.attr('data-type');

			if(that.hasClass('column-right-item')){
				if(len == 0){
					that.append('<em class="rigth-tis">请将左侧组件拖到此处，可以创建编号规则</em>');
					that.siblings('.show-format').hide().find('lable').text('');
				}
				if($('.cable:eq(0)').prev('.ui-sortable-handle').length == 0){
					$('.cable:eq(0)').remove();
				}
			}else{

				if(thatLen == 0){
					switch(dataType){
						case 'separator':
						case 'underLine':
							that.append(thatUi.clone()).find('span').removeClass('active');
							break;
					}

				}
			}
		},
		beforeStop: function(event,ui){
			var that = $(event.target),
				uiIndex = ui.item.index(),
				thatUi = ui.item;

			reloadRightItem(that);

		},
		update: function(event, ui) {
			var that = $(event.target),
				thatUi = ui.item,
				uiIndex = thatUi.index();

			if(that.hasClass('column-right-item')){
				getNoFormat(that);
				if(that.find('span[data-type=serial]').nextAll('span').length > 0){
					$.alert({
						animation: 'top',
						closeAnimation: 'scale',
						title: '提示',
						content: '<div class="pt15 pb15 pl15">流水号必须最后一位！</div>',
						confirm:function(){
							that.find('span').last().after(that.find('span[data-type=serial]'));
							reloadRightItem(that);
						}
					});
				}
			}
		}
	}).disableSelection();

	//编号规则选中当前 right span
	$('.column-right-item').delegate('span', 'click', function(event) {
		var that = $(this);
		var id = that.attr('data-uid');

		that.parents('.box').find('div[data-uid='+id+']').show().siblings().hide();
		that.addClass('active').siblings('span').removeClass('active');
	});


	//文本输入内容后触发
	$('.on-content').delegate('.input-text', 'keyup', function(event) {
		var that = $(this),
			reg ='',
			lbClass = that.attr('data-uid'),
			dataKey = that.attr('data-key'),
			$uiSpan = that.parents('.on-content').prev('.rigth-top').find('span[data-uid="'+lbClass+'"]'),
			thatVal = that.val();

		switch(dataKey){
			case 'text':
				reg = /^[0-9a-zA-Z]{1,8}$/;
				if(reg.test(thatVal)){
					$uiSpan.attr('data-val',thatVal);
				}
				break;
			case 'length':
				reg = /^[2-8]{1}$/;
				if(reg.test(thatVal)){
					$uiSpan.attr('data-length',thatVal);
				}
				break;
			case 'start':
				reg = /^[1-5]{1}$/;
				if(reg.test(thatVal)){
					$uiSpan.attr('data-start',thatVal);
				}
				break;
			case 'step':
				reg = /^[1-5]{1}$/;
				if(reg.test(thatVal)){
					$uiSpan.attr('data-step',thatVal);
				}
				break;
		}
		getNoFormat(that);

	});

	//日期选择事件
	$('.on-content').delegate('.ck-radio', 'click', function(event) {
		var that = $(this),
			lbClass = that.parent().attr('data-uid'),
			$uiSpan = that.parents('.on-content').prev('.rigth-top').find('span[data-uid="'+lbClass+'"]'),
			text = that.attr('data-val');

		$uiSpan.attr('data-val', text);

		that.find('em.radioem').addClass('radioemon').parent().siblings('span').find('em').removeClass('radioemon');

		getNoFormat(that);
	});

	//流水号
	$('.on-content').delegate('.ck-radio-type', 'click', function(event) {
		var that = $(this),
			lbClass = that.parents('div').attr('data-uid'),
			$uiSpan = that.parents('.on-content').prev('.rigth-top').find('span[data-uid="'+lbClass+'"]'),
			text = that.attr('data-val');

		$uiSpan.attr('data-val-type', text);

		that.find('em.radioem').addClass('radioemon').parent().siblings('span').find('em').removeClass('radioemon');

		if(text == '0'){
			that.parents('div').find('.ascending').show();
		}else{
			that.parents('div').find('.ascending').hide();
		}

	});

	//保存
	$('.control-on').delegate('.btn-redinner-save', 'click', function(event) {
		var that = $(this),
			itemData = [], itemFormat = [],
			$controlOn = that.parents('.control-on'),
			$columnRightItem = $controlOn.find('.column-right-item'),
			_id = $controlOn.attr('data-item-id');


		$columnRightItem.find('span').each(function(index, el) {
			var dataType = $(el).attr('data-type');

			itemFormat.push('{dataType:"'+ dataType +'"');
			if(dataType == 'serial'){
				var dataValType = $(el).attr('data-val-type');
				if(dataValType == '1'){
					itemFormat.push('dataVal:{serialType:'+ dataValType +',length:'+ $(el).attr('data-length') +'}');
				}else{
					itemFormat.push('dataVal:{serialType:'+ dataValType +',length:'+ $(el).attr('data-length') +'');
					itemFormat.push('start:'+ $(el).attr('data-start') +'');
					itemFormat.push('step:'+ $(el).attr('data-step') +'}');
				}
			}else{
				itemFormat.push('dataVal:"'+ $(el).attr('data-val') +'"}');
			}
		});

		itemData.push('{codeTypeId:"'+_id+'",format:['+ itemFormat.join(',') +']}');
		console.log('itemData: '+ itemData.join(''));
	});

	editCodeNumber()
	//修改
	function editCodeNumber(){
		var html = [];
		$.ajax({
			type: 'get',
			url: '../../../codeRule/a?id=0430537bd5a14648b7f9e3c5515dfc26',
			dataType: 'JSON',
			async:false,
			success: function (data) {
				if (data) {

					var formats = data.codeRule.format;
					console.log('formats:'+ formats);
					//$.each(jsonData, function (index, db) {
					//	console.log(db.dataType);
					//})
				}
			},
			error: function () {
			}

		})
		return html.join(' ');
	}
});

/**
 * 获取变量信息
 * @returns {string}
 */
function getShortLst() {
	var html = [];
	$.ajax({
		type: 'get',
		url: '../../../codeRule/a?id=0430537bd5a14648b7f9e3c5515dfc26',
		dataType: 'JSON',
		async:false,
		success: function (data) {
			html.push(' <p class="time">选择变量类型：</p>');
			if (data) {
				$.each(data.optionItem, function (index, db) {
					html.push(' <span class="ck-radio w280" data-val="' + db.shortName + '"><em class="radioem"></em>' + db.name + '</span>');
				})
			}
		},
		error: function () {
		}

	})
	return html.join(' ');
};

//刷新右侧item
function reloadRightItem(obj){
	var cable = '<i class="cable"></i>';
	var _rightItem = obj.parents('.control-on').find('.column-right-item');
	_rightItem.find('.cable').remove();
	_rightItem.find('.ui-sortable-handle').each(function(i, el) {
		if(i>0){
			$(el).before(cable);
		}
	})
};

//获取格式
function getNoFormat(obj){
	var format = '',
		$box = $(obj).parents('.box'),
		$columnRightItem = $box.find('.column-right-item');

	$columnRightItem.find('span').each(function(index, el) {
		if($(el).attr('data-val')){
			format += $(el).attr('data-val');
		}
	});

	$box.find('label.lb-format').text(format);
};

//模板
function controlOnTemplate(type,markId){
	var templateHtml = [];
	switch(type){
		case 'text': //文本
			templateHtml.push('<div class="'+type+'" data-uid="'+markId+'">');
			templateHtml.push(' <span class="mt20"><em>内容：</em>');
			templateHtml.push(' <input type="text" data-uid="'+markId+'" data-key="text" class="input-medium input-box input-text" data-toggle="tooltip" data-placement="right" title="范围A-Z、a-z、0-9，长度为1-8个字符。"></span>');
			templateHtml.push('</div>');
			break;
		case 'dateTime': //日期
			templateHtml.push('<div class="'+type+'" data-uid="'+markId+'">');
			templateHtml.push('<p class="time">日期格式： </p>');
			templateHtml.push('<span class="ck-radio" data-val="yyyy"><em class="radioem"></em>年（yyyy）</span>');
			templateHtml.push('<span class="ck-radio" data-val="yyyyMM"><em class="radioem"></em>年月（yyyyMM）</span>');
			templateHtml.push('<span class="ck-radio" data-val="yyMM"><em class="radioem"></em>年月（yyMM）</span>');
			templateHtml.push('<span class="ck-radio" data-val="yyyyMMdd"><em class="radioem"></em>年月日（yyyyMMdd）</span>');
			templateHtml.push('<span class="ck-radio" data-val="yyMMdd"><em class="radioem"></em>年月日（yyMMdd）</span>');
			templateHtml.push('</div>');
			break;
		case 'serial': //流水号
			templateHtml.push('<div class="'+type+'" data-uid="'+markId+'">');
			templateHtml.push(' <p class="time">类型：');
			templateHtml.push('   <span class="ck-radio-type" data-val="0"><em class="radioem radioemon"></em>递增</span>');
			templateHtml.push('   <span class="ck-radio-type" data-val="1"><em class="radioem"></em>随机生成</span>');
			templateHtml.push(' </p>');
			templateHtml.push(' <div class="random">');
			templateHtml.push('   <span class="mb10"><em class="w90">流水号位数：</em>');
			templateHtml.push('   <input type="text" data-uid="'+markId+'" data-key="length" class="input-medium input-box input-text" data-toggle="tooltip" data-placement="right" title="只能为2-8的数字！"></span>');
			templateHtml.push(' </div>');
			templateHtml.push(' <div class="ascending">');
			templateHtml.push('   <span class="mb10"><em class="w90">起始值：</em>');
			templateHtml.push('   <input type="text" data-uid="'+markId+'" data-key="start" class="input-medium input-box input-text" data-toggle="tooltip" data-placement="right" title="只能为1-5之间的数字！"></span>');
			templateHtml.push('   <span class="mb10"><em class="w90">步长：</em>');
			templateHtml.push('  <input type="text" data-uid="'+markId+'" data-key="step" class="input-medium input-box input-text" data-toggle="tooltip" data-placement="right" title="只能为1-5之间的数字！"></span>');
			templateHtml.push(' </div>');
			templateHtml.push('</div>');
			break;
		case 'variable': //变量
			templateHtml.push('<div class="'+type+'" data-uid="'+markId+'">');
			templateHtml.push(' <p class="time">选择变量类型：</p>');
			templateHtml.push(' <span class="ck-radio" data-val="1"><em class="radioem"></em>样品类型</span>');
			templateHtml.push(' <span class="ck-radio" data-val="2"><em class="radioem"></em>文库类型</span>');
			templateHtml.push(' <span class="ck-radio" data-val="3"><em class="radioem"></em>设备类型</span>');
			templateHtml.push(' <span class="ck-radio" data-val="4"><em class="radioem"></em>试剂类型</span>');
			templateHtml.push('</div>');
			break;
	}
	return templateHtml.join('');
};
