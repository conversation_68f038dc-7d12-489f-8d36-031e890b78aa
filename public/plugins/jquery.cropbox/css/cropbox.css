@charset "utf-8";
.cropbox.container {
	width: 750px;
	margin: 0 auto;
	position: relative;
	font-family: '微软雅黑';
	font-size: 12px;
}
.cropbox.container p {
	line-height: 12px;
	line-height: 0px;
	height: 0px;
	margin: 10px;
	color: #bbb
}
.cropbox .action {
	width: 400px;
	height: 70px;
	margin: 10px 0;
	text-align: center;
}
.cropbox .cropped {
	position: absolute;
	top:45px;
	right: 0;
	bottom: 0;
	width: 222px;
	padding: 4px;
	/*border: 1px #ddd solid;
	box-shadow: 0px 0px 12px #ddd;*/
	text-align: center;
}
.cropbox .imageBox {
	position: relative;
	height: 400px;
	width: 400px;
	border: 1px solid #aaa;
	background: #fff;
	overflow: hidden;
	background-repeat: no-repeat;
	cursor: move;
	box-shadow: 4px 4px 12px #B0B0B0; 
	background: url(../images/imgbg.jpg) no-repeat center;
}
.cropbox .imageBox .thumbBox {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 200px;
	height: 200px;
	margin-top: -100px;
	margin-left: -100px;
	box-sizing: border-box;
	border: 1px solid rgb(102, 102, 102);
	box-shadow: 0 0 0 1000px rgba(0, 0, 0, 0.5);
	background: none repeat scroll 0% 0% transparent;
}
.cropbox .imageBox .spinner {
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	text-align: center;
	line-height: 400px;
}
.cropbox .Btnsty_peyton{ 
	display: inline-block;
	*display: inline;
	*zoom: 1;
	margin-bottom: 10px;
	width: 34px;
	height: 34px;
	line-height: 34px;
	margin:0px 2px;
	background-color: #ffffff;
	border-radius: 5px;
	text-decoration: none;
	cursor: pointer;
    border: 1px solid #cbcbcb;
    color: #cbcbcb;
    font-size: 16px;
}
/*选择文件上传*/
.cropbox .new-contentarea {
	text-align: center;
	width: 70px;
	height: 70px;
	margin: 0 auto;
	display: inline-block;
	*display: inline;
	*zoom: 1;
	vertical-align: middle;
}
.cropbox .new-contentarea label {
	width:100%;
	height:100%;
	display:block;
}
.cropbox .new-contentarea input[type=file] {
	width:188px;
	height:60px;
	background:#333;
	margin: 0 auto;
	position:absolute;
	right:50%;
	margin-right:-94px;
	top:0;
	right/*\**/:0px\9;
	margin-right/*\**/:0px\9;
	width/*\**/:10px\9;
	opacity:0;
	filter:alpha(opacity=0);
	z-index:2;
}
.cropbox a.upload-img{
	background: url(../images/img_03.png) no-repeat center;
	width: 68px;
    height: 68px;
    border-radius: 50px;
    border: 2px solid #e8eaed;
    cursor: pointer;
    position: relative;
	display: inline-block;
	text-decoration:none;
	cursor:pointer;
}
.cropbox a.upload-img:hover{
	background:url(../images/001_03.png) no-repeat center; border:2px solid #a5adb8;
}

/* 2015-12-30 add <EMAIL> begin */
.cropbox .modal-body-rigth{
	position: absolute;
	right: 0;
	top: 0;
    background: #f4f4f4;
    width: 222px;
    float: right;
    height: 489px;
}
.cropbox .modal-body-left{
	position: relative;
	width: 513px;
	height: 489px;
 	float: left;
 	background: #fff url(../images/imglfbg.jpg) right repeat-y;}
.cropbox .inner-img {
    height: 44px;
    line-height: 44px;
    background: url(../images/imgrgbg.png) bottom no-repeat;
    text-align: center;
}

.cropbox .action-right{
	position: absolute;
    bottom: 75px;
    right: 56px;
    width: 40px;
    height: 100px;
}
.cropbox .action-right .Btnsty_peyton{margin-top: 8px;}
.cropbox .ction{
    text-align: center;
    width: 400px;
    margin: 5px 0px;
}

.cropbox .tailoring{   
	background: url(../images/img_05.png) no-repeat center;
    width: 68px;
    height: 68px;
    border-radius: 50px;
    border: 2px solid #e8eaed;
    cursor: pointer;
    position: relative;
    display: inline-block;
    text-decoration: none;
    cursor: pointer;
	vertical-align: middle;
	margin-left: 15px;}

.cropbox .tailoring:hover{background:url(../images/001_05.png) no-repeat center #4082ae; border:2px solid #a5adb8;}

.cropbox .ime-tis{ width:295px;position:absolute; z-index:11; left: 8px;bottom: -44px;}
.cropbox .icon-ing{ background:url(../images/sa.png) no-repeat center; height:10px;position: relative;bottom: -1px;}
.cropbox .p-cont{width:295px; height:44px; line-height:44px; color:#ef912b; border:1px solid #fadf9f; border-radius:5px; background:#fffbe5;}

.cropbox .new-contentarea:hover .ime-tis{display: block;}

.defalut1{margin: 0 auto; margin-top: 50px;
		border:1px solid #cacdce;
		border-radius:100px;
		box-shadow:0px 0px 12px #cacdce;
		width: 100px; height: 100px; background:url(../images/mrimg.jpg) no-repeat center;}

.defalut2{margin: 0 auto; margin-top: 50px;
		border:1px solid #cacdce;
		box-shadow:0px 0px 12px #cacdce;
		width: 100px; height: 100px; background:url(../images/mrimg.jpg) no-repeat center;}

/* 2015-12-30 add <EMAIL> end */
