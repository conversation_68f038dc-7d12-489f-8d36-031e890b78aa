﻿<!DOCTYPE>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>jQuery头像裁切预览代码</title>
<link href="../bootstrap/css/bootstrap.css" rel="stylesheet" type="text/css" />
<link href="../bootstrap/css/bootstrap-theme.css" rel="stylesheet" type="text/css" />
<link rel="stylesheet" href="../../stylesheets/common.css" type="text/css" />
<link href="../jquery-confirm2/css/jquery-confirm.css" rel="stylesheet" type="text/css" />
<link rel="stylesheet" href="css/cropbox.css" type="text/css" />
<link rel="stylesheet" type="text/css" href="../jquery.uploadify/css/uploadify.css">

<script type="text/javascript" src="../jquery/jquery-1.11.3.js"></script> 
<script type="text/javascript" src="js/jquery.cropbox.js"></script>
<script type="text/javascript" src="../jquery-confirm2/js/pretty.js"></script>
<script type="text/javascript" src="../jquery-confirm2/js/jquery-confirm.js"></script>
<script src="../jquery.uploadify/js/jquery.uploadify.js" type="text/javascript"></script>

<style type="text/css">
	.pop-container{padding: 15px 15px;}
</style>
</head>
<body>

<div class="pop-container ml30">
	<!-- 按钮触发模态框 -->
	<button class="btn btn-primary btn-lg dome1">
	   上传头像 点我
	</button>

</div>
<div id="jq-cropbox" style="display:none;">
	<div class="cropbox container">
		<div class="modal-body-left">
			<div class="imageBox">
				<div class="thumbBox"></div>
				<div class="spinner" style="display: none">Loading...</div>
			</div>
			<div class="action-right">
				<input type="button" id="btnZoomIn" class="Btnsty_peyton" value="+"  >
				<input type="button" id="btnZoomOut" class="Btnsty_peyton" value="-" >
			</div>
			<div class="action">
				<div class="new-contentarea">
					<a href="javascript:void(0)" class="upload-img">
						<label for="upload-file"></label>
					</a>
					<input type="file" class="" name="upload-file" id="upload-file" />
					<div class="ime-tis">
						<div class="icon-ing"></div>
						<div class="p-cont">图片格式必须为：bmp,jpg,jpeg,gif,不可大于2M</div>
					</div>
				</div>
				<input type="button" id="btnCrop"  class="Btnsty_peyton tailoring" value=" ">
			</div>
		</div>
		<div class="modal-body-rigth">
			<div class="inner-img">预览</div>
			<div class="cropped">
				<div class="croppen-d defalut1"></div>
				<div class="croppen-d defalut2"></div>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
$(function() {
	$('.dome1').click(function(event) {
		$.confirm({
			    animation: 'top',
			    closeAnimation: 'scale',
			    columnClass: 'col-md-8 col-md-offset-2',
			    title: '头像裁切',
			   	 content: $('#jq-cropbox').html()
			});
		});

	var options =
	{
		thumbBox: '.thumbBox',
		spinner: '.spinner',
		imgSrc: '',
		thumbBoxWidth: 100,
		thumbBoxHeight: 100,
		borderRadius: 100,
		shape:''  // round 圆的，quadrate 方的
	}
	var cropper = $('.imageBox').cropbox(options);
	$('#upload-file').on('change', function(){
		var reader = new FileReader();
		reader.onload = function(e) {
			options.imgSrc = e.target.result;
			cropper = $('.imageBox').cropbox(options);
		}
		reader.readAsDataURL(this.files[0]);
		this.files = [];
	})
	$('body').delegate('#btnCrop', 'click', function(){
		var img = cropper.getDataURL();
		$('.cropped').html('');
		if(cropper.shape == 'round'){
			$('.cropped').append('<img src="'+img+'" align="absmiddle" style="width:'+cropper.thumbBoxWidth+';margin-top:20px;border-radius:'+ cropper.borderRadius +';box-shadow:0px 0px 12px #7E7E7E;"><p>'+cropper.thumbBoxWidth+'px*'+ cropper.thumbBoxHeight+'px</p>');
		}else{
			$('.cropped').append('<img src="'+img+'" align="absmiddle" style="width:'+cropper.thumbBoxWidth+';margin-top:20px;box-shadow:0px 0px 12px #7E7E7E;"><p>'+cropper.thumbBoxWidth+'px*'+ cropper.thumbBoxHeight+'px</p>');
		}
	})
	$('body').delegate('#btnZoomIn', 'click', function(){
		cropper.zoomIn();
	})
	$('body').delegate('#btnZoomOut', 'click', function(){
		cropper.zoomOut();
	})
});
</script>

</body>
</html>
