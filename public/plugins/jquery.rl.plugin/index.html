<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>Title</title>
	<link href="../bootstrap/css/bootstrap.css" rel="stylesheet" type="text/css" />
	<link href="../bootstrap/css/bootstrap-theme.css" rel="stylesheet" type="text/css" />
	<link href="css/jquery.rl.plugin.css" rel="stylesheet" type="text/css" />
	<script type="text/javascript" src="../jquery/jquery-1.11.3.js"></script>
	<script type="text/javascript" src="../bootstrap/js/bootstrap.js"></script>
	<script type="text/javascript" src="js/jquery.rl.plugin.js"></script>
	<script>
		$(function(){
			$('.rd-test').rlCheckBox({defaultClass:'shop-ridao', checkedClass: 'shop-radioon',
				onClick: function (ele) {
					console.log(1);
				}
			});
			$('.rd-test1').rlRadio();

			var opt = {width:90, defaultClass:'rl-switch', checkedClass: 'rl-switchon',pointClass:'rl-pointon'};
			$('.rd-test2').rlSwitch(opt);

            var opt2 = opt;
            opt2.width = 200;
            opt2.double = true;
            opt2.opentitle='true';
            opt2.closetitle='false';
			$('.rd-test3').rlSwitch(opt2);

//			$('.btn').click(function(){
//				var arrL = [];
//				$('input[checked]').each(function (index, el) {
//					arrL.push($(el).val());
//				})
//				alert(arrL);
//			})
		});
	</script>
	<style>

	</style>
</head>
<body>
	<label class="rd-test ml15"><input type="checkbox" name="radio1"  value="0">北京</label>
	<label class="rd-test ml15"><input type="checkbox" name="radio1" checked="checked" value="1">天津</label>
	<label class="rd-test ml15"><input type="checkbox" name="radio1" value="2">河北</label>


	<label class="rd-test1 ml10"><input type="radio" name="radio2"  value="0">北京1</label>
	<label class="rd-test1 ml10"><input type="radio" name="radio2" checked value="1">天津1</label>
	<label class="rd-test1 ml10"><input type="radio" name="radio2" value="2">河北1</label>
	<!--<span class="rl-title">不提醒</span>-->
	<!--<em class="rl-switch">-->
		<!--<span class="pointon"></span>-->
	<!--</em>-->
	<label class="rd-test2"><input type="checkbox" name="switch" value="0" checked></label>

	<label class="rd-test3"><input type="checkbox" name="switch" value="0"></label>

	<button class="btn">获取value</button>
</body>
</html>