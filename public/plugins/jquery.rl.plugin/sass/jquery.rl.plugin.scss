@import "../../../sass-scss/global";
.rl-radio{
  margin-top: -3px;
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url(../images/radio.png) no-repeat center;
  vertical-align: middle;
  cursor: pointer;
  margin-right: 5px;
}
.rl-radioon {
  background: url(../images/radioon.png) no-repeat center;
}
.shop-ridao{
  background: url('../images/ridao2.png') no-repeat;
  background-position: 0px -16px;
  @include background-size(18px 33px);
  display: inline-block;
  width: 18px;
  height: 17px;
  vertical-align: middle;
}
.shop-ridao.shop-radioon{background-position: 0 0;}
.rl-switch{
  border-radius: 10px;
  display: inline-block;
  vertical-align: middle;
  position: relative;
  background:  $c-bd;
  width: 40px;
  height: 21px;
  line-height: 21px;
  cursor: pointer;
}
.rl-switch.rl-switchon{
  background: $c-00b;
}
.rl-switch.rl-switchon .rl-pointon{
  left:20px;
}
.rl-pointon{
  position: absolute;
  right: 1px;
  top: 1px;
  z-index: 22;
  left: 1px;
  width: 19px;
  color: $c-f;
  height: 19px;
  @include transition();
  border-radius: 100%;
  background: $c-f;
}
.rd-test2 .rl-on,.rd-test2 .rl-off{
  display: none;
  width:60px;
  font-style: normal;
  text-align: center;
  vertical-align: middle;
  position: relative;
  margin-right: 10px;
}
.rd-test2 .on{
  display: inline-block;
}



.rl-sdouble .rl-switch{
  border-radius: 10px;
  display: inline-block;
  vertical-align: middle;
  position: relative;
  background: $c-00b;
  height: 24px;
  @include border-radius(12px);
  background-color:$c-f4;
  @include box-shadow($c-da inset);
  line-height: 24px;
  cursor: pointer;
}
.rl-sdouble .rl-switch.rl-switchon .rl-pointon{
  left:40px;
}
.rl-sdouble .rl-pointon{
  position: absolute;
  top: 0px;
  z-index: 2;
  color: $c-f;
  height: 24px;
  @include transition();
  @include border-radius(19px);
  background-color: $c-56c;
}
.rl-sdouble .rl-on,.rl-sdouble .rl-off{
  position: absolute;
  left:0;
  top:0;
  width:40px;
  z-index: 3;
  font-size: 1rem;
  color: $c-6c;
  line-height: 24px;
  font-style: normal;
  text-align: center;
  @include transition();
}
.rl-sdouble .on{
  color: $c-f;
}
.rd-test3{position: relative;}
.rl-disabled i,.rl-disabled .rl-switch{cursor: not-allowed;}
.rl-disabled em{cursor: not-allowed;}
.rl-sdouble.rl-disabled .rl-pointon{background-color: $c-c;}