.select-wrapper{
	font:normal 14px '\5FAE\8F6F\96C5\9ED1';
	width:auto;
	height:34px;
	position:relative;
	cursor:pointer;
	display:inline-block;
	*display:inline;
	*float:left;
	*margin-right:10px;
	background:#fff;
	border:1px solid #dadde2;
	border-radius: 3px;
	vertical-align: middle;

}
.select-wrapper.focus{
	border:1px solid #69cadb;
}
.select-button{
	font:normal 14px '\5FAE\8F6F\96C5\9ED1';
	width:auto;
	height:34px;
	border:0;
	border-radius:3px;
	background:#fff;
	cursor:pointer;
	padding:0 40px 0 10px;
	overflow:hidden;
	text-align:left;
	outline:none;
	white-space:nowrap;
	word-break:keep-all;
	text-overflow:ellipsis;
}
.select-down{
	position:absolute;
	top:21px;
	right:12px;
	height:0;
	width:0;
	overflow:hidden;
	font-size:0;
	border-color:#333 transparent transparent transparent;
	border-style:solid;
	border-width:6px;
}  
.disabled{
	color:#cccccc;
}
.select-list{
	min-width:70px;
	background-color:#fff;
	border:1px solid #a6b5d2;
	box-shadow:0 0 3px #ddd;
	border-radius:3px;
	display:none;
	z-index:5;
	position:absolute;
	left:-1px;
	top:35px;
	overflow:hidden;
}
.select-list ul{
	margin:0;
	padding:0;
	overflow-x:hidden;
	overflow-y:auto;
	line-height:34px;
	max-height:240px;
}
.select-list ul li{
	width:auto;
	height:34px;
	padding-left:10px;
	cursor:pointer;
	overflow:hidden;
	white-space:nowrap;
	word-break:keep-all;
	text-overflow:ellipsis;
	text-align: left;
}
.select-list ul li.selected,
.select-list ul li:hover{
	color:#fff;
	background-color:#3c8ad9;
}

/* 下拉列表不可用样式 */
.select-button-disable{
	color:#ccc;
	width:238px;
	height:34px;
	border:1px solid #e3e3e3;
	border-radius:3px;
	background:#fff;
	cursor:pointer;
	padding-left:10px;
	overflow:hidden;
	text-align:left;
	outline:none;
}
.select-down-disable{
	color:#ccc;
	font-size:18px;
	position:absolute;
	top:0;
	right:10px;
	line-height:34px;
}
.select-wrapper.disabled:hover{
	border-color:#dadde2;
	box-shadow: none;/*css3的新属性边框的阴影*/
	-webkit-box-shadow: none;/**/
	-moz-box-shadow: none;
}

.select-wrapper.disabled:hover .s-select-icon{
	background-color:#fff;
}
.select-wrapper.disabled:hover  .select-down{
	color: inherit;
}

.select-wrapper:hover{
	border-color:#90b4d0;
   	outline: 0;/*outline描述的是input设置点状轮廓的颜色，样式和宽度*/
   	outline: no dotted \9;/* |9是针对IE6-9做的兼容 */
   	box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(48, 187, 206, 0.6);/*css3的新属性边框的阴影*/
   	-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(48, 187, 206, 0.6);/**/
   	-moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(48, 187, 206, 0.6);
   	text-shadow:none;}
.select-wrapper:hover .s-select-icon{background-color: #c5d3e3;
	border-top-right-radius: 4px; 
	-webkit-border-top-right-radius: 4px;
	-moz-border-top-right-radius: 4px;
	-o-border-top-right-radius: 4px;
	border-bottom-right-radius: 4px;
	-webkit-border-bottom-right-radius: 4px; 
	-moz-border-bottom-right-radius: 4px;
	-o-border-bottom-right-radius: 4px;}
.select-wrapper:hover .select-down{color:#ffffff;}

.select-wrapper .select-button,
.select-wrapper{border-radius: 5px;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	-o-border-radius: 5px;}

.select-wrapper .select-down{display: inline-block;
    width: 0;
    height: 0;
    margin-left: 2px;
    vertical-align: middle;
    border-top: 4px dashed;
    border-top: 4px solid \9;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent;}

.select-wrapper .s-select-icon{position: absolute; cursor: pointer; z-index: 2; top: 0;
	right: 0; bottom:0; width: 31px; display:block; border-left: 1px solid #e0e0e0;
	text-align:center; line-height: 30px;}


/* 2015-12-25 add <EMAIL>  begin*/
.s-select{width: 190px;position: relative; display: inline-block;*display: inline;*zoom: 1; border:1px solid #ddd;border-radius: 5px; -webkit-border-radius: 5px; -moz-border-radius: 5px;-o-border-radius: 5px;vertical-align: middle;}
.s-select .ss-label{width: 100%;height: 30px; line-height: 30px;display: block; cursor: pointer;padding: 0 31px 0 10px;
  text-overflow:ellipsis;overflow:hidden;white-space:nowrap; }
.s-select .s-select-icon{position: absolute; cursor: pointer; z-index: 2; top: 0; right: 0; width: 31px; height: 30px; display:block; border-left: 1px solid #e0e0e0;
text-align:center; line-height: 30px;}
.s-select .caret{vertical-align: middle;}
.s-select .s-select-lst{display: none; width: 100%;*width: 100%; max-height: 119px; position: absolute; z-index: 10; border:1px solid #ddd; background-color: #ffffff; text-align: left;left: 0; top: 31px; overflow-y: scroll;overflow-y:scroll\0;
-webkit-border-radius: 5px; -moz-border-radius: 5px;-o-border-radius: 5px;}
.s-select .s-select-lst li{font-size: 12px; padding:5px 10px; cursor: pointer;margin-bottom: 1px;}
.s-select .s-select-lst li.selected{background-color: #ebebeb; color: #333333;}
.s-select .s-select-lst li:hover{background-color: #ebebeb;}
.s-radio,.s-checkbox{vertical-align: middle;*vertical-align: 0;vertical-align: -2px\9; }
.radio-on{color: #e50011;}

.s-select:hover .caret{color:#ffffff;}
.s-select:hover .s-select-icon{background-color: #c5d3e3;border-top-right-radius: 4px; -webkit-border-top-right-radius: 4px; -moz-border-top-right-radius: 4px;-o-border-top-right-radius: 4px;
border-bottom-right-radius: 4px; -webkit-border-bottom-right-radius: 4px; -moz-border-bottom-right-radius: 4px;-o-border-bottom-right-radius: 4px;}

.s-select:hover{ border-color:#90b4d0;
   outline: 0;/*outline描述的是input设置点状轮廓的颜色，样式和宽度*/
   outline: no dotted \9;/* |9是针对IE6-9做的兼容 */
   box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(48, 187, 206, 0.6);/*css3的新属性边框的阴影*/
   -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(48, 187, 206, 0.6);/**/
   -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(48, 187, 206, 0.6);
   text-shadow:none;}
.in-pageNo .select-button {text-align: center;padding: 0 30px 0 0;}

#citySelect .select-wrapper,
.citySelect .select-wrapper{min-width: 120px;}
#citySelect .select-list ul li,
.citySelect .select-list ul li{min-width: 130px;}
.select-list .list-parent{font-weight: bold;}
.select-list ul li.list-sub{padding-left: 24px;}
/* 2015-12-25 add <EMAIL>  end */
