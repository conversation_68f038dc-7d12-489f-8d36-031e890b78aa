<!doctype html>
<html>
<head>
<meta charset="utf-8">
<title>select</title>
<link href="../bootstrap/css/bootstrap.css" rel="stylesheet" type="text/css" />
<link href="../bootstrap/css/bootstrap-theme.css" rel="stylesheet" type="text/css" />
<link href="../../stylesheets/common.css" rel="stylesheet" type="text/css" />
<link href="css/jquery.selectlist.css" rel="stylesheet" type="text/css" />
<style type="text/css">
	.pop-container{padding: 15px 15px;}
  
</style>
</head>
<body>
<h2>select</h2>
<div class="pop-container ml30" id="city_1">
    <select id="prov" class="prov in-pageNo">
      <option value="请选择">请选择</option>
    </select>
    <select id="city" class="city in-pageNo">
      <option value="请选择">请选择</option>
    </select>
    <select id="dist" class="dist in-pageNo" style="display:none;">
      <option value="请选择">请选择</option>
    </select>
</div>
<div class="pop-container ml30 clones">
    
    <select class="in-pageNo">
      <option value="10" data-type="1" data-layer="parent">10</option>
      <option value="15" data-type="0" data-layer="sub">15</option>
      <option value="20" data-type="0" data-layer="sub">20</option>
    </select>
    <select class="in-select">
        <option value="请选择" data-layer="parent">请选择parent1</option>
        <option value="请选择sub" data-layer="sub">请选择sub1-1</option>
        <option value="请选择sub" data-layer="sub">请选择sub1-2</option>
        <option value="请选择sub" data-layer="sub">请选择sub1-3</option>
        <option value="请选择" data-layer="parent">请选择parent2</option>
        <option value="请选择sub" data-layer="sub">请选择sub2-1</option>
        <option value="请选择sub" data-layer="sub">请选择sub2-2</option>
        <option value="请选择sub" data-layer="sub">请选择sub2-3</option>
    </select>
    <select class="in-select">
      <option value="请选择">请选择</option>
      <option value="请选择2">请选择2</option>
      <option value="请选择3">请选择3</option>
    </select>
    <select class="in-select">
      <option value="请选择">请选择</option>
      <option value="请选择">请选择</option>
    </select>
    <select class="in-select">
      <option value="请选择">请选择</option>
    </select>
</div>

<input class="clone" type="button" value="拷贝">
<div class="clone-cotent">

</div>

<script type="text/javascript" src="../jquery/jquery-1.11.3.js"></script>
<script type="text/javascript" src="../jquery.cityselect/js/jquery.cityselect.js"></script>
<script type="text/javascript" src="js/jquery.selectlist.js"></script>
<script type="text/javascript">
    $(document).ready(function(){

      //公共select 
      $('.in-select').selectlist({delegate:'body',topPosition:false,onChange:function(){
            var id = $(this).attr('id'); $elId = $('#'+id);
            $elId.next('div').selectlist({dataJson: {data:[{id:'1',name:'dddd'},{id:'2',name:'ddddwwwwww'   }]}
            });
        }
      });
      //table显示条数
      $('.in-pageNo').selectlist({width:100});

        $('.clone').click(function(){
            var clone = $('.clones').clone();
            $('.clone-cotent').empty().html(clone);
        });

//      $("#city_1").citySelect({
//        url:'', //../../../users/findAll.html
//        nodata:"none"
//      });
//        $.ajax({
//            url: "http://localhost:3000/users/findAll.html",
//            type: "GET",
//            cache: false,
//            //beforeSend: function () { },//$("#publish").attr("disabled", "disabled"); },
//            success: function (data) {
//                console.log(data);
//            },
//            error:function(){
//                console.log('error');
//            }
//        });

    });

</script>
</body>
</html>
