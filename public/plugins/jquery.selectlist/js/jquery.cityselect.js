/*
Ajax 三级省市联动
日期：2015-12-29

settings 参数说明
-----
url:省市数据josn文件路径
prov:默认省份
city:默认城市
dist:默认地区（县）
nodata:无数据状态
------------------------------ */
(function($){
	$.fn.citySelect=function(settings){
		if(this.length<1){return;};

		// 默认值
		settings=$.extend({
        	url:"",
			//all:null,
			prov:null,
			city:null,
			dist:null,
			nodata:null,
			required:true
		},settings);

		var box_obj=this;
		//var all_obj=box_obj.find("#all");
		var prov_obj=box_obj.find("#prov");
		var city_obj=box_obj.find("#city");
		var dist_obj=box_obj.find("#dist");
		//var all_val=settings.all;
		var prov_val=settings.prov;
		var city_val=settings.city;
		var dist_val=settings.dist;
		var select_prehtml=(settings.required) ? "" : "<li data-value='0'>请选择</li>";
		var city_json;
		var city_buffer = [];
					
		// 赋值市级函数
		var cityStart=function(){
			
			var prov_id=prov_obj.find('input[name="prov"]').val();
				//console.log("prov_id"+prov_id)
				if(prov_id == 0) return;

				if(city_buffer.length > 0){			
					var num = getCityObj(city_buffer, prov_id);
					if(num == 0){
						getJsonData(settings.url, {"provId": ""+prov_id+""}, prov_id);
					}
				}else{
					getJsonData(settings.url, {"provId": ""+prov_id+""}, prov_id);
				}

				temp_html=select_prehtml;
				$.each(city_buffer,function(i,city){
					if(city.pId == prov_id){
						$.each(city.c.data, function(index, city_c) {
							temp_html+="<li title='"+city_c.name+"' data-value='"+city_c.id+"'>"+city_c.name+"</li>";
						});
					}
				});
				//20160408 
				city_obj.find('input[type="button"]').val('请选择');
				city_obj.find('ul').html(temp_html).attr("disabled",false).css({"display":"","visibility":""});
			
				//distStart();
		};

		// 赋值地区（县）函数
		var distStart=function(){
			var prov_id=prov_obj.find('input[name="prov"]').val();
			var city_id=city_obj.find('input[name="city"]').val();
			
			if(prov_id == 0 && city_id == 0) return;

			if(city_buffer.length > 0){
				var num = getCityObj(city_buffer, city_id);
				if(num == 0){
					getJsonData(settings.url, {"provId": ""+city_id+""}, city_id);
				}
			}else{
				getJsonData(settings.url, {"provId": ""+city_id+""}, city_id);
			}
			
			
			// 遍历赋值市级下拉列表
			temp_html=select_prehtml;
			$.each(city_buffer,function(i,city){
				if(city.pId == city_id){
					if(city.c == null){
						dist_obj.hide();
					}else {
						//console.log('city.c.data:'+city.c.data);
						if(city.c.data.length == 0){
							dist_obj.hide().find('input').attr('disabled','disabled');
						}else {
							dist_obj.show().find('input').removeAttr('disabled');
							$.each(city.c.data, function (index, city_c) {
								temp_html += "<li title='"+city_c.name+"' data-value='" + city_c.id + "'>" + city_c.name + "</li>";
							});
						}
					}
				}
			});
			dist_obj.find('input[type="button"]').val('请选择');
			dist_obj.find('ul').html(temp_html).attr("disabled",false).css({"display":"","visibility":""});
		};

		//判断对象中是否存在
		var getCityObj = function(obj, id){
			var num = 0;
			$.each(obj, function(i, city) {
				if(city.pId == id){
					num ++; 
				}
			});
			return num;
		}

		// 获取数据
		var getJsonData = function(url, param, obj_id){
			if(url != null && param != null){
				$.ajaxSettings.async = false;
				$.getJSON(url, param, function(json){
					var tempJson = {"pId": ""+obj_id+"", "c": json};
					city_buffer.push(tempJson);
				});
		       ;
			}
			return city_buffer;
		}

		var init=function(){
			// 遍历赋值省份下拉列表			 
			temp_html = select_prehtml;

			$.each(city_json.data,function(i,prov){
				//console.log(prov.id+"  "+prov.name)
				temp_html+="<li title='"+prov.name+"' data-value='"+prov.id+"'>"+prov.name+"</li>";
			});

			prov_obj.find('ul').html(temp_html);
			//all_obj.find('ul').html(temp_html);

			// 若有传入省份与市级的值，则选中。（setTimeout为兼容IE6而设置）
			setTimeout(function(){
				if(settings.prov!=null){
					prov_obj.val(settings.prov);
					cityStart();
					setTimeout(function(){
						if(settings.city!=null){
							city_obj.val(settings.city);
							distStart();
							setTimeout(function(){
								if(settings.dist!=null){
									dist_obj.val(settings.dist);
								};
							},1);
						};
					},1);
				};
			},1);


			// 选择国家时发生事件
			//all_obj.delegate("li", "click", function(){
			//	allStart();
			//});
			// 选择省份时发生事件
			prov_obj.delegate("li", "click", function(){
				cityStart();
			});

			// 选择市级时发生事件
			city_obj.delegate("li", "click",function(){
				distStart();
			});
		};

		// 设置省市json数据
		if(typeof(settings.url)=="string"){
			//$.ajaxSettings.async = false;

			if(settings.url == ""){
				var json = {"data" :[{"name":"北京","id":"1"},{"name":"河北","id":"2"},{"name":"河南","id":"3"},{"name":"山西","id":"4"}]};
				city_json=json;
				init();
			}else{
				$.getJSON(settings.url,function(json){
					city_json=json;
					init();
				});
			}
		}else{

			city_json=settings.url;
			init();
		};
	};
})(jQuery);