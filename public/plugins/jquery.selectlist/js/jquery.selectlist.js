/*!
 * select v0.0.1
 * by <PERSON><PERSON><PERSON>@ronglian.com
 * Copyright 2015.12
 * Date: 2015
 */

;(function($,window,document){

	var pluginName = 'selectlist',
		defaults = {

			//默认属性配置
			delegate: '',
			enable: true,   //选择列表是否可用
			zIndex: null,  //选择列表z-index值，如需兼容IE6/7,必须加此属性
			width: 200,   //选择列表宽度
			height: 38,  //选择列表高度
			border: null,  //选择列表边框
			borderRadius: null,  //选择列表圆角
			showMaxHeight: null,  //选择列表显示最大高度
			optionHeight: 34,   //选择列表单项高度
			triangleSize: 4,   //右侧小三角大小
			triangleColor: '#333',  //右侧小三角颜色
			marginTop: 0,  //选择列表高度
			url:'', //ajax 获取后台数据
			setData: function(){},
			dataJson: null,
			topPosition: false,  //选择列表项在列表框上部显示,默认在下边显示
			speed: 100,   //选择列表框显示动画速度（毫秒）
			onChange: function(event){}  //自定义模拟选择列表项change事件
		};

	function SelectList(element,options){
		this.element = element;
		this.settings = $.extend({}, defaults, options);
		this._defaults = defaults;
		this._name = pluginName;

		if(element.tagName != 'SELECT'){
			//$(element).find('li')
			if(this.settings.dataJson){
				var lstTmp = this.setSelectList(this.settings.dataJson);
				$(element).find('ul').empty().html(lstTmp);
			}
			return;
		}else{
			this.init();
		}
	}

	SelectList.prototype={

		init: function(){
			var that = this,
				element = this.element,
				selectID = that.getSelectID(element);

			that.replaceProtoypeSelect(element,selectID);
			that.setSelectStyle(element);
			if(that.settings.delegate){
				that.setSelectEventDelegate(element,selectID);
			}else{
				that.setSelectEvent(element,selectID);
			}
			if(this.settings.dataJson){
				var lstTmp = this.setSelectList(this.settings.dataJson);
				$('#'+selectID).find('ul').empty().html(lstTmp);
			}
		},

		//获取原生选择列表id值
		getSelectID: function(element){
			var $this = $(element),
				selectID = $this.attr('id'),
				t = new Date().getTime();


			if(typeof(selectID) !== 'undefined'){
				return selectID;
			}else{
				selectID='jq-select'+ t;
				return selectID;
			}
		},

		//获取原生选择列表name值
		getSelectName: function(element){
			var that = this,
				$this = $(element),
				selectName = $this.attr('name');

			if(typeof(selectName) !== 'undefined'){
				return selectName;
			}else{
				return that.getSelectID(element);
			}
		},

		//获取原生选择列表class名
		getSelectClassName: function(element){
			var $this = $(element),
				tempClassNameArray = [],
				classNameArray = [],
				className = $this.attr('class');

			if(typeof(className) !== 'undefined'){
				classNameArray = className.split(' ');
				classNameArray.sort();
				tempClassNameArray =[classNameArray[0]];
				for(var i = 1; i < classNameArray.length; i++){
					if( classNameArray[i] !== tempClassNameArray[tempClassNameArray.length-1]){
						tempClassNameArray.push(classNameArray[i]);
					}
				}
				return tempClassNameArray.join(' ');
			}else{
				className='selectlist';
				return className;
			}
		},

		//获取原生选择列表选中索引值
		getSelectedIndex: function(element){
			var $this = $(element),
				selectedIndex = $this.get(0).selectedIndex || 0;

			return selectedIndex;
		},

		//获取原生选择列表option的数量
		getOptionCount: function(element){
			var $this = $(element);

			return  $this.children().length;
		},

		//获取原生选择列表option的内容
		getOptionText: function(element){
			var that = this,
				$this = $(element),
				$option = $this.children(),
				optionText = [],
				selectLength = that.getOptionCount($this);

			for(var i=0;i<selectLength;i++){
				optionText[i] = $.trim($option.eq(i).text());
			}
			return optionText;
		},

		//获取原生选择列表选中值
		getSelectedOptionText: function(element){
			var that = this,
				$this = $(element),
				selectedIndex = that.getSelectedIndex($this),
				optionText = that.getOptionText($this);

			return optionText[selectedIndex];

		},
		//获取原生选择列表选中datatype
		getSelectedOptionDataType: function(element){
			var that = this,
				$this = $(element),
				selectedIndex = that.getSelectedIndex($this),
				optionDataType = that.getOptionDataType($this);

			return optionDataType[selectedIndex];

		},

		//获取原生选择列表选中option的value值
		getSelectedOptionValue: function(element){
			var that = this,
				$this = $(element),
				selectedIndex = that.getSelectedIndex($this),
				optionValue = that.getOptionValue($this);

			return optionValue[selectedIndex];
		},

		//获取原生选择列表所有option的value,返回数组
		getOptionValue: function(element){
			var that = this,
				$this = $(element),
				$option = $this.children(),
				optionValue = [],
				selectLength = that.getOptionCount($this);

			for(var i = 0; i < selectLength; i++ ){
				optionValue[i] = $option.eq(i).val();
			}
			return optionValue;
		},

		//获取原生选择列表所有option的data-type,返回数组
		getOptionDataType: function(element){
			var that = this,
				$this = $(element),
				$option = $this.children(),
				optionDataType = [],
				selectLength = that.getOptionCount($this);

			for(var i = 0; i < selectLength; i++ ){
				optionDataType[i] = $option.eq(i).attr('data-type') ? $option.eq(i).attr('data-type') : '';
			}
			return optionDataType;
		},
		//获取原生选择列表所有option的data-layer,返回数组
		getOptionDataLayer: function(element){
			var that = this,
				$this = $(element),
				$option = $this.children(),
				optionDataLayer = [],
				selectLength = that.getOptionCount($this);

			for(var i = 0; i < selectLength; i++ ){
				optionDataLayer[i] = $option.eq(i).attr('data-layer') ? $option.eq(i).attr('data-layer') : '';
			}
			return optionDataLayer;
		},

		//生成模拟选择列表
		renderSelect: function(element,selectID){
			var that = this,
				$this = $(element),
				selectID = selectID,
				selectName = that.getSelectName($this),
				selectClassName = that.getSelectClassName($this),
				selectOptionText = that.getOptionText($this),
				selectedOptionText = that.getSelectedOptionText($this),
				selectOptionValue = that.getOptionValue($this),
				selectedIndex = that.getSelectedIndex($this),
				selectedValue = that.getSelectedOptionValue($this),
				selectLength = that.getOptionCount($this),
				selectDisplay = $this.css('display'),
				selectOptionDataType = that.getOptionDataType($this),
				selectedOptionDataType = that.getSelectedOptionDataType($this),
				selectOptionDataLayer = that.getOptionDataLayer($this),
				//selectDataType = $this.
				selectHTML = '<div id="' + selectID + '" class="select-wrapper ' + selectClassName + '" style="display:'+selectDisplay+';"><span class="s-select-icon"><i class="icon select-down"></i></span><input type="button" class="select-button" data-type="'+selectedOptionDataType+'" value="' + selectedOptionText + '" /><input type="hidden" class="hiddenId" name="' + selectName + '" data-type="'+selectedOptionDataType+'" value="' + selectedValue + '" /><input type="hidden" class="hiddenName" data-type="'+selectedOptionDataType+'" name="' + selectName + 'Name" value="' + selectedOptionText + '" /><div class="select-list"><ul>',
				selectListHTML = '';
			for(var i=0; i<selectLength; i++){
				var dataType = selectOptionDataType[i];
				if(i !== selectedIndex){
					if(selectOptionDataLayer[i] == 'sub') {
						selectListHTML = selectListHTML + '<li class="list-sub" data-value="' + selectOptionValue[i] + '" data-type="' + dataType + '">' + selectOptionText[i] + '</li>';
					}else if(selectOptionDataLayer[i] == 'parent'){
						selectListHTML = selectListHTML + '<li class="list-parent" data-value="' + selectOptionValue[i] + '" data-type="' + dataType + '">' + selectOptionText[i] + '</li>';
					}else{
						selectListHTML = selectListHTML + '<li data-value="' + selectOptionValue[i] + '" data-type="' + dataType + '">' + selectOptionText[i] + '</li>';
					}
				}else{
					if(selectOptionDataLayer[i] == 'sub') {
						selectListHTML = selectListHTML + '<li class="list-sub" data-value="' + selectOptionValue[i] + '" data-type="' + dataType + '" class="selected">' + selectOptionText[i] + '</li>';
					}else if(selectOptionDataLayer[i] == 'parent') {
						selectListHTML = selectListHTML + '<li class="list-parent" data-value="' + selectOptionValue[i] + '" data-type="' + dataType + '" >' + selectOptionText[i] + '</li>';
					}else {
						selectListHTML = selectListHTML + '<li data-value="' + selectOptionValue[i] + '" data-type="' + dataType + '" class="selected">' + selectOptionText[i] + '</li>';
					}
				}

			}
			selectHTML = selectHTML + selectListHTML + '</ul></div></div>';

			return selectHTML;
		},

		//替换原生选择列表
		replaceProtoypeSelect: function(element,selectID){
			var that = this,
				$this = $(element),
				selectHTML = that.renderSelect($this,selectID);

			$this.replaceWith(selectHTML);
		},

		//设置模拟选择列表不可用
		setSelectDisabled: function(element){
			var that = this,
				$this = $(element),
				selectID = '#' + that.getSelectID(element);
			$(selectID).addClass('disabled');
			$(selectID).children('i').addClass('disabled').end()
				.children('.select-button').attr('disabled','disabled');
		},

		//设置模拟选择列表可用
		setSelectEnabled: function(element){
			var that = this,
				$this = $(element),
				selectID = '#' + that.getSelectID(element);
			$(selectID).removeClass('disabled');
			$(selectID).children('i').removeClass('disabled').end()
				.children('.select-button').removeAttr('disabled');
		},

		//设置模拟选择列表样式
		setSelectStyle: function(element){
			var that = this,
				$this = $(element),
				selectID = '.' + that.getSelectClassName($this),
				selectLength = that.getOptionCount($this);

			//设置模拟选择列表外层样式
			$(selectID).css({
				'z-index': that.setStyleProperty(that.settings.zIndex),
				width: that.setStyleProperty(that.settings.width) - 2 + 'px',
				height: that.setStyleProperty(that.settings.height) + 'px',
				marginTop: that.setStyleProperty(that.settings.marginTop) + 'px'
			});

			//设置模拟选择列表向下箭头样式
			$(selectID).find('.select-down').css({
				top: that.setStyleProperty((that.settings.height - that.settings.triangleSize)/2) + 'px',
				'border-width': that.setStyleProperty(that.settings.triangleSize) + 'px'
				//'border-color': that.setStyleProperty(that.settings.triangleColor) + ' transparent transparent transparent'
			});

			//设置模拟选择列表按钮样式
			$(selectID).children('.select-button').css({
				width: function(){
					if(!that.settings.width){
						return;
					}else{
						return that.settings.width - 4 + 'px';
					}
				},
				height: that.setStyleProperty(that.settings.height) - 2 + 'px',
				border: that.setStyleProperty(that.settings.border),
				'border-radius': that.setStyleProperty(that.settings.borderRadius) + 'px'
			});

			//设置模拟选择列表下拉外层样式
			$(selectID).children('.select-list').css({
				width:  function(){
					if(!that.settings.width){
						return;
					}else{
						return that.settings.width - 2 + 'px';
					}
				},
				'top': function(index,value){
					if(!that.settings.height){
						return;
					}else{
						if(!that.settings.topPosition){
							return that.settings.height + 1 + 'px';
						}else{
							if(!that.settings.optionHeight){
								//计算下拉列表高度
							}else{
								return -that.settings.optionHeight*selectLength - 3 + 'px';
							}
						}
					}
				},
				'max-height': that.setStyleProperty(that.settings.showMaxHeight) + 'px'
			});

			//设置设置模拟选择列表选项外层样式
			$(selectID + ' ul').css({
				'max-height': that.setStyleProperty(that.settings.showMaxHeight) + 'px',
				'line-height': that.setStyleProperty(that.settings.optionHeight) + 'px'
			});

			//设置模拟选择列表选项样式
			$(selectID + ' li').css({
				height: that.setStyleProperty(that.settings.optionHeight) + 'px'
			});

		},

		//检测是否设置某个样式
		setStyleProperty: function(styleProperty){
			if(!styleProperty){
				return;
			}else{
				return styleProperty;
			}
		},
		setSelectList : function(json){
			var htmlTemp = [];
			if(json){
				var selectLength = json.data.length;
				var jsonData = json.data;
				for (var i = 0; i < selectLength; i++) {
					if (jsonData[i].layer) {
						if (jsonData[i].layer == 'parent') {
							htmlTemp.push('<li class="list-parent" data-value="' + jsonData[i]['id'] + '" data-type="">' + jsonData[i]['name'] + '</li>');
						} else {
							htmlTemp.push('<li class="list-sub" data-value="' + jsonData[i]['id'] + '" data-type="">' + jsonData[i]['name'] + '</li>');
						}
					} else {
						htmlTemp.push('<li data-value="' + jsonData[i]['id'] + '" data-type="">' + jsonData[i]['name'] + '</li>');
					}
				}
			}
			return htmlTemp.join(' ');
		},
		//绑定模拟选择列表一系列事件
		setSelectEvent: function(element,id){
			var that = this,
				$this = $(element),
				showSpeed = that.settings.speed,
				border = that.settings.border,
				selectID = '#' + id,
				selectName = that.getSelectName($this),
				selectedIndex = that.getSelectedIndex($this),
				selectLength = that.getOptionCount($this),
				selectBtn = $(selectID + ' input[type="button"]'),
				selectItem = $(selectID);
			that.id = id;
			if(that.settings.enable){
				$(selectID).click(function(event){
					event.stopPropagation();
					var _this = $(this);
					if(_this.children('.select-button').prop('disabled'))
						return;
					// getJson
					if ($.isFunction(that.settings.setData)) {
						that.settings.setData.call(that,that.setSelectList);
					}

					$(this).children('.select-list').slideToggle(showSpeed);
					if(that.settings.border){
						$(this).css({border:border});
					}else{
						$(this).addClass('focus');
					}

					$(this).find('li').each(function(){
						if($(this).text() === selectBtn.val() && !$(this).hasClass('list-parent')){

							$(this).addClass('selected').siblings().removeClass('selected');
						}
					})

				})
					.on('focusin','input[type="button"]',function(){
						$('.select-wrapper').children('.select-list').slideUp(showSpeed);
						if($('.select-wrapper').hasClass('focus')){
							$('.select-wrapper').removeClass('focus');
						}
					})
					.on('keyup','input[type="button"]',function(event){
						//缓存第一个被选中的值
						var $selectedItem = $(this).siblings('.select-list').children().children('li.selected');

						switch(event.keyCode){
							//Enter
							case 13:
								$(this)
									.val($selectedItem.text())
									.siblings('input.hiddenId').val($selectedItem.attr('data-value'));
								if ($.isFunction(that.settings.onChange)) {
									that.settings.onChange.call(that,event);
								}
								break;
							//Esc
							case 27:
								$(this).siblings('.select-list').slideUp(showSpeed);
								break;
							//Up
							case 38:
								event.preventDefault();
								if(selectedIndex !== 0){
									$selectedItem.removeClass('selected').prev().addClass('selected');
									selectedIndex =  selectedIndex - 1;
								}else{
									$selectItem.last().addClass('selected').siblings().removeClass('selected');
									selectedIndex = selectLength - 1;
								}
								$selectedItem =  $(this).siblings('.select-list').children().children('li.selected');
								$(this)
									.val($selectedItem.text())
									.prev().prev().val($selectedItem.attr('data-value'));
								break;
							//Down
							case 40:
								event.preventDefault();
								if(selectedIndex < selectLength - 1 ){
									$selectedItem.removeClass('selected').next().addClass('selected');
									selectedIndex =  selectedIndex + 1;
								}else{
									$selectItem.first().addClass('selected').siblings().removeClass('selected');
									selectedIndex = 0;
								}
								$selectedItem =  $(this).siblings('.select-list').children().children('li.selected');
								$(this)
									.val($selectedItem.text())
									.prev().prev().val($selectedItem.attr('data-value'));
								break;
						}

					})
					.children('i').removeClass('disabled').end()
					.children('.select-button').removeAttr('disabled');

				//绑定单击选项事件
				selectItem.delegate('li', 'click',function(event){
					event.stopPropagation();
					if(!$(this).hasClass('list-parent')) {
						$(this).addClass('selected').siblings().removeClass('selected');

						$(this).parent().parent().slideUp(showSpeed)
							.siblings('input.select-button').val($(this).text()).attr('data-type', $(this).attr('data-type'))
							.siblings('input.hiddenName').val($(this).text()).attr('data-type', $(this).attr('data-type'))
							.siblings('input.hiddenId').val($(this).attr('data-value')).attr('data-type', $(this).attr('data-type'));

						if ($('.select-wrapper').hasClass('focus')){
							$('.select-wrapper').removeClass('focus');
						}

						if ($.isFunction(that.settings.onChange)) {
							that.settings.onChange.call(that,event);
						}
					}

					return false;
				}).hover(function(){
					$(this).addClass('selected').siblings().removeClass('selected');
				}).mouseenter(function(event){
					var target = event.target,
						realWidth =  target.offsetWidth,
						wrapperWidth = target.scrollWidth,
						text = $(target).text();
					if(realWidth < wrapperWidth){
						$(target).attr( "title", text);
					}
				})

				$(document).on('click',function(){
					$(this).find('.select-list').slideUp(showSpeed);
					if($('.select-wrapper').hasClass('focus')){
						$('.select-wrapper').removeClass('focus');
					}
				})

			}else{
				$(selectID).addClass('disabled');
				$(selectID)
					.children('i').addClass('disabled').end()
					.children('.select-button').attr('disabled','disabled');
				return;
			}
		},

		//绑定模拟选择列表一系列事件
		setSelectEventDelegate: function(element,id){
			var that = this,
				$this = $(element),
				showSpeed = that.settings.speed,
				border = that.settings.border,
				selectID = '#' + id,
				selectName = that.getSelectName($this),
				selectedIndex = that.getSelectedIndex($this),
				selectLength = that.getOptionCount($this),
				selectBtn = $(selectID + ' input[type="button"]'),
				selectItem = $(selectID);
			that.id = id;
			if(that.settings.enable){
				$(that.settings.delegate).delegate(selectID, 'click', function(event){
					event.stopPropagation();
					var _this = $(this);
					// getJson
					if ($.isFunction(that.settings.setData)) {
						that.settings.setData.call(that,that.setSelectList);
					}

					$(this).children('.select-list').slideToggle(showSpeed);
					if(that.settings.border){
						$(this).css({border:border});
					}else{
						$(this).addClass('focus');
					}

					$(this).find('li').each(function(){
						if($(this).text() === selectBtn.val() && !$(this).hasClass('list-parent')){

							$(this).addClass('selected').siblings().removeClass('selected');
						}
					})

				})
					.on('focusin','input[type="button"]',function(){
						$('.select-wrapper').children('.select-list').slideUp(showSpeed);
						if($('.select-wrapper').hasClass('focus')){
							$('.select-wrapper').removeClass('focus');
						}
					})
					.on('keyup','input[type="button"]',function(event){
						//缓存第一个被选中的值
						var $selectedItem = $(this).siblings('.select-list').children().children('li.selected');

						switch(event.keyCode){
							//Enter
							case 13:
								$(this)
									.val($selectedItem.text())
									.siblings('input.hiddenId').val($selectedItem.attr('data-value'));
								if ($.isFunction(that.settings.onChange)) {
									that.settings.onChange.call(that, event);
								}
								break;
							//Esc
							case 27:
								$(this).siblings('.select-list').slideUp(showSpeed);
								break;
							//Up
							case 38:
								event.preventDefault();
								if(selectedIndex !== 0){
									$selectedItem.removeClass('selected').prev().addClass('selected');
									selectedIndex =  selectedIndex - 1;
								}else{
									$selectItem.last().addClass('selected').siblings().removeClass('selected');
									selectedIndex = selectLength - 1;
								}
								$selectedItem =  $(this).siblings('.select-list').children().children('li.selected');
								$(this)
									.val($selectedItem.text())
									.prev().prev().val($selectedItem.attr('data-value'));
								break;
							//Down
							case 40:
								event.preventDefault();
								if(selectedIndex < selectLength - 1 ){
									$selectedItem.removeClass('selected').next().addClass('selected');
									selectedIndex =  selectedIndex + 1;
								}else{
									$selectItem.first().addClass('selected').siblings().removeClass('selected');
									selectedIndex = 0;
								}
								$selectedItem =  $(this).siblings('.select-list').children().children('li.selected');
								$(this)
									.val($selectedItem.text())
									.prev().prev().val($selectedItem.attr('data-value'));
								break;
						}

					})
					.children('i').removeClass('disabled').end()
					.children('.select-button').removeAttr('disabled');

				//绑定单击选项事件
				$(that.settings.delegate).delegate(selectID+' li', 'click', function(event){
					event.stopPropagation();
					if(!$(this).hasClass('list-parent')) {
						$(this).addClass('selected').siblings().removeClass('selected');

						$(this).parent().parent().slideUp(showSpeed)
							.siblings('input.select-button').val($(this).text()).attr('data-type', $(this).attr('data-type'))
							.siblings('input.hiddenName').val($(this).text()).attr('data-type', $(this).attr('data-type'))
							.siblings('input.hiddenId').val($(this).attr('data-value')).attr('data-type', $(this).attr('data-type'));

						if ($('.select-wrapper').hasClass('focus')) {
							$('.select-wrapper').removeClass('focus');
						}

						if ($.isFunction(that.settings.onChange)) {
							that.settings.onChange.call(that, event);
						}
					}

					return false;
				}).hover(function(){
					$(this).addClass('selected').siblings().removeClass('selected');
				}).mouseenter(function(event){
					var target = event.target,
						realWidth =  target.offsetWidth,
						wrapperWidth = target.scrollWidth,
						text = $(target).text();
					if(realWidth < wrapperWidth){
						$(target).attr( "title", text);
					}
				})

				$(document).on('click',function(){
					$(this).find('.select-list').slideUp(showSpeed);
					if($('.select-wrapper').hasClass('focus')){
						$('.select-wrapper').removeClass('focus');
					}
				})

			}else{
				$(selectID).addClass('disabled');
				$(selectID)
					.children('i').addClass('disabled').end()
					.children('.select-button').attr('disabled','disabled');
				return;
			}
		}

	};

	$.fn[pluginName] = function(options) {
		this.each(function() {
			if (!$.data(this, "plugin_" + pluginName)) {
				$.data(this, "plugin_" + pluginName, new SelectList(this, options));
				if(!options.topPosition){
					options.zIndex--;
				}else{
					options.zIndex++;
				};
			}else{
				$.data(this, "plugin_" + pluginName, new SelectList(this, options));
			}
		});
		return this;
	};

})(jQuery,window,document);