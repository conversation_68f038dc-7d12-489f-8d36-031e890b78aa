<!doctype html>
<html>
<head>
<meta charset="utf-8">
<title>select</title>
<style type="text/css">
	.pop-container{padding: 15px 15px;}
  
</style>
</head>
<body>
<h2>select</h2>
<div class="pop-container ml30" id="city_1">
    <select id="prov" class="prov in-pageNo">
      <option value=""></option>
    </select>
    <select id="city" class="city in-pageNo">
        <option value=""></option>
    </select>
    <select id="dist" class="dist in-pageNo" style="display:none;">
      <option value="请选择">请选择</option>
    </select>
</div>
<div class="pop-container ml30">
    
    <select class="in-pageNo">
      <option value="10">10</option>
      <option value="15">15</option>
      <option value="20">20</option>
    </select>
    <select class="in-select-ss">
      <option value="请选择">请选择</option>
      <option selected value="1">请选择11</option>
    </select>
    <select class="in-select">
      <option value="请选择">请选择</option>
      <option value="请选择2">请选择2</option>
      <option value="请选择3">请选择3</option>
    </select>
    <select class="in-select">
      <option value="请选择">请选择</option>
      <option value="请选择">请选择</option>
    </select>
    <select class="in-select">
      <option value="请选择">请选择</option>
    </select>
</div>
<pre style="width: 80%; padding: 20px; margin: 40px;">
     $('.in-select').selectlist({topPosition:false});
      //table显示条数
      $('.in-pageNo').selectlist({width:100});

      $("#city_1").citySelect({
        url:'', //../../../users/findAll.html
        nodata:"none"
      });
</pre>

<script type="text/javascript" src="ALL-plugins/plugins/jquery.selectlist/js/jquery.cityselect.js"></script>
<script type="text/javascript">
    $(document).ready(function(){
      
      

    //公共select 
    $('.in-select').selectlist({topPosition:false});

    $('.in-select-ss').selectlist({topPosition:false, setData: function(){
        var _this = this;
        var dateTime = new Date().getTime();
        var _url = 'ALL-plugins/plugins/jquery.selectlist/js/cityJson2.js?dataTime='+dateTime;

        $.getJSON(_url, function(json){
            var lstHtml = _this.setSelectList(json);
             $('.in-select-ss').find('ul').empty().html(lstHtml);
        });
      }});

      //table显示条数
      $('.in-pageNo').selectlist({width:100});

      $("#city_1").citySelect({
        url:'', //../../../users/findAll.html
        nodata:"none"
      });
//        $.ajax({
//            url: "http://localhost:3000/users/findAll.html",
//            type: "GET",
//            cache: false,
//            //beforeSend: function () { },//$("#publish").attr("disabled", "disabled"); },
//            success: function (data) {
//                console.log(data);
//            },
//            error:function(){
//                console.log('error');
//            }
//        });

    });

</script>
</body>
</html>
