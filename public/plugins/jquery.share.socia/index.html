<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>jQuery弹出社交分享按钮</title>
    <link rel="stylesheet" href="css/share.css" type="text/css" />
    <script src="js/jquery1.8.2.min.js" type="text/javascript"></script>
    <script src="js/share.js" type="text/javascript"></script>
</head>

<body>
    <h1>分享组件</h1>
    <div id="socialShare" class="socialShare">
        <div class="social_group">
            <a target="_blank" class="uec-weixin-js msb_network_button weixin" style="display: block; left: 71px; top: 5.5px;">weixin</a>
            <a target="_blank" class="uec-weibo-js msb_network_button sina" style="display: block; left: 131px; top: 5.5px;">sina</a>
            <a target="_blank" class="  msb_network_button tQQ" style="display: block; left: 191px; top: 5.5px;">tQQ</a>
            <a target="_blank" class="uec-QQ-js msb_network_button qZone" style="display: block; left: 251px; top: 5.5px;">qZone</a>
            <a target="_blank" class="msb_network_button douban" style="display: block; left: 311px; top: 5.5px;">douban</a>
        </div>
    </div>
    <script>
        $(function() {
            $("#socialShare").socialShare({
                content: '',
				url:'',
				titile:''
            });
        });

    </script>

</body>

</html>
