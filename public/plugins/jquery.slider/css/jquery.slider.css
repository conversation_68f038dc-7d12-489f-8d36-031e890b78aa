.flexslider {margin:0 auto;width:100%;height:168px;overflow:hidden;zoom:1;position:relative;}
.flexslider .slides li {display: none; width:100%;height:100%;overflow:hidden; position: absolute;}
.flex-direction-nav a {width:70px;height:70px;line-height:99em;overflow:hidden;margin:-35px 0 0;display:block;background:url(../images/ad_ctr.png) no-repeat;position: absolute;
	top: 50%;z-index: 10;cursor: pointer;opacity: 0;filter: alpha(opacity=0);-webkit-transition: all .3s ease;border-radius: 35px;}
.flexslider .block-a{display: inline-block;*display:inline;*zoom:1; width: 100%; height: 100%;  background: no-repeat 0 0;background-size: cover;}
.flex-direction-nav .flex-next {background-position: 0 -70px;right: 0;}
.flex-direction-nav .flex-prev {left: 0;}
.flexslider:hover .flex-next {opacity: 0.8;filter: alpha(opacity=25);}
.flexslider:hover .flex-prev {opacity: 0.8;filter: alpha(opacity=25);}
.flexslider:hover .flex-next:hover,.flexslider:hover .flex-prev:hover {opacity: 1;filter: alpha(opacity=50);}
.flex-control-nav {width:100%;position: absolute;text-align:center;left: 0;right: 0;  bottom: 5px;  }
.flex-control-nav li {margin: 0 2px; display: inline-block; width: 16px; height: 16px; }
.flex-control-paging li a {background:url(../images/dot.png) no-repeat 0 -16px;	display: block;	height: 16px;overflow: hidden;text-indent: -99em;width:16px;cursor: pointer;}
.flex-control-paging li a.flex-active,.flex-control-paging li.active a {background-position: 0 0;}
.flexslider .slides a img {	width: 100%;height:100%;display: block;}
.flexslider .slides-title{display: none; margin: -30px 0 0 10px; text-align: left; font-size: 14px; line-height: 24px; color: #fff;}
.flexslider .slides .active .slides-title{ display:block;}

/*.flexslider.bisect{  height:auto;}*/
.flexslider.bisect .slides li{height: 168px;display: block;  position: relative;}
.flexslider.bisect .flex-control-nav{display: none;}
.flexslider.bisect .slides-title{ display:block;}
