<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" /> 
  <title>summernote</title>
  <link href="../../stylesheets/common.css" rel="stylesheet" type="text/css" />
  <link href="../bootstrap/css/bootstrap.css" rel="stylesheet" type="text/css" />
  <link href="../bootstrap/css/bootstrap-theme.css" rel="stylesheet" type="text/css"/>
  <link rel="stylesheet" type="text/css" href="dist/summernote.css">
  <link rel="stylesheet" type="text/css" href="dist/font-awesome.css">


  <!-- include libs stylesheets -->


</head>
<body>
<div class="container">
  <h4>Lately library
    <span class="label label-info">Bootstrap v3.1.1</span>
    <span class="label label-success">font-awesome v4.0.3</span>
    <span class="label label-danger">CodeMirror v3.20.0</span>
  </h4>
  <div class="summernote"></div>
  <button id="test1"> test1 </button>
</div>
<script src="../jquery/jquery-1.11.3.js"></script>
<script type="text/javascript" src="../bootstrap/js/bootstrap.js"></script>
<script src="dist/summernote.js"></script>
<script src="lang/summernote-zh-CN.js"></script>

<script type="text/javascript">
  $(function(){
      var $sum = $('.summernote').summernote({lang:'zh-CN',width:800,height:300,disableResizeEditor:true
      });

      $('#test1').click(function(){
        $('.summernote').summernote('code','ddddd')
        var code = $('.summernote').summernote('code');
        alert($('.summernote').summernote('code'));
      });
  })
</script>
</body>
</html>
