<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Load By Sea.js</title>
	<link rel="stylesheet" href="demo.css">
</head>

<body>

<form data-validator-option="{timely:2}">
    <div class="form-item">
		<input type="text" name="user[email]" data-rule="required;email" placeholder="邮箱">
	</div>
    <div class="form-item">
		<input type="password" name="password" data-rule="required;" placeholder="密码">
	</div>
    <div class="form-item">
        <button type="submit">提交</button>
    </div>
</form>


<script src="http://dn-nice.qbox.me/libs/seajs/2.2.1/sea.js" id="seajsnode"></script>
<script>
seajs.config({
    base: './',
    alias: {
        jquery: 'http://dn-nice.qbox.me/libs/jquery/1.x/jquery.min',
        validator: '../local/zh-CN'
    }
});

// define a module
define('demo', function(require){
    var $ = require('jquery');
    require('validator');
    // do something
});

// use module
seajs.use('demo');

</script>
</body>
</html>