/*********************
 * utils
 *********************/
/*
 * To define a message box
 */
theme($font_color, $background_color=#fff, $border_color=#FFBB76, $shadow_color){
    background-color: $background_color;
    border-color: $border_color;
    if $font_color {
        color: $font_color;
    }
    if $shadow_color {
        box-shadow: 0 1px 3px $shadow_color;
    }
    .n-arrow {
        b {
            color: $border_color;
            if $shadow_color {
                text-shadow: 0 0 2px $shadow_color;
            }
        }
        i {color: $background_color;}
    }
}

inline-block() {
    display:inline-block; *display:inline; *zoom:1;
}


/*********************
 * global settings
 *********************/
.n-inline-block {
    inline-block();
}
.nice-validator {
    input, select, textarea {
        @extend .n-inline-block
    }
    .msg-container {
        .msg-box {display: block;}
        .msg-wrap {position:static;}
    }
}
.msg-box {
    position:relative;
    *zoom:1;
}
.msg-wrap {
    position: relative;
    white-space: nowrap;
}
.msg-wrap,.n-icon,.n-msg {
    @extend .n-inline-block
    vertical-align:top;
}
.msg-box {
    .msg-wrap {
        .n-error, .n-ok, .n-tip {
            display: block;
            background: none;
            box-shadow: none;
            padding: 3px 2px;
        }
    }
}

.n-arrow {
    position: absolute; 
    overflow: hidden;
    b, i {
        position: absolute;
        left: 0; top: 0;
        border: 0;
        margin: 0;
        padding: 0;
        overflow: hidden;
        font-weight: 400;
        font-style: normal; 
        font-size: 12px;
        font-family: serif;
        line-height: 14px; _line-height: 15px;
    }
    i {text-shadow:none;}
}
.n-icon {
    width:16px; height:16px;
    overflow:hidden;
    background-repeat:no-repeat;
}
.n-msg {
    inline-block();
    line-height:16px;
    margin-left: 1px;
    font-size: 12px;
    //font-family: simsun;
}

// valid input
.n-valid {}
// invalid input
.n-invalid {}
// tooltip
.n-error {color:#CC3333;}
.n-ok {color:#390;}
.n-tip .n-msg, .n-loading {color:#808080;}

// icons
.n-error .n-icon {background-position:0 0;}
.n-ok .n-icon {background-position:-16px -0;}
.n-tip .n-icon {background-position:-32px -0;}
.n-loading .n-icon {background: url(images/loading.gif) 0 center no-repeat !important;}


/** arrow and position **/
.n-top, .n-right, .n-bottom, .n-left {
    inline-block();
    line-height: 0;
    vertical-align:top;
    outline: 0;
}
.n-top, .n-bottom {
    .n-arrow {height: 6px; width: 12px; left:8px;}
}
 
.n-left, .n-right {
    .n-arrow {width: 6px; height: 12px; top: 6px; }
}

/** top **/
.n-top {
    vertical-align: top;
    .msg-wrap { margin-bottom:6px; }
    .n-arrow {
        bottom: -6px;
        b {top: -6px;}
        i {top: -7px;}
    }
}
/** bottom **/
.n-bottom {
    vertical-align: bottom;
    .msg-wrap { margin-top: 6px; }
    .n-arrow {
        top: -6px;
        b {top: -1px;}
        i {top: 0;}
    }
}
/** left **/
.n-left {
    .msg-wrap {right: 100%; margin-right:6px; }
    .n-arrow {
        right:-6px;
        b {left:-6px;}
        i {left:-7px;}
    }
}
/** right **/
.n-right {
    .msg-wrap { margin-left:6px; }
    .n-arrow {
        left:-6px;
        b {left:1px;}
        i {left:2px;}
    }
}
