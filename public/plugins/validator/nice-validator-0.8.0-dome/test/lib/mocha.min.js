/*! mocha 2.0.1
 */
!function(){function a(b){var c=a.resolve(b),d=a.modules[c];if(!d)throw new Error('failed to require "'+b+'"');return d.exports||(d.exports={},d.call(d.exports,d,d.exports,a.relative(c))),d.exports}function o(){for(var a=(new c).getTime();m.length&&(new c).getTime()-a<100;)m.shift()();n=m.length?d(o,0):null}a.modules={},a.resolve=function(b){var c=b,d=b+".js",e=b+"/index.js";return a.modules[d]&&d||a.modules[e]&&e||c},a.register=function(b,c){a.modules[b]=c},a.relative=function(b){return function(c){if("."!=c.charAt(0))return a(c);var d=b.split("/"),e=c.split("/");d.pop();for(var f=0;f<e.length;f++){var g=e[f];".."==g?d.pop():"."!=g&&d.push(g)}return a(d.join("/"))}},a.register("browser/debug.js",function(a){a.exports=function(){return function(){}}}),a.register("browser/diff.js",function(a){var d=function(){function a(a){return{newPos:a.newPos,components:a.components.slice(0)}}function b(a){for(var b=[],c=0;c<a.length;c++)a[c]&&b.push(a[c]);return b}function c(a){var b=a;return b=b.replace(/&/g,"&amp;"),b=b.replace(/</g,"&lt;"),b=b.replace(/>/g,"&gt;"),b=b.replace(/"/g,"&quot;")}var d=function(a){this.ignoreWhitespace=a};d.prototype={diff:function(b,c){if(c===b)return[{value:c}];if(!c)return[{value:b,removed:!0}];if(!b)return[{value:c,added:!0}];c=this.tokenize(c),b=this.tokenize(b);var d=c.length,e=b.length,f=d+e,g=[{newPos:-1,components:[]}],h=this.extractCommon(g[0],c,b,0);if(g[0].newPos+1>=d&&h+1>=e)return g[0].components;for(var i=1;f>=i;i++)for(var j=-1*i;i>=j;j+=2){var k,l=g[j-1],m=g[j+1];h=(m?m.newPos:0)-j,l&&(g[j-1]=void 0);var n=l&&l.newPos+1<d,o=m&&h>=0&&e>h;if(n||o){!n||o&&l.newPos<m.newPos?(k=a(m),this.pushComponent(k.components,b[h],void 0,!0)):(k=a(l),k.newPos++,this.pushComponent(k.components,c[k.newPos],!0,void 0));var h=this.extractCommon(k,c,b,j);if(k.newPos+1>=d&&h+1>=e)return k.components;g[j]=k}else g[j]=void 0}},pushComponent:function(a,b,c,d){var e=a[a.length-1];e&&e.added===c&&e.removed===d?a[a.length-1]={value:this.join(e.value,b),added:c,removed:d}:a.push({value:b,added:c,removed:d})},extractCommon:function(a,b,c,d){for(var e=b.length,f=c.length,g=a.newPos,h=g-d;e>g+1&&f>h+1&&this.equals(b[g+1],c[h+1]);)g++,h++,this.pushComponent(a.components,b[g],void 0,void 0);return a.newPos=g,h},equals:function(a,b){var c=/\S/;return!this.ignoreWhitespace||c.test(a)||c.test(b)?a===b:!0},join:function(a,b){return a+b},tokenize:function(a){return a}};var e=new d,f=new d(!0),g=new d;f.tokenize=g.tokenize=function(a){return b(a.split(/(\s+|\b)/))};var h=new d(!0);h.tokenize=function(a){return b(a.split(/([{}:;,]|\s+)/))};var i=new d;return i.tokenize=function(a){return a.split(/^/m)},{Diff:d,diffChars:function(a,b){return e.diff(a,b)},diffWords:function(a,b){return f.diff(a,b)},diffWordsWithSpace:function(a,b){return g.diff(a,b)},diffLines:function(a,b){return i.diff(a,b)},diffCss:function(a,b){return h.diff(a,b)},createPatch:function(a,b,c,d,e){function h(a){return a.map(function(a){return" "+a})}function j(a,b,c){var d=g[g.length-2],e=b===g.length-2,f=b===g.length-3&&(c.added!==d.added||c.removed!==d.removed);/\n$/.test(c.value)||!e&&!f||a.push("\\ No newline at end of file")}var f=[];f.push("Index: "+a),f.push("==================================================================="),f.push("--- "+a+("undefined"==typeof d?"":"	"+d)),f.push("+++ "+a+("undefined"==typeof e?"":"	"+e));var g=i.diff(b,c);g[g.length-1].value||g.pop(),g.push({value:"",lines:[]});for(var k=0,l=0,m=[],n=1,o=1,p=0;p<g.length;p++){var q=g[p],r=q.lines||q.value.replace(/\n$/,"").split("\n");if(q.lines=r,q.added||q.removed){if(!k){var s=g[p-1];k=n,l=o,s&&(m=h(s.lines.slice(-4)),k-=m.length,l-=m.length)}m.push.apply(m,r.map(function(a){return(q.added?"+":"-")+a})),j(m,p,q),q.added?o+=r.length:n+=r.length}else{if(k)if(r.length<=8&&p<g.length-2)m.push.apply(m,h(r));else{var t=Math.min(r.length,4);f.push("@@ -"+k+","+(n-k+t)+" +"+l+","+(o-l+t)+" @@"),f.push.apply(f,m),f.push.apply(f,h(r.slice(0,t))),r.length<=4&&j(f,p,q),k=0,l=0,m=[]}n+=r.length,o+=r.length}}return f.join("\n")+"\n"},applyPatch:function(a,b){for(var c=b.split("\n"),d=[],e=!1,f=!1,g="I"===c[0][0]?4:0;g<c.length;g++)if("@"===c[g][0]){var h=c[g].split(/@@ -(\d+),(\d+) \+(\d+),(\d+) @@/);d.unshift({start:h[3],oldlength:h[2],oldlines:[],newlength:h[4],newlines:[]})}else"+"===c[g][0]?d[0].newlines.push(c[g].substr(1)):"-"===c[g][0]?d[0].oldlines.push(c[g].substr(1)):" "===c[g][0]?(d[0].newlines.push(c[g].substr(1)),d[0].oldlines.push(c[g].substr(1))):"\\"===c[g][0]&&("+"===c[g-1][0]?e=!0:"-"===c[g-1][0]&&(f=!0));for(var i=a.split("\n"),g=d.length-1;g>=0;g--){for(var j=d[g],k=0;k<j.oldlength;k++)if(i[j.start-1+k]!==j.oldlines[k])return!1;Array.prototype.splice.apply(i,[j.start-1,+j.oldlength].concat(j.newlines))}if(e)for(;!i[i.length-1];)i.pop();else f&&i.push("");return i.join("\n")},convertChangesToXML:function(a){for(var b=[],d=0;d<a.length;d++){var e=a[d];e.added?b.push("<ins>"):e.removed&&b.push("<del>"),b.push(c(e.value)),e.added?b.push("</ins>"):e.removed&&b.push("</del>")}return b.join("")},convertChangesToDMP:function(a){for(var c,b=[],d=0;d<a.length;d++)c=a[d],b.push([c.added?1:c.removed?-1:0,c.value]);return b}}}();"undefined"!=typeof a&&(a.exports=d)}),a.register("browser/escape-string-regexp.js",function(a){"use strict";var d=/[|\\{}()[\]^$+*?.]/g;a.exports=function(a){if("string"!=typeof a)throw new TypeError("Expected a string");return a.replace(d,"\\$&")}}),a.register("browser/events.js",function(a,b){function d(a){return"[object Array]"=={}.toString.call(a)}function e(){}b.EventEmitter=e,e.prototype.on=function(a,b){return this.$events||(this.$events={}),this.$events[a]?d(this.$events[a])?this.$events[a].push(b):this.$events[a]=[this.$events[a],b]:this.$events[a]=b,this},e.prototype.addListener=e.prototype.on,e.prototype.once=function(a,b){function d(){c.removeListener(a,d),b.apply(this,arguments)}var c=this;return d.listener=b,this.on(a,d),this},e.prototype.removeListener=function(a,b){if(this.$events&&this.$events[a]){var c=this.$events[a];if(d(c)){for(var e=-1,f=0,g=c.length;g>f;f++)if(c[f]===b||c[f].listener&&c[f].listener===b){e=f;break}if(0>e)return this;c.splice(e,1),c.length||delete this.$events[a]}else(c===b||c.listener&&c.listener===b)&&delete this.$events[a]}return this},e.prototype.removeAllListeners=function(a){return void 0===a?(this.$events={},this):(this.$events&&this.$events[a]&&(this.$events[a]=null),this)},e.prototype.listeners=function(a){return this.$events||(this.$events={}),this.$events[a]||(this.$events[a]=[]),d(this.$events[a])||(this.$events[a]=[this.$events[a]]),this.$events[a]},e.prototype.emit=function(a){if(!this.$events)return!1;var b=this.$events[a];if(!b)return!1;var c=[].slice.call(arguments,1);if("function"==typeof b)b.apply(this,c);else{if(!d(b))return!1;for(var e=b.slice(),f=0,g=e.length;g>f;f++)e[f].apply(this,c)}return!0}}),a.register("browser/fs.js",function(){}),a.register("browser/glob.js",function(){}),a.register("browser/path.js",function(){}),a.register("browser/progress.js",function(a){function d(){this.percent=0,this.size(0),this.fontSize(11),this.font("helvetica, arial, sans-serif")}a.exports=d,d.prototype.size=function(a){return this._size=a,this},d.prototype.text=function(a){return this._text=a,this},d.prototype.fontSize=function(a){return this._fontSize=a,this},d.prototype.font=function(a){return this._font=a,this},d.prototype.update=function(a){return this.percent=a,this},d.prototype.draw=function(a){try{var b=Math.min(this.percent,100),c=this._size,d=c/2,e=d,f=d,g=d-1,h=this._fontSize;a.font=h+"px "+this._font;var i=2*Math.PI*(b/100);a.clearRect(0,0,c,c),a.strokeStyle="#9f9f9f",a.beginPath(),a.arc(e,f,g,0,i,!1),a.stroke(),a.strokeStyle="#eee",a.beginPath(),a.arc(e,f,g-1,0,i,!0),a.stroke();var j=this._text||(0|b)+"%",k=a.measureText(j).width;a.fillText(j,e-k/2+1,f+h/2-1)}catch(l){}return this}}),a.register("browser/tty.js",function(a,c){c.isatty=function(){return!0},c.getWindowSize=function(){return"innerHeight"in b?[b.innerHeight,b.innerWidth]:[640,480]}}),a.register("context.js",function(a){function d(){}a.exports=d,d.prototype.runnable=function(a){return 0==arguments.length?this._runnable:(this.test=this._runnable=a,this)},d.prototype.timeout=function(a){return 0===arguments.length?this.runnable().timeout():(this.runnable().timeout(a),this)},d.prototype.enableTimeouts=function(a){return this.runnable().enableTimeouts(a),this},d.prototype.slow=function(a){return this.runnable().slow(a),this},d.prototype.inspect=function(){return JSON.stringify(this,function(a,b){return"_runnable"!=a&&"test"!=a?b:void 0},2)}}),a.register("hook.js",function(a,b,c){function e(a,b){d.call(this,a,b),this.type="hook"}function f(){}var d=c("./runnable");a.exports=e,f.prototype=d.prototype,e.prototype=new f,e.prototype.constructor=e,e.prototype.error=function(a){if(0==arguments.length){var a=this._error;return this._error=null,a}this._error=a}}),a.register("interfaces/bdd.js",function(a,b,c){var d=c("../suite"),e=c("../test"),g=(c("../utils"),c("browser/escape-string-regexp"));a.exports=function(a){var b=[a];a.on("pre-require",function(a,c,f){a.before=function(a,c){b[0].beforeAll(a,c)},a.after=function(a,c){b[0].afterAll(a,c)},a.beforeEach=function(a,c){b[0].beforeEach(a,c)},a.afterEach=function(a,c){b[0].afterEach(a,c)},a.describe=a.context=function(a,e){var f=d.create(b[0],a);return f.file=c,b.unshift(f),e.call(f),b.shift(),f},a.xdescribe=a.xcontext=a.describe.skip=function(a,c){var e=d.create(b[0],a);e.pending=!0,b.unshift(e),c.call(e),b.shift()},a.describe.only=function(b,c){var d=a.describe(b,c);return f.grep(d.fullTitle()),d},a.it=a.specify=function(a,d){var f=b[0];f.pending&&(d=null);var g=new e(a,d);return g.file=c,f.addTest(g),g},a.it.only=function(b,c){var d=a.it(b,c),e="^"+g(d.fullTitle())+"$";return f.grep(new RegExp(e)),d},a.xit=a.xspecify=a.it.skip=function(b){a.it(b)}})}}),a.register("interfaces/exports.js",function(a,b,c){var d=c("../suite"),e=c("../test");a.exports=function(a){function c(a,f){var g;for(var h in a)if("function"==typeof a[h]){var i=a[h];switch(h){case"before":b[0].beforeAll(i);break;case"after":b[0].afterAll(i);break;case"beforeEach":b[0].beforeEach(i);break;case"afterEach":b[0].afterEach(i);break;default:var j=new e(h,i);j.file=f,b[0].addTest(j)}}else g=d.create(b[0],h),b.unshift(g),c(a[h]),b.shift()}var b=[a];a.on("require",c)}}),a.register("interfaces/index.js",function(a,b,c){b.bdd=c("./bdd"),b.tdd=c("./tdd"),b.qunit=c("./qunit"),b.exports=c("./exports")}),a.register("interfaces/qunit.js",function(a,b,c){var d=c("../suite"),e=c("../test"),f=c("browser/escape-string-regexp");c("../utils"),a.exports=function(a){var b=[a];a.on("pre-require",function(a,c,g){a.before=function(a,c){b[0].beforeAll(a,c)},a.after=function(a,c){b[0].afterAll(a,c)},a.beforeEach=function(a,c){b[0].beforeEach(a,c)},a.afterEach=function(a,c){b[0].afterEach(a,c)},a.suite=function(a){b.length>1&&b.shift();var e=d.create(b[0],a);return e.file=c,b.unshift(e),e},a.suite.only=function(b,c){var d=a.suite(b,c);g.grep(d.fullTitle())},a.test=function(a,d){var f=new e(a,d);return f.file=c,b[0].addTest(f),f},a.test.only=function(b,c){var d=a.test(b,c),e="^"+f(d.fullTitle())+"$";g.grep(new RegExp(e))},a.test.skip=function(b){a.test(b)}})}}),a.register("interfaces/tdd.js",function(a,b,c){var d=c("../suite"),e=c("../test"),f=c("browser/escape-string-regexp");c("../utils"),a.exports=function(a){var b=[a];a.on("pre-require",function(a,c,g){a.setup=function(a,c){b[0].beforeEach(a,c)},a.teardown=function(a,c){b[0].afterEach(a,c)},a.suiteSetup=function(a,c){b[0].beforeAll(a,c)},a.suiteTeardown=function(a,c){b[0].afterAll(a,c)},a.suite=function(a,e){var f=d.create(b[0],a);return f.file=c,b.unshift(f),e.call(f),b.shift(),f},a.suite.skip=function(a,c){var e=d.create(b[0],a);e.pending=!0,b.unshift(e),c.call(e),b.shift()},a.suite.only=function(b,c){var d=a.suite(b,c);g.grep(d.fullTitle())},a.test=function(a,d){var f=b[0];f.pending&&(d=null);var g=new e(a,d);return g.file=c,f.addTest(g),g},a.test.only=function(b,c){var d=a.test(b,c),e="^"+f(d.fullTitle())+"$";g.grep(new RegExp(e))},a.test.skip=function(b){a.test(b)}})}}),a.register("mocha.js",function(a,c,d){function k(a){return __dirname+"/../images/"+a+".png"}function l(a){a=a||{},this.files=[],this.options=a,this.grep(a.grep),this.suite=new c.Suite("",new c.Context),this.ui(a.ui),this.bail(a.bail),this.reporter(a.reporter),null!=a.timeout&&this.timeout(a.timeout),this.useColors(a.useColors),null!==a.enableTimeouts&&this.enableTimeouts(a.enableTimeouts),a.slow&&this.slow(a.slow),this.suite.on("pre-require",function(a){c.afterEach=a.afterEach||a.teardown,c.after=a.after||a.suiteTeardown,c.beforeEach=a.beforeEach||a.setup,c.before=a.before||a.suiteSetup,c.describe=a.describe||a.suite,c.it=a.it||a.test,c.setup=a.setup||a.beforeEach,c.suiteSetup=a.suiteSetup||a.before,c.suiteTeardown=a.suiteTeardown||a.after,c.suite=a.suite||a.describe,c.teardown=a.teardown||a.afterEach,c.test=a.test||a.it})}var e=d("browser/path"),f=d("browser/escape-string-regexp"),g=d("./utils");if(c=a.exports=l,"undefined"!=typeof h&&"function"==typeof h.cwd){var i=e.join,j=h.cwd();a.paths.push(j,i(j,"node_modules"))}c.utils=g,c.interfaces=d("./interfaces"),c.reporters=d("./reporters"),c.Runnable=d("./runnable"),c.Context=d("./context"),c.Runner=d("./runner"),c.Suite=d("./suite"),c.Hook=d("./hook"),c.Test=d("./test"),l.prototype.bail=function(a){return 0==arguments.length&&(a=!0),this.suite.bail(a),this},l.prototype.addFile=function(a){return this.files.push(a),this},l.prototype.reporter=function(a){if("function"==typeof a)this._reporter=a;else{a=a||"spec";var b;try{b=d("./reporters/"+a)}catch(c){}if(!b)try{b=d(a)}catch(c){}if(b||"teamcity"!==a||console.warn("The Teamcity reporter was moved to a package named mocha-teamcity-reporter (https://npmjs.org/package/mocha-teamcity-reporter)."),!b)throw new Error('invalid reporter "'+a+'"');this._reporter=b}return this},l.prototype.ui=function(a){if(a=a||"bdd",this._ui=c.interfaces[a],!this._ui)try{this._ui=d(a)}catch(b){}if(!this._ui)throw new Error('invalid interface "'+a+'"');return this._ui=this._ui(this.suite),this},l.prototype.loadFiles=function(a){var c=this,f=this.suite,g=this.files.length;this.files.forEach(function(h){h=e.resolve(h),f.emit("pre-require",b,h,c),f.emit("require",d(h),h,c),f.emit("post-require",b,h,c),--g||a&&a()})},l.prototype._growl=function(a,b){var c=d("growl");a.on("end",function(){var d=b.stats;if(d.failures){var e=d.failures+" of "+a.total+" tests failed";c(e,{name:"mocha",title:"Failed",image:k("error")})}else c(d.passes+" tests passed in "+d.duration+"ms",{name:"mocha",title:"Passed",image:k("ok")})})},l.prototype.grep=function(a){return this.options.grep="string"==typeof a?new RegExp(f(a)):a,this},l.prototype.invert=function(){return this.options.invert=!0,this},l.prototype.ignoreLeaks=function(a){return this.options.ignoreLeaks=!!a,this},l.prototype.checkLeaks=function(){return this.options.ignoreLeaks=!1,this},l.prototype.growl=function(){return this.options.growl=!0,this},l.prototype.globals=function(a){return this.options.globals=(this.options.globals||[]).concat(a),this},l.prototype.useColors=function(a){return this.options.useColors=arguments.length&&void 0!=a?a:!0,this},l.prototype.useInlineDiffs=function(a){return this.options.useInlineDiffs=arguments.length&&void 0!=a?a:!1,this},l.prototype.timeout=function(a){return this.suite.timeout(a),this},l.prototype.slow=function(a){return this.suite.slow(a),this},l.prototype.enableTimeouts=function(a){return this.suite.enableTimeouts(arguments.length&&void 0!==a?a:!0),this},l.prototype.asyncOnly=function(){return this.options.asyncOnly=!0,this},l.prototype.noHighlighting=function(){return this.options.noHighlighting=!0,this},l.prototype.run=function(a){this.files.length&&this.loadFiles();var b=this.suite,d=this.options;d.files=this.files;var e=new c.Runner(b),f=new this._reporter(e,d);return e.ignoreLeaks=!1!==d.ignoreLeaks,e.asyncOnly=d.asyncOnly,d.grep&&e.grep(d.grep,d.invert),d.globals&&e.globals(d.globals),d.growl&&this._growl(e,f),c.reporters.Base.useColors=d.useColors,c.reporters.Base.inlineDiffs=d.useInlineDiffs,e.run(a)}}),a.register("ms.js",function(a){function i(a){var b=/^((?:\d+)?\.?\d+) *(ms|seconds?|s|minutes?|m|hours?|h|days?|d|years?|y)?$/i.exec(a);if(b){var c=parseFloat(b[1]),i=(b[2]||"ms").toLowerCase();switch(i){case"years":case"year":case"y":return c*h;case"days":case"day":case"d":return c*g;case"hours":case"hour":case"h":return c*f;case"minutes":case"minute":case"m":return c*e;case"seconds":case"second":case"s":return c*d;case"ms":return c}}}function j(a){return a>=g?Math.round(a/g)+"d":a>=f?Math.round(a/f)+"h":a>=e?Math.round(a/e)+"m":a>=d?Math.round(a/d)+"s":a+"ms"}function k(a){return l(a,g,"day")||l(a,f,"hour")||l(a,e,"minute")||l(a,d,"second")||a+" ms"}function l(a,b,c){return b>a?void 0:1.5*b>a?Math.floor(a/b)+" "+c:Math.ceil(a/b)+" "+c+"s"}var d=1e3,e=60*d,f=60*e,g=24*f,h=365.25*g;a.exports=function(a,b){return b=b||{},"string"==typeof a?i(a):b["long"]?k(a):j(a)}}),a.register("reporters/base.js",function(a,c,d){function q(a){var c=this.stats={suites:0,tests:0,passes:0,pending:0,failures:0},d=this.failures=[];a&&(this.runner=a,a.stats=c,a.on("start",function(){c.start=new j}),a.on("suite",function(a){c.suites=c.suites||0,a.root||c.suites++}),a.on("test end",function(){c.tests=c.tests||0,c.tests++}),a.on("pass",function(a){c.passes=c.passes||0;var b=a.slow()/2;a.speed=a.duration>a.slow()?"slow":a.duration>b?"medium":"fast",c.passes++}),a.on("fail",function(a,b){c.failures=c.failures||0,c.failures++,a.err=b,d.push(a)}),a.on("end",function(){c.end=new j,c.duration=new j-c.start}),a.on("pending",function(){c.pending++}))}function r(a,b){return a=String(a),Array(b-a.length+1).join(" ")+a}function s(a,b){var c=u(a,"WordsWithSpace",b),d=c.split("\n");if(d.length>4){var e=String(d.length).length;c=d.map(function(a,b){return r(++b,e)+" |"+" "+a}).join("\n")}return c="\n"+p("diff removed","actual")+" "+p("diff added","expected")+"\n\n"+c+"\n",c=c.replace(/^/gm,"      ")}function t(a,b){function d(a){return b&&(a=v(a)),"+"===a[0]?c+w("diff added",a):"-"===a[0]?c+w("diff removed",a):a.match(/\@\@/)?null:a.match(/\\ No newline/)?null:c+a}function e(a){return null!=a}var c="      ";msg=f.createPatch("string",a.actual,a.expected);var g=msg.split("\n").splice(4);return"\n      "+w("diff added","+ expected")+" "+w("diff removed","- actual")+"\n\n"+g.map(d).filter(e).join("\n")}function u(a,b,c){var d=c?v(a.actual):a.actual,e=c?v(a.expected):a.expected;return f["diff"+b](d,e).map(function(a){return a.added?w("diff added",a.value):a.removed?w("diff removed",a.value):a.value}).join("")}function v(a){return a.replace(/\t/g,"<tab>").replace(/\r/g,"<CR>").replace(/\n/g,"<LF>\n")}function w(a,b){return b.split("\n").map(function(b){return p(a,b)}).join("\n")}function x(a,b){return a=Object.prototype.toString.call(a),b=Object.prototype.toString.call(b),a==b}var e=d("browser/tty"),f=d("browser/diff"),g=d("../ms"),i=d("../utils"),j=b.Date;b.setTimeout,b.setInterval,b.clearTimeout,b.clearInterval;var o=e.isatty(1)&&e.isatty(2);c=a.exports=q,c.useColors=o||void 0!==h.env.MOCHA_COLORS,c.inlineDiffs=!1,c.colors={pass:90,fail:31,"bright pass":92,"bright fail":91,"bright yellow":93,pending:36,suite:0,"error title":0,"error message":31,"error stack":90,checkmark:32,fast:90,medium:33,slow:31,green:32,light:90,"diff gutter":90,"diff added":42,"diff removed":41},c.symbols={ok:"\u2713",err:"\u2716",dot:"\u2024"},"win32"==h.platform&&(c.symbols.ok="\u221a",c.symbols.err="\xd7",c.symbols.dot=".");var p=c.color=function(a,b){return c.useColors?"["+c.colors[a]+"m"+b+"[0m":b};c.window={width:o?h.stdout.getWindowSize?h.stdout.getWindowSize(1)[0]:e.getWindowSize()[1]:75},c.cursor={hide:function(){o&&h.stdout.write("[?25l")},show:function(){o&&h.stdout.write("[?25h")},deleteLine:function(){o&&h.stdout.write("[2K")},beginningOfLine:function(){o&&h.stdout.write("[0G")},CR:function(){o?(c.cursor.deleteLine(),c.cursor.beginningOfLine()):h.stdout.write("\r")}},c.list=function(a){console.error(),a.forEach(function(a,b){var d=p("error title","  %s) %s:\n")+p("error message","     %s")+p("error stack","\n%s\n"),e=a.err,f=e.message||"",g=e.stack||f,h=g.indexOf(f)+f.length,j=g.slice(0,h),k=e.actual,l=e.expected,m=!0;if(e.uncaught&&(j="Uncaught "+j),e.showDiff&&x(k,l)&&(m=!1,e.actual=k=i.stringify(k),e.expected=l=i.stringify(l)),e.showDiff&&"string"==typeof k&&"string"==typeof l){d=p("error title","  %s) %s:\n%s")+p("error stack","\n%s\n");var n=f.match(/^([^:]+): expected/);j="\n      "+p("error message",n?n[1]:j),j+=c.inlineDiffs?s(e,m):t(e,m)}g=g.slice(h?h+1:h).replace(/^/gm,"  "),console.error(d,b+1,a.fullTitle(),j,g)})},q.prototype.epilogue=function(){var c,a=this.stats;console.log(),c=p("bright pass"," ")+p("green"," %d passing")+p("light"," (%s)"),console.log(c,a.passes||0,g(a.duration)),a.pending&&(c=p("pending"," ")+p("pending"," %d pending"),console.log(c,a.pending)),a.failures&&(c=p("fail","  %d failing"),console.error(c,a.failures),q.list(this.failures),console.error()),console.log()}}),a.register("reporters/doc.js",function(a,b,c){function f(a){function h(){return Array(g).join("  ")}d.call(this,a);var g=(this.stats,a.total,2);a.on("suite",function(a){a.root||(++g,console.log('%s<section class="suite">',h()),++g,console.log("%s<h1>%s</h1>",h(),e.escape(a.title)),console.log("%s<dl>",h()))}),a.on("suite end",function(a){a.root||(console.log("%s</dl>",h()),--g,console.log("%s</section>",h()),--g)}),a.on("pass",function(a){console.log("%s  <dt>%s</dt>",h(),e.escape(a.title));var b=e.escape(e.clean(a.fn.toString()));console.log("%s  <dd><pre><code>%s</code></pre></dd>",h(),b)}),a.on("fail",function(a,b){console.log('%s  <dt class="error">%s</dt>',h(),e.escape(a.title));var c=e.escape(e.clean(a.fn.toString()));console.log('%s  <dd class="error"><pre><code>%s</code></pre></dd>',h(),c),console.log('%s  <dd class="error">%s</dd>',h(),e.escape(b))})}var d=c("./base"),e=c("../utils");b=a.exports=f}),a.register("reporters/dot.js",function(a,b,c){function f(a){d.call(this,a);var b=this,f=(this.stats,0|.75*d.window.width),g=-1;a.on("start",function(){h.stdout.write("\n  ")}),a.on("pending",function(){0==++g%f&&h.stdout.write("\n  "),h.stdout.write(e("pending",d.symbols.dot))}),a.on("pass",function(a){0==++g%f&&h.stdout.write("\n  "),"slow"==a.speed?h.stdout.write(e("bright yellow",d.symbols.dot)):h.stdout.write(e(a.speed,d.symbols.dot))}),a.on("fail",function(){0==++g%f&&h.stdout.write("\n  "),h.stdout.write(e("fail",d.symbols.dot))}),a.on("end",function(){console.log(),b.epilogue()})}function g(){}var d=c("./base"),e=d.color;b=a.exports=f,g.prototype=d.prototype,f.prototype=new g,f.prototype.constructor=f}),a.register("reporters/html-cov.js",function(a,b,c){function f(a){var b=c("jade"),f=__dirname+"/templates/coverage.jade",i=e.readFileSync(f,"utf8"),j=b.compile(i,{filename:f}),k=this;d.call(this,a,!1),a.on("end",function(){h.stdout.write(j({cov:k.cov,coverageClass:g}))})}function g(a){return a>=75?"high":a>=50?"medium":a>=25?"low":"terrible"}var d=c("./json-cov"),e=c("browser/fs");b=a.exports=f}),a.register("reporters/html.js",function(a,c,d){function o(a){e.call(this,a);var A,B,b=this,c=this.stats,j=(a.total,r(n)),k=j.getElementsByTagName("li"),l=k[1].getElementsByTagName("em")[0],m=k[1].getElementsByTagName("a")[0],o=k[2].getElementsByTagName("em")[0],p=k[2].getElementsByTagName("a")[0],w=k[3].getElementsByTagName("em")[0],x=j.getElementsByTagName("canvas")[0],y=r('<ul id="mocha-report"></ul>'),z=[y],C=document.getElementById("mocha");if(x.getContext){var D=window.devicePixelRatio||1;x.style.width=x.width,x.style.height=x.height,x.width*=D,x.height*=D,B=x.getContext("2d"),B.scale(D,D),A=new g}return C?(v(m,"click",function(){t();var a=/pass/.test(y.className)?"":" pass";y.className=y.className.replace(/fail|pass/g,"")+a,y.className.trim()&&s("test pass")}),v(p,"click",function(){t();var a=/fail/.test(y.className)?"":" fail";y.className=y.className.replace(/fail|pass/g,"")+a,y.className.trim()&&s("test fail")}),C.appendChild(j),C.appendChild(y),A&&A.size(40),a.on("suite",function(a){if(!a.root){var c=b.suiteURL(a),d=r('<li class="suite"><h1><a href="%s">%s</a></h1></li>',c,h(a.title));z[0].appendChild(d),z.unshift(document.createElement("ul")),d.appendChild(z[0])}}),a.on("suite end",function(a){a.root||z.shift()}),a.on("fail",function(b){"hook"==b.type&&a.emit("test end",b)}),a.on("test end",function(a){var d=0|100*(c.tests/this.total);A&&A.update(d).draw(B);var e=new i-c.start;if(u(l,c.passes),u(o,c.failures),u(w,(e/1e3).toFixed(2)),"passed"==a.state)var g=b.testURL(a),h=r('<li class="test pass %e"><h2>%e<span class="duration">%ems</span> <a href="%s" class="replay">\u2023</a></h2></li>',a.speed,a.title,a.duration,g);else if(a.pending)var h=r('<li class="test pass pending"><h2>%e</h2></li>',a.title);else{var h=r('<li class="test fail"><h2>%e <a href="?grep=%e" class="replay">\u2023</a></h2></li>',a.title,encodeURIComponent(a.fullTitle())),j=a.err.stack||a.err.toString();~j.indexOf(a.err.message)||(j=a.err.message+"\n"+j),"[object Error]"==j&&(j=a.err.message),!a.err.stack&&a.err.sourceURL&&void 0!==a.err.line&&(j+="\n("+a.err.sourceURL+":"+a.err.line+")"),h.appendChild(r('<pre class="error">%e</pre>',j))}if(!a.pending){var k=h.getElementsByTagName("h2")[0];v(k,"click",function(){m.style.display="none"==m.style.display?"block":"none"});var m=r("<pre><code>%e</code></pre>",f.clean(a.fn.toString()));h.appendChild(m),m.style.display="none"}z[0]&&z[0].appendChild(h)}),void 0):q("#mocha div missing, add it to your document")}function q(a){document.body.appendChild(r('<div id="mocha-error">%s</div>',a))}function r(a){var b=arguments,c=document.createElement("div"),d=1;return c.innerHTML=a.replace(/%([se])/g,function(a,c){switch(c){case"s":return String(b[d++]);case"e":return h(b[d++])}}),c.firstChild}function s(a){for(var b=document.getElementsByClassName("suite"),c=0;c<b.length;c++){var d=b[c].getElementsByClassName(a);0==d.length&&(b[c].className+=" hidden")}}function t(){for(var a=document.getElementsByClassName("suite hidden"),b=0;b<a.length;++b)a[b].className=a[b].className.replace("suite hidden","suite")}function u(a,b){a.textContent?a.textContent=b:a.innerText=b}function v(a,b,c){a.addEventListener?a.addEventListener(b,c,!1):a.attachEvent("on"+b,c)}var e=d("./base"),f=d("../utils"),g=d("../browser/progress"),h=f.escape,i=b.Date;b.setTimeout,b.setInterval,b.clearTimeout,b.clearInterval,c=a.exports=o;var n='<ul id="mocha-stats"><li class="progress"><canvas width="40" height="40"></canvas></li><li class="passes"><a href="#">passes:</a> <em>0</em></li><li class="failures"><a href="#">failures:</a> <em>0</em></li><li class="duration">duration: <em>0</em>s</li></ul>',p=function(a){var b=window.location.search;return(b?b+"&":"?")+"grep="+encodeURIComponent(a)};o.prototype.suiteURL=function(a){return p(a.fullTitle())},o.prototype.testURL=function(a){return p(a.fullTitle())}}),a.register("reporters/index.js",function(a,b,c){b.Base=c("./base"),b.Dot=c("./dot"),b.Doc=c("./doc"),b.TAP=c("./tap"),b.JSON=c("./json"),b.HTML=c("./html"),b.List=c("./list"),b.Min=c("./min"),b.Spec=c("./spec"),b.Nyan=c("./nyan"),b.XUnit=c("./xunit"),b.Markdown=c("./markdown"),b.Progress=c("./progress"),b.Landing=c("./landing"),b.JSONCov=c("./json-cov"),b.HTMLCov=c("./html-cov"),b.JSONStream=c("./json-stream")}),a.register("reporters/json-cov.js",function(a,c,d){function f(a,c){var d=this,c=1==arguments.length?!0:c;e.call(this,a);var f=[],i=[],k=[];a.on("test end",function(a){f.push(a)}),a.on("pass",function(a){k.push(a)}),a.on("fail",function(a){i.push(a)}),a.on("end",function(){var a=b._$jscoverage||{},e=d.cov=g(a);e.stats=d.stats,e.tests=f.map(j),e.failures=i.map(j),e.passes=k.map(j),c&&h.stdout.write(JSON.stringify(e,null,2))})}function g(a){var b={instrumentation:"node-jscoverage",sloc:0,hits:0,misses:0,coverage:0,files:[]};for(var c in a){var d=i(c,a[c]);b.files.push(d),b.hits+=d.hits,b.misses+=d.misses,b.sloc+=d.sloc}return b.files.sort(function(a,b){return a.filename.localeCompare(b.filename)}),b.sloc>0&&(b.coverage=100*(b.hits/b.sloc)),b}function i(a,b){var c={filename:a,coverage:0,hits:0,misses:0,sloc:0,source:{}};return b.source.forEach(function(a,d){d++,0===b[d]?(c.misses++,c.sloc++):void 0!==b[d]&&(c.hits++,c.sloc++),c.source[d]={source:a,coverage:void 0===b[d]?"":b[d]}}),c.coverage=100*(c.hits/c.sloc),c}function j(a){return{title:a.title,fullTitle:a.fullTitle(),duration:a.duration}}var e=d("./base");c=a.exports=f}),a.register("reporters/json-stream.js",function(a,b,c){function f(a){d.call(this,a);var b=this,e=(this.stats,a.total);a.on("start",function(){console.log(JSON.stringify(["start",{total:e}]))}),a.on("pass",function(a){console.log(JSON.stringify(["pass",g(a)]))}),a.on("fail",function(a,b){a=g(a),a.err=b.message,console.log(JSON.stringify(["fail",a]))}),a.on("end",function(){h.stdout.write(JSON.stringify(["end",b.stats]))})}function g(a){return{title:a.title,fullTitle:a.fullTitle(),duration:a.duration}}var d=c("./base");d.color,b=a.exports=f}),a.register("reporters/json.js",function(a,b,c){function g(a){var b=this;d.call(this,a);var c=[],e=[],f=[],g=[];a.on("test end",function(a){c.push(a)}),a.on("pass",function(a){g.push(a)}),a.on("fail",function(a){f.push(a)}),a.on("pending",function(a){e.push(a)}),a.on("end",function(){var d={stats:b.stats,tests:c.map(i),pending:e.map(i),failures:f.map(i),passes:g.map(i)};a.testResults=d,h.stdout.write(JSON.stringify(d,null,2))})}function i(a){return{title:a.title,fullTitle:a.fullTitle(),duration:a.duration,err:j(a.err||{})}}function j(a){var b={};return Object.getOwnPropertyNames(a).forEach(function(c){b[c]=a[c]},a),b}var d=c("./base");d.cursor,d.color,b=a.exports=g}),a.register("reporters/landing.js",function(a,b,c){function g(a){function n(){var a=Array(g).join("-");return"  "+f("runway",a)}d.call(this,a);var b=this,g=(this.stats,0|.75*d.window.width),i=a.total,j=h.stdout,k=f("plane","\u2708"),l=-1,m=0;a.on("start",function(){j.write("\n\n\n  "),e.hide()}),a.on("test end",function(a){var b=-1==l?0|g*++m/i:l;"failed"==a.state&&(k=f("plane crash","\u2708"),l=b),j.write("["+(g+1)+"D[2A"),j.write(n()),j.write("\n  "),j.write(f("runway",Array(b).join("\u22c5"))),j.write(k),j.write(f("runway",Array(g-b).join("\u22c5")+"\n")),j.write(n()),j.write("[0m")}),a.on("end",function(){e.show(),console.log(),b.epilogue()})}function i(){}var d=c("./base"),e=d.cursor,f=d.color;b=a.exports=g,d.colors.plane=0,d.colors["plane crash"]=31,d.colors.runway=90,i.prototype=d.prototype,g.prototype=new i,g.prototype.constructor=g}),a.register("reporters/list.js",function(a,b,c){function g(a){d.call(this,a);var b=this,g=(this.stats,0);a.on("start",function(){console.log()}),a.on("test",function(a){h.stdout.write(f("pass","    "+a.fullTitle()+": "))}),a.on("pending",function(a){var b=f("checkmark","  -")+f("pending"," %s");console.log(b,a.fullTitle())}),a.on("pass",function(a){var b=f("checkmark","  "+d.symbols.dot)+f("pass"," %s: ")+f(a.speed,"%dms");e.CR(),console.log(b,a.fullTitle(),a.duration)}),a.on("fail",function(a){e.CR(),console.log(f("fail","  %d) %s"),++g,a.fullTitle())}),a.on("end",b.epilogue.bind(b))}function i(){}var d=c("./base"),e=d.cursor,f=d.color;b=a.exports=g,i.prototype=d.prototype,g.prototype=new i,g.prototype.constructor=g}),a.register("reporters/markdown.js",function(a,b,c){function f(a){function i(a){return Array(f).join("#")+" "+a}function k(a,b){var c=b;return b=b[a.title]=b[a.title]||{suite:a},a.suites.forEach(function(a){k(a,b)}),c}function l(a,b){++b;var d,c="";for(var f in a)"suite"!=f&&(f&&(d=" - ["+f+"](#"+e.slug(a[f].suite.fullTitle())+")\n"),f&&(c+=Array(b).join("  ")+d),c+=l(a[f],b));return--b,c}function m(a){var b=k(a,{});return l(b,0)}d.call(this,a);var f=(this.stats,0),g="";m(a.suite),a.on("suite",function(a){++f;var b=e.slug(a.fullTitle());g+='<a name="'+b+'"></a>'+"\n",g+=i(a.title)+"\n"}),a.on("suite end",function(){--f}),a.on("pass",function(a){var b=e.clean(a.fn.toString());
g+=a.title+".\n",g+="\n```js\n",g+=b+"\n",g+="```\n\n"}),a.on("end",function(){h.stdout.write("# TOC\n"),h.stdout.write(m(a.suite)),h.stdout.write(g)})}var d=c("./base"),e=c("../utils");b=a.exports=f}),a.register("reporters/min.js",function(a,b,c){function e(a){d.call(this,a),a.on("start",function(){h.stdout.write("[2J"),h.stdout.write("[1;3H")}),a.on("end",this.epilogue.bind(this))}function f(){}var d=c("./base");b=a.exports=e,f.prototype=d.prototype,e.prototype=new f,e.prototype.constructor=e}),a.register("reporters/nyan.js",function(a,b,c){function f(a){d.call(this,a);var b=this,e=(this.stats,0|.75*d.window.width),k=(this.rainbowColors=b.generateColors(),this.colorIndex=0,this.numberOfLines=4,this.trajectories=[[],[],[],[]],this.nyanCatWidth=11);this.trajectoryWidthMax=e-k,this.scoreboardWidth=5,this.tick=0,a.on("start",function(){d.cursor.hide(),b.draw()}),a.on("pending",function(){b.draw()}),a.on("pass",function(){b.draw()}),a.on("fail",function(){b.draw()}),a.on("end",function(){d.cursor.show();for(var a=0;a<b.numberOfLines;a++)g("\n");b.epilogue()})}function g(a){h.stdout.write(a)}function i(){}var d=c("./base");d.color,b=a.exports=f,f.prototype.draw=function(){this.appendRainbow(),this.drawScoreboard(),this.drawRainbow(),this.drawNyanCat(),this.tick=!this.tick},f.prototype.drawScoreboard=function(){function c(a,b){g(" "),g("["+a+"m"+b+"[0m"),g("\n")}var a=this.stats,b=d.colors;c(b.green,a.passes),c(b.fail,a.failures),c(b.pending,a.pending),g("\n"),this.cursorUp(this.numberOfLines)},f.prototype.appendRainbow=function(){for(var a=this.tick?"_":"-",b=this.rainbowify(a),c=0;c<this.numberOfLines;c++){var d=this.trajectories[c];d.length>=this.trajectoryWidthMax&&d.shift(),d.push(b)}},f.prototype.drawRainbow=function(){var a=this;this.trajectories.forEach(function(b){g("["+a.scoreboardWidth+"C"),g(b.join("")),g("\n")}),this.cursorUp(this.numberOfLines)},f.prototype.drawNyanCat=function(){var a=this,b=this.scoreboardWidth+this.trajectories[0].length,c="["+b+"C",d="";g(c),g("_,------,"),g("\n"),g(c),d=a.tick?"  ":"   ",g("_|"+d+"/\\_/\\ "),g("\n"),g(c),d=a.tick?"_":"__";var e=a.tick?"~":"^";g(e+"|"+d+this.face()+" "),g("\n"),g(c),d=a.tick?" ":"  ",g(d+'""  "" '),g("\n"),this.cursorUp(this.numberOfLines)},f.prototype.face=function(){var a=this.stats;return a.failures?"( x .x)":a.pending?"( o .o)":a.passes?"( ^ .^)":"( - .-)"},f.prototype.cursorUp=function(a){g("["+a+"A")},f.prototype.cursorDown=function(a){g("["+a+"B")},f.prototype.generateColors=function(){for(var a=[],b=0;42>b;b++){var c=Math.floor(Math.PI/3),d=b*(1/6),e=Math.floor(3*Math.sin(d)+3),f=Math.floor(3*Math.sin(d+2*c)+3),g=Math.floor(3*Math.sin(d+4*c)+3);a.push(36*e+6*f+g+16)}return a},f.prototype.rainbowify=function(a){var b=this.rainbowColors[this.colorIndex%this.rainbowColors.length];return this.colorIndex+=1,"[38;5;"+b+"m"+a+"[0m"},i.prototype=d.prototype,f.prototype=new i,f.prototype.constructor=f}),a.register("reporters/progress.js",function(a,b,c){function g(a,b){d.call(this,a);var c=this,b=b||{},i=(this.stats,0|.5*d.window.width),j=a.total,k=0,m=(Math.max,-1);b.open=b.open||"[",b.complete=b.complete||"\u25ac",b.incomplete=b.incomplete||d.symbols.dot,b.close=b.close||"]",b.verbose=!1,a.on("start",function(){console.log(),e.hide()}),a.on("test end",function(){k++;var c=k/j,d=0|i*c,g=i-d;(m!==d||b.verbose)&&(m=d,e.CR(),h.stdout.write("[J"),h.stdout.write(f("progress","  "+b.open)),h.stdout.write(Array(d).join(b.complete)),h.stdout.write(Array(g).join(b.incomplete)),h.stdout.write(f("progress",b.close)),b.verbose&&h.stdout.write(f("progress"," "+k+" of "+j)))}),a.on("end",function(){e.show(),console.log(),c.epilogue()})}function i(){}var d=c("./base"),e=d.cursor,f=d.color;b=a.exports=g,d.colors.progress=90,i.prototype=d.prototype,g.prototype=new i,g.prototype.constructor=g}),a.register("reporters/spec.js",function(a,b,c){function g(a){function i(){return Array(g).join("  ")}d.call(this,a);var b=this,g=(this.stats,0),h=0;a.on("start",function(){console.log()}),a.on("suite",function(a){++g,console.log(f("suite","%s%s"),i(),a.title)}),a.on("suite end",function(){--g,1==g&&console.log()}),a.on("pending",function(a){var b=i()+f("pending","  - %s");console.log(b,a.title)}),a.on("pass",function(a){if("fast"==a.speed){var b=i()+f("checkmark","  "+d.symbols.ok)+f("pass"," %s ");e.CR(),console.log(b,a.title)}else{var b=i()+f("checkmark","  "+d.symbols.ok)+f("pass"," %s ")+f(a.speed,"(%dms)");e.CR(),console.log(b,a.title,a.duration)}}),a.on("fail",function(a){e.CR(),console.log(i()+f("fail","  %d) %s"),++h,a.title)}),a.on("end",b.epilogue.bind(b))}function h(){}var d=c("./base"),e=d.cursor,f=d.color;b=a.exports=g,h.prototype=d.prototype,g.prototype=new h,g.prototype.constructor=g}),a.register("reporters/tap.js",function(a,b,c){function g(a){d.call(this,a);var e=(this.stats,1),f=0,g=0;a.on("start",function(){var b=a.grepTotal(a.suite);console.log("%d..%d",1,b)}),a.on("test end",function(){++e}),a.on("pending",function(a){console.log("ok %d %s # SKIP -",e,h(a))}),a.on("pass",function(a){f++,console.log("ok %d %s",e,h(a))}),a.on("fail",function(a,b){g++,console.log("not ok %d %s",e,h(a)),b.stack&&console.log(b.stack.replace(/^/gm,"  "))}),a.on("end",function(){console.log("# tests "+(f+g)),console.log("# pass "+f),console.log("# fail "+g)})}function h(a){return a.fullTitle().replace(/#/g,"")}var d=c("./base");d.cursor,d.color,b=a.exports=g}),a.register("reporters/xunit.js",function(a,c,d){function m(a){e.call(this,a);var b=this.stats,c=[];a.on("pending",function(a){c.push(a)}),a.on("pass",function(a){c.push(a)}),a.on("fail",function(a){c.push(a)}),a.on("end",function(){console.log(p("testsuite",{name:"Mocha Tests",tests:b.tests,failures:b.failures,errors:b.failures,skipped:b.tests-b.failures-b.passes,timestamp:(new h).toUTCString(),time:b.duration/1e3||0},!1)),c.forEach(o),console.log("</testsuite>")})}function n(){}function o(a){var b={classname:a.parent.fullTitle(),name:a.title,time:a.duration/1e3||0};if("failed"==a.state){var c=a.err;console.log(p("testcase",b,!1,p("failure",{},!1,q(g(c.message)+"\n"+c.stack))))}else a.pending?console.log(p("testcase",b,!1,p("skipped",{},!0))):console.log(p("testcase",b,!0))}function p(a,b,c,d){var h,e=c?"/>":">",f=[];for(var i in b)f.push(i+'="'+g(b[i])+'"');return h="<"+a+(f.length?" "+f.join(" "):"")+e,d&&(h+=d+"</"+a+e),h}function q(a){return"<![CDATA["+g(a)+"]]>"}var e=d("./base"),f=d("../utils"),g=f.escape,h=b.Date;b.setTimeout,b.setInterval,b.clearTimeout,b.clearInterval,c=a.exports=m,n.prototype=e.prototype,m.prototype=new n,m.prototype.constructor=m}),a.register("runnable.js",function(a,c,d){function n(a,b){this.title=a,this.fn=b,this.async=b&&b.length,this.sync=!this.async,this._timeout=2e3,this._slow=75,this._enableTimeouts=!0,this.timedOut=!1,this._trace=new Error("done() called multiple times")}function o(){}var e=d("browser/events").EventEmitter,f=d("browser/debug")("mocha:runnable"),g=d("./ms"),h=b.Date,i=b.setTimeout,k=(b.setInterval,b.clearTimeout);b.clearInterval;var m=Object.prototype.toString;a.exports=n,o.prototype=e.prototype,n.prototype=new o,n.prototype.constructor=n,n.prototype.timeout=function(a){return 0==arguments.length?this._timeout:(0===a&&(this._enableTimeouts=!1),"string"==typeof a&&(a=g(a)),f("timeout %d",a),this._timeout=a,this.timer&&this.resetTimeout(),this)},n.prototype.slow=function(a){return 0===arguments.length?this._slow:("string"==typeof a&&(a=g(a)),f("timeout %d",a),this._slow=a,this)},n.prototype.enableTimeouts=function(a){return 0===arguments.length?this._enableTimeouts:(f("enableTimeouts %s",a),this._enableTimeouts=a,this)},n.prototype.fullTitle=function(){return this.parent.fullTitle()+" "+this.title},n.prototype.clearTimeout=function(){k(this.timer)},n.prototype.inspect=function(){return JSON.stringify(this,function(a,b){return"_"!=a[0]?"parent"==a?"#<Suite>":"ctx"==a?"#<Context>":b:void 0},2)},n.prototype.resetTimeout=function(){var a=this,b=this.timeout()||1e9;this._enableTimeouts&&(this.clearTimeout(),this.timer=i(function(){a._enableTimeouts&&(a.callback(new Error("timeout of "+b+"ms exceeded")),a.timedOut=!0)},b))},n.prototype.globals=function(a){this._allowedGlobals=a},n.prototype.run=function(a){function g(a){f||(f=!0,b.emit("error",a||new Error("done() called multiple times; stacktrace may be inaccurate")))}function i(d){var f=b.timeout();if(!b.timedOut){if(e)return g(d||b._trace);b.clearTimeout(),b.duration=new h-c,e=!0,!d&&b.duration>f&&b._enableTimeouts&&(d=new Error("timeout of "+f+"ms exceeded")),a(d)}}function k(a){var c=a.call(d);c&&"function"==typeof c.then?(b.resetTimeout(),c.then(function(){i()},function(a){i(a||new Error("Promise rejected with no or falsy reason"))})):i()}var e,f,b=this,c=new h,d=this.ctx;if(d&&d.runnable&&d.runnable(this),this.callback=i,this.async){this.resetTimeout();try{this.fn.call(d,function(a){return a instanceof Error||"[object Error]"===m.call(a)?i(a):null!=a?"[object Object]"===Object.prototype.toString.call(a)?i(new Error("done() invoked with non-Error: "+JSON.stringify(a))):i(new Error("done() invoked with non-Error: "+a)):(i(),void 0)})}catch(j){i(j)}}else{if(this.asyncOnly)return i(new Error("--async-only option in use without declaring `done()`"));try{this.pending?i():k(this.fn)}catch(j){i(j)}}}}),a.register("runner.js",function(a,c,d){function m(a){var b=this;this._globals=[],this._abort=!1,this.suite=a,this.total=a.total(),this.failures=0,this.on("test end",function(a){b.checkGlobals(a)}),this.on("hook end",function(a){b.checkGlobals(a)}),this.grep(/.*/),this.globals(this.globalProps().concat(p()))}function n(){}function o(a,c){return j(c,function(c){if(/^d+/.test(c))return!1;if(b.navigator&&/^getInterface/.test(c))return!1;if(b.navigator&&/^\d+/.test(c))return!1;if(/^mocha-/.test(c))return!1;var d=j(a,function(a){return~a.indexOf("*")?0==c.indexOf(a.split("*")[0]):c==a});return 0==d.length&&(!b.navigator||"onerror"!==c)})}function p(){if("object"==typeof h&&"string"==typeof h.version){var a=h.version.split(".").reduce(function(a,b){return a<<8|b});if(2315>a)return["errno"]}return[]}var e=d("browser/events").EventEmitter,f=d("browser/debug")("mocha:runner"),i=(d("./test"),d("./utils")),j=i.filter;i.keys;var l=["setTimeout","clearTimeout","setInterval","clearInterval","XMLHttpRequest","Date"];a.exports=m,m.immediately=b.setImmediate||h.nextTick,n.prototype=e.prototype,m.prototype=new n,m.prototype.constructor=m,m.prototype.grep=function(a,b){return f("grep %s",a),this._grep=a,this._invert=b,this.total=this.grepTotal(this.suite),this},m.prototype.grepTotal=function(a){var b=this,c=0;return a.eachTest(function(a){var d=b._grep.test(a.fullTitle());b._invert&&(d=!d),d&&c++}),c},m.prototype.globalProps=function(){for(var a=i.keys(b),c=0;c<l.length;++c)~i.indexOf(a,l[c])||a.push(l[c]);return a},m.prototype.globals=function(a){return 0==arguments.length?this._globals:(f("globals %j",a),this._globals=this._globals.concat(a),this)},m.prototype.checkGlobals=function(a){if(!this.ignoreLeaks){var d,b=this._globals,c=this.globalProps();a&&(b=b.concat(a._allowedGlobals||[])),this.prevGlobalsLength!=c.length&&(this.prevGlobalsLength=c.length,d=o(b,c),this._globals=this._globals.concat(d),d.length>1?this.fail(a,new Error("global leaks detected: "+d.join(", "))):d.length&&this.fail(a,new Error("global leak detected: "+d[0])))}},m.prototype.fail=function(a,b){++this.failures,a.state="failed","string"==typeof b&&(b=new Error('the string "'+b+'" was thrown, throw an Error :)')),this.emit("fail",a,b)},m.prototype.failHook=function(a,b){this.fail(a,b),this.suite.bail()&&this.emit("end")},m.prototype.hook=function(a,b){function g(a){var f=d[a];return f?e.failures&&c.bail()?b():(e.currentRunnable=f,f.ctx.currentTest=e.test,e.emit("hook",f),f.on("error",function(a){e.failHook(f,a)}),f.run(function(c){f.removeAllListeners("error");var d=f.error();return d&&e.fail(e.test,d),c?(e.failHook(f,c),b(c)):(e.emit("hook end",f),delete f.ctx.currentTest,g(++a),void 0)}),void 0):b()}var c=this.suite,d=c["_"+a],e=this;m.immediately(function(){g(0)})},m.prototype.hooks=function(a,b,c){function f(g){return d.suite=g,g?(d.hook(a,function(a){if(a){var g=d.suite;return d.suite=e,c(a,g)}f(b.pop())}),void 0):(d.suite=e,c())}var d=this,e=this.suite;f(b.pop())},m.prototype.hookUp=function(a,b){var c=[this.suite].concat(this.parents()).reverse();this.hooks(a,c,b)},m.prototype.hookDown=function(a,b){var c=[this.suite].concat(this.parents());this.hooks(a,c,b)},m.prototype.parents=function(){for(var a=this.suite,b=[];a=a.parent;)b.push(a);return b},m.prototype.runTest=function(a){var b=this.test,c=this;this.asyncOnly&&(b.asyncOnly=!0);try{b.on("error",function(a){c.fail(b,a)}),b.run(a)}catch(d){a(d)}},m.prototype.runTests=function(a,b){function f(a,d,e){var g=c.suite;c.suite=e?d.parent:d,c.suite?c.hookUp("afterEach",function(a,e){return c.suite=g,a?f(a,e,!0):(b(d),void 0)}):(c.suite=g,b(d))}function g(h,i){if(c.failures&&a._bail)return b();if(c._abort)return b();if(h)return f(h,i,!0);if(e=d.shift(),!e)return b();var j=c._grep.test(e.fullTitle());return c._invert&&(j=!j),j?e.pending?(c.emit("pending",e),c.emit("test end",e),g()):(c.emit("test",c.test=e),c.hookDown("beforeEach",function(a,b){return a?f(a,b,!1):(c.currentRunnable=c.test,c.runTest(function(a){return e=c.test,a?(c.fail(e,a),c.emit("test end",e),c.hookUp("afterEach",g)):(e.state="passed",c.emit("pass",e),c.emit("test end",e),c.hookUp("afterEach",g),void 0)}),void 0)}),void 0):g()}var e,c=this,d=a.tests.slice();this.next=g,g()},m.prototype.runSuite=function(a,b){function g(b){if(b)return b==a?h():h(b);if(d._abort)return h();var c=a.suites[e++];return c?(d.runSuite(c,g),void 0):h()}function h(c){d.suite=a,d.hook("afterAll",function(){d.emit("suite end",a),b(c)})}var c=this.grepTotal(a),d=this,e=0;return f("run suite %s",a.fullTitle()),c?(this.emit("suite",this.suite=a),this.hook("beforeAll",function(b){return b?h():(d.runTests(a,g),void 0)}),void 0):b()},m.prototype.uncaught=function(a){a?f("uncaught exception %s",a!==function(){return this}.call(a)?a:a.message||a):(f("uncaught undefined exception"),a=new Error("Caught undefined error, did you throw without specifying what?")),a.uncaught=!0;var b=this.currentRunnable;if(b){var c=b.state;if(this.fail(b,a),b.clearTimeout(),!c)return"test"==b.type?(this.emit("test end",b),this.hookUp("afterEach",this.next),void 0):(this.emit("end"),void 0)}},m.prototype.run=function(a){function c(a){b.uncaught(a)}var b=this,a=a||function(){};return f("start"),this.on("end",function(){f("end"),h.removeListener("uncaughtException",c),a(b.failures)}),this.emit("start"),this.runSuite(this.suite,function(){f("finished running"),b.emit("end")}),h.on("uncaughtException",c),this},m.prototype.abort=function(){f("aborting"),this._abort=!0}}),a.register("suite.js",function(a,b,c){function i(a,b){this.title=a;var c=function(){};c.prototype=b,this.ctx=new c,this.suites=[],this.tests=[],this.pending=!1,this._beforeEach=[],this._beforeAll=[],this._afterEach=[],this._afterAll=[],this.root=!a,this._timeout=2e3,this._enableTimeouts=!0,this._slow=75,this._bail=!1}function j(){}var d=c("browser/events").EventEmitter,e=c("browser/debug")("mocha:suite"),f=c("./ms"),g=c("./utils"),h=c("./hook");b=a.exports=i,b.create=function(a,b){var c=new i(b,a.ctx);return c.parent=a,a.pending&&(c.pending=!0),b=c.fullTitle(),a.addSuite(c),c},j.prototype=d.prototype,i.prototype=new j,i.prototype.constructor=i,i.prototype.clone=function(){var a=new i(this.title);return e("clone"),a.ctx=this.ctx,a.timeout(this.timeout()),a.enableTimeouts(this.enableTimeouts()),a.slow(this.slow()),a.bail(this.bail()),a},i.prototype.timeout=function(a){return 0==arguments.length?this._timeout:(0===a&&(this._enableTimeouts=!1),"string"==typeof a&&(a=f(a)),e("timeout %d",a),this._timeout=parseInt(a,10),this)},i.prototype.enableTimeouts=function(a){return 0===arguments.length?this._enableTimeouts:(e("enableTimeouts %s",a),this._enableTimeouts=a,this)},i.prototype.slow=function(a){return 0===arguments.length?this._slow:("string"==typeof a&&(a=f(a)),e("slow %d",a),this._slow=a,this)},i.prototype.bail=function(a){return 0==arguments.length?this._bail:(e("bail %s",a),this._bail=a,this)},i.prototype.beforeAll=function(a,b){if(this.pending)return this;"function"==typeof a&&(b=a,a=b.name),a='"before all" hook'+(a?": "+a:"");var c=new h(a,b);return c.parent=this,c.timeout(this.timeout()),c.enableTimeouts(this.enableTimeouts()),c.slow(this.slow()),c.ctx=this.ctx,this._beforeAll.push(c),this.emit("beforeAll",c),this},i.prototype.afterAll=function(a,b){if(this.pending)return this;"function"==typeof a&&(b=a,a=b.name),a='"after all" hook'+(a?": "+a:"");var c=new h(a,b);return c.parent=this,c.timeout(this.timeout()),c.enableTimeouts(this.enableTimeouts()),c.slow(this.slow()),c.ctx=this.ctx,this._afterAll.push(c),this.emit("afterAll",c),this},i.prototype.beforeEach=function(a,b){if(this.pending)return this;"function"==typeof a&&(b=a,a=b.name),a='"before each" hook'+(a?": "+a:"");var c=new h(a,b);return c.parent=this,c.timeout(this.timeout()),c.enableTimeouts(this.enableTimeouts()),c.slow(this.slow()),c.ctx=this.ctx,this._beforeEach.push(c),this.emit("beforeEach",c),this},i.prototype.afterEach=function(a,b){if(this.pending)return this;"function"==typeof a&&(b=a,a=b.name),a='"after each" hook'+(a?": "+a:"");var c=new h(a,b);return c.parent=this,c.timeout(this.timeout()),c.enableTimeouts(this.enableTimeouts()),c.slow(this.slow()),c.ctx=this.ctx,this._afterEach.push(c),this.emit("afterEach",c),this},i.prototype.addSuite=function(a){return a.parent=this,a.timeout(this.timeout()),a.enableTimeouts(this.enableTimeouts()),a.slow(this.slow()),a.bail(this.bail()),this.suites.push(a),this.emit("suite",a),this},i.prototype.addTest=function(a){return a.parent=this,a.timeout(this.timeout()),a.enableTimeouts(this.enableTimeouts()),a.slow(this.slow()),a.ctx=this.ctx,this.tests.push(a),this.emit("test",a),this},i.prototype.fullTitle=function(){if(this.parent){var a=this.parent.fullTitle();if(a)return a+" "+this.title}return this.title},i.prototype.total=function(){return g.reduce(this.suites,function(a,b){return a+b.total()},0)+this.tests.length},i.prototype.eachTest=function(a){return g.forEach(this.tests,a),g.forEach(this.suites,function(b){b.eachTest(a)}),this}}),a.register("test.js",function(a,b,c){function e(a,b){d.call(this,a,b),this.pending=!b,this.type="test"}function f(){}var d=c("./runnable");a.exports=e,f.prototype=d.prototype,e.prototype=new f,e.prototype.constructor=e}),a.register("utils.js",function(a,b,c){function l(a){return!~k.indexOf(a)}function m(a){return a.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\/\/(.*)/gm,'<span class="comment">//$1</span>').replace(/('.*?')/gm,'<span class="string">$1</span>').replace(/(\d+\.\d+)/gm,'<span class="number">$1</span>').replace(/(\d+)/gm,'<span class="number">$1</span>').replace(/\bnew[ \t]+(\w+)/gm,'<span class="keyword">new</span> <span class="init">$1</span>').replace(/\b(function|new|throw|return|var|if|else)\b/gm,'<span class="keyword">$1</span>')}var d=c("browser/fs"),e=c("browser/path"),f=e.basename,g=d.existsSync||e.existsSync,h=c("browser/glob"),i=e.join,j=c("browser/debug")("mocha:watch"),k=["node_modules",".git"];b.escape=function(a){return String(a).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/</g,"&lt;").replace(/>/g,"&gt;")},b.forEach=function(a,b,c){for(var d=0,e=a.length;e>d;d++)b.call(c,a[d],d)},b.map=function(a,b,c){for(var d=[],e=0,f=a.length;f>e;e++)d.push(b.call(c,a[e],e));return d},b.indexOf=function(a,b,c){for(var d=c||0,e=a.length;e>d;d++)if(a[d]===b)return d;return-1},b.reduce=function(a,b,c){for(var d=c,e=0,f=a.length;f>e;e++)d=b(d,a[e],e,a);return d},b.filter=function(a,b){for(var c=[],d=0,e=a.length;e>d;d++){var f=a[d];b(f,d,a)&&c.push(f)}return c},b.keys=Object.keys||function(a){var b=[],c=Object.prototype.hasOwnProperty;for(var d in a)c.call(a,d)&&b.push(d);return b},b.watch=function(a,b){var c={interval:100};a.forEach(function(a){j("file %s",a),d.watchFile(a,c,function(c,d){d.mtime<c.mtime&&b(a)})})},b.files=function(a,c,e){e=e||[],c=c||["js"];var f=new RegExp("\\.("+c.join("|")+")$");return d.readdirSync(a).filter(l).forEach(function(g){g=i(a,g),d.statSync(g).isDirectory()?b.files(g,c,e):g.match(f)&&e.push(g)}),e},b.slug=function(a){return a.toLowerCase().replace(/ +/g,"-").replace(/[^-\w]/g,"")},b.clean=function(a){a=a.replace(/\r\n?|[\n\u2028\u2029]/g,"\n").replace(/^\uFEFF/,"").replace(/^function *\(.*\) *{|\(.*\) *=> *{?/,"").replace(/\s+\}$/,"");var c=a.match(/^\n?( *)/)[1].length,d=a.match(/^\n?(\t*)/)[1].length,e=new RegExp("^\n?"+(d?"	":" ")+"{"+(d?d:c)+"}","gm");return a=a.replace(e,""),b.trim(a)},b.trim=function(a){return a.replace(/^\s+|\s+$/g,"")},b.parseQuery=function(a){return b.reduce(a.replace("?","").split("&"),function(a,b){var c=b.indexOf("="),d=b.slice(0,c),e=b.slice(++c);return a[d]=decodeURIComponent(e),a},{})},b.highlightTags=function(a){for(var b=document.getElementById("mocha").getElementsByTagName(a),c=0,d=b.length;d>c;++c)b[c].innerHTML=m(b[c].innerHTML)},b.stringify=function(a){return a instanceof RegExp?a.toString():JSON.stringify(b.canonicalize(a),null,2).replace(/,(\n|$)/g,"$1")},b.canonicalize=function(a,c){if(c=c||[],-1!==b.indexOf(c,a))return"[Circular]";var d;return"[object Array]"==={}.toString.call(a)?(c.push(a),d=b.map(a,function(a){return b.canonicalize(a,c)}),c.pop()):"object"==typeof a&&null!==a?(c.push(a),d={},b.forEach(b.keys(a).sort(),function(e){d[e]=b.canonicalize(a[e],c)}),c.pop()):d=a,d},b.lookupFiles=function n(a,b,c){var e=[],j=new RegExp("\\.("+b.join("|")+")$");if(!g(a)){if(!g(a+".js")){if(e=h.sync(a),!e.length)throw new Error("cannot resolve path (or pattern) '"+a+"'");return e}a+=".js"}try{var k=d.statSync(a);if(k.isFile())return a}catch(l){return}return d.readdirSync(a).forEach(function(g){g=i(a,g);try{var h=d.statSync(g);if(h.isDirectory())return c&&(e=e.concat(n(g,b,c))),void 0}catch(k){return}h.isFile()&&j.test(g)&&"."!==f(g)[0]&&e.push(g)}),e}});var b=function(){return this}(),c=b.Date,d=b.setTimeout;b.setInterval,b.clearTimeout,b.clearInterval;var h={};h.exit=function(){},h.stdout={};var i=[],j=b.onerror;h.removeListener=function(a,c){if("uncaughtException"==a){b.onerror=j?j:function(){};var d=k.utils.indexOf(i,c);-1!=d&&i.splice(d,1)}},h.on=function(a,c){"uncaughtException"==a&&(b.onerror=function(a,b,d){return c(new Error(a+" ("+b+":"+d+")")),!0},i.push(c))};var k=b.Mocha=a("mocha"),l=b.mocha=new k({reporter:"html"});l.suite.removeAllListeners("pre-require");var n,m=[];k.Runner.immediately=function(a){m.push(a),n||(n=d(o,0))},l.throwError=function(a){throw k.utils.forEach(i,function(b){b(a)}),a},l.ui=function(a){return k.prototype.ui.call(this,a),this.suite.emit("pre-require",b,null,this),this},l.setup=function(a){"string"==typeof a&&(a={ui:a});for(var b in a)this[b](a[b]);return this},l.run=function(a){var c=l.options;l.globals("location");var d=k.utils.parseQuery(b.location.search||"");return d.grep&&l.grep(d.grep),d.invert&&l.invert(),k.prototype.run.call(l,function(d){var e=b.document;e&&e.getElementById("mocha")&&c.noHighlighting!==!0&&k.utils.highlightTags("code"),a&&a(d)})},k.process=h}();

mocha.ui('bdd');
mocha.reporter(~location.search.indexOf('console') ? 'tap' : 'html');