@charset "utf-8";
/**
* @name         :shop.css
* @date         :2016-03-22
* @updateTime   :()
* <AUTHOR>
* @version      :电商前台平台1.0
* @type         :
* @explain      :
* @relating     :
* @dependent    :
**/
@font-face {font-family: "iconfont";
  src:url('../fonts/iconfont.eot'); /* IE9*/
  src: url('../fonts/iconfont.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('../fonts/iconfont.woff') format('woff'), /* chrome, firefox */
  url('../fonts/iconfont.ttf') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
  url('../fonts/iconfont.svg#iconfont') format('svg'); /* iOS 4.1- */
}

.iconfont {
    font-family:"iconfont" !important;
    font-size:16px;font-style:normal;
    -webkit-font-smoothing: antialiased;
    -webkit-text-stroke-width: 0px;
    -moz-osx-font-smoothing: grayscale;
}
.icon-gerenzhongxin:before { content: "\e600"; }
.icon-weibiaoti2:before { content: "\e609"; }
.icon-yuan:before { content: "\e615"; }
.icon-gb:before { content: "\e621"; }
.icon-mdbaogao:before { content: "\e610"; }
.icon-weigouxuan:before { content: "\e616"; }
.icon-wul:before { content: "\e60f"; }
.icon-tishi:before { content: "\e619"; }
.icon-mujh:before { content: "\e61f"; }
.icon-cha:before { content: "\e61b"; }
.icon-gerenziliao:before { content: "\e60a"; }
.icon-gene:before { content: "\e611"; }
.icon-dianhua:before { content: "\e605"; }
.icon-wodedingdan:before { content: "\e601"; }
.icon-time:before { content: "\e61e"; }
.icon-updown:before { content: "\e623"; }
.icon-down:before { content: "\e624"; }
.icon-youxiangdizhi:before { content: "\e602"; }
.icon-dingdan:before { content: "\e613"; }
.icon-arrowleft:before { content: "\e617"; }
.icon-arrowright:before { content: "\e618"; }
.icon-jibenxinxi:before { content: "\e60b"; }
.icon-banjiang:before { content: "\e614"; }
.icon-bianji:before { content: "\e60c"; }
.icon-liebiao:before { content: "\e61a"; }
.icon-lb:before { content: "\e622"; }
.icon-enable:before { content: "\e61d"; }
.icon-jiantou-copy:before { content: "\e604"; }
.icon-lib:before { content: "\e60d"; }
.icon-xiehezhijianbiaoqian:before { content: "\e612"; }
.icon-sanshuxian:before { content: "\e61c"; }
.icon-mubj:before { content: "\e620"; }


/*----导航css样式-----*/
.publicw{width:1280px; margin:0 auto; *zoom: 1;}
.publicw:before, .publicw:after { visibility:hidden; display:block; font-size:0; content:" "; clear:both; height:0; }
.sp-nav-bigbox{ width:100%;height:79px; border-bottom: 1px solid #d7d7d7;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3); background:#fff; min-width:1260px; z-index:99;position:fixed; opacity:0.9;top:0px;}
.sp-navigation-box{ height:78px; line-height:78px;}
.sp-logo{ width:200px; height:78px; margin-left:20px; display:table;}
.sp-logo-img{ display:table-cell; vertical-align:middle;}
.sp-nav-ul>li{ display:inline-block; min-width:100px; height:78px; line-height:78px; font-size:16px; text-align:center; cursor:pointer; position:relative; *display:inline;*zoom:1;}
.sp-nav-ul>li.on a{ color:#fc6b4c;}
.sp-login{ width:127px; height:40px; line-height:40px; border:1px solid #d9d9d9; margin:21px 0; border-radius:40px;}
.sp-login-p a{ min-width:52px; display:inline-block; font-size:14px; color:#9b9b9b;}
.sp-login-p a.sp-login-atwo{ width:125px;font-size:14px; color:#9b9b9b; text-align:center;}
.sp-login-p a.sp-login-atow{ width: 52px; display: inline-block; font-size:14px;color: #9b9b9b;}

.sp-login-p a:hover{ color:#fb6b4a;}
.sp-login-afirst{ text-align:right}
.sp-login-ateo{ text-align:right;}
.sp-login-i{ display:inline-block; border-left:1px solid #ccc;width:1px;height:16px;margin:-3px 6px;margin:-3px 6px\9;}
.sp-navbox{ position:relative;}
.sp-postion-span{width:92%; height:4px; border-radius:4px; background:#fc6b4c; position:absolute; bottom:4px; left:8px;}
.sp-innerbigbox{ position:relative;min-width:1260px; margin:78px auto 0; overflow:hidden;}
/*.sp-innerbigbox{ position:relative;top:78px; min-width:1260px;}*/


/*录播图片css*/
.sp-shuffling-bigbox{ width:100%; height:560px; overflow:hidden;}
.sp-shuffling-bigbox img{}

.sp-lefttext-rightimg,.sp-leftimg-righttext{ width:100%;  min-height:500px; background:#fff; min-width:1260px;}
.sp-leftimg-righttext{}
.sp-text-box{}
.sp-img-box{width:500px; height:500px;display:table;text-align:center; overflow:hidden;}
.sp-text-img{display:table-cell; vertical-align:middle; overflow:hidden; height:500px;}
.sp-text-text{ width:700px;margin:120px 0 90px 0px;}
.ml80{ margin-left:60px;}
.sp-text-nav{ font-size:34px;color:#707070; height:55px; line-height:55px; width:100%; overflow:hidden;text-overflow: ellipsis; white-space:nowrap;}
.sp-text-column,.sp-text-ulbox{ margin:35px 0 0 0;}
.sp-text-ulbox li{ height:43px; line-height:43px; color:#949494; font-size:20px; overflow:hidden;text-overflow: ellipsis; white-space:nowrap;}
.sp-text-ulbox li em{ margin-right:13px;}
.sp-btn-a,.sp-custombtn-a{ width:204px; height:48px; display:inline-block; border:2px solid #ff6f48; border-radius:2px; background:#ffffff;  line-height:46px; font-size:16px; color:#ff6f48; text-align:center; margin:23px 0 0px 22px;}  
.sp-btn-a:hover,.sp-custombtn-a:hover{ background:#ff6f48;border:2px solid #ff6f48; color:#fff;}
.sp-textcolumn-a,.sp-connection-a{ display:inline-block; line-height:46px; font-size:16px; color:#ff6f48; }
.sp-connection-a{margin-left:22px;}
.sp-textcolumn-a{ margin-left:0px;}
.sp-textcolumn-a:hover,.sp-connection-a:hover{ color:#0b667d;}
.sp-nav-box{ width:800px;}
.sp-login-box{ margin-right:33px;}
/*sp-bigimgbox*/
.sp-bigimgbox{width:100%; height:560px; overflow:hidden; display:table; min-width:1260px; overflow:hidden;}
.sp-bigimgbox img{display:table-cell; vertical-align:middle; text-align:center;}
/*文本*/
.sp-custombox{ width:100%; height:auto; min-width:1260px;}
.sp-custom-text{ width:1180px; margin:0 auto; padding:33px 0;}
.sp-custom-h3{ font-size:30px; color:#868686; width:1280px; text-align:center;}
.sp-customtextbox{color:#868686;}
.sp-custom-h2{ font-size:20px; color:#868686;height:75px; line-height:75px; text-align:left; overflow:hidden;text-overflow: ellipsis; white-space:nowrap;}
.sp-custom-p{ font-size:16px; line-height:32px; word-break:break-all; word-wrap:break-word;}
.sp-custombtn-box{ width:1280px;  }
.sp-custombtn-p{font-size:30px; color:#49494c;text-align:center;word-break:break-all; word-wrap:break-word;}
.sp-custombtn-pabox{ display:inline-block; text-align:center; width:1280px;}
/*footercss样式*/
.sp-footer-bigbox{ min-height:315px; min-width:1260px; }
.sp-footerbox{}
.sp-footerdlbox{ width:755px; min-height:315px; margin-left:30px; padding:0 10px;}
.sp-dl-box,.sp-weixin,.sp-relation{padding:34px 0;}
.sp-footerdlbox dl{ float:left; min-width:123px; color:#fdfeff; margin-right:56px; }
.sp-footerdlbox dl dt{ font-size:16px; border-bottom:1px solid #d4d5d6; height:50px; line-height:50px; text-align:left; margin-bottom:10px;}
.sp-footerdlbox dl dd{ font-size:14px; height:32px; line-height:32px; text-align:left;}
.sp-footerdlbox dl dd a{ color:#e8ebed;width:125px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display: block;}
.sp-footerdlbox dl dd a:hover{ color:#fff;}
.sp-border{ width:2px; border-left:1px solid #d4d5d6;height:218px;margin:42px 23px 0 23px;}
.sp-weixi-box{ width:160px; min-height:315px;}
.sp-weixin-nav,.sp-relation-nav{font-size:16px; color:#fff;height:50px; line-height:50px;}
.sp-weixin-nav{ text-align:center;}
.sp-relation-nav{ text-align:left;}
.sp-weixinlogo{width:84px; height:84px; overflow:hidden; margin:30px auto 0;}
.sp-weixinlogo img{width:84px; height:84px; }
.sp-relation-box{ padding:0 10px;margin-left:20px; height:315px}
.sp-relationul{margin-top:20px;}
.sp-relationul li{ height:35px; line-height:35px; font-size:14px; color:#fff; }
.sp-relationul li.sp-phone{ overflow:hidden; font-size:18px;}
.sp-phone em{ background:url(../images/dianh.png) no-repeat center; width:32px; height:32px; display:inline-block; float:left; margin-right:10px;}
.sp-email em,.sp-service em{ margin-right:10px;}
.sp-copyright{background:#7b7d80; min-width:1260px; }
.sp-copyright-innerbox{ height:42px; line-height:42px; text-align:center; color:#d1d2d3; font-size:14px;}
.sp-custombox2{ min-width:1260px;}
/*000*/
.sp-screen-box{ position: fixed; top:50%; right:0%; width:40px; height:100px; z-index:99;}

.banner{width:100%;overflow:hidden;height:560px;position:relative}
.banList li{height:560px;}
.sp-a-img{ background:url(../images/banner.jpg) no-repeat center; display:block; height:560px;}

.fomW{position:absolute;bottom:20px;left:50%;height:20px;z-index:1;width:1000px;margin-left:-500px}
.jsNav{text-align:center;}
.jsNav a{display:inline-block;background:#fff;width:15px;height:15px;border-radius:50%;margin:0 5px;}
.jsNav a.current{background:#fc8f0f;cursor:pointer;}

/*用户登录*/
.sp-userlogin-box{ background:url(../images/bg.jpg) no-repeat #fff; border:1px solid #dfdfdf; min-height:500px; margin:22px auto;}
.sp-userbox{ margin:23px 0;}
.sp-user-p{ width:306px; background:url(../images/xian.jpg) repeat-y  100% 0; height:480px;}
.sp-userpnav{ font-size:24px; width:92%; text-align:right; font-family:"微软雅黑"; margin-top:2px;}
.sp-user-innerbox{ margin-left:40px;}
.sp-user-dlbox{ height:69px;}
.sp-user-dlbox dt em{ font-size:12px; color:#F00; font-family:"宋体";}
.sp-user-dlbox dt{ float:left; font-size:14px; height:69px; line-height:69px;text-align:left; width:77px;}
.sp-userwb{text-indent:12px; display:inline-block; font-size:14px;}
.sp-yanzhen{text-indent:20px;display:inline-block; font-size:14px;}
.sp-user-dlbox dd{ float:left; height:69px;}
.sp-ipnut{/* color:#747474;*/ font-size:14px;width:310px; height:41px; line-height:41px; padding:0 5px; text-align:left; border:1px solid #e9e9e9; border-radius:2px;}
.mt13{ margin-top:13px;}
.w190{ width:190px;}
.sp-code{ border:1px solid #d6dfea; width:117px; height:40px; overflow:hidden; margin-left:15px;}
.sp-replace{ height:69px; line-height:69px; text-align:center; display:inline-block; margin-left:12px; color:#5ba4d9; font-size:14px; cursor:pointer;}
.sp-replace:hover{ color:#fa6b4b;}
.sp-password,.sp-passworderroe{ margin-left:82px; line-height:25px; height:25px; font-size:14px;}
.sp-password input{ margin:0 5px 0 0; vertical-align:middle; width:14px; height:14px;}
.sp-remember{ font-size:14px;  color:#747474;}
.sp-forgotpassword{color:#5ba4d9; font-size:14px; cursor:pointer;margin-left:25px;}
.sp-forgotpassword:hover{color:#fa6b4b;}
.sp-btnbox{margin:15px 0 0 79px}
.sp-login-button{ cursor:pointer;background:#fa6b4b; display:inline-block; width:330px; height:47px; line-height:47px; font-size:16px; color:#fff; text-align:center; border-radius:2px;}
.sp-login-button:hover{ background:#ff8469; color:#fff;}

.sp-passworderroe{ color:#ed5104; margin-top:10px; display:block;}

/*.sp-login-button:link{ color:#fff;}
.sp-login-button:active{color:#fff;}
*//*重置密码*/
.sp-password-dlbox dt{width:95px;}
.sp-password-btnbox{margin:17px 0 0 95px}
/*找回密码发送*/
.sp-password-text{ font-size:16px; color:#6e6e6e; height:36px; line-height:36px; text-align:left; margin-left:57px;}
.sp-password-zhbtn{margin:15px 0 0 57px;}
/*注册*/
.sp-registered-box{background:url(../images/zuc.jpg) no-repeat #fff; border:1px solid #dfdfdf; min-height:500px; margin:22px auto;}
.sp-registered-dlbox{height:69px;}
.sp-registered-dlbox dt em{ font-size:12px; color:#F00; font-family:"宋体";}
.sp-registered-dlbox dt{ float:left; font-size:14px; height:69px; line-height:69px;text-align:left; width:97px;}
.sp-registered-dlbox dd{ float:left; height:69px;}
.sp-account-p{ font-size:14px; color:#747474; height:16px; line-height:16px; margin-top:12px;}
.sp-account-p a{margin-left:12px; color:#5ba4d9; font-size:14px;}
.sp-account-p a:hover{color:#fa6b4b;}
.sp-registeredbtn{ margin-left:95px; line-height:25px; height:25px; font-size:14px;}
.sp-registeredbtn input{ margin:0 5px 0 0; vertical-align:middle; width:14px; height:14px;}
.sp-registeredbtnbox{margin:17px 0 0 95px}
/*协议*/
.sp-agreement-box{ border:1px solid #dfdfdf; background:#fff; min-height:500px; margin:23px auto;}

.sp-preview-box{ margin:0 60px 20px 60px;}
.sp-preview-box .disclaimer{font-size:24px; height:85px; line-height:85px; text-align:center; color:#434343;}
.sp-preview-box h2{font-size:16px;height:20px;margin:10px 0 20px 0; color:#6e6e6e;}
.main-content{color: #6e6e6e; font-size:14px;line-height: 22px;margin: 0;padding-bottom:18px;}
.sp-copyrighta,.sp-copyrighta:hover{ color:#d1d2d3;}
.sp-screen-box li{background:#5c5c5c;}
.sp-screen-box li a{ width:40px; height:37px; display:block; position:relative;}
.sp-screen-box i{ 
    color: #dadadd;
    display: block;
    width: 40px;
    line-height: 40px;
    height: 40px;
    text-align: center;font-style:normal;}
.sp-getback{font-size: 19px;}
.sp-phone{ font-size:20px;}
.sp-email{ font-size:20px;}
.sp-getbac-div,.sp-phone-div,.sp-email-div {position:absolute;top:0; height:40px;background-color:#fff;line-height:40px;text-align: center; border:1px solid #e6e6e6; color:#999999; display:none;}
.sp-getbac-div{left: -82px;width:82px;}
.sp-phone-div{ width:100px;left:-100px;}
.sp-email-div{width:145px;left:-145px;}
.sp-screen-box a:hover{ background:url(../images/topa.jpg) no-repeat 0% 50% #343434;}
.sp-screen-box a:hover .sp-getbac-div,.sp-screen-box a:hover .sp-phone-div,.sp-screen-box a:hover .sp-email-div{ display:block;}
/*左图右wen*/
.sp-leftimg-righttext{}
.sp-leftimg-righttext .sp-img-box{width:470px; margin-left:50px;}
.sp-leftimg-righttext .sp-text-text{ width:650px;}
/*左文右图*/
.sp-lefttext-rightimg .sp-text-text{ width:500px; margin-left:100px;}
/*会员中心*/
.sp-memberbox{height:100%; background:#fdfdfd; width:1220px; margin:20px auto; border:1px solid #e1e1e1;min-height:300px;}
.sp-member-left{ width:248px; height:auto;}
.sp-personalcente{ height:61px; line-height:61px; text-align:center; font-size:16px; border-bottom:1px solid #fbae9d;}
.sp-member-left ul li{ height:48px; line-height:48px; text-align:center; font-size:14px;}
.sp-member-left ul li a{ height:46px; line-height:46px; text-align:center; font-size:14px; display:block;}
.sp-member-left ul li.lion{background:#f2f2f2; border-top:1px solid #e2e2e2; border-bottom:1px solid #e2e2e2;}
.sp-member-left ul li.lion a{border-left:6px solid #fa6b4b; text-indent:-6px;}
.sp-member-right{ width:970px; border-left:1px solid #e3e3e3; height:100%; background:#fff; padding-bottom:20px; min-height:400px;}
.sp-order{height:61px; line-height:61px;font-size:16px; border-bottom:1px solid #e3e3e3; text-indent:18px; width:970px; color:#f73f13;}
.sp-orderinformation{ margin:15px 15px 0; height:42px; line-height:42px; background:#fffadc; border:1px solid #ffe3b8;color:#3d3d3d; font-size:14px; padding:0 10px;}
.sp-one-sapn{ width:257px; display:inline-block;}
.sp-two-sapn{ width:447px;display:inline-block;}
.sp-three-sapn{ width:200px;display:inline-block; text-align:right;}
.sp-three-em{ color:#fa5631;}
.sp-order-table{margin:15px 15px 0;}
.sp-table{ border:1px solid #e7e7e7; margin-bottom:0px;}
.sp-table>thead>tr>th{font-size:14px;border-right:1px solid #e7e7e7; border-bottom:none;vertical-align:middle; }
.sp-urse-table .sp-table>thead>tr>th{background:#f5f5f5; height:30px;}
.sp-table>tbody>tr>td{ vertical-align:middle; border-right:1px solid #e7e7e7; font-size:14px;}
.sp-orderimg{width:138px; height:113px; border:1px solid #d0d0d0; display:inline-block; float:left;vertical-align:middle; overflow:hidden; margin-right:20px;}
.sp-orderimg-p{height:113px; display:table;}
.sp-orderimg-p a{vertical-align:middle; display:table-cell; word-break:break-all; cursor:pointer;}
.sp-orderdetails{ height:35px; line-height:35px; text-align:right; font-size:14px;margin:10px 20px 0; font-size:14px;}
.sp-orderdetails em{ margin-left:10px;}
.sp-order-ursebox{margin:15px 15px 0;border-top:1px solid #e8e8e8;}
.sp-order-inner{ padding:10px 0; border-bottom:1px solid #e8e8e8;}
.sp-order-xxin{ float:left; width:100px; vertical-align:top; line-height:30px; font-size:14px;text-align:center;}
.sp-orderul{ margin-left:120px; vertical-align:top; }
.sp-orderul li{ font-size:14px; line-height:30px; min-height:30px; }
.sp-remarks{margin:15px; height:40px; line-height:40px; border:1px solid #e7e7e7; font-size:14px;}
.sp-remarks-nav{ background:#efefef; width:70px; display:block; height:38px; line-height:38px; text-align:center; float:left;}
.sp-remarks-innerbox,.sp-remarks-innerinput{  overflow:hidden;height:38px; line-height:38px; margin-left:70px;text-indent:12px; color:#a39f9f;text-overflow: ellipsis;white-space: nowrap;display: block;}
.sp-remarks-innerbox{width:867px;}
.sp-remarks-innerinput{width:1000px;}
.sp-remarks-innerinput input{ border:none;width:965px;height:38px; line-height:38px;color:#a39f9f;}
.sp-remarks-innerinput i.icon-cha{ color:#a39f9f; cursor:pointer;}
.sp-remarks-innerinput i.icon-cha:hover{ color:#d4d2d2;}
.sp-order-button{ background:#fa6b4b; display:inline-block; width:90px; height:38px; line-height:38px; font-size:14px; color:#fff; text-align:center; border-radius:2px;}
.sp-order-button:hover{ background:#ff8469; color:#fff;}
.sp-order-qx{ padding-left:10px; display:inline-block;}
.sp-order-span{ display:block; text-align:center; vertical-align:bottom; margin-top:25px;}
.sp-order-ddxq{ display:block; color:#fb6b49; text-align:center; padding-top:10px;}
.sp-order-ddxq:hover{ cursor:pointer;}
.sp-add,.sp-reduce{ text-decoration:none;cursor:pointer;border:1px solid #ccc;display:inline-block;height:26px;line-height:21px;overflow:hidden;padding:0;text-align:center;vertical-align:top;width:26px;font-size:21px; color:#8a8a8a;}
.sp-add{ margin-right:7px;}
.sp-reduce{ margin-left:5px;}
.sp-buy{ margin-top:10px;display:block; width:215px; height:52px; line-height:52px; text-align:center; font-size:20px; color:#fff; background:#ee4747; border-radius:5px; cursor:pointer;}
.sp-buy:hover{ color:#fff; background:#dc3b3b;}
.sp-grsere{ font-size:20px; margin-right:10px;}
.table a.sp-order-button,.table a.sp-gray-button{  display:inline-block; width:90px; height:38px; line-height:38px; font-size:14px; color:#fff; text-align:center; border-radius:2px;}
.table a.sp-order-button{background:#fa6b4b;}
.table a.sp-gray-button{ background:#e2e2e2; cursor:default;}
.table a.sp-gray-button:hover{color:#fff;}
.table a.sp-order-button:hover{ background:#ff8469; color:#fff;}
.sp-order-qx{ padding-left:10px; display:inline-block;}
.sp-order-span{ display:block; text-align:center; vertical-align:bottom; margin-top:25px;}
.sp-order-ddxq{ display:block; color:#fb6b49; text-align:center; padding-top:10px;}
.sp-order-ddxq:hover{ cursor:pointer;}
.sp-grsere{ font-size:20px; margin-right:10px;}
.icon-gerenzhongxin{ margin-right:5px;}
.icon-wodedingdan{ font-size:18px; margin-right:10px;}
.sp-bound-sample{ margin:25px 53px}
.sp-bound-sample .sp-user-dlbox dt{ width:108px;}
em.radioem{ display:inline-block; background:url(../images/radio.png) center no-repeat; width:22px; height:22px; cursor:pointer; margin:-5px 2px;}
em.radioem:hover,em.radioemon{background:url(../images/radioon.png) center no-repeat;}
em.clrem{color:#696969; margin:0 5px; font-size:14px;}
.sp-user-dlbox dd.sp-gender{ line-height:69px; vertical-align:middle;}
.sp-user-dlbox dd.sp-select-box{}
.sp-user-dlbox dd.sp-select-box .btn-group-box{ margin-right:20px;margin-top:20px;}
.sp-user-dlbox dd.sp-select-box .w30{ width:30px;}
.sp-user-dlbox dd.sp-select-box .w70{ width:70px;}
.sp-user-dlbox dd.sp-select-box .min-w70{ min-width:70px;}
.sp-user-meir{height:40px;}
.sp-user-meir dd.sp-mell{line-height:40px;font-size: 14px;}
.sp-user-meir dd,.sp-user-meir dt {height:40px;line-height:40px;}
.sp-user{ display:inline-block; *display:inline;*zoom:1; width:173px; height:45px; line-height:45px; font-size:16px;}
.sp-userbtn{ margin:27px 15px 0px;}

.sp-tabbable{ margin:10px;}
.sp-tabbable .nav{height:50px;}
.sp-tabbable .nav>li>a{ z-index:2;width:120px;overflow:hidden;text-overflow:ellipsis;white-space: nowrap; padding:0px;margin-right:0px;border:none; border-radius:0; color:#98a1a5; font-size:16px; line-height:40px; text-align:center; cursor:pointer;}
.sp-tabbable .nav .nav-tabs-em{ position:absolute; bottom:-9px; left:45%; display:block; background:url(../images/tab.png) no-repeat;width: 13px;height: 9px;}
.nav-tabs-span{ display:block; width:120px;border-bottom:2px solid #2da8e0;height:51px;position: absolute; z-index:1;}
.sp-tabbable .nav-tabs{border-bottom:none;}
.sp-tabbable .nav-tabs>li.active .nav-tabs-em{ display:block;}
.sp-tab-content{ border-top:2px solid #d6d8d9;}
.sp-tabbable .nav>li>a:hover .nav-tabs-em{ display:block;}
.nav>li>a:hover,.sp-conent .nav>li>a:focus{text-decoration:none;background-color:#fff; color:#98a1a5;}
.sp-tabbable .nav>li>a:hover{color:#98a1a5;}
.sp-tabbable .nav-tabs>li.active>a,.sp-tabbable .nav-tabs>li.active>a:hover,.sp-tabbable .nav-tabs>li.active>a:focus{ cursor:pointer; border:none;}
.tab-inner{ padding:0 20px;}
.nav-bigbox .nav-tabs>li.active .nav-tabs-em{bottom: -7px;}
.sp-tab-pane{ margin:29px 20px 20px;}
.sp-tab-left{ width:150px;}
.sp-tab-left a{ color:#145fc4; text-decoration:underline;}
.urse-img{ width:99px; height:99px; border-radius:5px;margin: 0 0 0 28px;}
.urse-img img{ vertical-align:middle; border-radius:5px;}
.sp-tab-left em{ display:block; width:150px; height:30px; line-height:30px; font-size:12px; text-align:center;  color:#a2a2a2;}
.sp-tab-left a{ width:150px; text-align:center; display:block; padding:5px 0;}
.sp-tab-right .sp-user-dlbox,.sp-tab-right .sp-user-dlbox dd{ height:54px;}
.sp-tab-right .sp-user-dlbox dt{ height:54px; line-height:54px;}
.sp-tab-right .sp-user-dlbox dd{ line-height:54px; font-size:14px;}
.sp-tab-right .sp-user-dlbox dd.sp-gender{ line-height:54px;}
.sp-tab-right .sp-user-dlbox dd.sp-select-box .btn-group-box{ margin-top:13px;}
.mt7{ margin-top:7px;}
.sp-tab-right{ margin:-10px 20px 0;}
.sp-p .sp-p-i{font-size:32px; margin-right: 5px;float: left;position: relative; top: -5px;font-style:normal;}
.sp-p {height: 38px;font-size:16px;border-bottom:3px solid #efeeee;line-height:38px;margin:10px 14px; }
.sp-p em{ width:116px; height:38px; line-height:38px;border-bottom:3px solid #fa6b4b; display:block;font-size:16px;}
.icon-gerenziliao{font-size:34px;float:left; position: relative;left: -1px;}
.icon-jibenxinxi,.icon-wodedingdan,.icon-lib{font-size:23px;float:left; position: relative;left: -1px;}
.sp-order-em{ margin-left:-15px;}
.sp-order-emxx{ margin-left:-22px;}
/**/
.sp-binding{ margin:0 12px;}
.sp-binding-inbox{ border-top:1px solid #ebebeb; background:#fdfdfd; min-height:149px;}
.sp-binding-ul{ margin:20px 17px;}
.sp-binding-ul li{float:left; font-size:14px;}
.sp-lispanp{ margin:20px 17px 0 17px; font-size:14px;}
.sp-lispanxb{ margin-left:5px;}
.sp-lispan{min-width:100px; margin-right:10px;}
.sp-litime{ margin-right:20px;}
.sp-bianji-a{ display:inline-block; width:23px; height:23px; border:1px solid #91b3f0; position:relative; border-radius:2px; margin-left:20px;}
.sp-bianji{position: absolute; top: -11px; left: -5px; font-size:31px; color:#52a9c1;}
.sp-bianji-a:hover{ background:#91b3f0; border:1px solid #91b3f0;}
.sp-bianji-a:hover .sp-bianji{ color:#fff;}
.sp-binding-abox{ margin:0 20px; word-break:break-all;}
.sp-binding-abox a{ color:#4f81c4; text-decoration:underline; font-size:14px; font-family:"宋体"; margin-right:20px; font-weight:bold;}
.sp-binding-nobox{ background:#f5f5f5;border-top:1px solid #ebebeb;}
.sp-binding-nobox .w200{ width:200px;}
.sp-binding-nobox-div{ width:500px; float:left; padding:10px 0 10px 20px;}
.sp-binding-nobox-div .sp-user-dlbox,.sp-binding-nobox-div .sp-user-dlbox dd{ height:54px;}
.sp-binding-nobox-div .sp-user-dlbox dt{ height:54px; line-height:54px;}
.sp-binding-nobox-div .sp-user-dlbox dd{ line-height:54px; font-size:14px;}
.sp-binding-nobox-div .sp-user-dlbox dd.sp-gender{ line-height:54px;}
.sp-binding-nobox-div .sp-user-dlbox dd.sp-select-box .btn-group-box{ margin-top:13px;}
.sp-binding-nobox-btndiv{display: block; margin: 72px 0 0 0; width: 300px; float: right;}
.sp-nav-box-top{ width:810px;}
.sp-top-right{ overflow:hidden; font-size:14px; height:22px; line-height:22px; margin-top:28px;}
.sp-top-right li{ float:left;  color:#909090; font-family:"宋体"}
.sp-top-right>li>a{color:#909090; cursor:pointer; margin:0 12px; font-family:"微软雅黑"}
.sp-top-right>li>a:hover,.sp-top-right>li>a.on{ color:#fb7d61;}
.sp-login-personal{ margin-right:0px;}
.sp-top-right>li>a.exit i{ font-style:normal; background:url(../images/home-ionc-tc.png) no-repeat; display:inline-block; width:16px; height:14px; margin-right:5px;}
.sp-top-right>li>a.exit{ color:#608eab;}
.sp-top-right>li>a.exit:hover{ color:#649cc0;}
.sp-order-ddxq:hover{ color:#fc9780;}
/*样品状态*/
.sp-status{ margin: 20px 20px 0; border:1px solid #e6e6e6;}
.sp-statusbox{ height:90px; text-align:center; border-bottom:1px dotted #bfbfbf; margin:0 20px;}
.sp-statusbox-p{ font-size:24px; height:30px; line-height:30px; margin-top:30px; color:#4f4f4f;}
.sp-small-em{ font-size:14px;height:24px; line-height:24px; margin-top:6px; color:#909090;}
.sp-statusbox-inner{ margin:15px;}

.sp-em-box{ font-size:14px;width:84px; height:84px; background:url(../images/yozga.png) no-repeat center; display:block; line-height:84px; text-align:center;}
.sp-em-boxon{ background:url(../images/ypzt.png) no-repeat center;}
.sp-success{ font-size:36px; color:#fff;}
.sp-logistics{ font-size:45px; color:#fff; line-height:95px;}
.sp-ybzj{font-size:57px;line-height:93px; color:#fff;}
.sp-kszj{font-size:47px;color:#fff;}
.sp-scbg{font-size:84px; height:30px; line-height:30px; color:#7e7e7e; text-align:center; display:block;}
.sp-statusbox-i{margin:50px 5px 30px;}
.sp-statusbox-i li{ float:left;}
.sp-arrow{ width:112px; height:99px; line-height:94px; text-align:center; }
.sp-arrow i,.sp-arrow div{font-size:24px; color:#eaebec;}
.sp-arrow div{color:#cac9c9;}
.sp-arrow div.icon-wul{ font-size:32px; width:50px;position:relative;}
.sp-arrow div.icon-xiehezhijianbiaoqian{ font-size:38px; width:50px;position:relative;}
.sp-arrow div.icon-gene{ font-size:36px; width:50px;position:relative;}
.sp-arrow div.icon-mdbaogao{ font-size:30px; width:50px; margin-top:-2px;position:relative;}
.sp-arrow div.icon-gb{ font-size:18px; width:50px; color:#ff6767;position:relative;}
.sp-shank{text-align:right; margin:24px 0;}
.sp-shank a{color:#ff4e36;border:1px solid #ff7979;padding:8px 20px;border-radius:50px;margin-right:18px;}
.sp-shank a:hover{color:#fff; background:#ff4e36;border:1px solid #ff7979;}
.sp-footer-bigbox{ position:relative;}
.sp-copyright{ position:relative;}
/*注册成功*/
.sp-registered{ width:500px; min-height:256px; margin:80px auto 20px;}
.sp-registered p{ height:45px; line-height:45px; text-align:center; font-size:18px; margin:10px 0;}
.sp-registered img{ margin-left:195px;}
.sp-registered-div{ width:435px; height:161px; text-align:center; line-height:161px; border:1px solid #e7e7e7; margin:0 auto;font-size:14px;}
.sp-registered-div a{ color:#0e8fb4; margin-left:5px;}
.sp-registered-div a:hover{ color:#149dc4; }
.sp-generate{ width:470px; margin:80px auto 0px;height:55px; line-height:55px; font-size:16px;}
.sp-generate img{ float:left; margin:5px 15px 0 0;}
.sp-generate-a{ width:196px; margin:0 auto; padding-top:5px;}
.sp-generate-a a{ margin:0 5px; font-size:14px; color:#177faa;}
.sp-generate-a a:hover{ color:#29a3d6;}
.sp-registered p.sp-registered-cs{ height:88px; line-height:88px; text-align:center; width:220px; margin:0 auto 30px;}
.sp-registered-cs img{ margin-left:0px; float:left;}
/*购买页面*/
.sp-wk{ width:1204px; text-indent:22px; border-bottom:1px solid #fea694; margin:0 4px;}
.sp-suer{ padding:10px; margin:10px 30px; border-top:2px solid #e0e0e0;}
.sp-urse-table{margin:24px 42px;}
.sp-suer-p{ font-size:15px; color:#686868; margin-bottom:5px; font-weight:bold;}
.sp-information-dlbox{ height:59px; line-height:59px; padding:0 40px;}
.sp-dizhi-box{ height:50px; line-height:50px;}
.sp-information-dlbox dt{ float:left;min-width:56px;}
.sp-information-dlbox dd{ float:left;}
.sp-suer dd.sp-select-box .btn-group-box{ margin-top:10px; margin-right:10px;}
.sp-suer dd.sp-select-box .btn-group-wenb{ color:#666;}
.sp-dizhi-box dt{ height:50px;}
.sp-dizhi-box dd input.sp-ipnut{ width:523px;}
.sp-zfgs{display: inline-block;height:56px;line-height:56px;margin-right:10px;font-size:14px;}
.sp-pay-dl,.sp-pay-dl dt,.sp-pay-dl dd,.sp-pay-dl .sp-zfgs{height:50px; line-height:50px;}
.sp-payremarks{ margin:18px 0 0 52px;}
.sp-paymoney{ text-align:right; margin-top:15px;}
.sp-paymoney span{ display:block; text-align:right; margin-bottom:10px;}
.sp-moneyje{ font-size:16px; color:#545454;}
.sp-moneyje em{ color:#737373; margin-right:5px;}
.sp-moneyyf{ font-size:16px; color:#3b3b3b;}
.sp-moneyyf em{ color:#ff4e27; font-size:28px; margin-right:5px;}
.sp-paymoney .mr0{ margin-right:0px; margin-bottom:0px;}
.sp-bigimgbox-a{ background:url(../images/bannernav.jpg) no-repeat center; display:block;height:560px;}
.table a.sp-add,.table a.sp-reduce{ cursor:pointer;border:1px solid #ccc;display:inline-block;height:26px;line-height:21px;overflow:hidden;padding:0;text-align:center;vertical-align:top;width:26px;font-size:21px; color:#8a8a8a;}
.sp-add{ margin-right:7px;}
.sp-reduce{ margin-left:5px;}
.sp-buy{ margin-top:10px;display:block; width:215px; height:52px; line-height:52px; text-align:center; font-size:20px; color:#fff; background:#ee4747; border-radius:5px; cursor:pointer;}
.sp-buy:hover{ color:#fff; background:#dc3b3b;}
.sp-text{background:#fff none repeat scroll 0 0;border:1px solid #ccc;color:#666;font-size:14px;height:26px;line-height:26px;text-align:center;width:35px;border-left:none;border-right:none}
.table a.sp-add:hover,.table a.sp-reduce:hover{ color:#666;}
/*商品列表1*/
.sp-categories-bg{ background:#fff;}
.sp-splbbox{ background:#fff; border:none; margin:33px auto 20px;}
.sp-splbbox h2.sp-xiaolian{ margin-left:15px; width:327px;background:#f9f9f9; border:2px solid #e4e4e4; color:#6d6d6d; text-indent:15px; height:50px; line-height:50px; border-radius:55px;}
.sp-splbbox h2.sp-xiaolian i.icon-banjiang{ font-size:35px; float:left; color:#a4a4aa; margin-left:7px;}
.sp-splbbox h2 em.sp-order-emxx{ margin-left:-11px; font-size:18px;}
.sp-order-emxx i{ color:#a7a7a7;font-style:normal;}
.sp-buylist{padding:23px 40px; border-bottom:2px solid #e2e2e2;}
.sp-buylist-left{position:relative;}
.sp-buylist-left,.sp-buy-a{ width:258px;height:209px;overflow:hidden; }
.sp-buylist-left .sp-buy-a img{border:1px solid #e8e8e8; width:258px; height:100%;}
.sp-buy-ab{ display:block; /*background:url(../images/pyimg.png) no-repeat;*/ position:absolute; top:-12px;right:-14px; width:56px; height:59px;} 
.sp-buylist-right{ width:720px; margin-left:40px;}
.sp-buy-nav{ font-size:24px; color:#464646; height:50px; line-height:43px; text-align:left;}
.sp-buy-courier{ color:#92aa00; font-size:15px; line-height:52px;  background:url(../images/spbg.jpg) no-repeat #fff;  text-indent:10px;}
.sp-buy-ainner{ font-size:12px; color:#6a6a6a; line-height:22px; word-break:break-all; padding-top:5px; display:block; margin-left:10px; font-size:14px;}
.sp-buy-ainner:hover{ color:#6a6a6a; text-decoration:underline;}
.sp-buy-price{ color:#ff542e; margin-top:20px; font-size:28px;}
.sp-buy-userbtn{ margin:5px;}
.sp-order-button:link,.sp-order-button:visited,.sp-order-button:active{color:#fff; text-decoration:none;}
/*商品列表2*/
.sp-buy-bigbox{position:relative;width:1260px; margin:20px auto;}
.sp-buy-ul{ width:1152px; margin:0 auto; overflow:hidden;}
.sp-text-center{text-align:center;}
.sp-buy-ul li{ width:356px; min-height:490px; border:1px solid #dddddd;  box-shadow: 0 0px 7px rgba(0, 0, 0, 0.1); position:relative; float:left;margin:0 14px;}
.sp-buyul-box{ margin:42px 0px 20px 47px; }
.sp-buyul-box a,.sp-buyul-box img{ display:block;}
.sp-buyul-box img{border:1px solid #e8e8e8;height:100%;width:258px;}
.sp-buy-money{ font-size:24px;color:#727272; text-align:center; padding:10px 0;}
.sp-buy-gw i{ font-size:12px;font-style:normal;}
.icon-arrowleft,.icon-arrowright{ position:absolute; top:40%;  display:block; font-size:45px; color:#e9e9eb; cursor:pointer;}
.sp-icon-arrow{color:#d1d1d6;}
.icon-arrowleft:hover,.icon-arrowright:hover{ color:#d1d1d6;}
.sp-icon-arrow{color:#d1d1d6;}
.sp-icon-arrow:hover{color:#bababa;}

.icon-arrowleft{left:0px;}
.icon-arrowright{ right:0px;}
.sp-buy-text{ margin:15px 64px 0; line-height:22px; font-size:14px;color:#6a6a6a;}
.sp-buy-gw{color:#a0b520; font-size:15px; height:30px;line-height:30px; text-align:center;}
.sp-buy-gw i{ font-size:12px; margin-right:5px;font-style:normal;}
.sp-lamp{ display:inline-block; text-align:center; width:1220px; margin-top:25px;}
.sp-lamp li{ margin:0 5px; display:inline-block;text-align:center; font-size:14px; cursor:pointer;color: #a5a4ab}
.sp-lamp li.icon-yuan{ font-size:15px;}
@media screen and (max-width:1280px) {
.sp-nav-box-top{ width:768px;}
.sp-login-personal{ margin-right:13px;}
.publicw{ width:1260px;}
.sp-buy-bigbox{ width:1220px;}
.sp-buy-ul{ width:1140px;}
.sp-buy-ul li{ margin:0 10px;}
}
/*商品详情*/
.sp-buylis-top{ border-top:none;}
.sp-buy-userfenbx{ margin-top:20px;}
.sp-spxx{ margin:10px 0; font-size:14px;}
.sp-femgx{ display:inline-block; margin-left:30px;}
.sp-fengx-i{ height:28px; font-size:14px; vertical-align:middle; position:relative; top:0px;}
.sp-fengx-w,.sp-fengx-b,.sp-fengx-t,.sp-fengx-x{ width:28px; height:28px; display:inline-block; margin-left:10px; vertical-align:middle;}
.sp-fengx-w{background:url(../images/fex01.png) no-repeat;}
.sp-fengx-b{background:url(../images/fex02.png) no-repeat;}
.sp-fengx-t{background:url(../images/fex03.png) no-repeat;}
.sp-fengx-x{background:url(../images/fex04.png) no-repeat;}
.sp-shanpxx{ width:100%; border-top:1px dashed #a4a4a4; background:#f9f9f9; min-height:300px; }
.sp-spjs-nav{ background:url(../images/spjx.png) no-repeat; width:333px; height:43px;}
.sp-spjs{ width:1133px; margin:30px auto 0;}
.sp-spjs-inner{ text-indent:24px; font-size:15px; color:#686868; word-break:break-all; line-height:24px; margin:40px 0;}
.sp-spjs-xq{ background:url(../images/shiabp.png) no-repeat; width:333px; height:43px;}
.sp-spxx-box{ margin-top:15px;width:1133px;}
.sp-spxx-box img{ max-width:1133px; margin-top:10px;}
.sp-spxx-big{ width:100%; background:#f9f9f9; padding-bottom:40px;}
/*商品发布*/
.sp-spno{ width:1200px; margin:30px auto 15px; background:#eeeeee; border:1px solid #dddddd; display:table; min-height:500px;}
.sp-spnobox{display:table-cell;vertical-align:middle; text-align:center;position:relative;top:-30px;}
.sp-spno i{ width:100px; font-size:95px; color:#c0c5cb;position:relative;left:0px;top:16px;font-style:normal;}
.sp-spno p.sp-innerbox{ font-size:25px; color:#545454; line-height:50px;width:242px;margin:0 auto;}
.sp-innersmall{ font-size:14px;}

/*新闻列表*/
.sp-news-box{ width:1240px; margin:20px auto 10px;}
.sp-news-left{ width:248px; min-height:500px;}
.sp-news-left dt{ height:54px; border-bottom:1px solid #fcaf9e; line-height:56px; text-align:center; font-size:16px; position:relative; margin-bottom:10px;}
.icon-liebiao{position:absolute;left:60px;font-size:23px;top:2px;color:#9c9c9c;}
.sp-news-left dd{ line-height:40px; height:40px; text-align:center; font-size:14px; color:#3c3c3c;}
.sp-news-left dd a{ text-decoration:none;}
.sp-news-left dd.on,.sp-news-left dd.on a,.sp-news-left dd a:hover{ color:#58bae6; text-shadow:0 0px 7px rgba(0, 0, 0, 0.1); font-size:16px;}
.sp-news-right{ border-left:1px solid #ccc; min-height:500px; width:900px;}
.sp-newslist-box{ margin:0 10px 0 15px; border-bottom:2px solid #e0e0e0; min-height:100px;  padding:10px 10px 15px;}
.sp-newslist-box-left{ margin-top:20px; height: 148px; width: 209px;}
.sp-newslist-box-left img{ border:1px solid #d1d1d1;}
.sp-newslist-box-right{ width:609px; margin-left:30px;}
.sp-newslist-nav{ font-size:20px; border-bottom:1px dashed #d6d6d6; height:60px; line-height:50px; text-indent:10px; color:#3c3c3c;text-overflow: ellipsis; white-space: nowrap; overflow:hidden;}
.sp-newslist-a { margin:22px 0; font-size:14px; color:#8c8c8c; padding-left:10px;word-break:break-word;word-wrap: break-word;}
.sp-newslist-a a:hover{ text-decoration:underline;color:#8c8c8c;}
.sp-newslist-time{ position:relative; bottom:0px;}
.sp-newslist-time a{ color:#149fe0; font-size:14px; line-height:20px; word-break:break-all; margin:0 10px;border-bottom:1PX #149fe0 solid;}
.sp-newslist-time a i{ font-family:"宋体"; font-size:12px;font-style:normal; }
.sp-newslist-time a:hover{ color:#27acea;}
.sp-newslist-time span{ float:right; font-size:12px; color:#8a8a8a;}
.sp-newslist-time span i{ margin-left:10px;font-style:normal;}
/*新闻详情*/
.sp-newsxx-left{ width:940px; min-height:500px; border-right:1px solid #dbdbdb;}
.sp-newsxx-left-nav{ margin:0 20px; border-bottom:1px dashed #CCC; padding-bottom:10px; overflow: hidden;}
.sp-newslis-nav{ font-size:30px; font-family:"宋体"; font-weight:bold; color:#1f1f1f; margin:10px 0; overflow: hidden;text-overflow: ellipsis; white-space: nowrap;}
.sp-newslisfemgx{ float:right;}
.sp-newslis-times{ width:500px; float:left; min-height:27px; line-height:27px; overflow: hidden; margin-top:10px;}
.sp-newslis-times i{ display:inline-block;*display:inline;*zoom:1; min-width:80px;font-style:normal;}
.sp-inner-tiem{ margin:15px; overflow:hidden;}
.sp-newslisimg-box{ width:443px; margin:15px auto; height:315px; border:1px solid #d2d2d2;}
.sp-newslisimg-box img{ max-height:315px;}
.sp-newslisp{ font-size:14px; color:#3c3c3c; margin:18px 0; }
.sp-newslisp img{ max-width:909px; margin-top:10px;}
.tindent24{text-indent:24px;}
.color01{color:#e21e14;}
.sp-newslis-img{ background:url(../images/newsjpg.jpg) no-repeat center; display:block; width:100%;min-height:375px;}
.sp-newsxx-right{ margin:10px 15px;}
.sp-newsxx-right-nav{ width:270px; border-bottom:2px solid #c1c1c1; height:44px; line-height:44px; font-size:16px; color:#3c3c3c; text-align:center; margin-bottom:15px;}
.sp-newsxx-list{ width:270px; margin:5px auto; padding:9px 8px 10px; border-bottom:dotted 1px #bababa;}
.sp-newsxx-list a{ display:inline-block; vertical-align:middle;}
.sp-newsxx-nav{ width:145px; margin-left:10px; color:#7f7f7f; font-size:14px; text-overflow: ellipsis; white-space: nowrap;overflow: hidden;}
.sp-newsxx-nav:hover{ text-decoration:underline; color:#545454;}
.sp-dingdan{ color:#fa5631;}
.sp-newsxx-list img{ border:1px solid #d2d2d2;}
/*调查问卷*/
.sp-questionnaire{ background:url(../images/bodybg.jpg) repeat; }
.sp-survey-box{ border:1px solid #e0e0e0;box-shadow: 0 0px 7px rgba(0, 0, 0, 0.1); background:#fff;}
.sp-surveynav{ height:98px; line-height:98px; text-align:center; font-size:30px;text-align:center; color:#4b4b4b; border-bottom:1px dashed #b9b9b9;}
.sp-surveyimg{border:1px solid #eaeaea; height:393px; width:1160px; margin:23px auto 10px;}
.sp-survey-text{ padding:0 80px;}
.sp-survey-text p{ margin:22px 0 15px; font-size:14px; color:#3c3c3c; line-height:22px; color:#636363;}
.sp-survey-innernav{ color:#eb2626; padding:0 80px; text-indent:30px; height:30px; line-height:30px; font-size:14px;}
.sp-surveyxq-box{ background:url(../images/tiaoc.jpg) repeat-x top; margin:0 80px; min-height:30px;}
.sp-surveyxq-radio{ padding:20px 15px 0;}
.sp-options{ margin:0 0 5px 54px; font-size:14px;}
.sp-options li{ min-height:40px; line-height:40px; font-size:14px;}
.sp-options li input{ margin:0 5px 0 0; vertical-align:middle; width:15px; height:24px; line-height:24px; background:#fff; position:relative; top:-1px\0;}
.sp-surveyxqsmell{ font-size:14px; color:#545454; line-height:24px; text-indent:8px;}
.sp-surveyxqsmell i{ color:#eb2626; vertical-align:middle; margin-right:7px; width:20px; display:inline-block; text-align:right;font-style:normal;}
.sp-border-top{ border-top:1px solid #dddddd;}
.sp-group-box{ margin:10px 0 15px 0; float:none; margin-left:50px; width:160px;border-color:#e0e4e6; border-radius:2px;}
.sp-group-box .dropdown-menu{ width:160px;border-color:#e0e4e6;}
.sp-ul-text{ margin-left:50px; margin-bottom:15px;}
.sp-ul-text input{ width:500px; border-radius:2px; height:42px; line-height:42px; margin-top:10px; font-size:14px; color:#545454; border-color:#e0e4e6;}
.sp-ul-text textarea,.sp-ul-text div {width:980px;border: 1px solid #cfdae4;padding:4px; margin:0; background:#fff; border-color:#e0e4e6; height:200px; margin-top:10px;}
.sp-ul-text div{ color:#696969;overflow-x:hidden;overflow-y:auto;}
.sp-surveyxq-span{float:right;font-size:12px;color:#92a0a6;}
.sp-surveyxq-btn{ display:inline-block; width:100%; text-align:center; margin:5px 0 20px 0; }
.sp-surveyxq-btn button{ width:100px; height:35px; line-height:22px; text-align:center; font-size:14px; padding:0px;}
.sp-survey-select{width:150px;border: 1px solid #dddddd;height: 30px;line-height: 30px;margin: 10px 0px 14px 52px;color:#545454; border-radius:3px;}
.sp-survey-select option{height: 30px;line-height: 30px; border:1px solid #dddddd; color:#545454;}
.sp-information-dlbox dt.w90{ width:90px;}
.sp-information-dlbox dt em.red{ color:#F00; font-size:12px; font-family:"宋体";}
.bordernone{ border:none;}
.sp-ul-text div{border:none;}

/*2016-04-21修改白色背景的问题*/
.sp-innerbigwhite{background:#fff; top:0; padding-top:78px; padding-bottom:20px; margin-top:0px;}
.sp-footer-bigwhite{background:#a9abae;top:0;}
.sp-copyrightwhite{bottom:0px; top:0px;}
.ml125{ margin-left:125px;}
.sp-zice{margin-left: 96px; margin-top: 10px;display:block;color: #f00;}
.sp-zice i{background: #f00; border-radius: 50px; color: #fff; margin-right: 4px;font-style:normal;}
/*弹窗*/
.modal-p{ margin:0 50px; height:120px;border-bottom-right-radius:0px;border-bottom-left-radius:0px; background:#fff; border:none;}
.modal-p {text-shadow:none; text-align:left; font-size:18px; display:table; padding-left:10px;}
.modal-p-warning{background:url(/images/ti.png) no-repeat 50%;display: table-cell;width:60px; height:51px;}
.modal-p-succeed{background:url(/images/win.png) no-repeat 50%;display: table-cell;width:60px; height:51px;}
.modal-p-delete{ background:url(/images/delete.png) no-repeat 50%;display: table-cell;width:60px; height:51px;}
.modal-p-inner{display: table-cell;vertical-align: middle; padding-left:10px; word-break:break-all; word-wrap:break-word;}
::-ms-clear, ::-ms-reveal{display: none;}
.pagernav {
    display: inline-block;
    text-align: right;
    width: 100%;
    background: #fff;
    padding-right: 20px;
}
.pagination {
    display: inline-block; padding-left: 0;
    margin: 5px 0;
    border-radius: 4px;}
.sp-femgx i{ position:absolute; margin-top:-22px;font-style:normal;}
.sp-newslisfemgx i{ position:inherit; margin-top:0px;font-style:normal;}
.sp-orderul a{ color:#149fe0; cursor:pointer;}
.icon-time{ font-size:22px; top:0px;}
.timeicon {
    color: #31bbcd;
    cursor: pointer;
    font-size: 20px;
    position: relative;
    right: 26px;
    top:3px;
    width: 22px;
    z-index: 2;
}
.sp-user{ display:inline-block; *display:inline;*zoom:1; width:173px; height:45px; line-height:45px; font-size:16px;}
.sp-gray-button{  display:inline-block; width:173px; height:38px; line-height:38px; font-size:16px; color:#fff; text-align:center; border-radius:2px;background:#e2e2e2; cursor:default;}
.sp-gray-button:hover{color:#fff;}


.mask-error{ position:fixed; width:100%; height:100%; background:#dadada;filter:alpha(opacity=50);-moz-opacity:0.5;-khtml-opacity: 0.5;opacity: 0.5; z-index:10;}
.error-box{background:#f3f3f3; border:1px solid #c9d0d6;box-shadow:0px 2px 6px #979797; overflow:hidden; position:absolute; width:692px; height:310px; top:50%; left:50%; margin:-155px 0 0 -346px; z-index:12; border-radius:5px;}
.error-box img{ display:block;}
.error-img{ float:left; margin:33px 0 0 40px;}
.error-p{ margin:90px 20px 0 0;text-align:center; width:390px;float:left;}
.errpor-pbox{ min-height:30px;font-size:25px; line-height:35px;}
.error-p a{ margin-top:20px;box-shadow:0px 2px 6px #979797; text-decoration:none;width:140px; height:47px; line-height:47px; text-align:center;font-size:18px; color:#30aa36; border:1px solid #aeddb1; border-radius:5px; display:inline-block;}
.error-p a:hover{ background:#30aa36; color:#fff; border:1px solid #30aa36;}

/*2016-06-07修改*/
.sp-spjs-inner em, .sp-spjs-inner i, .sp-inner-tiem em,.sp-inner-tiem i{ font-style:inherit; font-weight:inherit;}
.sp-spjs-inner ul, .sp-spjs-inner ul>li, .sp-inner-tiem ul, .sp-inner-tiem ul>li, .sp-spxx-box ul, .sp-spxx-box ul>li{ list-style-type:disc;}
.sp-spjs-inner ol, .sp-spjs-inner ol>li, .sp-inner-tiem ol, .sp-inner-tiem ol>li, .sp-spxx-box ol, .sp-spxx-box ol>li{list-style-type:decimal;}
.sp-spxx-box ol, .sp-spxx-box ol>li, .sp-spxx-box ul, .sp-spxx-box ul>li{ margin-left:10px;}
.sp-news-img{position:absolute;top:46%;left:55%; margin: -63px 0 0 0; width:126px;height:146px;}
.h40{height:40px; line-height:40px;}
.sp-msg-box{height:250px;text-align:center;font-size:16px;background:#f5f5f5;margin: -9px 2px 0; color:#a1a1a1;}
.sp-msg-em{ width:100%; background:url(../images/saobei.png) no-repeat center bottom; height:170px; display:block;}
.sp-msg-span{ display:inline-block; margin-top:10px;}
.sp-em-boxfail{ background:url(../images/ypzt_sb.png) no-repeat center; color:#fff;}
.sp-text-emfail{ color:#e52323;}
.sp-input-btn{text-align:right;width:943px;font-size:13px;height:30px;line-height:30px;margin-bottom:-12px;margin-top:10px;}
.sp-input-btn input{ margin-right:5px; width:15px; height:15px; position:relative; top:3px;}
.sp-yangp-on{ padding:0px; margin:0px; background:none;}
.sp-yangp{ margin:0px; height:100%;}

/*微信提示*/
.sp-weix-box{ margin:0 66px; height:75px; line-height:75px;}
.sp-weix-nav{ font-size:24px; font-weight:bold; color:#1d1b1c;}
.sp-weix-clues{ float:right;font-size:12px; text-align:center; color:#333;}
.sp-weixmnet-box{ background:#f2f2f2;margin:0 57px; border:1px solid #d5d5d5; height:90px;}
.sp-payment-details{ width:50%; margin:15px 0 0 29px;}
.sp-paymentspan{ display:block; height:25px; font-size:16px; color:#1f1f1f;}
.sp-paymentmake{ display:block;margin-top:7px;height:25px; font-size:16px; color:#1f1f1f;}
.sp-recommend-right,.sp-payment{ width:40%; text-align:right; line-height:90px; margin-right:35px; font-size:16px;color:#1f1f1f;}
.sp-payment em{ font-size:24px; color:#ff4e27;}
.sp-recommend-right{ font-size:14px;height:77px; line-height:77px;}
.sp-recommend-right em{font-size:18px; color:#ff4e27}
.sp-recommend-box{margin:10px 60px 0; height:77px; line-height:77px; border:1px solid #ff9900;}
.sp-recommend-left{ margin-left:20px; height:75px; overflow:hidden;}
.sp-recommend-left-em{ font-size:20px; color:#4e4d4d; margin:0 20px 0 3px;}
.sp-wei-logo{ margin-top:-7px;}
.sp-recommend-img{ margin-top:-5px; margin-right:20px;}
.sp-twobarcodes{ width:172px; height:171px; margin:20px auto 0;}
.sp-two-dimensional{ width:172px; height:57px; margin:20px auto 0;}
.sp-twobarcodes-p{ text-align:center; font-size:16px; margin:10px 0 50px 0;}
/*2016-07-01 添加*/
.sp-coupon-dd .btn-box{ position:relative; top:11px; margin-left:10px;}
.sp-payment-dlbox{ margin:10px 0;}
.sp-pay-dl dt{ width:128px;}
/*支付页面管理*/
.sp-payment-box{ background:none; border:none;}
.sp-member-box{width:95%;margin: 0 auto;}
.sp-member-nav{ font-size:24px;font-weight:bold;color:#1d1b1c;}
.sp-member-p{font-size:16px; color:#1f1f1f;margin-top:10px; overflow:hidden;}
.sp-member-span{ float:right;}
.sp-member-span i{ font-size:22px; color:#ff4e27;}
.sp-left-top{ background:url(../images/sp_zou.jpg) no-repeat; position:relative;display:inline-block; *display:inline;*zoom:1; width:8px; height:7px;top: 3px; left: 4px;}
.sp-center{ border-top:1px solid #c5c5c5; height:4px; background:#d4d4d4; width:98%; display:inline-block; *display:inline;*zoom:1;}
.sp-right-top{background:url(../images/sp_you.jpg) no-repeat; position:relative;display:inline-block; *display:inline;*zoom:1; width:8px; height:7px;top:3px;margin-left:-4px;}
.sp-select-inner{ width:98%; margin:0 auto; background:url(../images/sp_bottom.jpg) repeat-x 0 100% #fff; min-height:350px;position:relative;top:-2px;box-shadow:0px  -3px 5px #e4e4e4; }
.sp-selectinner-box{ padding-top:40px;}
.sp-select-zfpt{ margin:0 44px; height:45px; line-height:45px; border-bottom:1px solid #c6c7c7;}
.sp-select-zfpt span{ display:block; position:relative;top:5px; left:25px; width:114px; font-size:16px; height:40px; line-height:40px; text-align:center; border:1px solid #c6c7c7; border-top-left-radius:2px; border-top-right-radius:2px; border-bottom:none; background:#fff;}
.sp-select-div{ border-bottom:1px solid #c6c7c7; margin:34px 44px 0; padding-bottom:25px;}
.sp-method{ border:1px solid #d2d1d1;display:inline-block; *display:inline;*zoom:1; width:177px;height:50px; padding:0 10px; overflow:hidden; margin:0 15px;}
.sp-method img{ height:46px; margin:3px 24px;}
.sp-method:hover{ border:1px solid #00c803;}
/*登录2016-09-18*/
.zxmlx-conent .zxmlx-tabbable .nav>li>a{ padding:0 20px;margin-right:0px;border:none; border-radius:0; color:#98a1a5; font-size:16px; line-height:40px; text-align:center; min-width:120px;}
.zxmlx-conent .zxmlx-tabbable .nav>li .nav-tabs-em{ position:absolute; bottom:-9px; left:45%; display:none; background:url(../images/tab.png) no-repeat;width: 13px;height: 9px;}
.zxmlx-conent .zxmlx-tabbable .nav-tabs{border-bottom:none;}
.zxmlx-conent .zxmlx-tabbable .nav>li>a:hover,.zxmlx-conent .zxmlx-tabbable .nav-tabs>li.active>a,
.zxmlx-conent .zxmlx-tabbable .nav-tabs>li.active>a:hover,
.zxmlx-conent .zxmlx-tabbable .nav-tabs>li.active>a:focus{ cursor:pointer; font-size:16px;color:#11c9bb;cursor: pointer;background-color: #fff; border:none; border-bottom:2px solid #11c9bb; }
.zxmlx-conent .zxmlx-tabbable .nav-tabs>li.active .nav-tabs-em{ display:block;}
.zxmlx-tab-content{ border:1px solid #e9e9e9; border-top:2px solid #d6d8d9; padding:20px 10px 20px; margin-bottom:20px;	min-width:800px;}
.shopm-tab-content{min-width:890px; min-height:354px;}
.zxmlx-conent .zxmlx-tabbable .nav>li>a:hover .nav-tabs-em{ display:block;}
.zxmlx-conent .nav>li>a:hover,.zxmlx-conent .nav>li>a:focus{text-decoration:none;background-color:#fff; color:#98a1a5;}
.zxmlx-conent .zxmlx-tabbable .nav>li>a:hover{color:#98a1a5;}
.zxmlx-conent .tab-inner{ padding:0 20px;}
.zxmlx-conent .zxmlx-tabbable .sp-user-dlbox{ margin-left:6px;}
.zxmlx-conent .zxmlx-tabbable .btn-box,.sp-btn-dd{ padding:8px 18px; margin-top:15px;margin-left:10px;}
.sp-mcslogin{ color:#a38b4e; font-size:20px; padding:10px 11px 5px;}
.btn-bule{ background:#458dce; color:#fff; border:1px solid #458dce; }
.btn-bule:focus,
.btn-bule:active:focus,
.btn-bule:active:focus{ background:#2f79bb; border-color:#2f79bb;}
.btn-bule:hover,.btn-bule:focus{ background:#4793d7; border-color:#4793d7;}
.input-bule{ background:#fafaf9;border:2px solid #d9d1bb;}
.sp-password-div{margin-left:25px;}
.sp-password-div span{ display:inline-block; width:20px; height:4px; background:#000;  margin-right:2px;}
.sp-password-div em{ margin-left:5px; font-size:12px;}
.sp-password-weak span{ background:#fd3600;}
.sp-password-weak em{ color:#fd3600;}
.sp-password-fit span{ background:#fbce01;}
.sp-password-fit em{ color:#fbce01;}
.sp-password-strong span{ background:#3fa803;}
.sp-password-strong em{ color:#3fa803;} 
/*样品状态*/
.sp-text-em{width:73px;display:block;text-align:center;word-wrap:break-word;font-size:12px;margin:10px auto 0; color:#909090;}
.sp-arrow div.sp-iconfonton{width:50px;color:#f04e2a; animation:2s bounce infinite;-webkit-animation:1.5s bounce infinite;-ms-animation:2s bounce infinite;-0-animation:2s bounce infinite;}
.sp-position{ position:absolute;width:122px;height:68px;top:-39px;left:-39px;display:inline-block; *display:inline; *zoom:1; overflow:hidden;}
.sp-position-span{ display:block; width:122px; height:54px; border-radius:5px; background:#f3f3f3; color:#f56646; font-size:12px;line-height:18px;   padding: 10px;}
.sp-text-em{ display:none;}
@-webkit-keyframes bounce{0%,100%,20%,50%,80%{-webkit-transform:translateY(0);transform:translateY(0)}40%{-webkit-transform:translateY(-15px);transform:translateY(-15px)}60%{-webkit-transform:translateY(-7px);transform:translateY(-7px)}}@keyframes bounce{0%,100%,20%,50%,80%{-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0)}40%{-webkit-transform:translateY(-15px);-ms-transform:translateY(-15px);transform:translateY(-15px)}60%{-webkit-transform:translateY(-7px);-ms-transform:translateY(-7px);transform:translateY(-7px)}}.bounce{-webkit-animation-name:bounce;animation-name:bounce}
.sp-pfail{position: absolute;margin:-65px 0 0px -2px;width:97px;font-size:14px; color:#fb4848;}
.sp-pfail .sp-img-box,.sp-pfail .sp-position-span{ width:97px; color:#ff6767;}
.sp-table .sp-order-span{ margin-top:0px;}
.sp-table-pbox{height:46px;line-height:44px;border:1px solid #e7e7e7; border-bottom:none;background:#f5f5f5; font-size: 14px;}
.mr3{ margin-right:3px;}
.icon-lb{position: relative;top:2px;right:5px;}
/*三角形*/
.sp-nav-ul li.shop-lione{min-width:23px;position:relative;}
.sp-nav-ul li .shop-lione-ul{position:fixed;top:78px;margin-left: -65px; width:180px;background:#fff;z-index:12; border:1px solid #e0e0e0; border-bottom:none;box-shadow:1px 1px 6px rgba(0, 0, 0,0.3);}
.sp-nav-ul li .shop-lione-ul>li{ width:100%; height:48px; line-height:48px; border-bottom:1px solid #f4f4f4; position:relative;}
.sp-nav-ul li .shop-lione-ul>li span.sp-postion-span{ bottom:0px; margin-bottom:2px;}
.sp-nav-ul li.shop-lione span.icon-down{font-size:24px;top:2px;}
.sp-nav-ul li .shop-lione-ul>li a:hover{color:#f9471e;}
.sp-nav-ul li .shop-lione-ul>li.on{ color:#f9471e;}
/*.sp-postion-no{ position:relative;}*/
.sm-mdt-em{ background: url(../images/dingdan.png) no-repeat center bottom;}
/*样品状态修改*/
.sp-statusbox{ height:105px;}
.sp-shopnanme{ font-size:16px; margin-top:10px;}
.align-center{text-align:center;}
@media (max-width:767px){
	/*左图右文*/
	.sp-lefttext-rightimg, .sp-leftimg-righttext{ min-width:400px; min-height:300px;}
	.publicw{ width:auto;}
	.sp-leftimg-righttext .sp-img-box {width:400px; margin-left:0px; float:none; display:block; margin:0 auto;}
	.sp-text-img{ height:auto; display:block; margin:0 auto;}
	.sp-leftimg-righttext .sp-text-text,.sp-lefttext-rightimg .sp-text-text{width:337px;margin:0 20px;float:none;}
    .sp-img-box{ width:auto; min-height:300px;}
    .ml80{ margin-left:0px;}
	/*纯图片*/
	.sp-bigimgbox{ min-width:auto;}
	/*纯文字*/
	.sp-custom-text{width:auto;margin:0 20px;}
	.sp-custom-h3{ width:767px;}
}
@media (max-width:375px){
	/*左图右文*/
	.sp-lefttext-rightimg, .sp-leftimg-righttext{ min-width:320px;min-height:300px;}
	.publicw{ width:auto;}
	.sp-leftimg-righttext .sp-img-box {width:320px; margin-left:0px; float:none;display:block; margin:0 auto;}
	.sp-text-img{ height:auto;}
	.sp-leftimg-righttext .sp-text-text,.sp-lefttext-rightimg .sp-text-text{width:300px;margin:0 20px 10px;float:none;}
    .sp-img-box{ width:auto; min-height:300px;}
    .ml80{ margin-left:0px;}
	/*纯图片*/
	.sp-bigimgbox{ min-width:auto;}
	/*纯文字*/
	.sp-custom-text{width:auto;margin:0 20px;}
	.sp-custom-h3{ width:375px;}
}