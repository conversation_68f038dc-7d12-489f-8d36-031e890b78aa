@import "global";

@-webkit-keyframes shake-little {
  0% {
    -webkit-transform: translate(0px, 0px) rotate(0deg)
  }
  2% {
    -webkit-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  4% {
    -webkit-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  6% {
    -webkit-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  8% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  10% {
    -webkit-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  12% {
    -webkit-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  14% {
    -webkit-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  16% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  18% {
    -webkit-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  20% {
    -webkit-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  22% {
    -webkit-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  24% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  26% {
    -webkit-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  28% {
    -webkit-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  30% {
    -webkit-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  32% {
    -webkit-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  34% {
    -webkit-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  36% {
    -webkit-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  38% {
    -webkit-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  40% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  42% {
    -webkit-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  44% {
    -webkit-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  46% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  48% {
    -webkit-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  50% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  52% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  54% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  56% {
    -webkit-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  58% {
    -webkit-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  60% {
    -webkit-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  62% {
    -webkit-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  64% {
    -webkit-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  66% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  68% {
    -webkit-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  70% {
    -webkit-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  72% {
    -webkit-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  74% {
    -webkit-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  76% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  78% {
    -webkit-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  80% {
    -webkit-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  82% {
    -webkit-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  84% {
    -webkit-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  86% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  88% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  90% {
    -webkit-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  92% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  94% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  96% {
    -webkit-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  98% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
}
@-ms-keyframes shake-little {
  0% {
    -ms-transform: translate(0px, 0px) rotate(0deg)
  }
  2% {
    -ms-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  4% {
    -ms-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  6% {
    -ms-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  8% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  10% {
    -ms-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  12% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  14% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  16% {
    -ms-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  18% {
    -ms-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  20% {
    -ms-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  22% {
    -ms-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  24% {
    -ms-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  26% {
    -ms-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  28% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  30% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  32% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  34% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  36% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  38% {
    -ms-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  40% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  42% {
    -ms-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  44% {
    -ms-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  46% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  48% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  50% {
    -ms-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  52% {
    -ms-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  54% {
    -ms-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  56% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  58% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  60% {
    -ms-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  62% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  64% {
    -ms-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  66% {
    -ms-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  68% {
    -ms-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  70% {
    -ms-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  72% {
    -ms-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  74% {
    -ms-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  76% {
    -ms-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  78% {
    -ms-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  80% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  82% {
    -ms-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  84% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  86% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  88% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  90% {
    -ms-transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  92% {
    -ms-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  94% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
  96% {
    -ms-transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  98% {
    -ms-transform: translate(0px, -1px) rotate(-0.5deg)
  }
}
@keyframes shake-little {
  0% {
    transform: translate(0px, 0px) rotate(0deg)
  }
  2% {
    transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  4% {
    transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  6% {
    transform: translate(0px, 0px) rotate(-0.5deg)
  }
  8% {
    transform: translate(0px, -1px) rotate(-0.5deg)
  }
  10% {
    transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  12% {
    transform: translate(0px, 0px) rotate(-0.5deg)
  }
  14% {
    transform: translate(0px, 0px) rotate(-0.5deg)
  }
  16% {
    transform: translate(0px, -1px) rotate(-0.5deg)
  }
  18% {
    transform: translate(0px, 0px) rotate(-0.5deg)
  }
  20% {
    transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  22% {
    transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  24% {
    transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  26% {
    transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  28% {
    transform: translate(0px, -1px) rotate(-0.5deg)
  }
  30% {
    transform: translate(0px, -1px) rotate(-0.5deg)
  }
  32% {
    transform: translate(0px, 0px) rotate(-0.5deg)
  }
  34% {
    transform: translate(0px, -1px) rotate(-0.5deg)
  }
  36% {
    transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  38% {
    transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  40% {
    transform: translate(0px, 0px) rotate(-0.5deg)
  }
  42% {
    transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  44% {
    transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  46% {
    transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  48% {
    transform: translate(0px, -1px) rotate(-0.5deg)
  }
  50% {
    transform: translate(0px, -1px) rotate(-0.5deg)
  }
  52% {
    transform: translate(0px, 0px) rotate(-0.5deg)
  }
  54% {
    transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  56% {
    transform: translate(0px, 0px) rotate(-0.5deg)
  }
  58% {
    transform: translate(0px, 0px) rotate(-0.5deg)
  }
  60% {
    transform: translate(0px, 0px) rotate(-0.5deg)
  }
  62% {
    transform: translate(0px, -1px) rotate(-0.5deg)
  }
  64% {
    transform: translate(0px, 0px) rotate(-0.5deg)
  }
  66% {
    transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  68% {
    transform: translate(0px, -1px) rotate(-0.5deg)
  }
  70% {
    transform: translate(0px, 0px) rotate(-0.5deg)
  }
  72% {
    transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  74% {
    transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  76% {
    transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  78% {
    transform: translate(0px, 0px) rotate(-0.5deg)
  }
  80% {
    transform: translate(0px, -1px) rotate(-0.5deg)
  }
  82% {
    transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  84% {
    transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  86% {
    transform: translate(0px, -1px) rotate(-0.5deg)
  }
  88% {
    transform: translate(0px, 0px) rotate(-0.5deg)
  }
  90% {
    transform: translate(-1px, -1px) rotate(-0.5deg)
  }
  92% {
    transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  94% {
    transform: translate(-1px, 0px) rotate(-0.5deg)
  }
  96% {
    transform: translate(0px, -1px) rotate(-0.5deg)
  }
  98% {
    transform: translate(0px, -1px) rotate(-0.5deg)
  }
}

@-webkit-keyframes shake-rotate {
  0% {
    -webkit-transform: translate(0px, 0px) rotate(0deg)
  }
  2% {
    -webkit-transform: translate(0px, 0px) rotate(-5.5deg)
  }
  4% {
    -webkit-transform: translate(0px, 0px) rotate(1.5deg)
  }
  6% {
    -webkit-transform: translate(0px, 0px) rotate(-7.5deg)
  }
  8% {
    -webkit-transform: translate(0px, 0px) rotate(-7.5deg)
  }
  10% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  12% {
    -webkit-transform: translate(0px, 0px) rotate(-5.5deg)
  }
  14% {
    -webkit-transform: translate(0px, 0px) rotate(-4.5deg)
  }
  16% {
    -webkit-transform: translate(0px, 0px) rotate(0.5deg)
  }
  18% {
    -webkit-transform: translate(0px, 0px) rotate(3.5deg)
  }
  20% {
    -webkit-transform: translate(0px, 0px) rotate(-6.5deg)
  }
  22% {
    -webkit-transform: translate(0px, 0px) rotate(-5.5deg)
  }
  24% {
    -webkit-transform: translate(0px, 0px) rotate(3.5deg)
  }
  26% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  28% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  30% {
    -webkit-transform: translate(0px, 0px) rotate(5.5deg)
  }
  32% {
    -webkit-transform: translate(0px, 0px) rotate(3.5deg)
  }
  34% {
    -webkit-transform: translate(0px, 0px) rotate(-5.5deg)
  }
  36% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  38% {
    -webkit-transform: translate(0px, 0px) rotate(-1.5deg)
  }
  40% {
    -webkit-transform: translate(0px, 0px) rotate(4.5deg)
  }
  42% {
    -webkit-transform: translate(0px, 0px) rotate(6.5deg)
  }
  44% {
    -webkit-transform: translate(0px, 0px) rotate(0.5deg)
  }
  46% {
    -webkit-transform: translate(0px, 0px) rotate(4.5deg)
  }
  48% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  50% {
    -webkit-transform: translate(0px, 0px) rotate(0.5deg)
  }
  52% {
    -webkit-transform: translate(0px, 0px) rotate(-7.5deg)
  }
  54% {
    -webkit-transform: translate(0px, 0px) rotate(-1.5deg)
  }
  56% {
    -webkit-transform: translate(0px, 0px) rotate(0.5deg)
  }
  58% {
    -webkit-transform: translate(0px, 0px) rotate(6.5deg)
  }
  60% {
    -webkit-transform: translate(0px, 0px) rotate(-3.5deg)
  }
  62% {
    -webkit-transform: translate(0px, 0px) rotate(-6.5deg)
  }
  64% {
    -webkit-transform: translate(0px, 0px) rotate(1.5deg)
  }
  66% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  68% {
    -webkit-transform: translate(0px, 0px) rotate(2.5deg)
  }
  70% {
    -webkit-transform: translate(0px, 0px) rotate(-4.5deg)
  }
  72% {
    -webkit-transform: translate(0px, 0px) rotate(-1.5deg)
  }
  74% {
    -webkit-transform: translate(0px, 0px) rotate(-6.5deg)
  }
  76% {
    -webkit-transform: translate(0px, 0px) rotate(3.5deg)
  }
  78% {
    -webkit-transform: translate(0px, 0px) rotate(-5.5deg)
  }
  80% {
    -webkit-transform: translate(0px, 0px) rotate(1.5deg)
  }
  82% {
    -webkit-transform: translate(0px, 0px) rotate(4.5deg)
  }
  84% {
    -webkit-transform: translate(0px, 0px) rotate(-1.5deg)
  }
  86% {
    -webkit-transform: translate(0px, 0px) rotate(-2.5deg)
  }
  88% {
    -webkit-transform: translate(0px, 0px) rotate(-3.5deg)
  }
  90% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  92% {
    -webkit-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  94% {
    -webkit-transform: translate(0px, 0px) rotate(-6.5deg)
  }
  96% {
    -webkit-transform: translate(0px, 0px) rotate(-7.5deg)
  }
  98% {
    -webkit-transform: translate(0px, 0px) rotate(6.5deg)
  }
}
@-ms-keyframes shake-rotate {
  0% {
    -ms-transform: translate(0px, 0px) rotate(0deg)
  }
  2% {
    -ms-transform: translate(0px, 0px) rotate(0.5deg)
  }
  4% {
    -ms-transform: translate(0px, 0px) rotate(5.5deg)
  }
  6% {
    -ms-transform: translate(0px, 0px) rotate(-3.5deg)
  }
  8% {
    -ms-transform: translate(0px, 0px) rotate(-2.5deg)
  }
  10% {
    -ms-transform: translate(0px, 0px) rotate(-6.5deg)
  }
  12% {
    -ms-transform: translate(0px, 0px) rotate(6.5deg)
  }
  14% {
    -ms-transform: translate(0px, 0px) rotate(5.5deg)
  }
  16% {
    -ms-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  18% {
    -ms-transform: translate(0px, 0px) rotate(-6.5deg)
  }
  20% {
    -ms-transform: translate(0px, 0px) rotate(6.5deg)
  }
  22% {
    -ms-transform: translate(0px, 0px) rotate(3.5deg)
  }
  24% {
    -ms-transform: translate(0px, 0px) rotate(-4.5deg)
  }
  26% {
    -ms-transform: translate(0px, 0px) rotate(4.5deg)
  }
  28% {
    -ms-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  30% {
    -ms-transform: translate(0px, 0px) rotate(-5.5deg)
  }
  32% {
    -ms-transform: translate(0px, 0px) rotate(-6.5deg)
  }
  34% {
    -ms-transform: translate(0px, 0px) rotate(1.5deg)
  }
  36% {
    -ms-transform: translate(0px, 0px) rotate(-3.5deg)
  }
  38% {
    -ms-transform: translate(0px, 0px) rotate(0.5deg)
  }
  40% {
    -ms-transform: translate(0px, 0px) rotate(-3.5deg)
  }
  42% {
    -ms-transform: translate(0px, 0px) rotate(-5.5deg)
  }
  44% {
    -ms-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  46% {
    -ms-transform: translate(0px, 0px) rotate(-7.5deg)
  }
  48% {
    -ms-transform: translate(0px, 0px) rotate(1.5deg)
  }
  50% {
    -ms-transform: translate(0px, 0px) rotate(0.5deg)
  }
  52% {
    -ms-transform: translate(0px, 0px) rotate(-4.5deg)
  }
  54% {
    -ms-transform: translate(0px, 0px) rotate(0.5deg)
  }
  56% {
    -ms-transform: translate(0px, 0px) rotate(-4.5deg)
  }
  58% {
    -ms-transform: translate(0px, 0px) rotate(2.5deg)
  }
  60% {
    -ms-transform: translate(0px, 0px) rotate(-2.5deg)
  }
  62% {
    -ms-transform: translate(0px, 0px) rotate(4.5deg)
  }
  64% {
    -ms-transform: translate(0px, 0px) rotate(-1.5deg)
  }
  66% {
    -ms-transform: translate(0px, 0px) rotate(-5.5deg)
  }
  68% {
    -ms-transform: translate(0px, 0px) rotate(0.5deg)
  }
  70% {
    -ms-transform: translate(0px, 0px) rotate(-3.5deg)
  }
  72% {
    -ms-transform: translate(0px, 0px) rotate(-2.5deg)
  }
  74% {
    -ms-transform: translate(0px, 0px) rotate(-1.5deg)
  }
  76% {
    -ms-transform: translate(0px, 0px) rotate(-1.5deg)
  }
  78% {
    -ms-transform: translate(0px, 0px) rotate(6.5deg)
  }
  80% {
    -ms-transform: translate(0px, 0px) rotate(3.5deg)
  }
  82% {
    -ms-transform: translate(0px, 0px) rotate(1.5deg)
  }
  84% {
    -ms-transform: translate(0px, 0px) rotate(-0.5deg)
  }
  86% {
    -ms-transform: translate(0px, 0px) rotate(2.5deg)
  }
  88% {
    -ms-transform: translate(0px, 0px) rotate(-6.5deg)
  }
  90% {
    -ms-transform: translate(0px, 0px) rotate(2.5deg)
  }
  92% {
    -ms-transform: translate(0px, 0px) rotate(6.5deg)
  }
  94% {
    -ms-transform: translate(0px, 0px) rotate(-7.5deg)
  }
  96% {
    -ms-transform: translate(0px, 0px) rotate(6.5deg)
  }
  98% {
    -ms-transform: translate(0px, 0px) rotate(-4.5deg)
  }
}
@keyframes shake-rotate {
  0% {
    transform: translate(0px, 0px) rotate(0deg)
  }
  2% {
    transform: translate(0px, 0px) rotate(2.5deg)
  }
  4% {
    transform: translate(0px, 0px) rotate(-4.5deg)
  }
  6% {
    transform: translate(0px, 0px) rotate(2.5deg)
  }
  8% {
    transform: translate(0px, 0px) rotate(4.5deg)
  }
  10% {
    transform: translate(0px, 0px) rotate(-5.5deg)
  }
  12% {
    transform: translate(0px, 0px) rotate(-3.5deg)
  }
  14% {
    transform: translate(0px, 0px) rotate(-1.5deg)
  }
  16% {
    transform: translate(0px, 0px) rotate(-1.5deg)
  }
  18% {
    transform: translate(0px, 0px) rotate(2.5deg)
  }
  20% {
    transform: translate(0px, 0px) rotate(-0.5deg)
  }
  22% {
    transform: translate(0px, 0px) rotate(6.5deg)
  }
  24% {
    transform: translate(0px, 0px) rotate(0.5deg)
  }
  26% {
    transform: translate(0px, 0px) rotate(-5.5deg)
  }
  28% {
    transform: translate(0px, 0px) rotate(-4.5deg)
  }
  30% {
    transform: translate(0px, 0px) rotate(-1.5deg)
  }
  32% {
    transform: translate(0px, 0px) rotate(2.5deg)
  }
  34% {
    transform: translate(0px, 0px) rotate(0.5deg)
  }
  36% {
    transform: translate(0px, 0px) rotate(6.5deg)
  }
  38% {
    transform: translate(0px, 0px) rotate(-6.5deg)
  }
  40% {
    transform: translate(0px, 0px) rotate(3.5deg)
  }
  42% {
    transform: translate(0px, 0px) rotate(-4.5deg)
  }
  44% {
    transform: translate(0px, 0px) rotate(5.5deg)
  }
  46% {
    transform: translate(0px, 0px) rotate(2.5deg)
  }
  48% {
    transform: translate(0px, 0px) rotate(2.5deg)
  }
  50% {
    transform: translate(0px, 0px) rotate(5.5deg)
  }
  52% {
    transform: translate(0px, 0px) rotate(-1.5deg)
  }
  54% {
    transform: translate(0px, 0px) rotate(5.5deg)
  }
  56% {
    transform: translate(0px, 0px) rotate(-7.5deg)
  }
  58% {
    transform: translate(0px, 0px) rotate(2.5deg)
  }
  60% {
    transform: translate(0px, 0px) rotate(6.5deg)
  }
  62% {
    transform: translate(0px, 0px) rotate(1.5deg)
  }
  64% {
    transform: translate(0px, 0px) rotate(-7.5deg)
  }
  66% {
    transform: translate(0px, 0px) rotate(-2.5deg)
  }
  68% {
    transform: translate(0px, 0px) rotate(3.5deg)
  }
  70% {
    transform: translate(0px, 0px) rotate(-4.5deg)
  }
  72% {
    transform: translate(0px, 0px) rotate(2.5deg)
  }
  74% {
    transform: translate(0px, 0px) rotate(2.5deg)
  }
  76% {
    transform: translate(0px, 0px) rotate(-2.5deg)
  }
  78% {
    transform: translate(0px, 0px) rotate(-0.5deg)
  }
  80% {
    transform: translate(0px, 0px) rotate(-3.5deg)
  }
  82% {
    transform: translate(0px, 0px) rotate(5.5deg)
  }
  84% {
    transform: translate(0px, 0px) rotate(-1.5deg)
  }
  86% {
    transform: translate(0px, 0px) rotate(-1.5deg)
  }
  88% {
    transform: translate(0px, 0px) rotate(4.5deg)
  }
  90% {
    transform: translate(0px, 0px) rotate(5.5deg)
  }
  92% {
    transform: translate(0px, 0px) rotate(4.5deg)
  }
  94% {
    transform: translate(0px, 0px) rotate(-1.5deg)
  }
  96% {
    transform: translate(0px, 0px) rotate(3.5deg)
  }
  98% {
    transform: translate(0px, 0px) rotate(-7.5deg)
  }
}

@mixin carAnimations($name: shake-little){
  -webkit-animation-name: $name;
  -ms-animation-name: $name;
  animation-name: $name;
  -webkit-animation-duration: 100ms;
  -ms-animation-duration: 100ms;
  animation-duration: 100ms;
  -webkit-animation-iteration-count: infinite;
  -ms-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: ease-in-out;
  -ms-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
  -webkit-animation-delay: 0s;
  -ms-animation-delay: 0s;
  animation-delay: 0s;
  -webkit-animation-play-state: running;
  -ms-animation-play-state: running;
  animation-play-state: running
}

.uec-login{width:550px;margin:0 auto;}
.uec-login-box{  width: 546px;  height: auto;  background: rgba(255,255,255,0.8);  z-index: 50;  border-radius: 4px;}
.uec-head,.uec-s-title h2,.uec-industry{width: 90%;margin:0 auto;min-width: 320px;max-width: 1220px;}
.uec-head .uec-save{background-color: $c-52;color:$c-f;}
.uec-h-btn{float: right;padding:0 30px;color:$c-52;line-height: 2.2rem;background-color: $c-f;margin:5px;@include border-radius(3px);border:$c-52 solid 1px;}
.uec-h-btn:hover,.uec-h-set-a:hover{background-color: $c-8a9;color: $c-f;border:$c-8a9 solid 1px;}
.uec-h-set-a{position: absolute;right: -83px;line-height: 2.2rem;padding:0 15px;color:$c-52;background-color: $c-f;margin-top: 25px;@include border-radius(3px);border:$c-52 solid 1px;}
.uec-section{position: relative;}
.uec-section:hover{
  .uec-section-btn,.uec-section-btn1{display: block;}
}
.uec-section-btn,.uec-section-btn1{display: none;}
.uec-section-btn,.uec-section-btn1,.uec-custom-btn{z-index: 889;background-color: $rgb-01;
  .icon-xianshi{font-size: 0.8rem;}
  .icon-biyan1{font-size: 2rem;padding-top: 3px;}
  a,a.uec-icon{float: right;text-align:center;color:$c-337;width:28px;height: 28px;background-color: $c-f;line-height: 28px;@include border-radius(3px);margin-right: 10px;border: 0;}
}
.uec-custom-btn a:hover,
.uec-custom-btn a.uec-icon:hover{
  color:$c-235;
  background-color: $c-f;
}
.uec-section-btn,
.uec-section-btn1{
  position: absolute;right: 0;top:0;bottom:0;left: 0;text-align: right;
}
.uec-section-btn{padding-top:10px;}
.uec-section-btn1{padding-top:88px;}
.uec-service .uec-invester-ul li{font-size:1rem;line-height: 2.15rem;}
//banner or slider css begin
.uec-banner{
  min-height: 85px;width:100%;position: relative;
  .m-controls{margin-top: -32px;}
  .m-controls .m-page span{background: $c-6; opacity: 0.8;}
  .m-controls .m-page.active span{background: $c-f; opacity: 0.8;}
}
.uec-tit-h3{position: absolute; bottom:0;margin-bottom:1.4rem; left: 0; right: 0; text-align: center; color: $c-f; font-size: 1.15rem; line-height: 2rem;
  background: rgba(0, 0, 0, 0.3) none repeat scroll 0 0; padding-top: 0.15rem;}
.uec-c-roll .item{position:relative;}
.uec-c-roll .m-theme .m-controls .m-buttons{display: none;}
.uec-c-roll .m-slider{clear: both;}
.uec-head-solution{padding: 5.2rem 3rem; height: 100%;}
.uec-head-solution img{width: 90%;}
.uec-indcase-roll .uec-tit-h3{margin-bottom: 0; width: 100%; padding: 0.3rem 0.714rem 0.3rem 0.714rem; line-height: 1.4rem; font-size: 1rem;}
.uec-head-solution .uec-tit-h3{
  @include transition(0.7s);
  width: 90%; left: 0; font-size: 1.5rem; line-height: 2rem; padding: 0.5rem 0;
  .uec-info{overflow: hidden;height: 0rem; @include transition(0.7s);}
  strong{font-weight: normal;}
  p{ font-size: 1rem; color: $c-ba; line-height: 1.4rem;margin: 0.7rem 1rem;}
  hr{border-top: 1px solid $c-8a9; margin: 0; padding: 0;}
}
.uec-sol-itema{display: block;
  &:hover{
    margin-bottom:0;
    .uec-info{ height: 9rem;}
  }
}

.uec-head-solution .m-theme .m-controls{margin-top: -18%;}
.uec-head-solution .m-theme .m-controls .m-buttons div{
  background: none;@include border-radius(0px);
  &.m-prev{float: left; background-position: 0 0; width: 2.57rem; height: 5.15rem; margin-left: -50px;}
  &.m-next{float:right; background-position: -38px 0; width: 2.57rem; height: 5.15rem; margin-right: -26px;}
}
//banner or slider css end

.uec-b-p,.uec-b-a{text-align: center;}
.uec-b-p{color: $c-f;padding:20% 0 0;}
.uec-b-a{color: $c-f;padding:1% 0;border:$c-f solid 1px;@include border-radius(5px);display: block;margin:5% auto;}
.uec-b-a:hover,.uec-b-a:visited{color: $c-f;}
//index-section-title
.uec-s-title{width: 100%;border-bottom:$c-cf solid 1px;margin-bottom: 1.8rem;clear: both;
  h2{font-size: 1.43rem;line-height: 5.73rem;position: relative;}
  strong{font-weight: inherit;}
  b{font-size: 1.14rem;border-left:$c-9c solid 1px;display: inline-block;line-height: 2rem;padding-left: 1rem;margin-left: 1rem;font-weight: inherit;}
  h2:after{position: absolute;left: -0.3rem;bottom: -0.13rem;width:17rem;height: 3px;background-color:$c-d51 ;content: '';}
  h2.uec-s-2:after{background-color:$c-34c}
  h2.uec-s-3:after{background-color:$c-1dd}
  h2.uec-s-4:after{background-color:$c-4c5}
}

//index-section1-news
.uec-sect1-r li{display: none;}
.uec-sect1-r li.on{display: block;}
.uec-news-aimg{display: table;}
.uec-s1-news-img{vertical-align: middle; display: table-cell; text-align: center; overflow: hidden;}
.uec-sect1-l{float: left;padding-left: 4.5%;margin-top:1rem;
  li{margin-bottom: 5px;cursor: pointer;}
  li:hover,li.on{
    .uec-s1-span{background-color:$c-b01 ;color: $c-f;}
    a{color:$c-b01;}
  }
}
.uec-s1-span,.uec-s1-a{display: inline-block;vertical-align: middle;}
.uec-s1-span{font-size: 1.285rem;background-color:$c-eae;padding:3px 8px;color: $c-909;text-align: center;@include border-radius(3px);
  small{font-size: 2.14rem;width:100%;text-align: center;display: block;line-height: 2.14rem;}
}
.uec-s1-a{color: $c-42;margin-left: 13px;width:78%;line-height: 1.8rem;font-size:1.14rem;}
.uec-s1-more{width: 50%;margin-left: 88px;line-height: 4.1rem;font-size: 1.2rem;}
.uec-sect1-r{float: left;width: 39%; margin-top: 0.8rem;height: 24.4rem;overflow: hidden; border: 1px solid $c-d5;}
.uec-s1-img{width:100%;}
.uec-s1-r-p{width:87.37%;display: block;font-size: 1.142rem;line-height: 2rem;margin:1rem auto; margin-bottom: 0; color: $c-37; cursor: pointer;}

.uec-s1-more,.uec-c-r-a{color: $c-338;}
//index-section2-PRODUCT
.uec-product{width:100%;background-image: url("../images/onebridge.jpg");@include bg-para;color: $c-f;text-align: center;padding-bottom: 7.5%;}
.uec-p-h2{padding-top: 12%;margin:0;
  i,em{display: inline-block;vertical-align: middle;font-size: 4rem;font-style: normal;}
  i{font-size: 6rem;padding-right:5px;}
}
.uec-p-p1{font-size: 2rem;padding-top:0.5%;}
.uec-p-p2{font-size: 1.4rem;}
.uec-p-a{width: 20%;  background: $c-e95;  display: block;  margin: 1.5% auto 0;  font-size: 1.5rem;  line-height: 4rem;  color: $c-f;  @include border-radius(5px);}
.uec-p-a:hover{color: $c-f;}


//.index-section-case
.ml-case{margin-left: 4%;}
.uec-case-l img,.uec-case-r img{width:100%;}
.uec-case-l{margin:0.2rem 0 0 0;}
.uec-case-r,.uec-case-l{float:left;}

.uec-case-one{min-height: 13rem; padding-left: 0.9rem;}
.uec-c-r-p{line-height: 1.8rem;}
.uec-c-r-h3{font-size: 1.57rem;padding-bottom:15px;margin:0;}
.uec-c-roll{width:100%;}
.uec-c-ul{clear: both;
  li{float: left;width:30.6%;margin-top: 10px;position: relative;}
  .uec-c-bg{opacity:0.3;background-color: $c-0;}
  p{margin: 0;}
  .uec-c-bg,p{position: absolute;bottom: 0;left: 0;height:2.5rem;line-height:2.5rem;width:52%;text-align: center;color: $c-f;}
}
.uec-c-right{float: right;padding: 3px;border:$c-c solid 1px;@include border-radius(3px); margin-bottom: 0.76rem; margin-right: 0.17rem;
  li{display: inline-block;vertical-align: middle;color: $c-c;line-height: 1.2rem;font-size: 1.3rem;}
  i{font-size: 1rem;margin:0 3px;cursor: pointer;}
  i.uec-left{padding-left: 2px;display: block;}
  li:hover i{color: $c-7f;}
}
.uec-c-r-a{color: $c-337;}

//.index-section-solution
.uec-solution{width:100%;height: 35.7rem;background-image:url("../images/solutions.jpg") ;@include bg-para;margin-bottom: 1rem;}
.uec-s-div{position: relative;}


//solution.html
.uec-solution-banner{width:100%;@include bg-para;overflow: hidden; position: relative;
  .uec-banner-textimg{width: 42rem; height: 6.642rem; background-repeat: no-repeat; background-position: center;
    position: absolute; top: 50%; left: 50%; margin-top: -3.321rem;
    margin-left:-21rem;@include background-size(100%);}
}
.uec-news-textimg{background-image: url("../images/news-textimg.png");}
.uec-case-textimg{background-image: url("../images/case-textimg.png");}
.uec-product-textimg{background-image: url("../images/product-textimg.png");}
.uec-solution-textimg{background-image: url("../images/solution-textimg.png");}

.uec-s-b-img{margin:0 auto;display: block;}
.uec-s-tab{width:100%;border-bottom:$c-c solid 1px;font-size: 1.214rem;line-height: 3.6rem;
  li{display: inline-block;vertical-align: middle;margin-right:0.5%;color: $c-6f;}
  a{color: $c-6f;}
}
.uec-s-t-i{float: left;background-color: $c-ff6;
  i{font-size: 1.5rem;line-height: 5.3rem;color: $c-f;padding: 0 1rem;display: block;}
}
.uec-s-tab-css{float: left;  position: relative; height: 5.2rem; width: 82.2%;overflow: hidden;}
.uec-s-tab1{width: 100%;color: $c-5d; overflow: hidden;
  .uec-s-ul-js{overflow: hidden; position: relative; height: 5.286rem;}
  li{min-width: 9rem;float: left; font-size: 1.1rem; position: relative;}
  a,span{display: block;vertical-align: middle;}
  a{color: $c-6;text-align: center; width: 100%; line-height: 1.8rem; padding: 1.8rem 0.4rem 1.8rem 0.3rem;}
  span{position: absolute;right: -3px; top:0; color: $c-c;line-height: 5.3rem;font-size:1.287rem; }
  a:hover{color: $c-ff8;}
  li.on a{color:$c-ff8;}
}
//.uec-s-ul-js{float: left;}
.uec-s-t-r{position: absolute; right: 0; border:$c-c solid 1px;padding:0.357rem;margin-top:1rem;@include border-radius(100%);margin-right: 1%;display: none;
  i{line-height: 2rem;display: inline-block;vertical-align: middle;cursor: pointer;color: $c-c;}
  .icon-sanjiao{margin-right: -8px;}
  .icon-right{margin-top: 2px; margin-left: 0.4rem;}
  i:hover{color: $c-7f}
}
.uec-s-ul{max-width:73.71rem;width:90%;margin:0 auto;display: block;padding:2.1428rem 0;
  a{display:table;@include transition(1s);width:100%;height:22.5rem;position: relative;@include box-shadow($c-c)}
}
.uec-s-img1{display: table-cell;vertical-align: middle;}
.uec-s-li{margin:1.7857rem 4.2%;position: relative;float: left;width: 24%;;}

.uec-s-li img{width:100%;}
.uec-s-bg,.uec-s-h3{position: absolute;bottom: 1.7857rem;left:0;width:100%;height: 3.3rem;@include transition(0.5s);overflow: hidden;}
.uec-s-bg{background-color: $c-0;opacity: 0.3;}
.uec-s-h3{color: $c-f;}
.uec-s-em{font-style: normal;padding-top: 0.6rem;padding-bottom:0.7rem;font-size: 2rem;text-align: center;width:100%;display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;}
.uec-s-content{border-top:$c-c solid 1px;font-size: 1rem;word-wrap: break-word; line-height: 1.5rem;  padding: 1.071rem 10%;text-align: center;}
.uec-s-li a:hover{
  .uec-s-bg,.uec-s-h3{bottom:0;height: 16rem;}
}
.uec-s-li.uec-s-lilist a:hover{
  .uec-s-bg,.uec-s-h3{bottom:0;height: 10rem;}
}
.uec-herf-span{
  color: $c-1ea;
}
.uec-more-icon{font-family: SimSun; font-style: normal; margin-left: 0.2rem;}
.uec-herf-span:hover{color: $c-f;}
.uec-s-cover{position: absolute;top:0;left: 0;bottom:-1px;right:0;background-color: $rgb-01;text-align: right;display: none;
  .icon-moban1{padding-top: 1px;font-size: 1.3rem;}
  a{float:right;  width: 2rem;  height: 2rem;  background-color: $c-f; color: $c-337;@include border-radius(3px);margin-right: 0.5rem;margin-top: 0.5rem;  line-height: 2rem;  text-align: center;}
}
.uec-s-li:hover .uec-s-cover{display: block;}
.uec-s-li a.icon-jia,.uec-in-li a.icon-jia{text-align: center;background-color: $c-f7;color:$c-d9d ;border:$c-d9d solid 1px;line-height: 22.3rem;font-size: 5rem;}
.uec-s-li a.icon-jia:hover,.uec-in-li a.icon-jia:hover{color: $c-b9;}




//行业案例.html
.uec-in-li a.icon-jia{display: block;width:100%;line-height: 11.79rem;}
.uec-indust-ul{overflow: hidden;padding-bottom: 2%;}
.uec-in-li{float: left;width:49%;margin:3% 0.5% 2%;position: relative;}
.uec-in-li:hover .uec-s-cover{display: block;}
.uec-in-img,.uec-in-div{float: left;}
.uec-in-img img{width:100%;vertical-align: middle;}
.uec-in-img{width:41%;overflow: hidden;}
.uec-in-div{padding-left: 5%;width: 53%;
  h2{font-size: 1.28571rem;margin-top: 5%;a{color: $c-0;}a:hover{color: $c-1dd;border-bottom:$c-1dd solid 1px;}}
  a.uec-in-more{color: $c-b32; display: inline-block; border-bottom:1px solid $c-f; }
  a.uec-in-more:hover{border-bottom:1px solid $c-b32;}
  p{line-height: 1.5rem;margin-top:1.428rem;}
}
.uec-ind-tab{border-bottom: $c-ff6 solid 1px;position: relative; overflow: hidden;margin-top: 1.1428rem;}
.uec-ind-list{width:78%;height: 5.26rem;position: relative;overflow: hidden;float: left;}
.uec-in-more{font-size: 1.1rem;display: none;}

//solution-details.html
.uec-rc-ul{
  padding:3.57rem 0 0 6.4285rem;
  border-top:$c-d8 solid 1px;
  li{display:inline-block;vertical-align:middle;width: 24%;}
  a{width: 100%;}
  img{width: 84%;display: block;margin: 0 auto;}
}
.uec-solu-dt-cont{padding-bottom: 6%;}
.uec-solu-dt-h2{line-height: 5.6rem;font-size: 2.1428rem;}
.uec-solu-details .uec-ind-tab{display: none;}
.uec-sd-ul{padding-top: 0.5%;}
.uec-sd-li{
  .uec-icon{font-size: 1.5rem;}
  .uec-icon,.uec-sd-em,.uec-sd-sm{display: inline-block;vertical-align: middle;}
  float: left;width:48.5%;margin-right: 1.5%;  background-color: $c-f5;  margin-bottom: 1.2%;  padding-left:0.857rem;  line-height: 4.6rem;@include border-radius(2px);
}
.uec-sd-h4{padding-left: 1.1%;padding-top: 1%;font-size: 1.2857rem;}
.uec-sd-h3{margin: 3% 0 2%;
  em,span,b{display: inline-block;vertical-align: middle;}
  em{font-style: normal;margin-left: 1.1%;font-size: 1.7142rem;}
  b{font-size:1.4rem;margin-left:0.714rem;padding-left: 0.714rem;font-weight: 500;border-left: $c-9c solid 1px; }
  span{background-color: $c-ff6;width:3px;height:1.357rem;border-radius: 2px;}
}
.uec-sd-p{text-indent: 1.428rem;font-size: 1.142857rem;line-height:2rem;margin: 0 1.1%;}
.uec-sd-em{font-style: normal;width:8rem;font-size: 1.42857rem;margin-left: 0.714rem;}
.uec-sd-sm{font-size: 1.1rem;border-left: $c-c dashed 1px;line-height:1.3rem;padding-left: 0.714rem;width: 64%;}
.uec-sd-img1 img,.uec-sd-img2 img{width:100%;}
.uec-sd-img1{padding-top: 2%;}
.uec-sd-img2{margin-bottom: 5rem;}
.uec-related-case{ a{cursor: default;}}
.uec-sol-case{
  .sm,.hid,.sm .sm-carousel-spanimg{ height: 12.1428rem;}
}
.uec-adjunct{
  .sm,.hid,.sm .sm-carousel-spanimg{height: auto;}
  .hid{top:0; bottom:0; right: 0; left:0;}
}

//product.html
.uec-product-banner{width:100%;background-image: url("../images/solution.jpg");@include bg-para;}
.uec-prod-h3{text-align: center;font-size: 1.857142rem;position: relative;line-height:5rem;padding-top: 1.8%;}
.uec-prod-h3:after{position: absolute;bottom:-2px;left:50%;margin-left:-2.5714rem;width:5.142rem;height: 2px;background-color: $c-ff6;content: '';}
.uec-p-sect1{padding: 3% 0 2%;}
.uec-p-l,.uec-p-r{display: inline-block;vertical-align: middle;
  img{width:100%;}}
.uec-p-r{width:36.7%;}
.uec-p-l{width:63%;height: auto;font-size: 1.4285rem;
  .uec-p-text{padding-left: 2%;width:88%; word-break: break-all;}
  .uec-p-long{width:4px;height: 1.714rem;background-color: $c-ff6;float: left;@include border-radius(2px);}
}
.uec-p-d-img{width: 100%;height: 250px;overflow: hidden;}
.uec-p-sect2{background-color:$c-f0f;}
.uec-p-bg{background-color: $c-f5;width: 100%;height:11.4rem;position: relative;}

.uec-p-ul1{text-align: center;padding: 4% 0 5.5%;position: relative;
  //a:hover{.uec-icon{border:$c-ff6 solid 2px;color: $c-ff6;}}
  li{float: left;width:100%;}
  .uec-icon{color:$c-54;width: 6.428rem;height: 6.428rem;@include border-radius(45px);line-height: 6.428rem;display: block;margin: 0 auto;font-size: 4rem;  }
  .uec-p-img{width: 6.428rem;height: 6.428rem;@include border-radius(100%);overflow: hidden; display: block;margin: 0 auto;
    background: url('../images/case_01.jpg') no-repeat 50% 50% $c-f;
    @include background-size(100%);
    img{width:100%;height: 100%;}
  }
}
.uec-ps1-p{width:76%;margin:0 auto;color: $c-85;text-align: left;font-size: 1.2857rem;line-height: 2.5rem; word-break: break-all;}
.uec-ps1-h4{color: $c-54;font-size: 1.5714rem;line-height: 3rem;margin-top: 1.5714rem;}
.uec-p-ul2{margin-top: 4%;position: relative;
  a .uec-p-bg1,a .uec-p-p,a span,a em,a i{@include transition(1s);}
  a.not:hover{cursor: default;}
  a:hover .uec-p-bg1{height:17rem;background-color: $c-ff6;}
  a:hover{
    .uec-p-p{height: 8rem;color: $c-f;}
    em,span{color: $c-f;}
    i{color:$rgb-01;}
  }
  img{width:100%;}
  .item{margin:0 1.5rem;}
}
.uec-p-ul3{
  width:72%;padding:5% 0 0 0;position: relative;
  img{width: 100%;}
  .item{float: left;padding:0 5%;width:100%;}
}
.uec-ps4-p{text-align: center;font-size: 1.5714rem;padding-top: 1.571rem;width: 100%; word-break: break-all;}
.uec-p-p{height:0;font-size: 1.1rem;overflow: hidden;}
.uec-p-bg1{height: 4px;background-color:$c-e1 ;}
.uec-p-bg1,.uec-p-cCont{position: absolute;left: 0;right: 0;bottom: 0;}
.uec-p-cCont{text-align: center;
  span,em,i{display: block;font-size: 1.571428rem;}
  p{width:90%;text-align: left;color:$c-42 ;margin:0 auto;}
  em,span{font-style: normal;color: $c-42;line-height: 2.5rem;}
  em{padding-bottom: 2%; word-break: break-all;}
  span{padding-top:6% ;}
  i{color: $rgb-03;padding: 3% 0;}
}
#slider-roll1 .m-item,#slider-roll2 .m-item{float: left;padding:0 1%;}
#slider-roll{
  margin-left: 5px;
  .m-item{float: left;padding:0 0.5rem 0 0.5rem;}
}
#slider-roll3 .m-item{float:left;}
#slider-roll1,#slider-roll2,#slider-roll{
  .m-prev{left: 0;background-position: 0 50%;}
  .m-prev,.m-next{cursor:pointer;position: absolute;top:50%;@include background-size(200%);margin-top:-1.7857rem;width:3.5714rem;height: 3.5714rem;background-image:url("../images/solution_02.png");}
  .m-next{right:0;background-position: 3.5714rem 50%;}
}
.investor-listbox{overflow: inherit;}
#slider-roll3,.uec-p-slider,#slider-solution{
  .m-pagination{display: none;}
  &.m-theme .m-controls .m-buttons div{
    &.m-prev,&.m-next{cursor:pointer;font-size: 2.3rem; color: $c-969; opacity: 0.9; background-color: inherit; position: absolute;top:50%;margin-top:-1.42rem;width:2.85rem;height: 2.85rem;
      line-height: 2rem; text-align: center;}
  }
  .m-prev{left: -2.857rem;}
  .m-next{right:-1.92857rem;}
}
#slider-solution{
  width: 99%;
  .m-item {margin: 0 0.28rem;}
  &.m-theme .m-controls .m-buttons div {
    &.m-prev, &.m-next {
      cursor: pointer;
      font-size: 2.8rem;
      color: $c-f;
    }
  }
  .m-prev{left: 0; }
  .m-next{right:-0.8rem;}
}
.uec-ps5-l,.uec-ps5-r{
  float: left;
  width: 50%;
  padding-top: 3.2%;
}
.uec-prod-h4{
  color: $c-43;
  font-size: 1.68rem;
  letter-spacing:3px;
  position: relative;
  width: 50%;
  padding-left: 0.3rem;
  padding-bottom: 1rem;
  &:after{
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 38%;
    height: 2px;
    content: '';
    background-color: $c-ff6;
  }
  border-bottom:$c-c solid 2px;
}
.uec-ps5-ul{
  padding: 2.15rem 0;
  li{
    padding:0 2%;
  }
  a{
    display: block;
    color:$c-337 ;
    line-height: 2.8rem;
    font-size:1.1428rem;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow:ellipsis;
  }
  a:hover{
    color:$c-b01;
    text-decoration: underline;
  }
}
.uec-ps5-ul1{
  span{text-align: center;width: 100%;font-size:1.1428rem;display: block;line-height: 4rem;}
  padding-top: 2.15rem;
  li{float: left;padding-right: 0.88rem;
    width: 33.3333%;
  }
  img{width: 100%;}
  a{
    display: block;
  }
  a:hover{
    span{color: $c-b01;text-decoration: underline;}
  }
}


//investor.html
#uec-h-search-js{display: none;}
.investor-report-line{z-index:0;}
.investor-box{width: auto; min-height: 24.42857rem;}
.investor-list{margin:0 0.9285rem;}
.investor-stocktab a{margin-right: 1px;}
.investor-gover-dl{position: relative;}
.uec-manage-system{overflow:hidden;position: relative;}
.uec-invester-ul{float: left;height: 19.2857rem; display: none;}
.uec-invester-ul.on{display: block;}



//product-list.html
.uec-pL-js,.uec-pL-item-js{cursor: pointer;}


//case-detail.html
.uec-case-d-css{position: relative;}
.uec-case-d-css p.uec-sd-p{overflow: hidden;}
.uec-case-d-css .uec-sd-p.mt10{display: none;}
.uec-case-d-css .uec-in-more{display: block;padding-top: 0.8571rem;}
.uec-csd-img{float: right;margin-top: -2.0714rem;width:21%;}
.uec-cd-img{margin:0 auto;display: block;margin-top: 3%;width: 35%;min-width: 15.7142rem;}
.mt10{margin-top: 0.7142rem;}
.uec-cd-h4{    padding-left: 1.1%;font-size: 1.2857rem;}
.pb10{padding-bottom: 3%;}
.uec-rs-ul{
  border-top:$c-d8 solid 1px;width: 100%;padding:2rem 2rem 0rem 2rem;
  a:before{content: '';width: 4px;height: 4px;margin-right:1.07rem;background-color: $c-76;display: inline-block;vertical-align: middle;}
  a{line-height: 2.8571rem; color:$c-214 ;text-decoration: underline;font-size: 1.1428rem;}
  a:hover{color:$c-f60;}
}


//news.html
.uec-n-tab .uec-s-tab{border:none;line-height: 4.2rem;}
.uec-n-ul{margin-top: 3px;}
.uec-n-l,.uec-n-r,.uec-n-li{float: left;}
.uec-n-l{border:$c-d5 solid 1px;}
.uec-n-li{margin-bottom: 1.5rem;width:100%;}
.uec-n-l{margin-right: 4%;width:19%;min-width:15rem;overflow: hidden;}
.uec-n-r{width:77%;border-bottom:$c-c dashed 1px;cursor: pointer; padding-bottom: 0.5rem;}
.uec-n-l a{display: table;width: 100%;}
.uec-news-img{display: table-cell;vertical-align: middle; text-align: center;}
.uec-n-l img{width:100%;}
.uec-n-time{float: left;width: 5rem;height: 5rem;  background-color: $c-c; color:$c-91; @include border-radius(3px);text-align: center;  font-size: 1.2857rem;}
.uec-n-span{width: 100%;  display: block;  font-size: 2.5714rem;  line-height: 2.5714rem;padding-top: 0.5rem; color: $c-7f;}
.uec-n-r a:hover{
  .uec-n-time{background-color: $c-b01; color: $c-f;}
  .uec-n-span{color: $c-f;}
  .uec-n-h3{text-decoration: underline;color: $c-b01;}
}
.uec-n-div{float: right;width: 88%;min-height: 9.5rem;color: $c-6;}
.uec-n-h3{margin:0;font-size: 1.2857rem;line-height: 1.5rem;color: $c-2e2;}
.uec-n-p{line-height: 1.8rem;margin-top: 2%;font-size: 1rem;min-height: 5.1rem;}
.uec-n-share{position: relative;float: right; text-align: center;}
.uec-share-btn{display: block;width:5.35rem; background-color: $c-eb; font-size: 1rem; line-height: 2.28rem; border: 1px solid $c-ba;
  @include border-radius(3px);
  &:hover,&.on{color: $c-1e5; background-color: $c-f;}
}
.uec-n-link{position: absolute; z-index: 9; top:2.29rem; background-color: $c-f; width:5.35rem;border: 1px solid $c-ba; border-top:0; display: none;
  @include border-radius(3px);
  li{line-height: 1.72rem; position: relative;border-top: 1px solid $c-ba;}
  a{width:2rem;height: 1.4285rem;line-height:1.4285rem;@include border-radius(2px);font-size:1rem;color: $c-f;background-color: $c-c;}
  a.icon-weixin1:hover{background-color: $c-20a;}
  a.icon-unie61d:hover{background-color: $c-e89;}
  a.icon-in:hover{background-color: $c-006;}
  a.icon-iconfontkongjian:hover{background-color: $c-1c8;}
  .uec-ewm-close{ font-family: SimSun; font-style: normal; position: absolute;margin-right: 0.5rem;top: 0px;right: -1px;font-size: 1.2rem;cursor: pointer;}
  .uec-ewm{display: none; position: absolute;z-index: 9; top: 2rem;left: -3.4rem;  background-color: $c-eb;  padding:1.5rem 0.8rem 0.2rem 0.8rem;}
  .uec-ewm-text{color: $c-0;}
}

//news-detail.html
.uec-nd-ul li:last-child{border-bottom: none;}
.uec-nd-tab .uec-s-tab{border:none;line-height: 4.2rem;}
.uec-nd-l{float: left;}
.uec-nd-r{float: right;width:30%;padding-top: 3.5rem;}
.uec-nd-l{width:67%;padding-bottom: 3%;}
.uec-nd-h2{font-size: 2.1428rem;width: 90%;line-height: 3.5rem; text-align: center;}
.uec-nd-h3{padding-left:1%;font-size: 1.4285rem;margin:0;border-bottom:$c-c solid 1px;color: $c-b01;line-height: 4rem;padding-top: 2%;}
.uec-nd-h4{@extend %text-overflow;font-size: 1.14rem;margin:0;color:$c-337;}
.uec-nd-div{border-bottom: $c-c dashed 1px;}
.uec-nd-data,.uec-nd-fanart{line-height: 1.3rem;width: auto;text-align: center; display: inline-block;
  span{padding-left: 1.07rem;}
}
.uec-nd-fanart{width: 38rem;display: inline-block; margin-top: 0.73rem; }
.uec-nd-suite{position: relative; padding: 1.3rem 1rem 0.8rem 6rem; text-align: center;}
.uec-nd-link{float: right;}
.uec-nd-img{margin:8% auto 0;display: block;min-width: 16.4285rem;width:76%;}
.uec-nd-p{width: 90%;  font-size: 1rem; color: $c-42; padding-top: 4%;  line-height: 2rem;  margin: 0 auto;}
.uec-nd-r-a{float: right;font-size: 1rem;color: $c-c;}
.uec-nd-li{ float: left;border-bottom: $c-c solid 1px;padding:1rem 2% 0 2%;width:100%;}
.uec-nd-li a:hover,.uec-nd-li.on a{
  .uec-n-time{background-color: $c-b01;color: $c-f;}
  .uec-nd-h4{color: $c-b01;text-decoration: underline;}
  .uec-n-span{color: $c-f;}
}
.uec-nd-r-div{float: left;width: 67%;padding-left: 4%;}
.uec-nd-r-p{position:relative;min-height:4rem;color:$c-6;line-height: 1.5rem;margin:2% 0 3%;font-size: 1rem;;overflow: hidden;display: -webkit-box;-webkit-line-clamp: 3; -webkit-box-orient: vertical;}


//分页.html
.pagination>li>a, .pagination>li>span{
  background-color: $c-f;
  border: 1px solid $c-b5b;
  color: $c-3;
}
.pagination>.active>a, .pagination>.active>span,.pagination>.active>a:focus, .pagination>.active>span:focus,.pagination>.active>a:hover{
  background-color: $c-b5b;
  border-color: $c-b5b;
}
.pagination>li>a:hover, .pagination>li>span:hover,.pagination>li>a:focus, .pagination>li>span:focus{
  background-color: $c-e;
  border-color: $c-b5b;
  color: $c-3;
}
.pagination .uec-icon{font-size: 0.7142rem;color: #999;}

//shop.html
.uec-sp-cart-js{right: -276px;z-index:990;}
.uec-shop-icon-js{
  &.on{@include carAnimations(shake-rotate);}
}

//investor.html
.uec-in-st-box-js{position: relative;}
.uec-in-stock-js{position: absolute;left:0;top:0;width:9999px;}
.investor-stock-left{overflow: hidden;}
//.investor-stocktab a{width:83px;}

.uec-custom-tm{padding-bottom: 3.57rem;}


@media screen and (min-width: 1366px){
  html{font-size: 14px;}
  .uec-sect1-l{width:56%;}
  .uec-sect1-r{
    .uec-news-aimg{display: block;
      display: -webkit-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      -webkit-align-items: center;
      align-items: center;
    }
  }
  .uec-case-r{width:55%;padding-left: 0.7142rem;}
  .uec-b-p{font-size:3.57rem;}
  .uec-b-a{font-size: 2rem;width:22%;}
}
@media (max-width: 1366px) and (min-width: 1050px){
  .uec-sect1-l{width:56%;}
  .uec-sect1-r{
    .uec-news-aimg{ @include imgDisplay()}
  }
  .uec-case-l{width:45%;max-width: 37.57rem;}
  .uec-case-r{width:55%;}
  .uec-b-p{font-size:3.57rem;}
  .uec-b-a{font-size: 2rem;width:22%;}
}
@media (max-width: 1050px) and (min-width: 890px){
  .uec-ps5-l,.uec-ps5-r{width: 100%;}
  html{font-size: 13px;}
  .uec-sect1-l{width:56%;}
  .uec-sect1-r{
    .uec-news-aimg{ @include imgDisplay()}
  }
  .uec-case-l{width:45%;}
  .uec-case-r{width:54%;}
  .uec-b-p{font-size:1.8rem;}
  .uec-b-a{font-size: 2rem;width:22%;}
  .uec-s-tab-css{width: 66.8%;}
}
@media screen and (max-width: 890px){
  html,body{font-size: 12px; min-width: 320px;}
  .uec-sect1-l,.uec-sect1-r{width:100%;margin-bottom:2rem;}
  .uec-sect1-l .uec-s1-more{display: none;}
  .uec-case-r{width:100%;padding-bottom: 1.4285rem;}
  .uec-sect1-r{height: auto;margin-bottom: 1rem;padding:0 1rem; border: 0;
    .uec-s1-r-p{display: none;}
  }
  .uec-s-t-r{padding: 0.35rem;}
  .uec-nd-suite{padding-left: 0; padding-right: 0.5rem;}
  .uec-n-link .uec-ewm{left: -4.7rem;}
  .uec-b-p{font-size:1.8rem;}
  .uec-c-r-h3{line-height: 3rem;}
  .uec-b-a{font-size: 1rem;width:30%;}
  .uec-solution-banner .uec-s-b-img,.uec-product-banner .uec-s-b-img{width:100%;}
  .uec-solution-banner .uec-banner-textimg{@include background-size(56%); margin-top: -2.4rem;}
  .uec-s-tab{ .uec-crumbs{display: none;}}
  .uec-s-em{padding-top: 1rem;padding-bottom:0.9rem;}
  .uec-nd-tab .uec-s-tab{display: block;border:none;padding-top:3%;padding-left: 8%;
    h2{width:100%;}
    ul{display: none;}}
  .uec-solu-sect .uec-s-tab1{display: block;}
  .uec-c-roll{padding-left: 0; margin-left: -0.4rem;}
  .uec-head-solution .m-theme .m-controls{margin-top: -33%;}
  .uec-head-solution .m-theme .m-controls .m-buttons div{
    &.m-next{background-position: -2.1428rem 0; }
  }
  .uec-ps5-l,.uec-ps5-r{width: 100%;}
  .uec-s1-hv-js >li .uec-s1-a{width: 70%;}
  .uec-solu-sect .uec-s-ul a{box-shadow:none;}
  .uec-s-ul .uec-s-li{
    width: 46%;margin:0.5714rem 2%;
    em{font-size: 1.4rem;}
    a:hover{
      .uec-s-bg,.uec-s-h3{bottom:0;height: 14rem;}
    }
  }
  .uec-industry .uec-indust-ul{padding: 3% 0 3% 4%;}
  .uec-indust-ul .uec-in-more{display:block;}
  .uec-solu-sect .uec-industry{width:100%;}
  .uec-in-li{
    .uec-in-img{width: 90%;}
    .uec-in-div{width:90%;padding-left: 0;
      h2{font-size:1.3rem;}
      p{line-height: 1.8rem;  margin-top: 1.0714rem;}
    }
  }
  .uec-sd-ul .uec-sd-li{width:100%;.uec-sd-sm{width:60%;}}
  .uec-p-sect1{
    .uec-p-r,.uec-p-l{width: 100%;}
    .uec-p-l{height: auto;.uec-p-text{width:100%;}}
  }
  .uec-p-ul2 .uec-p-p{font-size: 1.1rem;}
  .uec-p-sect2 .uec-p-ul1{ .item{width: 100%;padding-bottom:7%;}}
  .uec-n-ul .uec-n-li{margin-bottom: 0;}
  .uec-n-p{ overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 3;-webkit-box-orient: vertical;word-spacing: normal;}
  .uec-n-li{
    .uec-n-l{width:90%;margin:1.2rem auto;border:none; float:none; clear: both; height: auto;}
    .uec-n-r{width: 100%;padding:0 1rem 0.3rem 1rem;}
    .uec-n-div{width: 85%;
      float: left;margin-left: 2%;
    }
  }
  .uec-head{
    .uec-rc-ul li{width: 49%;}
    .uec-nd-li{padding-bottom: 1rem;}
    .uec-nd-r,.uec-nd-l{width: 100%;}
    .uec-nd-l .uec-nd-data{padding-left: 0.2rem; text-align: left; width: 20rem;}
    .uec-nd-r{
      .uec-n-span{line-height: 2.7rem;}
      .uec-nd-r-div{width: 86%;padding-right: 0;}
      .uec-nd-r-p{-webkit-line-clamp:2;margin-top:2%;line-height:1.8rem;}
    }
  }
  #slider-roll3,.uec-p-slider,#slider-solution {
    .m-pagination {
      display: block;
    }
  }
  .uec-s-tab{display: none;}
}
@media screen and (max-width: 580px){
  .uec-ps5-l,.uec-ps5-r{width: 100%;}
  .uec-ps5-ul1{
    li{width: 80%;float: none;margin:0 auto;}
  }
  .m-controls .m-buttons{display: none;}
  .uec-head{
    .uec-nd-r,.uec-nd-l{width: 100%;}
    .uec-nd-r{
      .uec-nd-r-div{width: 75%;padding-right: 0;}
      .uec-nd-r-p{margin-top:5%;}
    }
  }
  .uec-n-li{
    .uec-n-div{width: 73%;
    }
  }
  .uec-s-tab-css{width: 66%;}
}

@media screen and (max-width: 480px){
  .uec-head-solution .m-theme .m-controls{margin-top: -67%;}
}
