#!/usr/bin/env node

const redis = require('redis');
const config = require('./libs/config');

// 创建Redis客户端 (旧版本语法)
const client = redis.createClient(config.redis.port, config.redis.ip, {
    auth_pass: config.redis.password || undefined
});

function viewRedisData() {
    console.log('✅ 正在连接Redis服务器...');
    console.log('📊 Redis配置信息:');
    console.log(`   主机: ${config.redis.ip}`);
    console.log(`   端口: ${config.redis.port}`);
    console.log(`   密码: ${config.redis.password ? '已设置' : '未设置'}`);
    console.log('');

    client.on('connect', function() {
        console.log('✅ 成功连接到Redis服务器');

        // 获取所有键
        client.keys('*', function(err, keys) {
            if (err) {
                console.error('❌ 获取键列表失败:', err.message);
                client.quit();
                return;
            }

            console.log(`🔑 Redis中共有 ${keys.length} 个键:`);
            console.log('');

            if (keys.length === 0) {
                console.log('📭 Redis中没有数据');
                client.quit();
                return;
            }

            let processed = 0;

            keys.forEach(function(key) {
                console.log(`📝 键名: ${key}`);

                // 获取键的类型
                client.type(key, function(typeErr, type) {
                    if (typeErr) {
                        console.log(`   类型: 获取失败 - ${typeErr.message}`);
                    } else {
                        console.log(`   类型: ${type}`);
                    }

                    // 获取TTL
                    client.ttl(key, function(ttlErr, ttl) {
                        if (ttlErr) {
                            console.log(`   过期时间: 获取失败 - ${ttlErr.message}`);
                        } else {
                            if (ttl > 0) {
                                const hours = Math.floor(ttl / 3600);
                                const minutes = Math.floor((ttl % 3600) / 60);
                                console.log(`   过期时间: ${hours}小时${minutes}分钟后过期`);
                            } else if (ttl === -1) {
                                console.log(`   过期时间: 永不过期`);
                            } else {
                                console.log(`   过期时间: 已过期或不存在`);
                            }
                        }

                        // 获取值
                        if (type === 'string') {
                            client.get(key, function(getErr, value) {
                                if (getErr) {
                                    console.log(`   内容: 获取失败 - ${getErr.message}`);
                                } else {
                                    try {
                                        // 尝试解析JSON
                                        const jsonData = JSON.parse(value);
                                        console.log(`   内容 (JSON):`);
                                        console.log(JSON.stringify(jsonData, null, 4).split('\n').map(line => `     ${line}`).join('\n'));
                                    } catch (e) {
                                        // 如果不是JSON，直接显示
                                        console.log(`   内容: ${value.substring(0, 200)}${value.length > 200 ? '...' : ''}`);
                                    }
                                }
                                console.log('');

                                processed++;
                                if (processed === keys.length) {
                                    // 显示Redis统计信息
                                    client.info('memory', function(infoErr, info) {
                                        if (!infoErr) {
                                            const memoryInfo = info.split('\n').filter(line =>
                                                line.includes('used_memory_human') ||
                                                line.includes('used_memory_peak_human') ||
                                                line.includes('connected_clients')
                                            );

                                            console.log('📈 Redis内存使用情况:');
                                            memoryInfo.forEach(line => {
                                                if (line.trim()) {
                                                    console.log(`   ${line}`);
                                                }
                                            });
                                        }
                                        client.quit();
                                    });
                                }
                            });
                        } else {
                            console.log(`   内容: 非字符串类型 (${type})`);
                            console.log('');

                            processed++;
                            if (processed === keys.length) {
                                client.quit();
                            }
                        }
                    });
                });
            });
        });
    });

    client.on('error', function(err) {
        console.error('❌ Redis连接错误:', err.message);
    });
}

// 运行脚本
viewRedisData();
