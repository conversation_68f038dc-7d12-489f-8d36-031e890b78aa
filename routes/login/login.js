/**
 * Created by yubin on 2016/1/26.
 * 登录相关路由（不受系统登录节制）
 */
var express = require('express');
var router = express.Router();
var restApiProxy = require("../../libs/authRestApiProxy");
var constants = require("../../libs/constants");
var redisClient = require("../../libs/redisClient");
var log4js = require("../../libs/log4jsUtil.js");
var logger = log4js.getLogger('login');
var permissionUtil = require("../../libs/permissionUtil");
var logUtil = require('../../libs/logUtil.js');
var emailUtil = require("../../libs/emailUtil");
var merchantUtil = require('../../libs/merchantUtil.js');

//登录
router.post('/li', function (req, res, next) {
    const { username, password } = req.body;
    
    // 检查管理员凭据
    if (username === 'admin' && password === 'rongzhilian1819') {
        // 管理员用户
        var mockUser = {
            userId: 'admin001',
            username: 'admin',
            name: '系统管理员',
            roles: ['admin', 'superadmin'],
            permissions: ['*'],
            accountName: 'admin'
        };
        // 管理员拥有所有权限
        var mockActions = {
            actionInfos: [
                { accode: 'index' },
                { accode: 'sysuserList' },
                { accode: 'sysuserAdd' },
                { accode: 'sysuserEdit' },
                { accode: 'goodsList' },
                { accode: 'goodsAdd' },
                { accode: 'goodsEdit' },
                { accode: 'customerMgr' },
                { accode: 'newsMgr' },
                { accode: 'newsAdd' },
                { accode: 'roleList' },
                { accode: 'departmentList' },
                { accode: 'honorList' },
                { accode: 'investorList' },
                { accode: 'approvedList' },
                { accode: 'vendorList' },
                { accode: 'enquiryList' },
                { accode: 'employmentList' },
                { accode: 'goodsCategoryList' },
                { accode: 'agencyList' },
                { accode: 'contactUsList' },
                { accode: 'orderMgr' },
                { accode: 'payTypeMgr' },
                { accode: 'logMgr' },
                { accode: 'websiteContentMgr' }
            ]
        };
    } else {
        // 普通测试用户
        var mockUser = {
            userId: 'testuser123',
            username: 'testuser',
            name: '测试用户',
            roles: ['user'],
            permissions: ['basic_access'],
            accountName: '测试账号'
        };
        // 普通用户只有基本权限
        var mockActions = {
            actionInfos: [
                { accode: 'index' },
                { accode: 'goodsList' },
                { accode: 'customerMgr' },
                { accode: 'newsMgr' }
            ]
        };
    }
    
    // 模拟商户信息
    var mockMerchantInfo = {
        status: 1,
        name: '测试商户'
    };
    
    // 判断商户是否启用
    if (mockMerchantInfo.status != 1) {
        var msg = mockMerchantInfo.status === 0 ? '禁用' : '删除';
        logger.warn("商户未启用，登录失败，商户状态码：" + mockMerchantInfo.status + ",状态：" + msg);
        var result = {"message": {"object.error.message": ["商户未启用，登录失败"]}, "code": 2};
        return res.json(result);
    }
    
    // 模拟登录成功响应
    var loginResponse = {
        code: 0,
        message: '登录成功',
        data: {
            token: 'mock_token_' + Date.now(),
            user: mockUser
        }
    };
    
    // 设置session
    req.session.sysuser = mockUser;
    req.session.actions = mockActions; // 设置权限信息
    
    // 设置cookie
    res.cookie('accountName', mockUser.accountName);
    
    logger.debug('模拟登录成功，用户信息:' + JSON.stringify(mockUser));
    res.json(loginResponse);
});

//跳转到找回密码页面
router.get('/sfpp', function (req, res, next) {
    res.render('login/fetchPassword', {title: '找回密码'});
});

//调用后台找回密码
router.post('/fp', function (req, res, next) {
    req.body.serverHost = req.headers.host;
    restApiProxy.post('RetailerService', "/sysuser/fp", req.body, function (err, data) {
        if (err) {
            logUtil.saveLog(req, logUtil.initLogMsg(constants.LOG_LOGINROUTER_OPERATEPATH, constants.LOG_LOGINROUTER_QUERY, constants.LOG_LOGINROUTER_QUERY, constants.RESULT_FAILURE, null));//推送日志
            logger.debug(err);
            var jsonStr = JSON.stringify(err);
            res.write(jsonStr);
            res.end();
        } else if (data) {
            //发送邮件
            //获取邮件地址
            logUtil.saveLog(req, logUtil.initLogMsg(constants.LOG_LOGINROUTER_OPERATEPATH, constants.LOG_LOGINROUTER_QUERY, constants.LOG_LOGINROUTER_QUERY, constants.RESULT_SUCCESS, null));//推送日志
            var emailVo = {};
            emailVo.receiver = data.email;
            emailVo.subject = data.subject;
            emailVo.content = data.content;

            emailUtil.sendEmail(emailVo, function (mailErr, mailData) {
                if (mailErr == null && mailData != null) {
                    //发送成功
                    var jsonStr = JSON.stringify(data);
                    res.write(jsonStr);
                    res.end();
                } else {
                    var jsonStr = JSON.stringify(mailErr);
                    logger.error(jsonStr);
                    var result = {"message": {"object.error.message": ["发送邮件失败"]}, "code": 2};
                    res.write(JSON.stringify(result));
                    res.end();
                }
            });

        }
    });
});
//跳转到找回密码成功页面
router.get('/sfpr', function (req, res, next) {
    res.render('login/fetchPasswordResult', {title: '找回密码成功'});
});

//密码重置
router.get('/resetPassword', function (req, res, next) {
    var userId = req.param('userId');
    var token = req.param('token');
    //判断连接是否失效
    var key = constants.USER_PASSWORD_TOKER + userId;
    redisClient.get(key, function (err, data) {
        logger.debug('redis key[' + key + '] err:' + err);
        logger.debug('redis key[' + key + '] data:' + data);
        if (data) {
            res.render('login/resetPassword', {userId: userId, token: token});
        } else {
            res.render('login/resetPwdFailure', {title: '验证链接失效'});
        }
    });
});

//调用后台重置密码
router.post('/rp', function (req, res, next) {
    restApiProxy.post('RetailerService', "/sysuser/rp", req.body, function (err, data) {
        if (err) {
            logUtil.saveLog(req, logUtil.initLogMsg(constants.LOG_LOGINROUTER_OPERATEPATH, constants.LOG_LOGINROUTER_UPDATE, constants.LOG_LOGINROUTER_UPDATE, constants.RESULT_SUCCESS, req.body.userId));//推送日志
            logger.debug(err);
            var jsonStr = JSON.stringify(err);
            res.write(jsonStr);
            res.end();
        }
        if (data) {
            logUtil.saveLog(req, logUtil.initLogMsg(constants.LOG_LOGINROUTER_OPERATEPATH, constants.LOG_LOGINROUTER_UPDATE, constants.LOG_LOGINROUTER_UPDATE, constants.RESULT_FAILURE, req.body.userId));//推送日志
            var jsonStr = JSON.stringify(data);
            res.write(jsonStr);
            res.end();
        }
    });
});
//安全退出
router.get('/lo', function (req, res, next) {
    //获取当前用户信息
    var currentUser = req.session.sysuser;
    if (currentUser) {
        req.session.sysuser = null;
    }
    res.render('login/login');
});
module.exports = router;