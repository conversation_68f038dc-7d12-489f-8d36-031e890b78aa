#!/usr/bin/env node

const http = require('http');
const querystring = require('querystring');

// 测试配置
const TEST_CONFIG = {
    host: 'localhost',
    port: 8088,
    loginPath: '/login/li',
    indexPath: '/index',
    logoutPath: '/login/lo'
};

function makeRequest(options, postData, cookies = '') {
    return new Promise((resolve, reject) => {
        if (cookies) {
            options.headers = options.headers || {};
            options.headers['Cookie'] = cookies;
        }
        
        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    body: data
                });
            });
        });
        
        req.on('error', (err) => {
            reject(err);
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

function extractCookies(headers) {
    const setCookieHeader = headers['set-cookie'];
    if (!setCookieHeader) return '';
    
    return setCookieHeader.map(cookie => cookie.split(';')[0]).join('; ');
}

async function testSessionFlow() {
    console.log('🔐 测试完整的会话流程');
    console.log('=' .repeat(50));
    
    try {
        // 步骤1: 登录获取会话
        console.log('\n📝 步骤1: 管理员登录');
        const loginData = querystring.stringify({
            username: 'admin',
            password: 'rongzhilian1819'
        });
        
        const loginOptions = {
            hostname: TEST_CONFIG.host,
            port: TEST_CONFIG.port,
            path: TEST_CONFIG.loginPath,
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Content-Length': Buffer.byteLength(loginData)
            }
        };
        
        const loginResponse = await makeRequest(loginOptions, loginData);
        console.log(`   登录状态: ${loginResponse.statusCode}`);
        
        // 提取会话Cookie
        const sessionCookies = extractCookies(loginResponse.headers);
        console.log(`   会话Cookie: ${sessionCookies ? '✅ 已获取' : '❌ 未获取'}`);
        
        if (sessionCookies) {
            console.log(`   Cookie内容: ${sessionCookies.substring(0, 100)}...`);
        }
        
        // 解析登录响应
        const loginResult = JSON.parse(loginResponse.body);
        console.log(`   登录结果: ${loginResult.code === 0 ? '✅ 成功' : '❌ 失败'}`);
        if (loginResult.data && loginResult.data.user) {
            console.log(`   登录用户: ${loginResult.data.user.name} (${loginResult.data.user.username})`);
        }
        
        // 步骤2: 使用会话访问首页
        console.log('\n🏠 步骤2: 访问首页 (带会话)');
        const indexOptions = {
            hostname: TEST_CONFIG.host,
            port: TEST_CONFIG.port,
            path: TEST_CONFIG.indexPath,
            method: 'GET'
        };
        
        const indexResponse = await makeRequest(indexOptions, null, sessionCookies);
        console.log(`   首页状态: ${indexResponse.statusCode}`);
        console.log(`   内容长度: ${indexResponse.body.length} 字符`);
        
        // 检查是否成功访问首页
        if (indexResponse.statusCode === 200 && indexResponse.body.length > 1000) {
            console.log(`   ✅ 成功访问首页`);
        } else if (indexResponse.body.includes('登录') || indexResponse.body.includes('login')) {
            console.log(`   ❌ 被重定向到登录页`);
        } else {
            console.log(`   ⚠️  响应异常`);
        }
        
        // 步骤3: 测试其他受保护的页面
        console.log('\n👥 步骤3: 访问用户管理页面');
        const sysuserOptions = {
            hostname: TEST_CONFIG.host,
            port: TEST_CONFIG.port,
            path: '/sysuser',
            method: 'GET'
        };
        
        const sysuserResponse = await makeRequest(sysuserOptions, null, sessionCookies);
        console.log(`   用户管理页状态: ${sysuserResponse.statusCode}`);
        
        if (sysuserResponse.statusCode === 200) {
            console.log(`   ✅ 成功访问用户管理页面`);
        } else {
            console.log(`   ❌ 访问用户管理页面失败`);
        }
        
        // 步骤4: 测试无会话访问
        console.log('\n🚫 步骤4: 无会话访问首页');
        const noSessionResponse = await makeRequest(indexOptions);
        console.log(`   无会话访问状态: ${noSessionResponse.statusCode}`);
        
        if (noSessionResponse.body.includes('登录') || noSessionResponse.body.includes('login')) {
            console.log(`   ✅ 正确重定向到登录页`);
        } else {
            console.log(`   ❌ 未正确处理无会话访问`);
        }
        
        // 步骤5: 测试登出
        console.log('\n🚪 步骤5: 测试登出功能');
        const logoutOptions = {
            hostname: TEST_CONFIG.host,
            port: TEST_CONFIG.port,
            path: TEST_CONFIG.logoutPath,
            method: 'GET'
        };
        
        const logoutResponse = await makeRequest(logoutOptions, null, sessionCookies);
        console.log(`   登出状态: ${logoutResponse.statusCode}`);
        
        if (logoutResponse.statusCode === 200 && 
            (logoutResponse.body.includes('登录') || logoutResponse.body.includes('login'))) {
            console.log(`   ✅ 成功登出并重定向到登录页`);
        } else {
            console.log(`   ⚠️  登出响应异常`);
        }
        
        // 步骤6: 验证登出后无法访问受保护页面
        console.log('\n🔒 步骤6: 验证登出后访问控制');
        const afterLogoutResponse = await makeRequest(indexOptions, null, sessionCookies);
        console.log(`   登出后访问首页状态: ${afterLogoutResponse.statusCode}`);
        
        if (afterLogoutResponse.body.includes('登录') || afterLogoutResponse.body.includes('login')) {
            console.log(`   ✅ 登出后正确重定向到登录页`);
        } else {
            console.log(`   ❌ 登出后仍能访问受保护页面`);
        }
        
    } catch (error) {
        console.log(`❌ 测试过程中出错: ${error.message}`);
    }
    
    console.log('\n' + '=' .repeat(50));
    console.log('🎯 会话流程测试完成');
}

// 运行测试
testSessionFlow().catch(console.error);
