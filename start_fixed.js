#!/usr/bin/env node

/**
 * Module dependencies.
 */

var app = require('./app_fixed');
var debug = require('debug')('portal:server');
var http = require('http');
var config = require('./libs/config');

/**
 * Get port from environment and store in Express.
 */

var port = normalizePort(process.env.PORT || '8088');
app.set('port', port);

/**
 * Create HTTP server.
 */

var server = http.createServer(app);

/**
 * Listen on provided port, on all network interfaces.
 */

server.listen(port, '0.0.0.0');
server.on('error', onError);
server.on('listening', onListening);

console.log('🚀 Portal 应用程序启动成功！');
console.log('📍 访问地址: http://localhost:' + port);
console.log('📍 或者访问: http://127.0.0.1:' + port);
console.log('⏰ 启动时间: ' + new Date().toLocaleString('zh-CN'));
console.log('🔐 管理员账号: admin / rongzhilian1819');

/**
 * Normalize a port into a number, string, or false.
 */

function normalizePort(val) {
  var port = parseInt(val, 10);

  if (isNaN(port)) {
    // named pipe
    return val;
  }

  if (port >= 0) {
    // port number
    return port;
  }

  return false;
}

/**
 * Event listener for HTTP server "error" event.
 */

function onError(error) {
  if (error.syscall !== 'listen') {
    throw error;
  }

  var bind = typeof port === 'string'
    ? 'Pipe ' + port
    : 'Port ' + port;

  // handle specific listen errors with friendly messages
  switch (error.code) {
    case 'EACCES':
      console.error('❌ ' + bind + ' 需要管理员权限');
      process.exit(1);
      break;
    case 'EADDRINUSE':
      console.error('❌ ' + bind + ' 端口已被占用');
      process.exit(1);
      break;
    default:
      throw error;
  }
}

/**
 * Event listener for HTTP server "listening" event.
 */

function onListening() {
  var addr = server.address();
  var bind = typeof addr === 'string'
    ? 'pipe ' + addr
    : 'port ' + addr.port;
  debug('Listening on ' + bind);
  console.log('✅ 服务器正在监听 ' + bind);
}
