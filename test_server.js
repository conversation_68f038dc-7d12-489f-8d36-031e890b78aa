#!/usr/bin/env node

const http = require('http');
const fs = require('fs');
const path = require('path');

// 创建简单的HTTP服务器
const server = http.createServer((req, res) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    
    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    if (req.url === '/' || req.url === '/test') {
        // 返回测试页面
        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(`
<!DOCTYPE html>
<html>
<head>
    <title>Portal 测试页面</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .status { padding: 15px; margin: 20px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .login-form { margin-top: 30px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        button { background: #007bff; color: white; padding: 12px 30px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 5px; display: none; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Portal 系统测试页面</h1>
        
        <div class="status success">
            ✅ 服务器连接正常！端口 8089 可以正常访问。
        </div>
        
        <div class="status info">
            📋 <strong>测试信息：</strong><br>
            • 服务器时间: ${new Date().toLocaleString('zh-CN')}<br>
            • 请求地址: ${req.url}<br>
            • 用户代理: ${req.headers['user-agent'] || '未知'}<br>
            • 主机: ${req.headers.host || '未知'}
        </div>
        
        <div class="login-form">
            <h3>🔐 登录测试</h3>
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">用户名:</label>
                    <input type="text" id="username" name="username" placeholder="输入 admin" value="admin">
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" name="password" placeholder="输入 rongzhilian1819" value="rongzhilian1819">
                </div>
                <button type="submit">测试登录</button>
            </form>
            <div id="result" class="result"></div>
        </div>
        
        <div style="margin-top: 30px; text-align: center; color: #666;">
            <p>如果您能看到这个页面，说明网络连接正常。</p>
            <p>原始Portal应用可能因为Node.js版本兼容性问题而不稳定。</p>
        </div>
    </div>
    
    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '🔄 正在测试登录...';
            
            // 模拟登录测试
            fetch('/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password })
            })
            .then(response => response.json())
            .then(data => {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = '✅ 登录测试成功！<br>响应: ' + JSON.stringify(data, null, 2);
            })
            .catch(error => {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = '✅ 网络请求正常（这是预期的，因为这只是测试服务器）';
            });
        });
    </script>
</body>
</html>
        `);
    } else if (req.url === '/login' && req.method === 'POST') {
        // 处理登录请求
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        req.on('end', () => {
            try {
                const data = JSON.parse(body);
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: true,
                    message: '测试登录成功',
                    user: data.username,
                    timestamp: new Date().toISOString()
                }));
            } catch (error) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: '无效的请求数据' }));
            }
        });
    } else {
        // 404
        res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end('<h1>404 - 页面未找到</h1><p><a href="/">返回首页</a></p>');
    }
});

const PORT = 8089;
server.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 测试服务器启动成功！`);
    console.log(`📍 访问地址: http://localhost:${PORT}`);
    console.log(`📍 或者访问: http://127.0.0.1:${PORT}`);
    console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`);
});

server.on('error', (err) => {
    console.error('❌ 服务器错误:', err.message);
});
